const bcrypt = require('bcryptjs')
const { userCollection } = require('../utils/db')
const { validateLogin, validateRegister, validateRefreshToken, validateActivationCode } = require('../utils/validate')
const { generateTokenPair, verifyToken, REFRESH_TOKEN_EXPIRES_IN } = require('../middleware/auth')
const { createBusinessError, successResponse, ERROR_CODES } = require('../middleware/error_handler')
const { redeemActivationCode, validateActivationCode: validateCodeOnly } = require('../utils/activation_code')
const logger = require('../utils/logger')

/**
 * 用户登录
 * @param {object} params 登录参数
 * @returns {object} 登录结果
 */
async function login(params) {
  try {
    // 参数校验
    const { username, password } = validateLogin(params)
    
    logger.info('User login attempt', { username })
    
    // 查找用户
    const user = await userCollection.findByUsername(username)
    if (!user) {
      throw createBusinessError(
        ERROR_CODES.INVALID_CREDENTIALS,
        '用户名或密码错误',
        401
      )
    }
    
    // 检查用户状态
    if (user.status !== '激活') {
      throw createBusinessError(
        ERROR_CODES.USER_DISABLED,
        '用户账户已被禁用',
        401
      )
    }
    
    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password)
    if (!isPasswordValid) {
      throw createBusinessError(
        ERROR_CODES.INVALID_CREDENTIALS,
        '用户名或密码错误',
        401
      )
    }
    
    // 生成Token对
    const tokens = generateTokenPair(user._id, user.username)

    // 计算token过期时间
    const accessTokenExpiresAt = new Date()
    accessTokenExpiresAt.setTime(accessTokenExpiresAt.getTime() + 60 * 60 * 1000) // 1小时后过期

    // 计算refreshToken过期时间
    const refreshTokenExpiresAt = new Date()
    refreshTokenExpiresAt.setDate(refreshTokenExpiresAt.getDate() + 30) // 30天后过期
    
    // 更新用户的refreshToken和登录时间
    await userCollection.update(user._id, {
      refreshToken: tokens.refreshToken,
      refreshTokenExpiresAt,
      lastLoginAt: new Date()
    })
    
    logger.info('User login successful', {
      userId: user._id,
      username: user.username
    })
    
    // 返回用户信息和Token（不包含敏感信息）
    return successResponse({
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        phone: user.phone,
        membership: user.membership,
        availableCount: user.availableCount,
        totalUsageCount: user.totalUsageCount,
        createdAt: user.createdAt
      },
      tokens: {
        ...tokens,
        expiresAt: accessTokenExpiresAt.toISOString()
      }
    }, '登录成功')
    
  } catch (error) {
    logger.error('Login failed', {
      username: params.username,
      error: error.message
    })
    throw error
  }
}

/**
 * 用户注册
 * @param {object} params 注册参数
 * @returns {object} 注册结果
 */
async function register(params) {
  try {
    // 参数校验
    const { username, password, email, phone, activationCode } = validateRegister(params)

    logger.info('User registration attempt', { username, email, phone, hasActivationCode: !!activationCode })

    // 检查用户名是否已存在
    const existingUser = await userCollection.findByUsername(username)
    if (existingUser) {
      throw createBusinessError(
        ERROR_CODES.USER_ALREADY_EXISTS,
        '用户名已存在',
        409
      )
    }

    // 处理激活码（如果提供）
    let activationQuota = 0
    let activationInfo = null
    if (activationCode && activationCode.trim()) {
      try {
        activationInfo = await redeemActivationCode(activationCode.trim(), null, username)
        activationQuota = activationInfo.quota
        logger.info('Activation code redeemed successfully', {
          username,
          quota: activationQuota,
          creator: activationInfo.creator
        })
      } catch (activationError) {
        logger.error('Activation code redemption failed', {
          username,
          error: activationError.message
        })
        throw activationError // 激活码错误直接抛出，阻止注册
      }
    }

    // 加密密码
    const saltRounds = 12
    const hashedPassword = await bcrypt.hash(password, saltRounds)

    // 创建用户数据
    const userData = {
      username,
      password: hashedPassword,
      email: email || null,
      phone: phone || null,
      membership: {
        type: '普通用户',
        expiresAt: null
      },
      availableCount: activationQuota, // 使用激活码获得的算力
      purchaseHistory: [], // 新用户无购买历史
      totalUsageCount: 0,
      status: '激活',
      refreshToken: null,
      refreshTokenExpiresAt: null,
      isWeakAdmin: false // 新增管理员字段，默认为false
    }

    // 创建用户
    const userId = await userCollection.create(userData)

    logger.info('User registration successful', {
      userId,
      username,
      activationQuota,
      hasActivationCode: !!activationCode
    })

    // 构建响应消息
    let message = '注册成功'
    if (activationQuota > 0) {
      message += `，获得${activationQuota}算力`
    }

    // 返回成功信息（不返回敏感信息）
    return successResponse({
      user: {
        id: userId,
        username,
        email,
        phone,
        membership: userData.membership,
        availableCount: userData.availableCount
      },
      activation: activationInfo ? {
        quota: activationQuota,
        creator: activationInfo.creator
      } : null
    }, message)

  } catch (error) {
    logger.error('Registration failed', {
      username: params.username,
      error: error.message
    })
    throw error
  }
}

/**
 * 刷新Token
 * @param {object} params 刷新参数
 * @returns {object} 新的Token
 */
async function refresh(params) {
  try {
    // 参数校验
    const { refreshToken } = validateRefreshToken(params)
    
    logger.info('Token refresh attempt')
    
    // 验证refreshToken
    const decoded = verifyToken(refreshToken, 'refresh')
    
    // 查找用户并验证refreshToken
    const user = await userCollection.findByRefreshToken(refreshToken)
    if (!user) {
      throw createBusinessError(
        ERROR_CODES.TOKEN_INVALID,
        'Refresh Token无效',
        401
      )
    }
    
    // 检查refreshToken是否过期
    if (user.refreshTokenExpiresAt && new Date() > new Date(user.refreshTokenExpiresAt)) {
      throw createBusinessError(
        ERROR_CODES.TOKEN_EXPIRED,
        'Refresh Token已过期',
        401
      )
    }
    
    // 检查用户状态
    if (user.status !== '激活') {
      throw createBusinessError(
        ERROR_CODES.USER_DISABLED,
        '用户账户已被禁用',
        401
      )
    }
    
    // 生成新的Token对
    const tokens = generateTokenPair(user._id, user.username)

    // 计算新的accessToken过期时间
    const accessTokenExpiresAt = new Date()
    accessTokenExpiresAt.setTime(accessTokenExpiresAt.getTime() + 60 * 60 * 1000) // 1小时后过期

    // 计算新的refreshToken过期时间
    const refreshTokenExpiresAt = new Date()
    refreshTokenExpiresAt.setDate(refreshTokenExpiresAt.getDate() + 30)
    
    // 更新用户的refreshToken
    await userCollection.update(user._id, {
      refreshToken: tokens.refreshToken,
      refreshTokenExpiresAt
    })
    
    logger.info('Token refresh successful', {
      userId: user._id,
      username: user.username
    })
    
    return successResponse({
      tokens: {
        ...tokens,
        expiresAt: accessTokenExpiresAt.toISOString()
      }
    }, 'Token刷新成功')
    
  } catch (error) {
    logger.error('Token refresh failed', {
      error: error.message
    })
    throw error
  }
}

/**
 * 验证激活码
 * @param {object} params 验证参数
 * @returns {object} 验证结果
 */
async function validateActivationCodeHandler(params) {
  try {
    // 参数校验
    const { activationCode } = validateActivationCode(params)

    logger.info('Activation code validation attempt')

    // 验证激活码
    const codeData = await validateCodeOnly(activationCode)

    logger.info('Activation code validation successful', {
      quota: codeData.quota,
      creator: codeData.creator
    })

    return successResponse({
      valid: true,
      quota: codeData.quota,
      creator: codeData.creator,
      timestamp: codeData.timestamp
    }, '激活码验证成功')

  } catch (error) {
    logger.error('Activation code validation failed', {
      error: error.message
    })

    // 如果是业务错误，返回具体错误信息
    if (error.code) {
      return successResponse({
        valid: false,
        error: error.message
      }, '激活码验证失败')
    }

    throw error
  }
}

module.exports = {
  login,
  register,
  refresh,
  validateActivationCode: validateActivationCodeHandler
}