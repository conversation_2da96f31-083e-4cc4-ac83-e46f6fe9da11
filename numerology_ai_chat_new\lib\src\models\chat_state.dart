import 'package:flutter/foundation.dart';
import 'package:collection/collection.dart';
import 'conversation_model.dart';
import 'agent_model.dart';
import 'ai_model.dart';
import 'bazi_model.dart';
import 'chat_message.dart';

@immutable
class ChatState {
  final List<ConversationModel> conversations;
  final String? currentSessionId;
  final bool isLoading;
  final String? error;
  final Set<String> retryingMessageIds; // 正在重试的消息ID集合

  const ChatState({
    this.conversations = const [],
    this.currentSessionId,
    this.isLoading = false,
    this.error,
    this.retryingMessageIds = const {},
  });

  /// 获取当前会话
  ConversationModel? get currentConversation =>
      currentSessionId == null
          ? null
          : conversations.firstWhereOrNull((c) => c.id == currentSessionId);

  /// 是否正在等待AI响应
  bool get isStreaming => currentConversation?.isStreaming ?? false;

  /// 获取当前选择的智能体
  AgentModel? get selectedAgent => currentConversation?.selectedAgent;

  /// 获取当前选择的AI模型
  AIModel? get selectedModel => currentConversation?.selectedModel;

  /// 获取当前八字数据
  BaziResultModel? get currentBaziData => currentConversation?.baziData;
  
  /// 是否可以设置八字信息
  bool get canSetBazi => !isStreaming && (currentConversation == null || currentConversation!.isEmpty);

  /// 获取当前消息列表
  List<ChatMessage> get messages => currentConversation?.messages ?? [];

  /// 是否可以切换智能体（新对话或对话未开始）
  bool get canSwitchAgent => !isStreaming && (currentConversation == null || currentConversation!.isEmpty);

  /// 是否可以切换模型（新对话或对话未开始）
  bool get canSwitchModel => !isStreaming && (currentConversation == null || currentConversation!.isEmpty);

  /// 是否可以发送消息（需要满足所有必要条件）
  bool get canSendMessage {
    if (isStreaming) return false;
    if (selectedAgent == null) return false;
    if (selectedModel == null) return false;

    // 检查是否需要八字信息
    if (selectedAgent!.requiresBazi && currentBaziData == null) {
      return false;
    }

    return true;
  }

  /// 获取阻止发送消息的原因
  String get sendMessageBlockReason {
    if (isStreaming) return 'AI正在回复中，请稍候...';
    if (selectedAgent == null) return '请先选择智能体';
    if (selectedModel == null) return '请先选择AI模型';

    // 检查是否需要八字信息
    if (selectedAgent!.requiresBazi && currentBaziData == null) {
      return '该智能体需要八字信息，请先完成排盘';
    }

    return '请输入消息内容';
  }

  /// 兼容旧代码：获取当前会话【currentSession当前会话】
  ConversationModel? get currentSession => currentConversation;

  /// 兼容旧代码：是否正在输入（流式响应中）【isTyping正在输入】
  bool get isTyping => isStreaming;

  /// 检查指定消息是否正在重试
  bool isMessageRetrying(String messageId) => retryingMessageIds.contains(messageId);

  ChatState copyWith({
    List<ConversationModel>? conversations,
    String? currentSessionId,
    bool? isLoading,
    String? error,
    Set<String>? retryingMessageIds,
    bool clearError = false,
  }) {
    return ChatState(
      conversations: conversations ?? this.conversations,
      currentSessionId: currentSessionId ?? this.currentSessionId,
      isLoading: isLoading ?? this.isLoading,
      error: clearError ? null : error ?? this.error,
      retryingMessageIds: retryingMessageIds ?? this.retryingMessageIds,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ChatState &&
        listEquals(other.conversations, conversations) &&
        other.currentSessionId == currentSessionId &&
        other.isLoading == isLoading &&
        other.error == error &&
        setEquals(other.retryingMessageIds, retryingMessageIds);
  }

  @override
  int get hashCode {
    return conversations.hashCode ^
        currentSessionId.hashCode ^
        isLoading.hashCode ^
        error.hashCode ^
        retryingMessageIds.hashCode;
  }
} 