<template>
  <div class="user-quota">
    <div class="page-header">
      <h2>用户算力管理</h2>
      <p>查询和管理用户算力</p>
    </div>

    <!-- 用户查询区域 -->
    <el-card class="search-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>用户查询</span>
        </div>
      </template>
      
      <el-form
        ref="searchFormRef"
        :model="searchForm"
        :rules="searchRules"
        inline
        class="search-form"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="searchForm.username"
            placeholder="请输入用户名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            :loading="searching"
            @click="handleSearch"
          >
            {{ searching ? '查询中...' : '查询用户' }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 用户信息展示 -->
    <el-card v-if="userInfo" class="user-info-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>用户信息</span>
        </div>
      </template>
      
      <div class="user-info">
        <div class="info-row">
          <span class="label">用户名：</span>
          <span class="value">{{ userInfo.username }}</span>
        </div>
        <div class="info-row">
          <span class="label">邮箱：</span>
          <span class="value">{{ userInfo.email || '未设置' }}</span>
        </div>
        <div class="info-row">
          <span class="label">手机号：</span>
          <span class="value">{{ userInfo.phone || '未设置' }}</span>
        </div>
        <div class="info-row">
          <span class="label">状态：</span>
          <el-tag :type="userInfo.status === '激活' ? 'success' : 'danger'">
            {{ userInfo.status }}
          </el-tag>
        </div>
        <div class="info-row">
          <span class="label">当前算力：</span>
          <span class="value quota">{{ userInfo.availableCount }}</span>
        </div>
        <div class="info-row">
          <span class="label">总使用次数：</span>
          <span class="value">{{ userInfo.totalUsageCount }}</span>
        </div>
        <div class="info-row">
          <span class="label">注册时间：</span>
          <span class="value">{{ formatDate(userInfo.createdAt) }}</span>
        </div>
        <div class="info-row">
          <span class="label">最后登录：</span>
          <span class="value">{{ formatDate(userInfo.lastLoginAt) }}</span>
        </div>
      </div>
    </el-card>

    <!-- 算力操作区域 -->
    <el-card v-if="userInfo" class="operation-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>算力操作</span>
        </div>
      </template>
      
      <el-form
        ref="operationFormRef"
        :model="operationForm"
        :rules="operationRules"
        class="operation-form"
      >
        <el-form-item label="操作类型" prop="operationType">
          <el-radio-group v-model="operationForm.operationType">
            <el-radio label="increase">增加算力</el-radio>
            <el-radio label="decrease">减少算力</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="算力数量" prop="quotaAmount">
          <el-input-number
            v-model="operationForm.quotaAmount"
            :min="1"
            :max="100000"
            placeholder="请输入算力数量"
          />
        </el-form-item>
        
        <el-form-item label="操作原因">
          <el-input
            v-model="operationForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入操作原因（可选）"
            maxlength="200"
            show-word-limit
            style="width: 400px"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            :loading="operating"
            @click="handleOperation"
          >
            {{ operating ? '操作中...' : '确认操作' }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import weakAdminService from '@/services/weakAdminService'

// 表单引用
const searchFormRef = ref()
const operationFormRef = ref()

// 搜索表单数据
const searchForm = reactive({
  username: ''
})

// 搜索表单验证规则
const searchRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 1, max: 50, message: '用户名长度在 1 到 50 个字符', trigger: 'blur' }
  ]
}

// 操作表单数据
const operationForm = reactive({
  operationType: 'increase',
  quotaAmount: 100,
  reason: ''
})

// 操作表单验证规则
const operationRules = {
  operationType: [
    { required: true, message: '请选择操作类型', trigger: 'change' }
  ],
  quotaAmount: [
    { required: true, message: '请输入算力数量', trigger: 'blur' },
    { type: 'number', min: 1, max: 100000, message: '算力数量必须在1-100000之间', trigger: 'blur' }
  ]
}

// 状态
const searching = ref(false)
const operating = ref(false)
const userInfo = ref(null)

// 查询用户
const handleSearch = async () => {
  if (!searchFormRef.value) return

  try {
    const valid = await searchFormRef.value.validate()
    if (!valid) return

    searching.value = true
    const response = await weakAdminService.getUserInfo(searchForm.username)

    if (response.success) {
      userInfo.value = response.data.user
      ElMessage.success('查询成功')
    } else {
      userInfo.value = null
      ElMessage.error(response.error?.message || '查询失败')
    }
  } catch (error) {
    console.error('Search error:', error)
    userInfo.value = null
    ElMessage.error('查询用户失败')
  } finally {
    searching.value = false
  }
}

// 执行算力操作
const handleOperation = async () => {
  if (!operationFormRef.value || !userInfo.value) return

  try {
    const valid = await operationFormRef.value.validate()
    if (!valid) return

    // 确认对话框
    const operationText = operationForm.operationType === 'increase' ? '增加' : '减少'
    const confirmText = `确定要${operationText} ${operationForm.quotaAmount} 算力吗？`

    await ElMessageBox.confirm(confirmText, '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    operating.value = true
    const response = await weakAdminService.modifyUserQuota(
      userInfo.value.username,
      operationForm.operationType,
      operationForm.quotaAmount,
      operationForm.reason
    )

    if (response.success) {
      // 更新用户信息中的算力数量
      userInfo.value.availableCount = response.data.user.availableCount
      ElMessage.success(response.message)

      // 重置操作表单
      operationForm.quotaAmount = 100
      operationForm.reason = ''
    } else {
      ElMessage.error(response.error?.message || '操作失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Operation error:', error)
      ElMessage.error('算力操作失败')
    }
  } finally {
    operating.value = false
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}
</script>

<style scoped>
.user-quota {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 5px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card,
.user-info-card,
.operation-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.user-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.info-row {
  display: flex;
  align-items: center;
  padding: 10px;
  background: #fafafa;
  border-radius: 6px;
}

.label {
  font-weight: 600;
  color: #606266;
  min-width: 100px;
}

.value {
  color: #303133;
  flex: 1;
}

.value.quota {
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
}

.operation-form {
  max-width: 600px;
}

.operation-form .el-form-item {
  margin-bottom: 20px;
}
</style>
