import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/utils/api'
import TokenManager from '@/utils/tokenManager'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref(localStorage.getItem('weak_admin_token') || '')
  const refreshToken = ref(localStorage.getItem('weak_admin_refresh_token') || '')
  const user = ref(JSON.parse(localStorage.getItem('weak_admin_user') || 'null'))
  const loading = ref(false)
  const tokenExpiryTime = ref(null)

  // TokenManager实例
  let tokenManager = null

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)

  // 初始化TokenManager
  const initTokenManager = () => {
    if (!tokenManager) {
      tokenManager = new TokenManager({
        apiService: api,
        onTokenRefreshed: (newToken) => {
          console.log('Auth Store: Token续签成功')
          token.value = newToken
          tokenExpiryTime.value = tokenManager.tokenExpiryTime
        },
        onRefreshFailed: () => {
          console.log('Auth Store: Token续签失败，需要重新登录')
          logout()
          // 可以在这里添加跳转到登录页的逻辑
          window.location.href = '/login'
        }
      })
    }
  }

  // 登录
  const login = async (credentials) => {
    loading.value = true
    try {
      const response = await api.post('/exeWeakAdmin', {
        action: 'weakAdminLogin',
        ...credentials
      })

      if (response.data.success) {
        const { user: userData, tokens } = response.data.data

        // 保存token和用户信息
        token.value = tokens.accessToken
        refreshToken.value = tokens.refreshToken
        user.value = userData

        // 设置token过期时间
        if (tokens.expiresAt) {
          tokenExpiryTime.value = new Date(tokens.expiresAt)
        } else {
          tokenExpiryTime.value = new Date()
          tokenExpiryTime.value.setTime(tokenExpiryTime.value.getTime() + 60 * 60 * 1000) // 默认1小时
        }

        // 持久化存储
        localStorage.setItem('weak_admin_token', tokens.accessToken)
        localStorage.setItem('weak_admin_refresh_token', tokens.refreshToken)
        localStorage.setItem('weak_admin_user', JSON.stringify(userData))

        // 初始化并启动TokenManager
        initTokenManager()
        tokenManager.startAutoRefresh(tokenExpiryTime.value)

        console.log('Auth Store: 登录成功，Token过期时间:', tokenExpiryTime.value)

        return { success: true }
      } else {
        throw new Error(response.data.error?.message || '登录失败')
      }
    } catch (error) {
      console.error('Login error:', error)
      return {
        success: false,
        message: error.response?.data?.error?.message || error.message || '登录失败'
      }
    } finally {
      loading.value = false
    }
  }

  // 退出登录
  const logout = () => {
    // 停止TokenManager
    if (tokenManager) {
      tokenManager.dispose()
      tokenManager = null
    }

    token.value = ''
    refreshToken.value = ''
    user.value = null
    tokenExpiryTime.value = null
    localStorage.removeItem('weak_admin_token')
    localStorage.removeItem('weak_admin_refresh_token')
    localStorage.removeItem('weak_admin_user')
  }

  // 检查token有效性
  const checkAuth = () => {
    const storedToken = localStorage.getItem('weak_admin_token')
    const storedRefreshToken = localStorage.getItem('weak_admin_refresh_token')
    const storedUser = localStorage.getItem('weak_admin_user')

    if (storedToken && storedUser) {
      token.value = storedToken
      refreshToken.value = storedRefreshToken || ''
      user.value = JSON.parse(storedUser)

      // 估算token过期时间（如果没有存储的话）
      if (!tokenExpiryTime.value) {
        tokenExpiryTime.value = new Date()
        tokenExpiryTime.value.setTime(tokenExpiryTime.value.getTime() + 60 * 60 * 1000) // 默认1小时
      }

      // 初始化TokenManager
      initTokenManager()
      if (tokenExpiryTime.value) {
        tokenManager.startAutoRefresh(tokenExpiryTime.value)
      }

      return true
    }

    return false
  }

  // 强制刷新token
  const forceRefreshToken = async () => {
    if (tokenManager) {
      return await tokenManager.forceRefreshToken()
    }
    return false
  }

  // 检查是否需要续签
  const shouldRefreshToken = () => {
    if (tokenManager) {
      return tokenManager.shouldRefreshToken()
    }
    return false
  }

  return {
    token,
    refreshToken,
    user,
    loading,
    tokenExpiryTime,
    isAuthenticated,
    login,
    logout,
    checkAuth,
    forceRefreshToken,
    shouldRefreshToken
  }
})
