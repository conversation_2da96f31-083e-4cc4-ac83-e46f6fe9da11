/// 智能体类型枚举（根据云函数数据库结构）
enum AgentType {
  bazi('八字'),
  z<PERSON><PERSON>('紫微'),
  general('无需携带内容');

  const AgentType(this.displayName);
  final String displayName;

  /// 从字符串创建枚举
  static AgentType fromString(String value) {
    switch (value) {
      case '八字':
        return AgentType.bazi;
      case '紫微':
        return AgentType.ziwei;
      case '无需携带内容':
        return AgentType.general;
      default:
        return AgentType.general;
    }
  }
}

/// 智能体模型（匹配云函数数据结构）
class AgentModel {
  final String id;
  final String agentName;
  final String description;
  final AgentType agentType;
  final String pricingTierId; // 档次ID

  /// 智能体描述（兼容性属性）
  String? get agentDescription => description;

  /// 获取智能体类型的字符串表示
  String get agentTypeString {
    switch (agentType) {
      case AgentType.bazi:
        return '八字';
      case AgentType.ziwei:
        return '紫微';
      case AgentType.general:
        return '无需携带内容';
    }
  }
  final bool isActive;
  final int sortOrder;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? createdBy;
  final String? updatedBy;
  final bool isDefault;
  final bool needLaymanVersion; // 是否需要大白话版本
  final String laymanPrompt; // 大白话润色提示词

  const AgentModel({
    required this.id,
    required this.agentName,
    required this.description,
    required this.agentType,
    required this.pricingTierId,
    required this.isActive,
    required this.sortOrder,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
    this.isDefault = false,
    this.needLaymanVersion = false,
    this.laymanPrompt = '',
  });

  /// 从JSON创建智能体模型（匹配云函数返回格式）
  factory AgentModel.fromJson(Map<String, dynamic> json) {
    return AgentModel(
      id: json['_id'] as String? ?? json['id'] as String,
      agentName: json['agentName'] as String,
      description: json['description'] as String,
      agentType: AgentType.fromString(json['agentType'] as String),
      pricingTierId: json['pricingTierId'] as String? ?? '',
      isActive: json['isActive'] as bool? ?? true,
      sortOrder: json['sortOrder'] as int? ?? 0,
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt'] as String) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt'] as String) : null,
      createdBy: json['createdBy'] as String?,
      updatedBy: json['updatedBy'] as String?,
      isDefault: json['isDefault'] as bool? ?? false,
      needLaymanVersion: json['needLaymanVersion'] as bool? ?? false,
      laymanPrompt: json['laymanPrompt'] as String? ?? '',
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'agentName': agentName,
      'description': description,
      'agentType': agentType.displayName,
      'pricingTierId': pricingTierId,
      'isActive': isActive,
      'sortOrder': sortOrder,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'createdBy': createdBy,
      'updatedBy': updatedBy,
      'isDefault': isDefault,
      'needLaymanVersion': needLaymanVersion,
      'laymanPrompt': laymanPrompt,
    };
  }

  /// 复制并修改智能体模型
  AgentModel copyWith({
    String? id,
    String? agentName,
    String? description,
    AgentType? agentType,
    String? pricingTierId,
    bool? isActive,
    int? sortOrder,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
    bool? isDefault,
    bool? needLaymanVersion,
    String? laymanPrompt,
  }) {
    return AgentModel(
      id: id ?? this.id,
      agentName: agentName ?? this.agentName,
      description: description ?? this.description,
      agentType: agentType ?? this.agentType,
      pricingTierId: pricingTierId ?? this.pricingTierId,
      isActive: isActive ?? this.isActive,
      sortOrder: sortOrder ?? this.sortOrder,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      isDefault: isDefault ?? this.isDefault,
      needLaymanVersion: needLaymanVersion ?? this.needLaymanVersion,
      laymanPrompt: laymanPrompt ?? this.laymanPrompt,
    );
  }

  /// 是否可用
  bool get isAvailable => isActive;

  /// 是否需要八字数据
  bool get requiresBazi => agentType == AgentType.bazi;

  /// 是否需要紫微数据
  bool get requiresZiwei => agentType == AgentType.ziwei;

  /// 获取类型颜色
  String get typeColor {
    switch (agentType) {
      case AgentType.bazi:
        return '#8B5CF6';
      case AgentType.ziwei:
        return '#F59E0B';
      case AgentType.general:
        return '#10B981';
    }
  }

  /// 获取类型图标
  String get typeIcon {
    switch (agentType) {
      case AgentType.bazi:
        return '☯';
      case AgentType.ziwei:
        return '⭐';
      case AgentType.general:
        return '🤖';
    }
  }

  @override
  String toString() {
    return 'AgentModel(id: $id, agentName: $agentName, agentType: $agentType, '
        'isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AgentModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
