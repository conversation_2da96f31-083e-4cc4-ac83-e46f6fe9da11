const cloud = require('wx-server-sdk')
const { errorHandler } = require('./src/middleware/error_handler')
const authHandler = require('./src/handlers/auth')
const agentsHandler = require('./src/handlers/agents')
const modelsHandler = require('./src/handlers/models')
const pricingTiersHandler = require('./src/handlers/pricing_tiers')
const userInfoHandler = require('./src/handlers/user_info')
const purchaseHistoryHandler = require('./src/handlers/purchase_history')
const paymentPackagesHandler = require('./src/handlers/payment_packages')
const baziHandler = require('./src/handlers/bazi')
const versionHandler = require('./src/handlers/version')
const systemConfigHandler = require('./src/handlers/system_config')
const { authMiddleware } = require('./src/middleware/auth')
const logger = require('./src/utils/logger')

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 格式化成功响应 (兼容HTTP触发器)
function createSuccessResponse(data) {
  return {
    isBase64Encoded: false,
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type,Authorization',
    },
    body: JSON.stringify({
      code: 0,
      message: '成功',
      data: data
    })
  };
}

// 格式化错误响应 (兼容HTTP触发器)
function createErrorResponse(error) {
  const { code, message } = errorHandler(error);
  return {
    isBase64Encoded: false,
    statusCode: 200, // API网关要求200，业务错误通过code判断
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type,Authorization',
    },
    body: JSON.stringify({
      code,
      message,
      data: null
    })
  };
}

/**
 * 云函数入口函数
 * 通过action参数路由到不同的处理器
 */
exports.main = async (event, context) => {
  // 处理CORS预检请求
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Max-Age': '86400'
      },
      body: ''
    };
  }

  try {
    let payload;
    // 兼容API网关和直接调用
    if (event.body) {
      try {
        payload = JSON.parse(event.body);
      } catch(e) {
        throw new Error('无效的JSON请求体');
      }
    } else {
      payload = event;
    }

    // 从请求头获取token
    const token = event.headers ? (event.headers['authorization'] || event.headers['Authorization']) : null;
    if (token) {
      // 如果是从 Authorization 头获取的，可能带有 Bearer 前缀
      payload.token = token.startsWith('Bearer ') ? token.substring(7) : token;
    }

    const { action, ...params } = payload;

    if (!action) {
      throw new Error('缺少action参数');
    }

    logger.info(`Action: ${action}`, { params, context });

    // 定义需要鉴权的actions
    const protectedActions = new Set([
      'getAgents',
      'getModels',
      'getPricingTiers',
      'getUserInfo',
      'updateUsage',
      'getUserUsageHistory',
      'getFullAgents',
      'getFullModels',
      'getUserOrders',
      'getOrderDetail',
      'retryPayment',
      'cancelOrder',
      'createPurchaseOrder',
      'simulatePayment',
      'queryPaymentStatus'
    ]);

    // 定义购买相关的actions（跳过算力检查）
    const purchaseActions = new Set([
      'createPurchaseOrder',
      'simulatePayment',
      'queryPaymentStatus',
      'getUserOrders',
      'getOrderDetail',
      'retryPayment',
      'cancelOrder'
    ]);

    // 如果action需要鉴权，则执行鉴权中间件
    if (protectedActions.has(action)) {
      const skipQuotaCheck = purchaseActions.has(action);
      await authMiddleware(payload, { skipQuotaCheck });
    }

    let result;
    // 路由分发
    switch (action) {
      // 认证相关 - 无需鉴权
      case 'login':
        result = await authHandler.login(params);
        break;
      case 'register':
        result = await authHandler.register(params);
        break;
      case 'refreshToken':
        result = await authHandler.refresh(params);
        break;
      case 'validateActivationCode':
        result = await authHandler.validateActivationCode(params);
        break;

      // 八字分析 - 无需鉴权
      case 'baziAnalyze':
        result = await baziHandler.analyze(params.params || params);
        break;

      // 版本检查 - 无需鉴权
      case 'checkVersion':
        result = await versionHandler.checkVersion(params);
        break;

      // 系统配置 - 无需鉴权
      case 'getSystemConfig':
        result = await systemConfigHandler.getSystemConfig(params);
        break;
      case 'getGoProxyApiUrl':
        result = await systemConfigHandler.getGoProxyApiUrl(params);
        break;

      // 需要鉴权的接口
      case 'getAgents':
        result = await agentsHandler.getList(payload);
        break;
      case 'getModels':
        result = await modelsHandler.getList(payload);
        break;
      case 'getPricingTiers':
        result = await pricingTiersHandler.getList(payload);
        break;
      case 'getUserInfo':
        result = await userInfoHandler.get(payload);
        break;
      case 'updateUsage':
        result = await userInfoHandler.updateUsage(payload);
        break;
      case 'getUserUsageHistory':
        result = await userInfoHandler.getUserUsageHistory(payload);
        break;

      // Go程序专用接口（包含敏感信息）
      case 'getFullAgents':
        result = await agentsHandler.getFullList(payload);
        break;
      case 'getFullModels':
        result = await modelsHandler.getFullList(payload);
        break;

      // 购买历史相关接口
      case 'getUserOrders':
        result = await purchaseHistoryHandler.getUserOrders(payload);
        break;
      case 'getOrderDetail':
        result = await purchaseHistoryHandler.getOrderDetail(payload);
        break;
      case 'retryPayment':
        result = await purchaseHistoryHandler.retryPayment(payload);
        break;
      case 'cancelOrder':
        result = await purchaseHistoryHandler.cancelOrder(payload);
        break;

      // 套餐相关接口
      case 'getActivePackages':
        result = await paymentPackagesHandler.getActivePackages(payload);
        break;
      case 'getPackageDetail':
        result = await paymentPackagesHandler.getPackageDetail(payload);
        break;
      case 'createPurchaseOrder':
        result = await paymentPackagesHandler.createPurchaseOrder(payload);
        break;
      case 'simulatePayment':
        result = await paymentPackagesHandler.simulatePayment(payload);
        break;
      case 'queryPaymentStatus':
        result = await paymentPackagesHandler.queryPaymentStatus(payload);
        break;

      // 内部接口（供回调云函数调用）
      case 'processPaymentSuccess':
        result = await paymentPackagesHandler.processPaymentSuccess(payload);
        break;

      default:
        throw new Error(`Unknown action: ${action}`);
    }
    return createSuccessResponse(result);
  } catch (error) {
    return createErrorResponse(error);
  }
}