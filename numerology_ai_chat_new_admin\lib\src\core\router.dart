import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:numerology_ai_chat_admin/src/providers/auth_provider.dart';
import 'package:numerology_ai_chat_admin/src/screens/agent_management_screen.dart';
import 'package:numerology_ai_chat_admin/src/screens/dashboard_screen.dart';
import 'package:numerology_ai_chat_admin/src/screens/login_screen.dart';
import 'package:numerology_ai_chat_admin/src/screens/main_layout.dart';
import 'package:numerology_ai_chat_admin/src/screens/model_management_screen.dart';
import 'package:numerology_ai_chat_admin/src/screens/order_management_screen.dart';
import 'package:numerology_ai_chat_admin/src/screens/package_management_screen.dart';
import 'package:numerology_ai_chat_admin/src/screens/page_management_screen.dart';
import 'package:numerology_ai_chat_admin/src/screens/payment_log_screen.dart';
import 'package:numerology_ai_chat_admin/src/screens/system_config_screen.dart';
import 'package:numerology_ai_chat_admin/src/screens/usage_history_screen.dart';
import 'package:numerology_ai_chat_admin/src/screens/user_management_screen.dart';

final _rootNavigatorKey = GlobalKey<NavigatorState>();
final _shellNavigatorKey = GlobalKey<NavigatorState>();

final routerProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authProvider);

  return GoRouter(
    navigatorKey: _rootNavigatorKey,
    initialLocation: authState.isAuthenticated ? '/home' : '/login',
    routes: [
      GoRoute(
        path: '/login',
        builder: (context, state) => const LoginScreen(),
      ),
      ShellRoute(
        navigatorKey: _shellNavigatorKey,
        builder: (context, state, child) {
          return MainLayout(child: child);
        },
        routes: [
          GoRoute(
            path: '/home',
            builder: (context, state) => const DashboardScreen(),
          ),
          GoRoute(
            path: '/users',
            builder: (context, state) => const UserManagementScreen(),
          ),

          GoRoute(
            path: '/agents',
            builder: (context, state) => const AgentManagementScreen(),
          ),

          GoRoute(
            path: '/models',
            builder: (context, state) => const ModelManagementScreen(),
          ),
          GoRoute(
            path: '/pages',
            builder: (context, state) => const PageManagementScreen(),
          ),
          GoRoute(
            path: '/config',
            builder: (context, state) => const SystemConfigScreen(),
          ),
          GoRoute(
            path: '/packages',
            builder: (context, state) => const PackageManagementScreen(),
          ),
          GoRoute(
            path: '/orders',
            builder: (context, state) => const OrderManagementScreen(),
          ),
          GoRoute(
            path: '/payment-logs',
            builder: (context, state) => const PaymentLogScreen(),
          ),
          GoRoute(
            path: '/usage-history',
            builder: (context, state) => const UsageHistoryScreen(),
          ),
        ],
      ),
    ],
    redirect: (context, state) {
      final isAuthenticated = authState.isAuthenticated;
      final isLoggingIn = state.matchedLocation == '/login';

      if (!isAuthenticated && !isLoggingIn) {
        return '/login';
      }
      if (isAuthenticated && isLoggingIn) {
        return '/home';
      }

      return null;
    },
    refreshListenable: GoRouterRefreshStream(ref.watch(authProvider.notifier).stream),
  );
});

class GoRouterRefreshStream extends ChangeNotifier {
  GoRouterRefreshStream(Stream<dynamic> stream) {
    notifyListeners();
    _subscription = stream.asBroadcastStream().listen((_) => notifyListeners());
  }

  late final StreamSubscription<dynamic> _subscription;

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
}