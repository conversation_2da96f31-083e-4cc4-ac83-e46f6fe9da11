import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../providers/payment_log_provider.dart';
import '../models/payment_log_model.dart';
import '../widgets/payment_log_detail_dialog.dart';

class PaymentLogScreen extends ConsumerStatefulWidget {
  const PaymentLogScreen({super.key});

  @override
  ConsumerState<PaymentLogScreen> createState() => _PaymentLogScreenState();
}

class _PaymentLogScreenState extends ConsumerState<PaymentLogScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedOperationType = '';
  String _selectedStatus = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(paymentLogProvider.notifier).loadPaymentLogs();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final logState = ref.watch(paymentLogProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('支付日志管理'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Column(
        children: [
          // 搜索和筛选区域
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: TextField(
                        controller: _searchController,
                        decoration: const InputDecoration(
                          labelText: '搜索订单号或用户ID',
                          prefixIcon: Icon(Icons.search),
                          border: OutlineInputBorder(),
                        ),
                        onSubmitted: (value) {
                          ref.read(paymentLogProvider.notifier).searchLogs(value);
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedOperationType.isEmpty ? null : _selectedOperationType,
                        decoration: const InputDecoration(
                          labelText: '操作类型',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: '', child: Text('全部类型')),
                          DropdownMenuItem(value: 'CREATE_ORDER', child: Text('创建订单')),
                          DropdownMenuItem(value: 'FUIOU_UNIFIED_ORDER', child: Text('富友统一下单')),
                          DropdownMenuItem(value: 'PAYMENT_CALLBACK', child: Text('支付回调')),
                          DropdownMenuItem(value: 'REFUND', child: Text('退款')),
                          DropdownMenuItem(value: 'CANCEL_ORDER', child: Text('取消订单')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedOperationType = value ?? '';
                          });
                          ref.read(paymentLogProvider.notifier).filterByOperationType(_selectedOperationType);
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedStatus.isEmpty ? null : _selectedStatus,
                        decoration: const InputDecoration(
                          labelText: '状态',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: '', child: Text('全部状态')),
                          DropdownMenuItem(value: 'SUCCESS', child: Text('成功')),
                          DropdownMenuItem(value: 'FAILED', child: Text('失败')),
                          DropdownMenuItem(value: 'PENDING', child: Text('处理中')),
                          DropdownMenuItem(value: 'TIMEOUT', child: Text('超时')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedStatus = value ?? '';
                          });
                          ref.read(paymentLogProvider.notifier).filterByStatus(_selectedStatus);
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    ElevatedButton.icon(
                      onPressed: () {
                        ref.read(paymentLogProvider.notifier).refreshLogs();
                      },
                      icon: const Icon(Icons.refresh),
                      label: const Text('刷新'),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const Divider(),
          // 日志列表
          Expanded(
            child: logState.isLoading
                ? const Center(child: CircularProgressIndicator())
                : logState.error != null
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              '错误: ${logState.error}',
                              style: const TextStyle(color: Colors.red),
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: () {
                                ref.read(paymentLogProvider.notifier).refreshLogs();
                              },
                              child: const Text('重试'),
                            ),
                          ],
                        ),
                      )
                    : logState.logs.isEmpty
                        ? const Center(child: Text('暂无支付日志数据'))
                        : Column(
                            children: [
                              Expanded(
                                child: SingleChildScrollView(
                                  scrollDirection: Axis.horizontal,
                                  child: DataTable(
                                    columns: const [
                                      DataColumn(label: Text('操作类型')),
                                      DataColumn(label: Text('订单ID')),
                                      DataColumn(label: Text('用户ID')),
                                      DataColumn(label: Text('状态')),
                                      DataColumn(label: Text('时间')),
                                      DataColumn(label: Text('操作')),
                                    ],
                                    rows: logState.logs.map((log) {
                                      return DataRow(
                                        cells: [
                                          DataCell(Text(log.operationTypeText)),
                                          DataCell(
                                            Text(
                                              log.orderId.length > 12 
                                                  ? '${log.orderId.substring(0, 12)}...'
                                                  : log.orderId,
                                              style: const TextStyle(fontFamily: 'monospace'),
                                            ),
                                          ),
                                          DataCell(
                                            Text(
                                              log.userId.length > 8 
                                                  ? '${log.userId.substring(0, 8)}...'
                                                  : log.userId,
                                              style: const TextStyle(fontFamily: 'monospace'),
                                            ),
                                          ),
                                          DataCell(
                                            Container(
                                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                              decoration: BoxDecoration(
                                                color: _getStatusColor(log.status),
                                                borderRadius: BorderRadius.circular(4),
                                              ),
                                              child: Text(
                                                log.statusText,
                                                style: const TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 12,
                                                ),
                                              ),
                                            ),
                                          ),
                                          DataCell(
                                            Text(DateFormat('yyyy-MM-dd HH:mm:ss').format(log.timestamp)),
                                          ),
                                          DataCell(
                                            IconButton(
                                              icon: const Icon(Icons.visibility),
                                              onPressed: () => _showLogDetail(log),
                                              tooltip: '查看详情',
                                            ),
                                          ),
                                        ],
                                      );
                                    }).toList(),
                                  ),
                                ),
                              ),
                              // 分页控件
                              if (logState.totalPages > 1)
                                Container(
                                  padding: const EdgeInsets.all(16),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      IconButton(
                                        onPressed: logState.currentPage > 1
                                            ? () => ref.read(paymentLogProvider.notifier).loadPage(logState.currentPage - 1)
                                            : null,
                                        icon: const Icon(Icons.chevron_left),
                                      ),
                                      Text('${logState.currentPage} / ${logState.totalPages}'),
                                      IconButton(
                                        onPressed: logState.currentPage < logState.totalPages
                                            ? () => ref.read(paymentLogProvider.notifier).loadPage(logState.currentPage + 1)
                                            : null,
                                        icon: const Icon(Icons.chevron_right),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'SUCCESS':
        return Colors.green;
      case 'FAILED':
        return Colors.red;
      case 'PENDING':
        return Colors.orange;
      case 'TIMEOUT':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  void _showLogDetail(PaymentLogModel log) {
    showDialog(
      context: context,
      builder: (context) => PaymentLogDetailDialog(log: log),
    );
  }
}
