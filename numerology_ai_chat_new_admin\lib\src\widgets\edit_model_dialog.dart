
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:numerology_ai_chat_admin/src/models/model_model.dart';
import 'package:numerology_ai_chat_admin/src/providers/model_provider.dart';

class EditModelDialog extends ConsumerStatefulWidget {
  final Model? model;
  const EditModelDialog({super.key, this.model});

  @override
  ConsumerState<EditModelDialog> createState() => _EditModelDialogState();
}

class _EditModelDialogState extends ConsumerState<EditModelDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _displayNameController;
  late TextEditingController _apiUrlController;
  late TextEditingController _apiKeyController;
  late TextEditingController _maxTokensController;
  late TextEditingController _tempController;
  late String _modelLevel;
  late bool _isActive;

  @override
  void initState() {
    super.initState();
    final model = widget.model;
    _nameController = TextEditingController(text: model?.modelName ?? '');
    _displayNameController = TextEditingController(text: model?.modelDisplayName ?? '');
    _apiUrlController = TextEditingController(text: model?.modelApiUrl ?? '');
    _apiKeyController = TextEditingController(text: '');
    _maxTokensController = TextEditingController(text: model?.maxTokens.toString() ?? '4096');
    _tempController = TextEditingController(text: model?.temperature.toString() ?? '0.7');
    _modelLevel = '高级';
    _isActive = model?.isActive ?? true;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.model == null ? '创建模型' : '编辑模型'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(controller: _nameController, decoration: const InputDecoration(labelText: '模型名称 (modelName)')),
              TextFormField(controller: _displayNameController, decoration: const InputDecoration(labelText: '显示名称 (modelDisplayName)')),
              TextFormField(controller: _apiUrlController, decoration: const InputDecoration(labelText: 'API URL')),
              TextFormField(controller: _apiKeyController, decoration: const InputDecoration(labelText: 'API Key')),
              TextFormField(controller: _maxTokensController, keyboardType: TextInputType.number, decoration: const InputDecoration(labelText: 'Max Tokens')),
              TextFormField(controller: _tempController, keyboardType: TextInputType.number, decoration: const InputDecoration(labelText: 'Temperature')),
              DropdownButtonFormField<String>(
                value: _modelLevel,
                items: ['初级', '中级', '高级'].map((e) => DropdownMenuItem(value: e, child: Text(e))).toList(),
                onChanged: (value) => setState(() => _modelLevel = value!),
                decoration: const InputDecoration(labelText: '模型等级 (modelLevel)'),
              ),
              SwitchListTile(
                title: const Text('是否激活'),
                value: _isActive,
                onChanged: (value) => setState(() => _isActive = value),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('取消')),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              final modelData = {
                'modelName': _nameController.text,
                'modelDisplayName': _displayNameController.text,
                'modelApiUrl': _apiUrlController.text,
                'modelApiKey': _apiKeyController.text,
                'maxTokens': int.tryParse(_maxTokensController.text) ?? 4096,
                'temperature': double.tryParse(_tempController.text) ?? 0.7,
                'modelLevel': _modelLevel,
                'isActive': _isActive,
              };

              if (widget.model == null) {
                ref.read(modelProvider.notifier).createModel(modelData);
              } else {
                ref.read(modelProvider.notifier).updateModel(widget.model!.modelId, modelData);
              }
              Navigator.of(context).pop();
            }
          },
          child: const Text('保存'),
        ),
      ],
    );
  }
}
