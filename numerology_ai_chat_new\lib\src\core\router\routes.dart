/// 应用路由常量
class Routes {
  // 认证相关路由
  static const String splash = '/';
  static const String login = '/login';
  static const String register = '/register';
  
  // 主要功能路由
  static const String home = '/home';
  static const String chat = '/chat';
  static const String settings = '/settings';
  
  // 选择相关路由
  static const String agentSelection = '/agent-selection';
  static const String modelSelection = '/model-selection';
  
  // 其他页面路由
  static const String markdownPage = '/markdown-page';
  static const String profile = '/profile';
  static const String bazi = '/bazi';
  
  // 路由参数键
  static const String paramAgentId = 'agentId';
  static const String paramModelId = 'modelId';
  static const String paramSessionId = 'sessionId';
  static const String paramPageType = 'pageType';
  static const String paramTitle = 'title';
  static const String paramContent = 'content';
  
  // 私有构造函数，防止实例化
  Routes._();
  
  /// 获取所有路由列表
  static List<String> get allRoutes => [
    splash,
    login,
    register,
    home,
    chat,
    settings,
    agentSelection,
    modelSelection,
    markdownPage,
    profile,
    bazi,
  ];
  
  /// 检查路由是否需要认证
  static bool requiresAuth(String route) {
    const publicRoutes = [splash, login, register];
    return !publicRoutes.contains(route);
  }
  
  /// 检查是否为认证相关路由
  static bool isAuthRoute(String route) {
    const authRoutes = [login, register];
    return authRoutes.contains(route);
  }
  
  /// 获取路由显示名称
  static String getDisplayName(String route) {
    switch (route) {
      case splash:
        return '启动页';
      case login:
        return '登录';
      case register:
        return '注册';
      case home:
        return '首页';
      case chat:
        return '聊天';
      case settings:
        return '设置';
      case agentSelection:
        return '选择智能体';
      case modelSelection:
        return '选择模型';
      case markdownPage:
        return '页面详情';
      case profile:
        return '个人中心';
      case bazi:
        return '八字分析';
      default:
        return '未知页面';
    }
  }
}
