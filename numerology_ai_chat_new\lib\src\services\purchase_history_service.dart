import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/purchase_history_model.dart';
import '../core/constants/app_constants.dart';

/// 购买历史服务
class PurchaseHistoryService {
  static const String _baseUrl = AppConstants.exeFunctionUrl;

  /// 获取用户购买历史
  Future<List<PurchaseHistoryModel>> getUserPurchaseHistory({
    required String userId,
    required String token,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'action': 'getUserOrders',
          'token': token,
          'userId': userId,
          'page': page,
          'limit': limit,
        }),
      ).timeout(AppConstants.connectTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['code'] == 0 && data['data'] != null) {
          final List<dynamic> ordersData = data['data']['data']['orders'] ?? [];
          return ordersData
              .map((json) => PurchaseHistoryModel.fromJson(json))
              .toList();
        } else {
          throw Exception(data['message'] ?? '获取购买历史失败');
        }
      } else {
        throw Exception('网络请求失败: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('获取购买历史失败: $e');
    }
  }



  /// 获取订单详情
  Future<PurchaseHistoryModel?> getOrderDetail({
    required String orderId,
    required String token,
  }) async {
    try {
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'action': 'getOrderDetail',
          'token': token,
          'orderId': orderId,
        }),
      ).timeout(AppConstants.connectTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['code'] == 0 && data['data'] != null) {
          return PurchaseHistoryModel.fromJson(data['data']);
        } else {
          throw Exception(data['message'] ?? '获取订单详情失败');
        }
      } else {
        throw Exception('网络请求失败: ${response.statusCode}');
      }
    } catch (e) {
      // 如果API调用失败，返回null
      return null;
    }
  }

  /// 重新支付订单
  Future<Map<String, dynamic>?> retryPayment({
    required String orderId,
    required String token,
  }) async {
    try {
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'action': 'retryPayment',
          'token': token,
          'orderId': orderId,
        }),
      ).timeout(AppConstants.connectTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['code'] == 0) {
          return data['data'];
        } else {
          throw Exception(data['message'] ?? '重新支付失败');
        }
      } else {
        throw Exception('网络请求失败: ${response.statusCode}');
      }
    } catch (e) {
      return null;
    }
  }

  /// 取消订单
  Future<bool> cancelOrder({
    required String orderId,
    required String token,
  }) async {
    try {
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'action': 'cancelOrder',
          'token': token,
          'orderId': orderId,
        }),
      ).timeout(AppConstants.connectTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['code'] == 0;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }
}
