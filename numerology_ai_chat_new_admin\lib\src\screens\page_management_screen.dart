import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:numerology_ai_chat_admin/src/models/page_model.dart';
import 'package:numerology_ai_chat_admin/src/providers/page_provider.dart';
import 'package:numerology_ai_chat_admin/src/widgets/page_edit_dialog.dart';

class PageManagementScreen extends ConsumerStatefulWidget {
  const PageManagementScreen({super.key});

  @override
  ConsumerState<PageManagementScreen> createState() => _PageManagementScreenState();
}

class _PageManagementScreenState extends ConsumerState<PageManagementScreen> {
  String? _selectedPageType;
  bool? _selectedIsActive;

  @override
  void initState() {
    super.initState();
    // 初始加载页面列表
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(pageProvider.notifier).fetchPages();
    });
  }

  @override
  Widget build(BuildContext context) {
    final pageState = ref.watch(pageProvider);

    // 监听错误状态
    ref.listen<PageState>(pageProvider, (previous, next) {
      if (next.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.error!),
            backgroundColor: Colors.red,
          ),
        );
        ref.read(pageProvider.notifier).clearError();
      }
    });

    return Scaffold(
      body: Column(
        children: [
          // 页面标题和操作栏
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Text(
                  '页面配置管理',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () => _refreshPages(),
                  tooltip: '刷新',
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: () => _showCreatePageDialog(),
                  icon: const Icon(Icons.add),
                  label: const Text('新建页面'),
                ),
              ],
            ),
          ),
          // 筛选条件
          _buildFilterBar(),
          const Divider(height: 1),
          // 页面列表
          Expanded(
            child: pageState.isLoading
                ? const Center(child: CircularProgressIndicator())
                : pageState.pages == null || pageState.pages!.isEmpty
                    ? const Center(child: Text('暂无页面数据'))
                    : _buildPageList(pageState.pages!),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // 页面类型筛选
          SizedBox(
            width: 150,
            child: DropdownButtonFormField<String>(
              value: _selectedPageType,
              decoration: const InputDecoration(
                labelText: '页面类型',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: const [
                DropdownMenuItem(value: null, child: Text('全部类型')),
                DropdownMenuItem(value: '帮助', child: Text('帮助')),
                DropdownMenuItem(value: '关于', child: Text('关于')),
                DropdownMenuItem(value: '条款', child: Text('条款')),
                DropdownMenuItem(value: '隐私', child: Text('隐私')),
                DropdownMenuItem(value: '使用指南', child: Text('使用指南')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedPageType = value;
                });
                _refreshPages();
              },
            ),
          ),
          const SizedBox(width: 16),
          // 状态筛选
          SizedBox(
            width: 120,
            child: DropdownButtonFormField<bool>(
              value: _selectedIsActive,
              decoration: const InputDecoration(
                labelText: '状态',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: const [
                DropdownMenuItem(value: null, child: Text('全部状态')),
                DropdownMenuItem(value: true, child: Text('启用')),
                DropdownMenuItem(value: false, child: Text('禁用')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedIsActive = value;
                });
                _refreshPages();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPageList(List<PageModel> pages) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('页面标题')),
          DataColumn(label: Text('页面类型')),
          DataColumn(label: Text('URL标识')),
          DataColumn(label: Text('状态')),
          DataColumn(label: Text('排序')),
          DataColumn(label: Text('更新时间')),
          DataColumn(label: Text('操作')),
        ],
        rows: pages.map((page) => _buildPageRow(page)).toList(),
      ),
    );
  }

  DataRow _buildPageRow(PageModel page) {
    return DataRow(
      cells: [
        DataCell(
          SizedBox(
            width: 200,
            child: Text(
              page.pageTitle,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        DataCell(Text(page.pageType)),
        DataCell(Text(page.slug)),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: page.isActive ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              page.isActive ? '启用' : '禁用',
              style: TextStyle(
                color: page.isActive ? Colors.green : Colors.red,
                fontSize: 12,
              ),
            ),
          ),
        ),
        DataCell(Text(page.sortOrder.toString())),
        DataCell(Text(_formatDateTime(page.updatedAt))),
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: const Icon(Icons.edit, size: 18),
                onPressed: () => _showEditPageDialog(page),
                tooltip: '编辑',
              ),
              IconButton(
                icon: const Icon(Icons.delete, size: 18),
                onPressed: () => _showDeleteConfirmDialog(page),
                tooltip: '删除',
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _refreshPages() {
    ref.read(pageProvider.notifier).fetchPages(
          pageType: _selectedPageType,
          isActive: _selectedIsActive,
        );
  }

  void _showCreatePageDialog() {
    showDialog(
      context: context,
      builder: (context) => PageEditDialog(
        onSave: (request) async {
          final success = await ref.read(pageProvider.notifier).createPage(request);
          if (success) {
            Navigator.of(context).pop();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('页面创建成功')),
            );
          }
        },
      ),
    );
  }

  void _showEditPageDialog(PageModel page) {
    showDialog(
      context: context,
      builder: (context) => PageEditDialog(
        page: page,
        onSave: (request) async {
          final success = await ref.read(pageProvider.notifier).updatePage(page.id, request);
          if (success) {
            Navigator.of(context).pop();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('页面更新成功')),
            );
          }
        },
      ),
    );
  }

  void _showDeleteConfirmDialog(PageModel page) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除页面"${page.pageTitle}"吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final success = await ref.read(pageProvider.notifier).deletePage(page.id);
              if (success) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('页面删除成功')),
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }
}
