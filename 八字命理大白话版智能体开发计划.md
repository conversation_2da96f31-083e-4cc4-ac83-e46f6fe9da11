# 八字命理大白话版智能体开发计划

## 需求概述

需要在现有系统中添加一个新的智能体"八字命理大白话版"，该智能体需要满足以下要求：

1. 与现有的"八字命理"智能体具有相同的第一次输出结果
2. 在第一次输出完毕后，自动生成一份大白话版本的输出
3. 定价为25个算力
4. 大白话版本的输出不能携带进历史对话中
5. 但大白话版本需要在前端显示给用户查看

## 技术架构分析

### 当前系统架构
- **前端**: Flutter桌面应用 (numerology_ai_chat_new)
- **中转服务**: Go代理服务 (go_proxy)
- **后端服务**: 云函数 (cloudfunctions/exeFunction)
- **数据存储**: 云开发数据库

### 数据库结构分析

#### exe_agents 集合
当前包含7个智能体记录，主要字段：
- agentName: 智能体名称
- agentPrompt: 智能体提示词
- agentType: 智能体类型（八字/紫微/无需携带内容）
- pricingTierId: 定价层级ID
- isActive: 是否激活
- sortOrder: 排序顺序
- **需要新增字段**: needLaymanVersion: 是否需要大白话版本（布尔值）
- **需要新增字段**: laymanPrompt: 大白话润色提示词（可选字段）

#### exe_pricing_tiers 集合
包含3个定价层级：
- 基础档次: 高级模型20算力，基础模型10算力
- 标准档次: 高级模型30算力，基础模型15算力  
- 高级档次: 需要创建25算力的新定价层级

## 详细修改计划

### 第一阶段：数据库层面修改

#### 1. 新增定价层级
在 `exe_pricing_tiers` 集合中新增一个定价层级：
- tierName: "八字大白话档次"
- tierDescription: "适用于八字命理大白话版智能体，包含二次润色处理"
- basicModelCost: 25
- advancedModelCost: 25
- isActive: true
- sortOrder: 4

#### 2. 修改现有智能体记录结构
为 `exe_agents` 集合添加新字段：
- needLaymanVersion: 布尔值，标识是否需要生成大白话版本
- laymanPrompt: 字符串，存储大白话润色的提示词（当needLaymanVersion为true时使用）

#### 3. 新增智能体记录
在 `exe_agents` 集合中新增智能体：
- agentName: "八字命理大白话版"
- agentType: "八字"
- agentPrompt: 与现有"八字命理"智能体相同的提示词
- pricingTierId: 新创建的定价层级ID
- isActive: true
- sortOrder: 排在"八字命理"之后
- description: "专业八字命理分析，附加通俗易懂的大白话解读版本"
- **needLaymanVersion: true**
- **laymanPrompt: 大白话润色提示词内容**

#### 4. 更新现有智能体记录
为现有的智能体记录添加新字段的默认值：
- needLaymanVersion: false（现有智能体默认不需要大白话版本）
- laymanPrompt: null 或空字符串

### 第二阶段：Go代理服务修改

#### 1. 识别需要大白话处理的智能体
在 `internal/proxy/llm_proxy.go` 中添加逻辑：
- 检查智能体的 `needLaymanVersion` 字段是否为 true
- 如果是，标记需要进行二次处理，并获取对应的 `laymanPrompt`

#### 2. 实现二次AI调用
修改聊天处理流程：
- 第一次调用：使用智能体的 `agentPrompt`，获取专业版结果
- 第二次调用：如果 `needLaymanVersion` 为 true，使用智能体的 `laymanPrompt` 结合第一次结果，生成大白话版本
- 根据智能体的定价层级进行相应的算力扣减验证

#### 3. 特殊响应格式处理
设计特殊的响应格式来区分两部分内容：
- 使用特殊的JSON格式或分隔符来标识专业版和大白话版
- 确保前端可以正确识别和分别展示两部分内容

### 第三阶段：云函数服务修改

#### 1. exeFunction智能体列表接口修改
在 `src/handlers/agents.js` 中：
- 确保新字段 `needLaymanVersion` 和 `laymanPrompt` 能正确返回到前端
- 前端可以根据 `needLaymanVersion` 字段来判断是否需要特殊处理

#### 2. 算力计费逻辑调整
如果算力计费在云函数中处理：
- 确认新智能体的25算力计费正确执行
- 处理二次调用的算力验证逻辑

### 第四阶段：Flutter前端修改

#### 1. 智能体模型更新
在 `lib/src/models/` 中：
- 更新智能体数据模型，添加 `needLaymanVersion` 和 `laymanPrompt` 字段
- 前端可以根据 `needLaymanVersion` 字段来判断当前智能体是否需要大白话处理

#### 2. 聊天界面调整
在 `lib/src/screens/` 和 `lib/src/widgets/` 中：
- 修改聊天界面来支持显示两段不同的内容
- 专业版内容正常显示并加入历史对话
- 大白话版本显示但不加入历史对话上下文

#### 3. 服务层修改
在 `lib/src/services/go_proxy_service.dart` 中：
- 处理特殊的响应格式
- 正确解析和分离两部分内容
- 确保历史对话上下文的正确管理

#### 4. 用户界面优化
添加用户界面元素来区分显示：
- 为大白话版本添加特殊的样式标识
- 可能添加切换按钮来显示/隐藏大白话版本
- 在界面上明确标注哪部分是专业版，哪部分是大白话版

### 第五阶段：管理后台修改

#### 1. numerology_ai_chat_new_admin 项目
在管理后台中添加对新智能体的管理功能：
- 智能体列表中显示新智能体
- 支持编辑新智能体的配置
- 支持管理润色提示词

#### 2. exeAdmin云函数
在 `src/handlers/agent_management.js` 中：
- 支持新字段 `needLaymanVersion` 和 `laymanPrompt` 的管理
- 提供界面来配置智能体是否需要大白话处理及对应的润色提示词

## 技术实现细节

### 润色提示词设计
建议的润色提示词：
```
请将以下专业的八字命理分析报告转换为通俗易懂的大白话版本。要求：
1. 保持原有分析结论的准确性
2. 将专业术语转换为日常用语
3. 使用生活化的比喻和例子
4. 语言要亲切自然，易于理解
5. 保持报告的完整结构

原始专业报告：
[第一次AI输出的内容]

请输出大白话版本：
```

### 响应格式设计
```json
{
  "professional_version": "专业版八字分析内容...",
  "layman_version": "大白话版解读内容..."
}
```

### 历史对话管理
- 只将 `professional_version` 的内容加入对话历史
- `layman_version` 仅用于当次显示，不参与后续对话上下文

## 开发优先级和时间估算

### 高优先级（核心功能）
1. 数据库修改（1-2小时）
2. Go代理服务核心逻辑（1天）
3. Flutter前端基础显示（1天）

### 中优先级（完善功能）
1. 用户界面优化（半天）
2. 管理后台支持（半天）
3. 错误处理和边界情况（半天）

### 低优先级（增强功能）
1. 详细的用户体验优化（按需）
2. 性能优化（按需）
3. 详细的日志和监控（按需）

## 风险评估和注意事项

### 技术风险
1. 二次AI调用可能增加响应时间
2. 算力计费逻辑的准确性需要仔细验证
3. 历史对话上下文管理的复杂性

### 业务风险
1. 用户可能对25算力的定价有疑问
2. 大白话版本的质量需要持续优化
3. 需要明确向用户说明这是两次AI处理的结果

### 兼容性考虑
1. 确保对现有智能体功能无影响
2. 保持现有API接口的向后兼容性
3. 数据库迁移的平滑进行

## 测试计划

### 功能测试
1. 新智能体的基本聊天功能
2. 算力计费的准确性
3. 两个版本内容的正确显示
4. 历史对话上下文的正确管理

### 性能测试
1. 二次AI调用的响应时间
2. 并发用户使用的性能表现
3. 系统资源使用情况

### 用户体验测试
1. 界面展示的清晰度
2. 两个版本内容的区分度
3. 用户操作的便利性

## 部署计划

### 开发环境部署
1. 先在开发环境完成所有功能开发
2. 进行充分的功能和性能测试
3. 确保所有边界情况都得到处理

### 生产环境部署
1. 数据库变更（需要谨慎操作）
2. 云函数更新部署
3. Go代理服务更新部署
4. Flutter应用更新发布
5. 管理后台更新部署

### 回滚计划
1. 保留所有现有功能的完整性
2. 新功能出问题时可以快速禁用
3. 数据库变更需要有回滚脚本

## 后续优化方向

### 功能扩展
1. 通过设置 `needLaymanVersion` 字段，可以轻松为任何智能体启用大白话功能
2. 用户可以选择是否需要大白话版本
3. 支持不同程度的通俗化（通过配置不同的 `laymanPrompt` 实现）
4. 可以为不同智能体配置不同的润色风格

### 技术优化
1. 缓存机制优化响应速度
2. 异步处理减少用户等待时间
3. AI调用的智能重试机制

### 用户体验优化
1. 更丰富的界面展示效果
2. 用户反馈机制
3. 个性化的展示设置

## 方案优势总结

### 相比硬编码名字判断的优势
1. **可扩展性**: 通过 `needLaymanVersion` 字段，可以轻松为任何智能体启用大白话功能，而不需要修改代码
2. **灵活性**: 每个智能体可以配置自己的 `laymanPrompt`，实现不同的润色风格
3. **可维护性**: 避免了硬编码智能体名称，降低了代码耦合度
4. **配置化**: 管理员可以通过后台界面灵活配置哪些智能体需要大白话功能
5. **向后兼容**: 现有智能体默认 `needLaymanVersion` 为 false，不影响现有功能

### 数据库字段设计说明
- `needLaymanVersion` (boolean): 标识智能体是否需要生成大白话版本
- `laymanPrompt` (string, nullable): 存储大白话润色的提示词，只有当 `needLaymanVersion` 为 true 时才使用

这种设计使得系统更加灵活和可扩展，未来可以轻松为其他智能体启用大白话功能。

---

本计划为添加"八字命理大白话版"智能体的完整技术方案，采用字段标识而非硬编码名称的方式，提供了更好的可扩展性和可维护性。涵盖了从数据库到前端的全栈修改需求，建议按照优先级逐步实施，确保每个阶段的质量和稳定性。