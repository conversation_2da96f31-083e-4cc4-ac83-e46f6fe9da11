const cloud = require('wx-server-sdk')
const { handleRequest } = require('./src/handlers/index')
const { errorHandler } = require('./src/middleware/error_handler')
const logger = require('./src/utils/logger')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

/**
 * 云函数入口函数
 * @param {object} event 事件对象
 * @param {object} context 上下文对象
 * @returns {object} 响应结果
 */
exports.main = async (event, context) => {
  // 设置CORS头部
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
    'Access-Control-Max-Age': '86400'
  }

  // 处理OPTIONS预检请求
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    }
  }

  try {
    let payload;

    // 处理HTTP请求体解析
    if (event.body) {
      try {
        payload = JSON.parse(event.body);
      } catch(e) {
        throw new Error('无效的JSON请求体');
      }
    } else {
      payload = event;
    }

    logger.info('WeakAdmin function called', {
      action: payload.action,
      requestId: context.requestId,
      userAgent: event.userAgent || 'unknown'
    })

    // 从请求头中提取token
    const authorization = event.headers ?
      (event.headers['authorization'] || event.headers['Authorization']) :
      (payload.authorization || payload.token)

    if (authorization) {
      // 如果是Bearer格式，提取token部分
      const token = authorization.startsWith('Bearer ')
        ? authorization.slice(7)
        : authorization
      payload.token = token
    }

    // 处理请求
    const result = await handleRequest(payload)

    logger.info('WeakAdmin function completed successfully', {
      action: payload.action,
      requestId: context.requestId
    })

    // 返回带CORS头部的响应
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify(result)
    }

  } catch (error) {
    logger.error('WeakAdmin function error', {
      action: event.action || (event.body ? JSON.parse(event.body).action : 'unknown'),
      requestId: context.requestId,
      error: error.message,
      stack: error.stack
    })

    const errorResult = errorHandler(error)

    // 返回带CORS头部的错误响应
    return {
      statusCode: errorResult.success ? 200 : 500,
      headers: corsHeaders,
      body: JSON.stringify(errorResult)
    }
  }
}
