# Go 中转服务项目结构

## 一、概述

Go 中转服务是整个应用架构的核心安全组件，扮演着前端应用与后端（云函数、大语言模型）之间的"安全网关"。它的主要职责是：

1.  **安全隔离**：完全隐藏大语言模型（LLM）的 API 密钥和核心智能体提示词（Prompt），前端应用无需也无法接触任何敏感信息。
2.  **统一鉴权**：作为 AI 功能的唯一入口，它强制所有请求必须通过云函数进行用户身份和权限的验证。
3.  **逻辑解耦**：将 AI 相关的调用逻辑（如构造请求、处理流式响应、解密密钥等）与核心业务逻辑（用户管理、订单等）分离，使得职责更清晰。
4.  **性能与流式处理**：利用 Go 的高并发特性，高效地处理来自客户端的并发请求，并支持将大语言模型的响应以流式（Stream）方式直接传回给客户端，提升用户体验。

> 该服务**不直接处理**用户登录、注册等业务，这些业务由 `exeFunction` 云函数负责。它只专注于一件事：**安全、高效地代理 AI 调用**。

---

## 二、顶级目录结构

```text
go_proxy/
├── cmd/
│   └── server/
│       └── main.go         # 主程序入口：启动 HTTP 服务
├── internal/
│   ├── api/                # API 路由和处理器
│   │   ├── handlers/
│   │   │   ├── chat_handler.go    # 聊天请求的核心处理器
│   │   │   └── health_handler.go  # 健康检查处理器
│   │   └── router.go              # Gin 路由配置
│   ├── config/             # 配置加载
│   │   └── config.go              # 从 .env 文件加载环境变量
│   ├── middleware/         # Gin 中间件
│   │   └── auth_middleware.go     # 鉴权中间件
│   ├── services/           # 外部服务调用封装
│   │   ├── cloudfunction/
│   │   │   └── client.go          # 调用云函数的客户端
│   │   └── database/
│   │       └── client.go          # 连接腾讯云数据库的客户端
│   └── proxy/              # 核心代理逻辑
│       └── llm_proxy.go           # 实现对 LLM 的请求构造和响应流式转发
├── go.mod
├── go.sum
└── .env.example            # 环境变量示例文件
```

---

## 三、核心工作流

![Go中转服务工作流](https://your-image-url/go_proxy_workflow.png)  <!-- 建议在此处放置一张流程图 -->

1.  **接收请求**：Flutter 客户端向 Go 服务发起 `POST /v1/chat/completions` 请求，请求头中包含 `Authorization: Bearer <access_token>`，请求体包含 `agentId`、`modelId` 和用户消息。

2.  **鉴权中间件** (`auth_middleware.go`)：
    *   拦截请求，提取 `access_token`。
    *   调用 `services/cloudfunction/client.go` 模块，向 `exeFunction` 云函数发起 `getUserInfo` 操作的调用。
    *   云函数验证 Token 的有效性、检查用户状态和会员有效期。
    *   如果验证失败，Go 服务直接返回 `401 Unauthorized` 错误。
    *   如果验证成功，将从云函数返回的 `userId` 等信息存入 Gin 的 `Context`，传递给后续处理器。

3.  **核心处理器** (`chat_handler.go`)：
    *   从 `Context` 中获取 `userId`，从请求体中获取 `agentId` 和 `modelId`。
    *   调用 `services/database/client.go` 连接到腾讯云数据库。
    *   根据 `agentId` 从 `exe_agents` 集合获取智能体的 `agentPrompt` 和 `pricingTierId`。
    *   根据 `modelId` 从 `exe_models` 集合获取模型的配置，包括加密的 `modelApiKey`、`modelApiUrl` 和 `modelLevel`。
    *   根据 `pricingTierId` 从 `exe_pricing_tiers` 集合获取档次的算力消耗配置。

4.  **解密与构造** (`llm_proxy.go`)：
    *   使用预置在环境变量中的 `AES_SECRET_KEY` 解密 `modelApiKey`。
    *   将 `agentPrompt` 和用户的 `messages` 合并，构造成符合目标 LLM 要求的最终请求体。

5.  **代理与流式转发** (`llm_proxy.go`)：
    *   向 `modelApiUrl` 发起一个携带解密后 `modelApiKey` 的新 HTTP 请求。
    *   将来自 LLM 的**流式响应**（Server-Sent Events）不做任何处理，直接"透传"回 Flutter 客户端。这确保了打字机效果的流畅性。

6.  **算力扣费**：
    *   在成功发起代理请求后（或流式结束后），调用云函数的 `updateUsage` 操作，传递 `userId`、`agentId`、`modelId`。
    *   云函数根据智能体档次和模型等级计算实际扣费算力，原子地扣减用户的 `availableCount`。
    *   扣费逻辑：查询智能体关联的档次 → 根据模型等级选择对应算力消耗 → 扣减用户算力。

---

## 四、API 端点

| 方法 | 路径 | 处理器 | 中间件 | 描述 |
|------|------|--------|----------|------|
| POST | `/v1/chat/completions` | `chat_handler.HandleChatCompletion` | `AuthMiddleware` | 核心 AI 对话接口 |
| GET  | `/healthz` | `health_handler.HealthCheck` | - | 服务健康检查 |

---

## 五、环境变量配置 (`.env.example`)

服务启动依赖以下环境变量：

```dotenv
# 服务运行端口
SERVER_PORT=8080

# 云函数 exeFunction 的 HTTP 访问地址
CLOUDFUNC_BASE_URL="https://your-cloud-function-url.ap-shanghai.service.tcloudbase.com/exeFunction"

# 腾讯云开发环境 ID
TENCENT_CLOUD_ENV_ID="your-env-id"

# 腾讯云数据库操作所需的 SecretId 和 SecretKey，建议使用子用户密钥并限制权限
# 注意：在生产环境中，强烈建议通过更安全的方式（如KMS、配置中心）管理密钥
TENCENT_SECRET_ID="your_secret_id"
TENCENT_SECRET_KEY="your_secret_key"

# 用于解密数据库中模型 API Key 的 AES 密钥（必须与后端加密时使用的密钥一致）
AES_SECRET_KEY="a_very_strong_and_long_secret_key_for_aes"
```

---

## 六、安全设计

- **无状态服务**：Go 服务本身不存储任何会话信息或用户数据，所有状态依赖于 Token 和数据库，易于水平扩展和维护。
- **密钥隔离**：通过环境变量管理所有敏感密钥，避免硬编码在代码中。
- **最小权限原则**：
    - Go 服务连接数据库所用的 `SecretKey` 应仅授予对 `exe_agents` 和 `exe_models` 两个集合的只读权限。
    - 对云函数的调用严格遵循其定义的 Action 规范。
- **日志记录**：所有关键操作（如鉴权成功/失败、代理请求发起）都应记录日志，便于问题排查和安全审计。

---

## 七、部署

### 1. 构建
```bash
# 交叉编译为 Linux 可执行文件
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o go_proxy ./cmd/server/main.go
```

### 2. 运行
可将编译后的二进制文件 `go_proxy` 和 `.env` 配置文件部署在任何服务器或容器中。

```bash
# 直接运行
./go_proxy

# 推荐使用 Docker 部署
# (Dockerfile 示例)
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o /go_proxy ./cmd/server/main.go

FROM alpine:latest
WORKDIR /root/
COPY --from=builder /go_proxy .
# .env 文件需要通过卷挂载或 k8s configmap 注入
# COPY .env .
EXPOSE 8080
CMD ["./go_proxy"]
``` 