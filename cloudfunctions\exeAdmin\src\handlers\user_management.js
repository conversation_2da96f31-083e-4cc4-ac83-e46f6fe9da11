const bcrypt = require('bcryptjs')
const { validate, createUserSchema, updateUserMembershipSchema, updateUserQuotaSchema, updateUserStatusSchema } = require('../utils/validate')
const { createBusinessError, ERROR_CODES, formatSuccessResponse } = require('../utils/error_handler')
const { userCollection } = require('../utils/db')
const { checkPermission } = require('../middleware/auth')
const logger = require('../utils/logger')

/**
 * 创建用户
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 创建结果
 */
async function createUserByAdmin(event, adminAuth) {
  try {
    // 暂时跳过权限检查和参数校验，实现基本功能
    const { username, password, email, phone, initialQuota = 0, reason = '管理员创建' } = event

    logger.info('管理员创建用户', {
      adminId: adminAuth.adminId,
      username,
      initialQuota
    })

    // 检查用户名是否已存在
    const existingUser = await userCollection.findByUsername(username)
    if (existingUser) {
      throw createBusinessError(ERROR_CODES.USER_ALREADY_EXISTS, '用户名已存在', 400)
    }

    // 加密密码
    const saltRounds = 10
    const hashedPassword = await bcrypt.hash(password, saltRounds)

    // 创建用户数据
    const userData = {
      username,
      password: hashedPassword,
      email: email || '',
      phone: phone || '',
      availableCount: initialQuota,
      totalUsageCount: 0,
      membership: {
        type: '普通用户',
        expiresAt: null,
        isValid: false
      },
      status: '激活',
      createdAt: new Date(),
      updatedAt: new Date(),
      lastLoginAt: null,
      purchaseHistory: [{
        date: new Date(),
        type: '额度充值',
        details: { addedCount: initialQuota },
        reason: reason,
        operator: adminAuth.adminAccount
      }]
    }

    // 创建用户
    const userId = await userCollection.create(userData)

    logger.info('管理员创建用户成功', {
      adminId: adminAuth.adminId,
      userId,
      username
    })

    const responseData = {
      userId,
      username,
      email: email || '',
      phone: phone || '',
      availableCount: initialQuota,
      status: '激活'
    }

    return formatSuccessResponse(responseData, '用户创建成功')

  } catch (error) {
    logger.error('管理员创建用户异常', {
      adminId: adminAuth.adminId,
      username: event?.username,
      error: error.message
    })
    throw error
  }
}

/**
 * 获取用户列表（分页）
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 用户列表
 */
async function getUserList(event, adminAuth) {
  try {
    // 暂时跳过权限检查，直接获取用户列表
    const { page = 1, pageSize = 20 } = event || {}

    logger.info('管理员获取用户列表', {
      adminId: adminAuth.adminId,
      page,
      pageSize
    })

    // 获取分页数据
    const result = await userCollection.getList(page, pageSize)

    const responseData = {
      users: result.data,
      pagination: {
        page: result.page,
        pageSize: result.pageSize,
        total: result.total,
        totalPages: result.totalPages
      }
    }

    return formatSuccessResponse(responseData, '获取用户列表成功')

  } catch (error) {
    logger.error('管理员获取用户列表异常', {
      adminId: adminAuth.adminId,
      error: error.message
    })
    throw error
  }
}

async function updateUserMembershipByAdmin(event, adminAuth) {
  try {
    const { userId, membershipType, durationDays, reason } = event

    logger.info('管理员更新用户会员状态', {
      adminId: adminAuth.adminId,
      userId,
      membershipType,
      durationDays
    })

    // 检查用户是否存在
    const user = await userCollection.findById(userId)
    if (!user) {
      throw createBusinessError(ERROR_CODES.USER_NOT_FOUND, '用户不存在', 404)
    }

    // 计算会员到期时间
    let expiresAt = null
    if (membershipType !== '永久会员' && durationDays) {
      const now = new Date()
      expiresAt = new Date(now.getTime() + durationDays * 24 * 60 * 60 * 1000)
    }

    // 更新用户会员信息
    const updateData = {
      'membership.type': membershipType,
      'membership.expiresAt': expiresAt,
      'membership.isValid': membershipType === '永久会员' || (expiresAt && expiresAt > new Date()),
      updatedAt: new Date()
    }

    await userCollection.update(userId, updateData)

    logger.info('管理员更新用户会员状态成功', {
      adminId: adminAuth.adminId,
      userId,
      membershipType
    })

    return formatSuccessResponse(null, '用户会员状态更新成功')

  } catch (error) {
    logger.error('管理员更新用户会员状态异常', {
      adminId: adminAuth.adminId,
      userId: event?.userId,
      error: error.message
    })
    throw error
  }
}

async function updateUserQuotaByAdmin(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'user_quota_update')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限更新用户额度', 403)
    }

    const { userId, addedCount, reason } = event

    logger.info('管理员更新用户额度', {
      adminId: adminAuth.adminId,
      userId,
      addedCount
    })

    // 检查用户是否存在
    const user = await userCollection.findById(userId)
    if (!user) {
      throw createBusinessError(ERROR_CODES.USER_NOT_FOUND, '用户不存在', 404)
    }

    // 使用原子操作增加用户额度并记录购买历史
    await userCollection.addQuotaWithHistory(userId, addedCount, reason || '管理员充值', adminAuth.adminAccount)

    logger.info('管理员更新用户额度成功', {
      adminId: adminAuth.adminId,
      userId,
      addedCount
    })

    return formatSuccessResponse(null, '用户额度更新成功')

  } catch (error) {
    logger.error('管理员更新用户额度异常', {
      adminId: adminAuth.adminId,
      userId: event?.userId,
      error: error.message
    })
    throw error
  }
}

async function updateUserStatusByAdmin(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'user_status_update')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限更新用户状态', 403)
    }

    const { userId, status, reason } = event

    logger.info('管理员更新用户状态', {
      adminId: adminAuth.adminId,
      userId,
      status
    })

    // 检查用户是否存在
    const user = await userCollection.findById(userId)
    if (!user) {
      throw createBusinessError(ERROR_CODES.USER_NOT_FOUND, '用户不存在', 404)
    }

    // 更新用户状态
    const updateData = {
      status,
      updatedAt: new Date()
    }

    await userCollection.update(userId, updateData)

    logger.info('管理员更新用户状态成功', {
      adminId: adminAuth.adminId,
      userId,
      status
    })

    return formatSuccessResponse(null, '用户状态更新成功')

  } catch (error) {
    logger.error('管理员更新用户状态异常', {
      adminId: adminAuth.adminId,
      userId: event?.userId,
      error: error.message
    })
    throw error
  }
}

module.exports = {
  createUserByAdmin,
  getUserList,
  updateUserMembershipByAdmin,
  updateUserQuotaByAdmin,
  updateUserStatusByAdmin
}