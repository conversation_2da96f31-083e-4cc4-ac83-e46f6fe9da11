const { createBusinessError, ERROR_CODES, formatSuccessResponse } = require('../utils/error_handler')
const { checkPermission } = require('../middleware/auth')
const logger = require('../utils/logger')
const cloud = require('wx-server-sdk')

// 初始化数据库
const db = cloud.database()
const packageCollection = db.collection('exe_payment_packages')

/**
 * 获取套餐列表
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 套餐列表
 */
async function getPackageList(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'package_read')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限查看套餐列表', 403)
    }

    const { isActive, page = 1, pageSize = 20 } = event || {}

    logger.info('管理员获取套餐列表', {
      adminId: adminAuth.adminId,
      isActive,
      page,
      pageSize
    })

    // 构建查询条件
    const where = {}
    if (typeof isActive === 'boolean') {
      where.isActive = isActive
    }

    // 分页查询
    const skip = (page - 1) * pageSize
    const { data: packages } = await packageCollection
      .where(where)
      .orderBy('sortOrder', 'asc')
      .orderBy('createdAt', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get()

    const { total } = await packageCollection.where(where).count()

    logger.info('管理员获取套餐列表成功', {
      adminId: adminAuth.adminId,
      total,
      currentPage: page,
      pageSize
    })

    return formatSuccessResponse({
      packages,
      pagination: {
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    }, '获取套餐列表成功')

  } catch (error) {
    logger.error('获取套餐列表失败', { error: error.message, adminId: adminAuth.adminId })
    throw error
  }
}

/**
 * 创建套餐
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 创建结果
 */
async function createPackage(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'package_write')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限创建套餐', 403)
    }

    const { 
      packageName, 
      packageDescription, 
      originalPrice, 
      currentPrice, 
      quotaAmount, 
      isActive = true, 
      sortOrder = 0,
      promotionText,
      promotionStartTime,
      promotionEndTime
    } = event

    // 参数验证
    if (!packageName || !packageDescription || originalPrice === undefined || currentPrice === undefined || quotaAmount === undefined) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '缺少必要参数', 400)
    }

    if (originalPrice < 0 || currentPrice < 0 || quotaAmount <= 0) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '价格和算力数量必须为正数', 400)
    }

    logger.info('管理员创建套餐', {
      adminId: adminAuth.adminId,
      packageName,
      originalPrice,
      currentPrice,
      quotaAmount
    })

    // 创建套餐
    const now = new Date()
    const packageData = {
      packageName,
      packageDescription,
      originalPrice,
      currentPrice,
      quotaAmount,
      isActive,
      sortOrder,
      promotionText: promotionText || null,
      promotionStartTime: promotionStartTime ? new Date(promotionStartTime) : null,
      promotionEndTime: promotionEndTime ? new Date(promotionEndTime) : null,
      createdAt: now,
      updatedAt: now,
      createdBy: adminAuth.adminId,
      updatedBy: adminAuth.adminId
    }

    const { _id } = await packageCollection.add(packageData)

    logger.info('管理员创建套餐成功', {
      adminId: adminAuth.adminId,
      packageId: _id,
      packageName
    })

    return formatSuccessResponse({
      packageId: _id,
      packageName,
      originalPrice,
      currentPrice,
      quotaAmount,
      isActive,
      createdAt: now
    }, '套餐创建成功')

  } catch (error) {
    logger.error('创建套餐失败', { error: error.message, adminId: adminAuth.adminId })
    throw error
  }
}

/**
 * 更新套餐
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 更新结果
 */
async function updatePackage(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'package_write')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限更新套餐', 403)
    }

    const { 
      packageId, 
      packageName, 
      packageDescription, 
      originalPrice, 
      currentPrice, 
      quotaAmount, 
      isActive, 
      sortOrder,
      promotionText,
      promotionStartTime,
      promotionEndTime
    } = event

    // 参数验证
    if (!packageId) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '缺少套餐ID', 400)
    }

    logger.info('管理员更新套餐', {
      adminId: adminAuth.adminId,
      packageId
    })

    // 检查套餐是否存在
    const { data: existingPackage } = await packageCollection.doc(packageId).get()
    if (!existingPackage) {
      throw createBusinessError(ERROR_CODES.RESOURCE_NOT_FOUND, '套餐不存在', 404)
    }

    // 构建更新数据
    const updateData = {
      updatedAt: new Date(),
      updatedBy: adminAuth.adminId
    }

    if (packageName !== undefined) updateData.packageName = packageName
    if (packageDescription !== undefined) updateData.packageDescription = packageDescription
    if (originalPrice !== undefined) updateData.originalPrice = originalPrice
    if (currentPrice !== undefined) updateData.currentPrice = currentPrice
    if (quotaAmount !== undefined) updateData.quotaAmount = quotaAmount
    if (isActive !== undefined) updateData.isActive = isActive
    if (sortOrder !== undefined) updateData.sortOrder = sortOrder
    if (promotionText !== undefined) updateData.promotionText = promotionText
    if (promotionStartTime !== undefined) updateData.promotionStartTime = promotionStartTime ? new Date(promotionStartTime) : null
    if (promotionEndTime !== undefined) updateData.promotionEndTime = promotionEndTime ? new Date(promotionEndTime) : null

    // 更新套餐
    await packageCollection.doc(packageId).update(updateData)

    logger.info('管理员更新套餐成功', {
      adminId: adminAuth.adminId,
      packageId
    })

    return formatSuccessResponse({
      packageId,
      updatedAt: updateData.updatedAt
    }, '套餐更新成功')

  } catch (error) {
    logger.error('更新套餐失败', { error: error.message, adminId: adminAuth.adminId })
    throw error
  }
}

/**
 * 删除套餐
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 删除结果
 */
async function deletePackage(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'package_write')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限删除套餐', 403)
    }

    const { packageId } = event

    // 参数验证
    if (!packageId) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '缺少套餐ID', 400)
    }

    logger.info('管理员删除套餐', {
      adminId: adminAuth.adminId,
      packageId
    })

    // 检查套餐是否存在
    const { data: existingPackage } = await packageCollection.doc(packageId).get()
    if (!existingPackage) {
      throw createBusinessError(ERROR_CODES.RESOURCE_NOT_FOUND, '套餐不存在', 404)
    }

    // 删除套餐
    await packageCollection.doc(packageId).remove()

    logger.info('管理员删除套餐成功', {
      adminId: adminAuth.adminId,
      packageId
    })

    return formatSuccessResponse({
      packageId,
      deletedAt: new Date()
    }, '套餐删除成功')

  } catch (error) {
    logger.error('删除套餐失败', { error: error.message, adminId: adminAuth.adminId })
    throw error
  }
}

module.exports = {
  getPackageList,
  createPackage,
  updatePackage,
  deletePackage
}
