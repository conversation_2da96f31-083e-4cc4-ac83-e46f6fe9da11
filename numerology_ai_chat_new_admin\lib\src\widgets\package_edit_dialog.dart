import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:numerology_ai_chat_admin/src/models/package_model.dart';

class PackageEditDialog extends StatefulWidget {
  final PaymentPackage? package;
  final Function(dynamic) onSave;

  const PackageEditDialog({
    super.key,
    this.package,
    required this.onSave,
  });

  @override
  State<PackageEditDialog> createState() => _PackageEditDialogState();
}

class _PackageEditDialogState extends State<PackageEditDialog> {
  final _formKey = GlobalKey<FormState>();
  final _packageNameController = TextEditingController();
  final _packageDescriptionController = TextEditingController();
  final _originalPriceController = TextEditingController();
  final _currentPriceController = TextEditingController();
  final _quotaAmountController = TextEditingController();
  final _sortOrderController = TextEditingController();
  final _promotionTextController = TextEditingController();

  bool _isActive = true;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.package != null) {
      _packageNameController.text = widget.package!.packageName;
      _packageDescriptionController.text = widget.package!.packageDescription;
      _originalPriceController.text = (widget.package!.originalPrice / 100).toString();
      _currentPriceController.text = (widget.package!.price / 100).toString();
      _quotaAmountController.text = widget.package!.quotaAmount.toString();
      _sortOrderController.text = widget.package!.sortOrder.toString();
      _promotionTextController.text = widget.package!.tags?.join(', ') ?? '';
      _isActive = widget.package!.isActive;
    } else {
      _sortOrderController.text = '0';
    }
  }

  @override
  void dispose() {
    _packageNameController.dispose();
    _packageDescriptionController.dispose();
    _originalPriceController.dispose();
    _currentPriceController.dispose();
    _quotaAmountController.dispose();
    _sortOrderController.dispose();
    _promotionTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.7,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Row(
              children: [
                Text(
                  widget.package == null ? '新建套餐' : '编辑套餐',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // 表单内容
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 套餐名称
                      TextFormField(
                        controller: _packageNameController,
                        decoration: const InputDecoration(
                          labelText: '套餐名称 *',
                          border: OutlineInputBorder(),
                          helperText: '如：基础套餐、高级套餐等',
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return '请输入套餐名称';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      // 套餐描述
                      TextFormField(
                        controller: _packageDescriptionController,
                        decoration: const InputDecoration(
                          labelText: '套餐描述 *',
                          border: OutlineInputBorder(),
                          helperText: '详细描述套餐内容和特点',
                        ),
                        maxLines: 3,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return '请输入套餐描述';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      // 价格和算力行
                      Row(
                        children: [
                          // 原价
                          Expanded(
                            child: TextFormField(
                              controller: _originalPriceController,
                              decoration: const InputDecoration(
                                labelText: '原价 *',
                                border: OutlineInputBorder(),
                                prefixText: '¥',
                              ),
                              keyboardType: const TextInputType.numberWithOptions(decimal: true),
                              inputFormatters: [
                                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                              ],
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return '请输入原价';
                                }
                                final price = double.tryParse(value);
                                if (price == null || price <= 0) {
                                  return '请输入有效的价格';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          // 现价
                          Expanded(
                            child: TextFormField(
                              controller: _currentPriceController,
                              decoration: const InputDecoration(
                                labelText: '现价 *',
                                border: OutlineInputBorder(),
                                prefixText: '¥',
                              ),
                              keyboardType: const TextInputType.numberWithOptions(decimal: true),
                              inputFormatters: [
                                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                              ],
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return '请输入现价';
                                }
                                final price = double.tryParse(value);
                                if (price == null || price <= 0) {
                                  return '请输入有效的价格';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          // 算力数量
                          Expanded(
                            child: TextFormField(
                              controller: _quotaAmountController,
                              decoration: const InputDecoration(
                                labelText: '算力数量 *',
                                border: OutlineInputBorder(),
                                suffixText: '算力',
                              ),
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return '请输入算力数量';
                                }
                                final amount = int.tryParse(value);
                                if (amount == null || amount <= 0) {
                                  return '请输入有效的算力数量';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      // 排序和状态行
                      Row(
                        children: [
                          // 排序权重
                          Expanded(
                            child: TextFormField(
                              controller: _sortOrderController,
                              decoration: const InputDecoration(
                                labelText: '排序权重',
                                border: OutlineInputBorder(),
                                helperText: '数字越小越靠前',
                              ),
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                              validator: (value) {
                                if (value != null && value.isNotEmpty) {
                                  if (int.tryParse(value) == null) {
                                    return '请输入有效数字';
                                  }
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          // 状态开关
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('状态'),
                              const SizedBox(height: 8),
                              Switch(
                                value: _isActive,
                                onChanged: (value) {
                                  setState(() {
                                    _isActive = value;
                                  });
                                },
                              ),
                              Text(
                                _isActive ? '启用' : '禁用',
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      // 促销文本
                      TextFormField(
                        controller: _promotionTextController,
                        decoration: const InputDecoration(
                          labelText: '促销文本',
                          border: OutlineInputBorder(),
                          helperText: '可选，如：限时优惠、热门推荐等',
                        ),
                        maxLines: 2,
                      ),
                      const SizedBox(height: 16),
                      // 价格对比提示
                      _buildPriceComparisonTip(),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            // 操作按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _isLoading ? null : _handleSave,
                  child: _isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Text(widget.package == null ? '创建' : '保存'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceComparisonTip() {
    final originalPrice = double.tryParse(_originalPriceController.text) ?? 0;
    final currentPrice = double.tryParse(_currentPriceController.text) ?? 0;
    final quotaAmount = int.tryParse(_quotaAmountController.text) ?? 0;

    if (originalPrice <= 0 || currentPrice <= 0 || quotaAmount <= 0) {
      return const SizedBox.shrink();
    }

    final discount = ((originalPrice - currentPrice) / originalPrice * 100);
    final pricePerQuota = currentPrice / quotaAmount;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.blue.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue, size: 16),
              const SizedBox(width: 8),
              Text(
                '价格分析',
                style: TextStyle(
                  color: Colors.blue[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          if (discount > 0)
            Text(
              '优惠幅度: ${discount.toStringAsFixed(1)}%',
              style: TextStyle(color: Colors.blue[700], fontSize: 12),
            ),
          Text(
            '单位算力价格: ¥${pricePerQuota.toStringAsFixed(4)}/算力',
            style: TextStyle(color: Colors.blue[700], fontSize: 12),
          ),
        ],
      ),
    );
  }

  void _handleSave() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      if (widget.package == null) {
        // 创建新套餐
        final request = CreatePackageRequest(
          packageName: _packageNameController.text.trim(),
          packageDescription: _packageDescriptionController.text.trim(),
          originalPrice: double.parse(_originalPriceController.text),
          currentPrice: double.parse(_currentPriceController.text),
          quotaAmount: int.parse(_quotaAmountController.text),
          isActive: _isActive,
          sortOrder: int.tryParse(_sortOrderController.text) ?? 0,
          promotionText: _promotionTextController.text.trim().isEmpty ? null : _promotionTextController.text.trim(),
        );
        await widget.onSave(request);
      } else {
        // 更新套餐
        final request = UpdatePackageRequest(
          packageName: _packageNameController.text.trim(),
          packageDescription: _packageDescriptionController.text.trim(),
          originalPrice: double.parse(_originalPriceController.text),
          currentPrice: double.parse(_currentPriceController.text),
          quotaAmount: int.parse(_quotaAmountController.text),
          isActive: _isActive,
          sortOrder: int.tryParse(_sortOrderController.text) ?? 0,
          promotionText: _promotionTextController.text.trim().isEmpty ? null : _promotionTextController.text.trim(),
        );
        await widget.onSave(request);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
