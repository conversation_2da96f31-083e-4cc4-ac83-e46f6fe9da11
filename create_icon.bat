@echo off
chcp 65001 >nul
echo ════════════════════════════════════════════
echo    创建超高质量应用图标
echo ════════════════════════════════════════════
echo.

echo 🔄 正在生成高质量图标...
python create_high_quality_icon.py

if %errorlevel% equ 0 (
    echo.
    echo 🎉 图标生成成功！
    echo.
    echo 📝 接下来请运行：
    echo    flutter clean
    echo    flutter build windows --release
    echo.
) else (
    echo.
    echo ❌ 图标生成失败！
    echo 💡 请检查Python环境和PIL库是否正确安装
    echo.
)

pause
