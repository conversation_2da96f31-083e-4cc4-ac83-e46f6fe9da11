/// 额度消耗历史模型
/// 用于存储用户每次AI对话的算力消耗详细记录
class UsageHistoryModel {
  final String id;
  final String userId;
  final String agentId;
  final String agentName;
  final String modelId;
  final String modelName;
  final String pricingTierId;
  final String tierName;
  final String modelLevel;
  final int powerCost;
  final int balanceBefore;
  final int balanceAfter;
  final DateTime consumeTime;
  final String description;
  final DateTime createdAt;

  const UsageHistoryModel({
    required this.id,
    required this.userId,
    required this.agentId,
    required this.agentName,
    required this.modelId,
    required this.modelName,
    required this.pricingTierId,
    required this.tierName,
    required this.modelLevel,
    required this.powerCost,
    required this.balanceBefore,
    required this.balanceAfter,
    required this.consumeTime,
    required this.description,
    required this.createdAt,
  });

  /// 从JSON创建UsageHistoryModel实例
  factory UsageHistoryModel.fromJson(Map<String, dynamic> json) {
    return UsageHistoryModel(
      id: json['_id'] as String,
      userId: json['userId'] as String,
      agentId: json['agentId'] as String,
      agentName: json['agentName'] as String,
      modelId: json['modelId'] as String,
      modelName: json['modelName'] as String,
      pricingTierId: json['pricingTierId'] as String? ?? '',
      tierName: json['tierName'] as String? ?? '未知档次',
      modelLevel: json['modelLevel'] as String? ?? '初级',
      powerCost: json['powerCost'] as int,
      balanceBefore: json['balanceBefore'] as int,
      balanceAfter: json['balanceAfter'] as int,
      consumeTime: DateTime.parse(json['consumeTime'] as String),
      description: json['description'] as String? ?? '',
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'userId': userId,
      'agentId': agentId,
      'agentName': agentName,
      'modelId': modelId,
      'modelName': modelName,
      'pricingTierId': pricingTierId,
      'tierName': tierName,
      'modelLevel': modelLevel,
      'powerCost': powerCost,
      'balanceBefore': balanceBefore,
      'balanceAfter': balanceAfter,
      'consumeTime': consumeTime.toIso8601String(),
      'description': description,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  /// 获取格式化的消耗时间
  String get formattedConsumeTime {
    final now = DateTime.now();
    final difference = now.difference(consumeTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  /// 获取格式化的消耗时间（详细）
  String get formattedConsumeTimeDetailed {
    return '${consumeTime.year}-${consumeTime.month.toString().padLeft(2, '0')}-${consumeTime.day.toString().padLeft(2, '0')} '
           '${consumeTime.hour.toString().padLeft(2, '0')}:${consumeTime.minute.toString().padLeft(2, '0')}';
  }

  /// 获取模型等级显示名称
  String get modelLevelDisplayName {
    switch (modelLevel) {
      case '初级':
        return '初级模型';
      case '高级':
        return '高级模型';
      default:
        return modelLevel;
    }
  }

  /// 获取余额变化描述
  String get balanceChangeDescription {
    return '$balanceBefore → $balanceAfter (-$powerCost)';
  }

  /// 获取简化的描述
  String get shortDescription {
    return '$tierName + $modelLevelDisplayName';
  }

  /// 复制并修改部分属性
  UsageHistoryModel copyWith({
    String? id,
    String? userId,
    String? agentId,
    String? agentName,
    String? modelId,
    String? modelName,
    String? pricingTierId,
    String? tierName,
    String? modelLevel,
    int? powerCost,
    int? balanceBefore,
    int? balanceAfter,
    DateTime? consumeTime,
    String? description,
    DateTime? createdAt,
  }) {
    return UsageHistoryModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      agentId: agentId ?? this.agentId,
      agentName: agentName ?? this.agentName,
      modelId: modelId ?? this.modelId,
      modelName: modelName ?? this.modelName,
      pricingTierId: pricingTierId ?? this.pricingTierId,
      tierName: tierName ?? this.tierName,
      modelLevel: modelLevel ?? this.modelLevel,
      powerCost: powerCost ?? this.powerCost,
      balanceBefore: balanceBefore ?? this.balanceBefore,
      balanceAfter: balanceAfter ?? this.balanceAfter,
      consumeTime: consumeTime ?? this.consumeTime,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UsageHistoryModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UsageHistoryModel(id: $id, agentName: $agentName, modelName: $modelName, powerCost: $powerCost, consumeTime: $consumeTime)';
  }
}

/// 消耗历史分页数据模型
class UsageHistoryPageModel {
  final List<UsageHistoryModel> history;
  final int page;
  final int limit;
  final int total;
  final int totalPages;

  const UsageHistoryPageModel({
    required this.history,
    required this.page,
    required this.limit,
    required this.total,
    required this.totalPages,
  });

  /// 从JSON创建UsageHistoryPageModel实例
  factory UsageHistoryPageModel.fromJson(Map<String, dynamic> json) {
    final historyList = (json['history'] as List<dynamic>)
        .map((item) => UsageHistoryModel.fromJson(item as Map<String, dynamic>))
        .toList();

    final pagination = json['pagination'] as Map<String, dynamic>;

    return UsageHistoryPageModel(
      history: historyList,
      page: pagination['page'] as int,
      limit: pagination['limit'] as int,
      total: pagination['total'] as int,
      totalPages: pagination['totalPages'] as int,
    );
  }

  /// 是否有更多数据
  bool get hasMore => page < totalPages;

  /// 是否为空
  bool get isEmpty => history.isEmpty;

  /// 是否不为空
  bool get isNotEmpty => history.isNotEmpty;

  @override
  String toString() {
    return 'UsageHistoryPageModel(page: $page, total: $total, historyCount: ${history.length})';
  }
}
