// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'overall_stats_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OverallStatsModel _$OverallStatsModelFromJson(Map<String, dynamic> json) =>
    OverallStatsModel(
      users: UserStatsModel.fromJson(json['users'] as Map<String, dynamic>),
      orders: OrderStatsModel.fromJson(json['orders'] as Map<String, dynamic>),
      payments:
          PaymentStatsModel.fromJson(json['payments'] as Map<String, dynamic>),
      usage: UsageStatsModel.fromJson(json['usage'] as Map<String, dynamic>),
      agents: AgentStatsModel.fromJson(json['agents'] as Map<String, dynamic>),
      models: ModelStatsModel.fromJson(json['models'] as Map<String, dynamic>),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$OverallStatsModelToJson(OverallStatsModel instance) =>
    <String, dynamic>{
      'users': instance.users,
      'orders': instance.orders,
      'payments': instance.payments,
      'usage': instance.usage,
      'agents': instance.agents,
      'models': instance.models,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };

UserStatsModel _$UserStatsModelFromJson(Map<String, dynamic> json) =>
    UserStatsModel(
      total: (json['total'] as num).toInt(),
      todayNew: (json['todayNew'] as num).toInt(),
      activeLastWeek: (json['activeLastWeek'] as num).toInt(),
    );

Map<String, dynamic> _$UserStatsModelToJson(UserStatsModel instance) =>
    <String, dynamic>{
      'total': instance.total,
      'todayNew': instance.todayNew,
      'activeLastWeek': instance.activeLastWeek,
    };

OrderStatsModel _$OrderStatsModelFromJson(Map<String, dynamic> json) =>
    OrderStatsModel(
      total: (json['total'] as num).toInt(),
      completed: (json['completed'] as num).toInt(),
      pending: (json['pending'] as num).toInt(),
      todayNew: (json['todayNew'] as num).toInt(),
      totalRevenue: (json['totalRevenue'] as num).toDouble(),
    );

Map<String, dynamic> _$OrderStatsModelToJson(OrderStatsModel instance) =>
    <String, dynamic>{
      'total': instance.total,
      'completed': instance.completed,
      'pending': instance.pending,
      'todayNew': instance.todayNew,
      'totalRevenue': instance.totalRevenue,
    };

PaymentStatsModel _$PaymentStatsModelFromJson(Map<String, dynamic> json) =>
    PaymentStatsModel(
      total: (json['total'] as num).toInt(),
      success: (json['success'] as num).toInt(),
      failed: (json['failed'] as num).toInt(),
      todayNew: (json['todayNew'] as num).toInt(),
      successRate: json['successRate'] as String,
    );

Map<String, dynamic> _$PaymentStatsModelToJson(PaymentStatsModel instance) =>
    <String, dynamic>{
      'total': instance.total,
      'success': instance.success,
      'failed': instance.failed,
      'todayNew': instance.todayNew,
      'successRate': instance.successRate,
    };

UsageStatsModel _$UsageStatsModelFromJson(Map<String, dynamic> json) =>
    UsageStatsModel(
      total: (json['total'] as num).toInt(),
      todayNew: (json['todayNew'] as num).toInt(),
      totalPowerConsumed: (json['totalPowerConsumed'] as num).toInt(),
      todayPowerConsumed: (json['todayPowerConsumed'] as num).toInt(),
    );

Map<String, dynamic> _$UsageStatsModelToJson(UsageStatsModel instance) =>
    <String, dynamic>{
      'total': instance.total,
      'todayNew': instance.todayNew,
      'totalPowerConsumed': instance.totalPowerConsumed,
      'todayPowerConsumed': instance.todayPowerConsumed,
    };

AgentStatsModel _$AgentStatsModelFromJson(Map<String, dynamic> json) =>
    AgentStatsModel(
      total: (json['total'] as num).toInt(),
      enabled: (json['enabled'] as num).toInt(),
      popular: (json['popular'] as List<dynamic>)
          .map((e) => PopularAgentModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$AgentStatsModelToJson(AgentStatsModel instance) =>
    <String, dynamic>{
      'total': instance.total,
      'enabled': instance.enabled,
      'popular': instance.popular,
    };

PopularAgentModel _$PopularAgentModelFromJson(Map<String, dynamic> json) =>
    PopularAgentModel(
      id: json['id'] as String,
      name: json['name'] as String,
      count: (json['count'] as num).toInt(),
    );

Map<String, dynamic> _$PopularAgentModelToJson(PopularAgentModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'count': instance.count,
    };

ModelStatsModel _$ModelStatsModelFromJson(Map<String, dynamic> json) =>
    ModelStatsModel(
      total: (json['total'] as num).toInt(),
      enabled: (json['enabled'] as num).toInt(),
      usageByLevel: Map<String, int>.from(json['usageByLevel'] as Map),
    );

Map<String, dynamic> _$ModelStatsModelToJson(ModelStatsModel instance) =>
    <String, dynamic>{
      'total': instance.total,
      'enabled': instance.enabled,
      'usageByLevel': instance.usageByLevel,
    };
