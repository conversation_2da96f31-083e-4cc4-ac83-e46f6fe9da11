
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:numerology_ai_chat_admin/src/models/model_model.dart';
import 'package:numerology_ai_chat_admin/src/providers/auth_provider.dart';
import 'package:numerology_ai_chat_admin/src/services/admin_api_service.dart';

// Model列表状态
class ModelListState {
  final List<Model>? models;
  final bool isLoading;
  final String? error;

  ModelListState({this.models, this.isLoading = false, this.error});
}

// ModelProvider
class ModelNotifier extends StateNotifier<ModelListState> {
  final AdminApiService _apiService;
  final String? _token;

  ModelNotifier(this._apiService, this._token) : super(ModelListState()) {
    if (_token != null) {
      fetchModels();
    }
  }

  Future<void> fetchModels() async {
    state = ModelListState(isLoading: true);
    try {
      final response = await _apiService.getModels(_token!);
      if (response['code'] == 0 && response['data'] != null) {
        final data = response['data']['data'];
        if (data != null && data['models'] != null) {
          final modelList = (data['models'] as List)
              .map((modelJson) => Model.fromJson(modelJson))
              .toList();
          state = ModelListState(models: modelList);
        } else {
          state = ModelListState(models: []);
        }
      } else {
        state = ModelListState(error: response['message'] ?? '获取模型列表失败');
      }
    } catch (e) {
      state = ModelListState(error: e.toString());
    }
  }

  Future<bool> createModel(Map<String, dynamic> modelData) async {
    try {
      final response = await _apiService.createModel(_token!, modelData);
      if (response['code'] == 0) {
        fetchModels(); // Refresh the list
        return true;
      } else {
        state = ModelListState(models: state.models, error: response['message'] ?? '创建模型失败');
        return false;
      }
    } catch (e) {
      state = ModelListState(models: state.models, error: e.toString());
      return false;
    }
  }

  Future<bool> updateModel(String modelId, Map<String, dynamic> modelData) async {
    try {
      final response = await _apiService.updateModel(_token!, modelId, modelData);
      if (response['code'] == 0) {
        fetchModels(); // Refresh the list
        return true;
      } else {
        state = ModelListState(models: state.models, error: response['message'] ?? '更新模型失败');
        return false;
      }
    } catch (e) {
      state = ModelListState(models: state.models, error: e.toString());
      return false;
    }
  }

  Future<bool> deleteModel(String modelId) async {
    try {
      final response = await _apiService.deleteModel(_token!, modelId);
      if (response['code'] == 0) {
        fetchModels(); // Refresh the list
        return true;
      } else {
        state = ModelListState(models: state.models, error: response['message'] ?? '删除模型失败');
        return false;
      }
    } catch (e) {
      state = ModelListState(models: state.models, error: e.toString());
      return false;
    }
  }
}

// Model状态提供者
final modelProvider = StateNotifierProvider<ModelNotifier, ModelListState>((ref) {
  final apiService = ref.watch(adminApiServiceProvider);
  final token = ref.watch(authProvider).token;
  return ModelNotifier(apiService, token);
});
