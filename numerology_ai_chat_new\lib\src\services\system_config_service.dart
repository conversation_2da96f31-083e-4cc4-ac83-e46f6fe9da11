import 'dart:convert';
import 'package:http/http.dart' as http;
import '../core/constants/app_constants.dart';
import '../models/api_response.dart';
import '../core/storage/storage_service.dart';
import '../core/errors/app_error.dart';

/// 系统配置服务
class SystemConfigService {
  static const String _baseUrl = AppConstants.exeFunctionUrl;
  static SystemConfigService? _instance;
  static String? _cachedGoProxyApiUrl;
  static DateTime? _cacheTime;
  static const Duration _cacheExpiry = Duration(minutes: 30);

  final StorageService _storageService;

  SystemConfigService._(this._storageService);

  /// 获取单例实例
  static SystemConfigService getInstance(StorageService storageService) {
    _instance ??= SystemConfigService._(storageService);
    return _instance!;
  }

  /// 确保存储服务已初始化
  Future<void> _ensureStorageInitialized() async {
    try {
      await _storageService.init();
    } catch (e) {
      print('SystemConfigService: 存储服务初始化失败: $e');
      // 不抛出异常，让后续逻辑继续执行
    }
  }

  /// 获取Go代理API地址
  /// [forceRefresh] 是否强制从云端刷新，忽略所有缓存
  Future<String> getGoProxyApiUrl({bool forceRefresh = false}) async {
    // 确保存储服务已初始化
    await _ensureStorageInitialized();

    // 如果强制刷新，直接跳过所有缓存检查
    if (!forceRefresh) {
      // 检查内存缓存
      if (_cachedGoProxyApiUrl != null &&
          _cacheTime != null &&
          DateTime.now().difference(_cacheTime!).compareTo(_cacheExpiry) < 0) {
        print('SystemConfigService: 使用内存缓存的API地址: $_cachedGoProxyApiUrl');
        return _cachedGoProxyApiUrl!;
      }

      // 检查本地存储缓存（添加时间戳验证）
      final result = await _storageService.get<String>(AppConstants.goProxyApiUrlKey);
      final timestampResult = await _storageService.get<String>('${AppConstants.goProxyApiUrlKey}_timestamp');

      if (result.isSuccess && result.data != null &&
          timestampResult.isSuccess && timestampResult.data != null) {
        try {
          final cacheTime = DateTime.parse(timestampResult.data!);
          final cacheAge = DateTime.now().difference(cacheTime);

          // 本地存储缓存也有30分钟的有效期
          if (cacheAge.compareTo(_cacheExpiry) < 0) {
            _cachedGoProxyApiUrl = result.data!;
            _cacheTime = cacheTime;
            print('SystemConfigService: 使用本地存储缓存的API地址: $_cachedGoProxyApiUrl');
            return _cachedGoProxyApiUrl!;
          } else {
            print('SystemConfigService: 本地存储缓存已过期，缓存时间: $cacheAge');
          }
        } catch (e) {
          print('SystemConfigService: 解析缓存时间戳失败: $e');
        }
      }
    } else {
      print('SystemConfigService: 强制刷新API地址，忽略所有缓存');
    }

    // 从云函数获取
    try {
      print('SystemConfigService: 从云函数获取API地址');
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'action': AppConstants.actionGetGoProxyApiUrl,
        }),
      ).timeout(AppConstants.connectTimeout);

      final jsonData = jsonDecode(response.body) as Map<String, dynamic>;

      if (jsonData['code'] == 0 && jsonData['data'] != null) {
        final configValue = jsonData['data']['configValue'] as String;

        // 缓存到内存和本地存储（带时间戳）
        _cachedGoProxyApiUrl = configValue;
        _cacheTime = DateTime.now();
        await _storageService.set(AppConstants.goProxyApiUrlKey, configValue);
        await _storageService.set('${AppConstants.goProxyApiUrlKey}_timestamp', _cacheTime!.toIso8601String());

        print('SystemConfigService: 从云函数获取到API地址: $configValue');
        return configValue;
      } else {
        throw Exception(jsonData['message'] ?? '获取配置失败');
      }
    } catch (e) {
      print('获取Go代理API地址失败: $e');
      // 清除缓存
      await clearCache();
      // 抛出异常，不提供兜底机制
      throw Exception('无法获取Go代理API地址: $e');
    }
  }

  /// 获取系统配置
  Future<ApiResponse<CloudFunctionData<Map<String, dynamic>>>> getSystemConfig({
    String? configField,
  }) async {
    try {
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'action': AppConstants.actionGetSystemConfig,
          if (configField != null) 'configField': configField,
        }),
      ).timeout(AppConstants.connectTimeout);

      final jsonData = jsonDecode(response.body) as Map<String, dynamic>;

      return ApiResponse.fromJson(
        jsonData,
        (data) => CloudFunctionData.fromJson(
          data as Map<String, dynamic>,
          (innerData) => innerData as Map<String, dynamic>,
        ),
      );
    } catch (e) {
      return ApiResponse<CloudFunctionData<Map<String, dynamic>>>(
        code: -1,
        message: '获取系统配置失败: $e',
        data: null,
      );
    }
  }

  /// 获取特定配置项的值
  Future<T?> getConfigValue<T>(String configField, {T? defaultValue}) async {
    try {
      final response = await getSystemConfig(configField: configField);
      if (response.isSuccess && response.data?.data != null) {
        final configValue = response.data!.data['configValue'];
        if (configValue is T) {
          return configValue;
        }
      }
      return defaultValue;
    } catch (e) {
      print('获取配置项失败: $configField, $e');
      return defaultValue;
    }
  }

  /// 清除缓存
  Future<void> clearCache() async {
    print('SystemConfigService: 清除所有API地址缓存');
    _cachedGoProxyApiUrl = null;
    _cacheTime = null;

    // 确保存储服务已初始化
    await _ensureStorageInitialized();

    await _storageService.delete(AppConstants.goProxyApiUrlKey);
    await _storageService.delete('${AppConstants.goProxyApiUrlKey}_timestamp');
  }

  /// 预加载Go代理API地址（在应用启动时调用）
  /// [forceRefresh] 是否强制从云端刷新
  Future<void> preloadGoProxyApiUrl({bool forceRefresh = false}) async {
    try {
      await getGoProxyApiUrl(forceRefresh: forceRefresh);
      print('Go代理API地址预加载成功: $_cachedGoProxyApiUrl');
    } catch (e) {
      print('Go代理API地址预加载失败: $e');
    }
  }

  /// 强制刷新API地址（登录后调用）
  Future<String> forceRefreshGoProxyApiUrl() async {
    print('SystemConfigService: 强制刷新Go代理API地址');
    return await getGoProxyApiUrl(forceRefresh: true);
  }
}

/// 云函数数据包装类
class CloudFunctionData<T> {
  final T data;

  CloudFunctionData({required this.data});

  factory CloudFunctionData.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic) fromJsonT,
  ) {
    return CloudFunctionData(
      data: fromJsonT(json),
    );
  }
}
