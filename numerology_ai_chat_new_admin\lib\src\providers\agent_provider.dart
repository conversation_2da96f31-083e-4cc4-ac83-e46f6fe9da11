
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:numerology_ai_chat_admin/src/models/agent_model.dart';
import 'package:numerology_ai_chat_admin/src/providers/auth_provider.dart';
import 'package:numerology_ai_chat_admin/src/services/admin_api_service.dart';

// Agent列表状态
class AgentListState {
  final List<Agent>? agents;
  final bool isLoading;
  final String? error;

  AgentListState({this.agents, this.isLoading = false, this.error});
}

// AgentProvider
class AgentNotifier extends StateNotifier<AgentListState> {
  final AdminApiService _apiService;
  final String? _token;

  AgentNotifier(this._apiService, this._token) : super(AgentListState()) {
    if (_token != null) {
      fetchAgents();
    }
  }

  Future<void> fetchAgents() async {
    state = AgentListState(isLoading: true);
    try {
      final response = await _apiService.getAgents(_token!);
      if (response['code'] == 0 && response['data'] != null) {
        final data = response['data']['data'];
        if (data != null && data['agents'] != null) {
          final agentList = (data['agents'] as List)
              .map((agentJson) => Agent.fromJson(agentJson))
              .toList();
          state = AgentListState(agents: agentList);
        } else {
          state = AgentListState(agents: []);
        }
      } else {
        state = AgentListState(error: response['message'] ?? '获取智能体列表失败');
      }
    } catch (e) {
      state = AgentListState(error: e.toString());
    }
  }

  Future<bool> createAgent(Map<String, dynamic> agentData) async {
    try {
      final response = await _apiService.createAgent(_token!, agentData);
      if (response['code'] == 0) {
        fetchAgents(); // Refresh the list
        return true;
      } else {
        state = AgentListState(agents: state.agents, error: response['message'] ?? '创建智能体失败');
        return false;
      }
    } catch (e) {
      state = AgentListState(agents: state.agents, error: e.toString());
      return false;
    }
  }

  Future<bool> updateAgent(String agentId, Map<String, dynamic> agentData) async {
    try {
      final response = await _apiService.updateAgent(_token!, agentId, agentData);
      if (response['code'] == 0) {
        fetchAgents(); // Refresh the list
        return true;
      } else {
        state = AgentListState(agents: state.agents, error: response['message'] ?? '更新智能体失败');
        return false;
      }
    } catch (e) {
      state = AgentListState(agents: state.agents, error: e.toString());
      return false;
    }
  }

  Future<bool> deleteAgent(String agentId) async {
    try {
      final response = await _apiService.deleteAgent(_token!, agentId);
      if (response['code'] == 0) {
        fetchAgents(); // Refresh the list
        return true;
      } else {
        state = AgentListState(agents: state.agents, error: response['message'] ?? '删除智能体失败');
        return false;
      }
    } catch (e) {
      state = AgentListState(agents: state.agents, error: e.toString());
      return false;
    }
  }
}

// Agent状态提供者
final agentProvider = StateNotifierProvider<AgentNotifier, AgentListState>((ref) {
  final apiService = ref.watch(adminApiServiceProvider);
  final token = ref.watch(authProvider).token;
  return AgentNotifier(apiService, token);
});
