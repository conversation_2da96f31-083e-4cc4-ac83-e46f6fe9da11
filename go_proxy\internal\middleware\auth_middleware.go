package middleware

import (
	"log"
	"net/http"
	"strings"

	"go_proxy/internal/config"
	"go_proxy/internal/services/cloudfunction"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware 鉴权中间件
func AuthMiddleware(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 提取Authorization头
		authHeader := c.Get<PERSON>eader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Missing authorization header",
			})
			c.Abort()
			return
		}

		// 检查Bearer token格式
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid authorization header format",
			})
			c.Abort()
			return
		}

		accessToken := parts[1]
		if accessToken == "" {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"error": "Empty access token",
			})
			c.Abort()
			return
		}

		// 调用云函数验证token
		client := cloudfunction.NewClient(cfg)
		userInfo, err := client.GetUserInfo(accessToken)
		if err != nil {
			log.Printf("Auth failed: %v", err)
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid or expired token",
			})
			c.Abort()
			return
		}

		// 将用户信息和token存入context
		c.Set("userId", userInfo.UserID)
		c.Set("username", userInfo.Username)
		c.Set("userInfo", userInfo)
		c.Set("token", accessToken)

		c.Next()
	}
}