#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_simulate_payment():
    """测试模拟支付功能"""
    
    # 云函数URL
    api_url = "https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction"
    
    # 用户token（从Flutter日志中获取）
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJlZWExNzU0ZDY4NGU2MzA0MDJkNmY3MTkxNjIyYTU4MSIsInVzZXJuYW1lIjoiZGVtbyIsImlhdCI6MTc1MDA5OTU5NCwiZXhwIjoxNzUwMTAzMTk0LCJhdWQiOiJhY2Nlc3MiLCJpc3MiOiJudW1lcm9sb2d5LWFpLWNoYXQifQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"
    
    # 测试订单ID
    order_id = "adea46666850684a02f71c1c4279d340"
    
    # 请求数据
    request_data = {
        "action": "simulatePayment",
        "orderId": order_id,
        "success": True
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    try:
        print("开始测试模拟支付...")
        print(f"订单ID: {order_id}")
        print(f"API URL: {api_url}")
        
        # 发送请求
        response = requests.post(api_url, json=request_data, headers=headers, timeout=30)
        response.raise_for_status()
        
        # 解析响应
        data = response.json()
        print(f"响应状态码: {response.status_code}")
        print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        # 检查结果
        if data.get("code") == 0:
            print("✅ 模拟支付请求成功")
            payment_data = data.get("data", {})
            if payment_data.get("success"):
                print(f"✅ 支付成功，充值算力: {payment_data.get('quotaAdded', 0)}")
            else:
                print("❌ 支付失败")
        else:
            print(f"❌ 请求失败: {data.get('message', '未知错误')}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")

def test_get_user_info():
    """测试获取用户信息"""
    
    # 云函数URL
    api_url = "https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction"
    
    # 用户token
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJlZWExNzU0ZDY4NGU2MzA0MDJkNmY3MTkxNjIyYTU4MSIsInVzZXJuYW1lIjoiZGVtbyIsImlhdCI6MTc1MDA5OTU5NCwiZXhwIjoxNzUwMTAzMTk0LCJhdWQiOiJhY2Nlc3MiLCJpc3MiOiJudW1lcm9sb2d5LWFpLWNoYXQifQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"
    
    # 请求数据
    request_data = {
        "action": "getUserInfo"
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    try:
        print("\n开始获取用户信息...")
        
        # 发送请求
        response = requests.post(api_url, json=request_data, headers=headers, timeout=30)
        response.raise_for_status()
        
        # 解析响应
        data = response.json()
        print(f"响应状态码: {response.status_code}")
        print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        # 检查结果
        if data.get("code") == 0:
            print("✅ 获取用户信息成功")
            user_data = data.get("data", {}).get("data", {}).get("user", {})
            print(f"用户算力: {user_data.get('availableCount', 0)}")
        else:
            print(f"❌ 请求失败: {data.get('message', '未知错误')}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")

if __name__ == "__main__":
    # 先获取用户当前信息
    test_get_user_info()
    
    # 然后测试模拟支付
    test_simulate_payment()
    
    # 再次获取用户信息查看变化
    test_get_user_info()
