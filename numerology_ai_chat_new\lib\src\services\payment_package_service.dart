import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/payment_package_model.dart';
import '../core/constants/app_constants.dart';

/// 算力套餐服务
class PaymentPackageService {
  static const String _baseUrl = AppConstants.exeFunctionUrl;

  /// 获取启用的套餐列表
  Future<List<PaymentPackageModel>> getActivePackages() async {
    try {
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'action': 'getActivePackages',
        }),
      ).timeout(AppConstants.connectTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['code'] == 0 && data['data'] != null) {
          // 云函数返回的数据结构是嵌套的，需要获取内层的data
          final responseData = data['data'];
          if (responseData['success'] == true && responseData['data'] != null) {
            final List<dynamic> packagesData = responseData['data'] as List<dynamic>;
            return packagesData
                .map((json) => PaymentPackageModel.fromJson(json))
                .toList();
          } else {
            throw Exception(responseData['message'] ?? '获取套餐列表失败');
          }
        } else {
          throw Exception(data['message'] ?? '获取套餐列表失败');
        }
      } else {
        throw Exception('网络请求失败: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('获取套餐列表失败: $e');
    }
  }

  /// 根据ID获取套餐详情
  Future<PaymentPackageModel?> getPackageDetail(String packageId) async {
    try {
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'action': 'getPackageDetail',
          'packageId': packageId,
        }),
      ).timeout(AppConstants.connectTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['code'] == 0 && data['data'] != null) {
          // 云函数返回的数据结构是嵌套的，需要获取内层的data
          final responseData = data['data'];
          if (responseData['success'] == true && responseData['data'] != null) {
            return PaymentPackageModel.fromJson(responseData['data']);
          } else {
            throw Exception(responseData['message'] ?? '获取套餐详情失败');
          }
        } else {
          throw Exception(data['message'] ?? '获取套餐详情失败');
        }
      } else {
        throw Exception('网络请求失败: ${response.statusCode}');
      }
    } catch (e) {
      return null;
    }
  }

  /// 创建购买订单
  Future<Map<String, dynamic>?> createPurchaseOrder({
    required String token,
    required String packageId,
    int quantity = 1,
    String paymentMethod = 'WECHAT', // 默认微信支付
  }) async {
    try {
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'action': 'createPurchaseOrder',
          'packageId': packageId,
          'quantity': quantity,
          'paymentMethod': paymentMethod,
        }),
      ).timeout(AppConstants.connectTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['code'] == 0 && data['data'] != null) {
          // 云函数返回的数据结构是嵌套的：{code: 0, data: {success: true, data: {...}}}
          final responseData = data['data'];
          if (responseData['success'] == true && responseData['data'] != null) {
            return responseData['data'];
          } else {
            throw Exception(responseData['message'] ?? '创建订单失败');
          }
        } else {
          throw Exception(data['message'] ?? '创建订单失败');
        }
      } else {
        throw Exception('网络请求失败: ${response.statusCode}');
      }
    } catch (e) {
      return null;
    }
  }

  /// 查询支付状态
  Future<Map<String, dynamic>?> queryPaymentStatus({
    required String token,
    required String orderId,
  }) async {
    try {
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'action': 'queryPaymentStatus',
          'orderId': orderId,
        }),
      ).timeout(AppConstants.connectTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['code'] == 0 && data['data'] != null) {
          // 云函数返回的数据结构是嵌套的
          final responseData = data['data'];
          if (responseData['success'] == true) {
            return responseData;
          } else {
            throw Exception(responseData['message'] ?? '查询支付状态失败');
          }
        } else {
          throw Exception(data['message'] ?? '查询支付状态失败');
        }
      } else {
        throw Exception('网络请求失败: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('查询支付状态失败: $e');
    }
  }

  /// 模拟支付
  Future<Map<String, dynamic>?> simulatePayment({
    required String token,
    required String orderId,
    required bool success,
  }) async {
    try {
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'action': 'simulatePayment',
          'orderId': orderId,
          'success': success,
        }),
      ).timeout(AppConstants.connectTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['code'] == 0) {
          return data['data'];
        } else {
          throw Exception(data['message'] ?? '模拟支付失败');
        }
      } else {
        throw Exception('网络请求失败: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('模拟支付失败: $e');
    }
  }
}
