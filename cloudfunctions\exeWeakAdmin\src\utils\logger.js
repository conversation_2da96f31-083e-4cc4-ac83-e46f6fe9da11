/**
 * 日志工具
 */

/**
 * 记录信息日志
 * @param {string} message 日志消息
 * @param {object} meta 元数据
 */
function info(message, meta = {}) {
  console.log(JSON.stringify({
    level: 'info',
    message,
    timestamp: new Date().toISOString(),
    ...meta
  }))
}

/**
 * 记录警告日志
 * @param {string} message 日志消息
 * @param {object} meta 元数据
 */
function warn(message, meta = {}) {
  console.warn(JSON.stringify({
    level: 'warn',
    message,
    timestamp: new Date().toISOString(),
    ...meta
  }))
}

/**
 * 记录错误日志
 * @param {string} message 日志消息
 * @param {object} meta 元数据
 */
function error(message, meta = {}) {
  console.error(JSON.stringify({
    level: 'error',
    message,
    timestamp: new Date().toISOString(),
    ...meta
  }))
}

/**
 * 记录调试日志
 * @param {string} message 日志消息
 * @param {object} meta 元数据
 */
function debug(message, meta = {}) {
  console.log(JSON.stringify({
    level: 'debug',
    message,
    timestamp: new Date().toISOString(),
    ...meta
  }))
}

module.exports = {
  info,
  warn,
  error,
  debug
}
