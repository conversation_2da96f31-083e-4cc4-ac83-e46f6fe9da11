import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:collection/collection.dart';
import '../models/agent_model.dart';
import '../services/agent_service.dart';
import 'auth_provider.dart';
import 'settings_provider.dart';

/// 智能体状态
enum AgentLoadingState {
  initial,
  loading,
  loaded,
  error,
}

/// 智能体状态数据
class AgentState {
  final AgentLoadingState loadingState;
  final List<AgentModel> agents;
  final String? error;
  final DateTime? lastUpdated;

  const AgentState({
    this.loadingState = AgentLoadingState.initial,
    this.agents = const [],
    this.error,
    this.lastUpdated,
  });

  AgentState copyWith({
    AgentLoadingState? loadingState,
    List<AgentModel>? agents,
    String? error,
    DateTime? lastUpdated,
  }) {
    return AgentState(
      loadingState: loadingState ?? this.loadingState,
      agents: agents ?? this.agents,
      error: error,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  bool get isLoading => loadingState == AgentLoadingState.loading;
  bool get isLoaded => loadingState == AgentLoadingState.loaded;
  bool get hasError => loadingState == AgentLoadingState.error;
  bool get isEmpty => agents.isEmpty;
  bool get isNotEmpty => agents.isNotEmpty;
}

/// 智能体状态通知器
class AgentNotifier extends StateNotifier<AgentState> {
  AgentNotifier(this._agentService) : super(const AgentState());

  final AgentService _agentService;

  /// 加载智能体列表
  Future<void> loadAgents(String token, {bool forceRefresh = false}) async {
    if (state.isLoading) return;

    state = state.copyWith(
      loadingState: AgentLoadingState.loading,
      error: null,
    );

    try {
      final agents = await _agentService.getAgents(
        token: token,
        forceRefresh: forceRefresh,
      );

      state = state.copyWith(
        loadingState: AgentLoadingState.loaded,
        agents: agents,
        lastUpdated: DateTime.now(),
      );
    } catch (e) {
      state = state.copyWith(
        loadingState: AgentLoadingState.error,
        error: e.toString(),
      );
    }
  }

  /// 刷新智能体列表
  Future<void> refreshAgents(String token) async {
    await loadAgents(token, forceRefresh: true);
  }

  /// 根据ID获取智能体
  AgentModel? getAgentById(String agentId) {
    try {
      return state.agents.firstWhere((agent) => agent.id == agentId);
    } catch (e) {
      return null;
    }
  }

  /// 根据类型获取智能体列表
  List<AgentModel> getAgentsByType(AgentType type) {
    return state.agents.where((agent) => agent.agentType == type).toList();
  }

  /// 搜索智能体
  List<AgentModel> searchAgents(String query) {
    if (query.isEmpty) return state.agents;

    final lowerQuery = query.toLowerCase();
    return state.agents.where((agent) {
      return agent.agentName.toLowerCase().contains(lowerQuery) ||
             agent.description.toLowerCase().contains(lowerQuery) ||
             agent.agentType.displayName.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// 获取推荐智能体
  List<AgentModel> getRecommendedAgents() {
    return state.agents.take(3).toList();
  }

  /// 清除缓存
  void clearCache() {
    _agentService.clearCache();
    state = const AgentState();
  }

  /// 重置状态
  void reset() {
    state = const AgentState();
  }
}

/// 智能体服务提供者
final agentServiceProvider = Provider<AgentService>((ref) {
  return AgentService();
});

/// 智能体状态提供者
final agentProvider = StateNotifierProvider<AgentNotifier, AgentState>((ref) {
  final agentService = ref.read(agentServiceProvider);
  return AgentNotifier(agentService);
});

/// 智能体列表提供者
final agentsProvider = Provider<List<AgentModel>>((ref) {
  return ref.watch(agentProvider).agents;
});

/// 智能体加载状态提供者
final agentLoadingStateProvider = Provider<AgentLoadingState>((ref) {
  return ref.watch(agentProvider).loadingState;
});

/// 智能体错误提供者
final agentErrorProvider = Provider<String?>((ref) {
  return ref.watch(agentProvider).error;
});

/// 按类型分组的智能体提供者
final agentsByTypeProvider = Provider<Map<AgentType, List<AgentModel>>>((ref) {
  final agents = ref.watch(agentsProvider);
  final Map<AgentType, List<AgentModel>> grouped = {};
  
  for (final agent in agents) {
    if (agent.isActive) {
      grouped.putIfAbsent(agent.agentType, () => []).add(agent);
    }
  }
  
  return grouped;
});

/// 推荐智能体提供者
final recommendedAgentsProvider = Provider<List<AgentModel>>((ref) {
  final agents = ref.watch(agentsProvider);
  return agents.take(3).toList();
});

/// 智能体搜索提供者
final agentSearchProvider = StateProvider<String>((ref) => '');

/// 搜索结果智能体提供者
final searchedAgentsProvider = Provider<List<AgentModel>>((ref) {
  final query = ref.watch(agentSearchProvider);
  final agentNotifier = ref.read(agentProvider.notifier);
  return agentNotifier.searchAgents(query);
});

/// 自动加载智能体提供者
final autoLoadAgentsProvider = Provider<void>((ref) {
  final authState = ref.watch(authProvider);
  final agentNotifier = ref.read(agentProvider.notifier);
  
  // 当用户登录时自动加载智能体
  if (authState.isAuthenticated && authState.token != null) {
    Future.microtask(() {
      agentNotifier.loadAgents(authState.token!);
    });
  }
});

/// 智能体是否已加载提供者
final agentsLoadedProvider = Provider<bool>((ref) {
  final agentState = ref.watch(agentProvider);
  return agentState.isLoaded && agentState.isNotEmpty;
});

/// 智能体加载中提供者
final agentsLoadingProvider = Provider<bool>((ref) {
  return ref.watch(agentProvider).isLoading;
});

/// 智能体加载失败提供者
final agentsErrorProvider = Provider<bool>((ref) {
  return ref.watch(agentProvider).hasError;
});

/// 默认智能体提供者
final defaultAgentProvider = Provider<AgentModel?>((ref) {
  final agents = ref.watch(agentsProvider);
  final settings = ref.watch(settingsProvider);

  // 只有在用户设置了默认智能体时才返回
  if (settings.defaultAgentId != null && agents.isNotEmpty) {
    // 查找用户设置的默认智能体
    final defaultAgent = agents.firstWhereOrNull((a) => a.id == settings.defaultAgentId);
    return defaultAgent;
  }

  // 如果用户没有设置默认智能体，返回 null
  return null;
});
