# 技术栈与构建系统

## 前端 (Flutter桌面应用)
- **框架**: Flutter 3.7.2+ Windows平台
- **语言**: Dart
- **状态管理**: Riverpod + Provider
- **路由**: go_router
- **HTTP客户端**: dio + http
- **本地存储**: Hive + flutter_secure_storage
- **UI组件**: Material 3设计系统
- **字体**: LXGWWenKai (霞鹜文楷) 中文字体

### 核心依赖
```yaml
# 状态管理与导航
riverpod: ^2.5.1
flutter_riverpod: ^2.5.1
go_router: ^14.2.7

# 网络与存储  
dio: ^5.4.0
hive: ^2.2.3
flutter_secure_storage: latest

# UI与媒体
flutter_markdown: ^0.7.3
image_picker: ^1.0.4
lucide_icons: ^0.257.0
```

## 后端服务

### 云函数 (Node.js 16.13)
- **平台**: 腾讯云开发
- **运行时**: Node.js 16.13
- **函数**: exeFunction (用户API), exeAdmin (管理API)
- **数据库**: 腾讯云文档型数据库
- **认证**: JWT配合刷新令牌

### Go代理服务
- **语言**: Go 1.21
- **框架**: Gin web框架
- **用途**: 安全的AI API代理和认证中间件
- **部署**: Linux二进制文件配合systemd服务

### Web管理界面
- **框架**: Vue 3 + Vite
- **UI库**: Element Plus
- **状态管理**: Pinia
- **构建工具**: Vite 4.4.5

## 常用构建命令

### Flutter桌面应用
```bash
# 安装依赖
flutter pub get

# 调试模式运行
flutter run -d windows

# 构建发布版本
flutter build windows --release
# 输出: build/windows/x64/runner/Release/numerology_ai_chat.exe

# 代码生成 (JSON序列化, Hive适配器)
flutter packages pub run build_runner build

# 清理构建
flutter clean && flutter pub get
```

### Go代理服务
```bash
# 安装依赖
go mod tidy

# 本地运行
go run ./cmd/server

# 构建Linux部署版本
GOOS=linux GOARCH=amd64 go build -o go_proxy_linux ./cmd/server

# 创建部署包
./package.sh
```

### Web管理界面
```bash
# 安装依赖
npm install

# 开发服务器
npm run dev

# 生产构建
npm run build
# 输出: dist/

# 预览生产构建
npm run preview
```

## 开发环境设置

### 前置要求
- Flutter SDK 3.7.2+
- Go 1.21+
- Node.js 16.13+
- Windows 10/11 用于桌面开发

### 环境变量
```bash
# Flutter
--dart-define=ENV=dev|prod

# Go服务 (.env文件)
SERVER_PORT=8080
CLOUDFUNC_BASE_URL=https://your-cloud-function-url
AES_SECRET_KEY=your-32-char-secret-key
```

## 代码生成
- **Flutter**: 使用build_runner进行JSON序列化和Hive适配器生成
- **图标**: flutter_launcher_icons用于应用图标生成
- **字体**: pubspec.yaml中的自定义字体配置

## 部署策略
- **前端**: Windows可执行文件(.exe)分发
- **Go服务**: Linux二进制文件配合systemd服务
- **云函数**: 通过腾讯云CLI部署
- **Web管理**: 静态文件通过CDN/Web服务器提供