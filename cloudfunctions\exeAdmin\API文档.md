# exeAdmin 云函数接口文档

## 概述
exeAdmin是面向管理员的云函数，提供用户管理、智能体管理、模型管理、页面配置管理等功能。

## 接口列表

### 1. 管理员登录 ✅
- **Action**: `adminLogin`
- **参数**:
  - `adminAccount` (string): 管理员账号
  - `adminPassword` (string): 管理员密码
- **返回**: 管理员信息和token
- **示例**:
```json
{
  "action": "adminLogin",
  "adminAccount": "admin",
  "adminPassword": "password"
}
```
- **响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "success": true,
    "message": "管理员登录成功",
    "data": {
      "adminInfo": {
        "adminId": "adea4666684d772802c99a0b5c7d4e6a",
        "adminAccount": "admin",
        "adminRole": "超级管理员"
      },
      "accessToken": "eyJhbGciOiJIUzI1NiIs...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
    }
  }
}
```

### 2. 用户管理

#### 2.1 获取用户列表 ✅
- **Action**: `listUsers`
- **鉴权**: 需要管理员token
- **参数**:
  - `token` (string): 管理员访问令牌
  - `page` (number, 可选): 页码，默认1
  - `pageSize` (number, 可选): 每页数量，默认20
- **示例**:
```json
{
  "action": "listUsers",
  "token": "eyJhbGciOiJIUzI1NiIs..."
}
```

#### 2.2 创建用户 ✅
- **Action**: `createUser`
- **鉴权**: 需要管理员token
- **参数**:
  - `token` (string): 管理员访问令牌
  - `username` (string): 用户名
  - `password` (string): 密码
  - `email` (string, 可选): 邮箱
  - `phone` (string, 可选): 手机号
  - `initialQuota` (number, 可选): 初始额度，默认10
  - `reason` (string, 可选): 创建原因
- **示例**:
```json
{
  "action": "createUser",
  "token": "eyJhbGciOiJIUzI1NiIs...",
  "username": "newuser",
  "password": "123456",
  "email": "<EMAIL>",
  "initialQuota": 50
}
```

#### 2.3 更新用户会员状态
- **Action**: `updateUserMembership`
- **鉴权**: 需要管理员token
- **参数**: 
  - `token` (string): 管理员访问令牌
  - `userId` (string): 用户ID
  - `membershipType` (string): 会员类型
  - `expiresAt` (string): 过期时间

#### 2.4 更新用户额度
- **Action**: `updateUserQuota`
- **鉴权**: 需要管理员token
- **参数**: 
  - `token` (string): 管理员访问令牌
  - `userId` (string): 用户ID
  - `addedCount` (number): 增加的额度数量

#### 2.5 更新用户状态
- **Action**: `updateUserStatus`
- **鉴权**: 需要管理员token
- **参数**: 
  - `token` (string): 管理员访问令牌
  - `userId` (string): 用户ID
  - `status` (string): 用户状态

### 3. 智能体管理

#### 3.1 获取智能体列表 ✅
- **Action**: `listAgents`
- **鉴权**: 需要管理员token
- **参数**:
  - `token` (string): 管理员访问令牌
- **示例**:
```json
{
  "action": "listAgents",
  "token": "eyJhbGciOiJIUzI1NiIs..."
}
```
- **响应示例**: 返回3个智能体，包含完整的管理员视角数据（含提示词）

#### 3.2 创建智能体 ✅
- **Action**: `createAgent`
- **鉴权**: 需要管理员token
- **参数**:
  - `token` (string): 管理员访问令牌
  - `agentName` (string): 智能体名称
  - `agentDisplayName` (string): 显示名称
  - `systemPrompt` (string): 系统提示词
  - `agentType` (string, 可选): 智能体类型，默认"自定义"
  - `description` (string, 可选): 描述
  - `isActive` (boolean, 可选): 是否激活，默认true
  - `sortOrder` (number, 可选): 排序，默认0
- **示例**:
```json
{
  "action": "createAgent",
  "token": "eyJhbGciOiJIUzI1NiIs...",
  "agentName": "周易占卜大师",
  "agentDisplayName": "周易占卜大师",
  "systemPrompt": "你是一位精通周易占卜的大师...",
  "agentType": "占卜",
  "description": "专业周易占卜"
}
```

#### 3.3 更新智能体 ✅
- **Action**: `updateAgent`
- **鉴权**: 需要管理员token
- **参数**:
  - `token` (string): 管理员访问令牌
  - `agentId` (string): 智能体ID
  - 其他可更新字段（同创建智能体）

#### 3.4 删除智能体 ✅
- **Action**: `deleteAgent`
- **鉴权**: 需要管理员token
- **参数**:
  - `token` (string): 管理员访问令牌
  - `agentId` (string): 智能体ID

### 4. 模型管理

#### 4.1 获取模型列表 ✅
- **Action**: `listModels`
- **鉴权**: 需要管理员token
- **参数**:
  - `token` (string): 管理员访问令牌
  - `page` (number, 可选): 页码，默认1
  - `pageSize` (number, 可选): 每页数量，默认20
  - `keyword` (string, 可选): 搜索关键词
  - `isActive` (boolean, 可选): 是否激活
- **示例**:
```json
{
  "action": "listModels",
  "token": "eyJhbGciOiJIUzI1NiIs..."
}
```

#### 4.2 创建模型 ✅
- **Action**: `createModel`
- **鉴权**: 需要管理员token
- **参数**:
  - `token` (string): 管理员访问令牌
  - `modelName` (string): 模型名称
  - `modelApiKey` (string): API密钥
  - `modelApiUrl` (string): API地址
  - `modelDisplayName` (string): 显示名称
  - `maxTokens` (number, 可选): 最大token数，默认4000
  - `temperature` (number, 可选): 温度参数，默认0.7
  - `description` (string, 可选): 描述
  - `isActive` (boolean, 可选): 是否激活，默认true
  - `sortOrder` (number, 可选): 排序，默认0
- **示例**:
```json
{
  "action": "createModel",
  "token": "eyJhbGciOiJIUzI1NiIs...",
  "modelName": "claude-3-sonnet",
  "modelApiKey": "sk-test-api-key",
  "modelApiUrl": "https://api.anthropic.com/v1/messages",
  "modelDisplayName": "Claude 3 Sonnet"
}
```

#### 4.3 更新模型 ✅
- **Action**: `updateModel`
- **鉴权**: 需要管理员token
- **参数**:
  - `token` (string): 管理员访问令牌
  - `modelId` (string): 模型ID
  - 其他可更新字段（同创建模型）

#### 4.4 删除模型 ✅
- **Action**: `deleteModel`
- **鉴权**: 需要管理员token
- **参数**:
  - `token` (string): 管理员访问令牌
  - `modelId` (string): 模型ID

### 5. 页面配置管理

#### 5.1 获取页面列表 ✅
- **Action**: `listPages`
- **鉴权**: 需要管理员token
- **参数**:
  - `token` (string): 管理员访问令牌
  - `page` (number, 可选): 页码，默认1
  - `pageSize` (number, 可选): 每页数量，默认20
  - `pageType` (string, 可选): 页面类型
  - `isActive` (boolean, 可选): 是否激活

#### 5.2 创建页面 ✅
- **Action**: `createPage`
- **鉴权**: 需要管理员token
- **参数**:
  - `token` (string): 管理员访问令牌
  - `pageTitle` (string): 页面标题
  - `pageContent` (string): 页面内容（Markdown格式）
  - `pageType` (string): 页面类型
  - `slug` (string): URL标识
  - `metaDescription` (string, 可选): SEO描述
  - `keywords` (array, 可选): 关键词数组
  - `isActive` (boolean, 可选): 是否激活，默认true
  - `sortOrder` (number, 可选): 排序，默认0

#### 5.3 更新页面 ✅
- **Action**: `updatePage`
- **鉴权**: 需要管理员token
- **参数**:
  - `token` (string): 管理员访问令牌
  - `pageId` (string): 页面ID
  - 其他可更新字段（同创建页面）

#### 5.4 删除页面 ✅
- **Action**: `deletePage`
- **鉴权**: 需要管理员token
- **参数**:
  - `token` (string): 管理员访问令牌
  - `pageId` (string): 页面ID

### 6. 日志和统计

#### 6.1 查看日志 ✅
- **Action**: `viewLogs`
- **鉴权**: 需要管理员token
- **参数**:
  - `token` (string): 管理员访问令牌
  - `page` (number, 可选): 页码，默认1
  - `pageSize` (number, 可选): 每页数量，默认50
  - `logType` (string, 可选): 日志类型
  - `userId` (string, 可选): 用户ID
  - `startTime` (string, 可选): 开始时间
  - `endTime` (string, 可选): 结束时间
  - `keyword` (string, 可选): 搜索关键词

#### 6.2 统计概览 ✅
- **Action**: `stats`
- **鉴权**: 需要管理员token
- **参数**:
  - `token` (string): 管理员访问令牌
- **返回**: 包含用户、模型、智能体、页面统计，使用趋势等完整数据

## 响应格式
所有接口统一返回格式：
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "success": true,
    "message": "操作成功",
    "data": {},
    "requestId": ""
  }
}
```

## 鉴权说明
1. 除了`adminLogin`外，所有接口都需要管理员token
2. Token通过`Authorization`头或`admin-token`头传递
3. Token有效期为2小时
4. 不同操作需要对应的权限

## 注意事项
1. 管理员账号状态必须为激活状态（isActive: true）
2. API密钥等敏感信息会自动加密存储
3. 所有操作都会记录操作日志
