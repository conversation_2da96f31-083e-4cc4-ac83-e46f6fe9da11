
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>命理AI聊天项目完整架构图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            overflow-x: hidden;
        }

        .container {
            max-width: 100%;
            margin: 0 auto;
        }

        .diagram-container {
            position: relative;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .mermaid {
            width: 100%;
            height: 80vh;
            min-height: 600px;
            cursor: grab;
            transform-origin: 0 0;
            transition: transform 0.1s ease-out;
        }

        .mermaid:active {
            cursor: grabbing;
        }

        .controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1000;
            display: flex;
            gap: 5px;
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
            user-select: none;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .zoom-info {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }

        .legend {
            margin-top: 30px;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .legend h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .legend-item {
            display: inline-block;
            margin: 5px 15px;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .flutter { background-color: #e0f7fa; color: #00796b; }
        .go { background-color: #e8eaf6; color: #303f9f; }
        .nodejs { background-color: #fbe9e7; color: #d84315; }
        .db { background-color: #f9fbe7; color: #f9a825; }
        .external { background-color: #f1f8e9; color: #558b2f; }
        .admin { background-color: #fce4ec; color: #c2185b; }

        .instructions {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .instructions h3 {
            margin-top: 0;
            color: #1976d2;
        }

        .instructions ul {
            margin-bottom: 0;
        }

        /* 全屏模式样式 */
        .fullscreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 9999;
            background: white;
        }

        .fullscreen .mermaid {
            height: 100vh;
        }

        /* 导出菜单样式 */
        .export-menu {
            position: relative;
            display: inline-block;
        }

        .export-dropdown {
            display: none;
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1001;
            min-width: 200px;
            padding: 8px 0;
        }

        .export-dropdown.show {
            display: block;
        }

        .export-option {
            display: block;
            width: 100%;
            padding: 8px 16px;
            border: none;
            background: none;
            text-align: left;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }

        .export-option:hover {
            background-color: #f5f5f5;
        }

        .export-option-group {
            border-bottom: 1px solid #eee;
            margin-bottom: 4px;
            padding-bottom: 4px;
        }

        .export-option-group:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .export-option-title {
            font-weight: bold;
            color: #666;
            font-size: 12px;
            padding: 4px 16px;
            text-transform: uppercase;
        }

        /* 加载指示器 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        }

        .loading-content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>命理AI聊天项目完整架构图</h1>

        <div class="instructions">
            <h3>🎮 交互操作指南</h3>
            <ul>
                <li><strong>缩放：</strong>使用鼠标滚轮或点击 +/- 按钮</li>
                <li><strong>平移：</strong>按住鼠标左键拖拽图表</li>
                <li><strong>重置：</strong>点击"重置"按钮恢复初始视图</li>
                <li><strong>全屏：</strong>点击"全屏"按钮进入全屏模式，按ESC退出</li>
            </ul>
        </div>

        <div class="diagram-container">
            <div class="controls">
                <button class="control-btn" onclick="zoomIn()">🔍+</button>
                <button class="control-btn" onclick="zoomOut()">🔍-</button>
                <button class="control-btn" onclick="resetZoom()">重置</button>
                <button class="control-btn" onclick="toggleFullscreen()">全屏</button>
                <div class="export-menu">
                    <button class="control-btn" onclick="toggleExportMenu()">📥导出</button>
                    <div class="export-dropdown" id="exportDropdown">
                        <div class="export-option-group">
                            <div class="export-option-title">PNG 图片</div>
                            <button class="export-option" onclick="exportImage('png', 1)">标准分辨率 (1x)</button>
                            <button class="export-option" onclick="exportImage('png', 2)">高分辨率 (2x)</button>
                            <button class="export-option" onclick="exportImage('png', 3)">超高分辨率 (3x)</button>
                        </div>
                        <div class="export-option-group">
                            <div class="export-option-title">SVG 矢量图</div>
                            <button class="export-option" onclick="exportImage('svg', 1)">SVG 格式</button>
                        </div>
                        <div class="export-option-group">
                            <div class="export-option-title">PDF 文档</div>
                            <button class="export-option" onclick="exportImage('pdf', 1)">PDF 格式</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="zoom-info" id="zoomInfo">缩放: 100%</div>
            <div class="mermaid" id="mermaidDiagram">
flowchart TD
    %% Flutter客户端应用层
    subgraph FlutterApp ["Flutter客户端应用 (numerology_ai_chat_new)"]
        subgraph Screens ["页面层 (Screens)"]
            splash["`启动页面
            SplashScreen
            • 版本检查
            • 存储初始化
            • 认证状态检查`"]
            login["`登录页面
            LoginScreen
            • 用户登录
            • 记住密码
            • 跳转注册`"]
            register["`注册页面
            RegisterScreen
            • 用户注册
            • 参数验证
            • 自动跳转登录`"]
            home["`主页面
            HomeScreen
            • 底部导航
            • 页面容器
            • 路由管理`"]
            chat["`聊天页面
            ChatScreen
            • 对话界面
            • 流式输出
            • 图片选择
            • 消息存储`"]
            bazi["`八字排盘页面
            BaziScreen
            • 地址选择
            • 真太阳时
            • 子时规则
            • 历史记录`"]
            purchase["`购买套餐页面
            PurchaseScreen
            • 套餐选择
            • 支付流程
            • 订单管理`"]
            profile["`个人中心页面
            ProfileScreen
            • 用户信息
            • 购买历史
            • 消费历史`"]
            settings["`设置页面
            SettingsScreen
            • 默认配置
            • 主题设置
            • 系统设置`"]
        end

        subgraph Providers ["状态管理层 (Providers)"]
            auth_provider["`认证状态
            AuthProvider
            • 登录状态
            • Token管理
            • 自动续签`"]
            chat_provider["`聊天状态
            ChatProvider
            • 对话管理
            • 消息流
            • 历史记录`"]
            agent_provider["`智能体状态
            AgentProvider
            • 智能体列表
            • 选择状态
            • 缓存管理`"]
            model_provider["`模型状态
            ModelProvider
            • 模型列表
            • 选择状态
            • 配置管理`"]
            version_provider["`版本状态
            VersionProvider
            • 版本检查
            • 更新提示
            • 强制更新`"]
        end

        subgraph Services ["服务层 (Services)"]
            auth_service["`认证服务
            AuthService
            • login()
            • register()
            • refreshToken()`"]
            cloud_service["`云函数服务
            CloudFunctionService
            • callFunction()
            • handleResponse()
            • errorHandling()`"]
            go_proxy_service["`Go代理服务
            GoProxyService
            • sendChatMessage()
            • streamResponse()
            • tokenAuth()`"]
            storage_service["`存储服务
            StorageService
            • saveConversation()
            • loadHistory()
            • cacheManagement()`"]
            bazi_service["`八字服务
            BaziService
            • calculateBazi()
            • solarTimeCorrection()
            • locationService()`"]
        end

        subgraph Models ["数据模型层 (Models)"]
            user_model["`用户模型
            UserModel
            • username
            • availableCount
            • membership`"]
            chat_model["`聊天模型
            ChatMessage
            • content
            • role
            • timestamp
            • images`"]
            agent_model["`智能体模型
            AgentModel
            • agentName
            • agentPrompt
            • pricingTierId`"]
            model_model["`AI模型
            AIModel
            • modelName
            • modelLevel
            • maxTokens`"]
        end
    end

    %% Go代理服务层
    subgraph GoProxy ["Go代理服务 (go_proxy)"]
        subgraph MainCmd ["主程序 (cmd/server)"]
            main_go["`主程序入口
            main.go
            • 服务启动
            • 配置加载
            • 路由初始化`"]
        end

        subgraph APILayer ["API层 (internal/api)"]
            router_go["`路由配置
            router.go
            • NewRouter()
            • CORS中间件
            • 路由分组`"]
            chat_handler["`聊天处理器
            chat_handler.go
            • HandleChatCompletion()
            • 流式响应
            • 错误处理`"]
            health_handler["`健康检查
            health_handler.go
            • HealthCheck()
            • 服务状态
            • 监控端点`"]
        end

        subgraph MiddlewareLayer ["中间件层 (internal/middleware)"]
            auth_middleware["`认证中间件
            auth_middleware.go
            • AuthMiddleware()
            • Token验证
            • 用户信息注入`"]
        end

        subgraph ProxyLayer ["代理层 (internal/proxy)"]
            llm_proxy["`LLM代理
            llm_proxy.go
            • ProxyRequest()
            • buildMessages()
            • forwardRequest()
            • streamResponse()`"]
        end

        subgraph ServiceLayer ["服务层 (internal/services)"]
            cloudfunction_client["`云函数客户端
            cloudfunction/client.go
            • GetUserInfo()
            • GetAgent()
            • GetModel()`"]
            database_client["`数据库客户端
            database/client.go
            • 数据库连接
            • 查询封装
            • 连接池管理`"]
        end

        subgraph ConfigLayer ["配置层 (internal/config)"]
            config_go["`配置管理
            config.go
            • Load()
            • 环境变量
            • 默认配置`"]
        end
    end

    %% 云函数服务层
    subgraph CloudFunctions ["云函数服务 (cloudfunctions)"]
        subgraph ExeFunction ["用户侧云函数 (exeFunction)"]
            exe_index["`路由分发
            index.js
            • 路由分发
            • 鉴权中间件
            • 错误处理`"]

            subgraph Handlers ["处理器 (src/handlers)"]
                auth_handler["`认证处理器
                auth.js
                • login()
                • register()
                • refresh()`"]
                agents_handler["`智能体处理器
                agents.js
                • getAgents()
                • getFullAgents()
                • 缓存管理`"]
                models_handler["`模型处理器
                models.js
                • getModels()
                • getFullModels()
                • 配置解密`"]
                user_info_handler["`用户信息处理器
                user_info.js
                • getUserInfo()
                • updateUsage()
                • 算力扣除`"]
                payment_handler["`支付处理器
                payment_packages.js
                • createPurchaseOrder()
                • simulatePayment()
                • processPaymentSuccess()`"]
                bazi_handler["`八字处理器
                bazi.js
                • analyze()
                • 真太阳时计算
                • lunar-javascript`"]
                version_handler["`版本处理器
                version.js
                • checkVersion()
                • 版本比较
                • 更新策略`"]
            end

            subgraph Middleware ["中间件 (src/middleware)"]
                exe_auth_middleware["`鉴权中间件
                auth.js
                • authMiddleware()
                • verifyToken()
                • 用户状态检查`"]
                error_handler["`错误处理器
                error_handler.js
                • errorHandler()
                • BusinessError
                • 统一响应格式`"]
            end

            subgraph Utils ["工具类 (src/utils)"]
                db_utils["`数据库工具
                db.js
                • 集合操作
                • 加密解密
                • 查询封装`"]
                validate_utils["`参数校验
                validate.js
                • Joi校验
                • 业务规则
                • 错误消息`"]
                logger_utils["`日志工具
                logger.js
                • 日志记录
                • 错误追踪
                • 性能监控`"]
            end
        end

        subgraph ExeAdmin ["管理侧云函数 (exeAdmin)"]
            admin_index["`管理路由分发
            index.js
            • 管理员路由
            • 权限验证
            • 操作日志`"]

            subgraph AdminHandlers ["管理处理器 (src/handlers)"]
                admin_auth_handler["`管理员认证
                admin_auth.js
                • adminLogin()
                • verifyAdminToken()
                • 权限管理`"]
                user_manage_handler["`用户管理
                user_management.js
                • getUserList()
                • updateUserQuota()
                • 用户状态管理`"]
                agent_manage_handler["`智能体管理
                agent_manage.js
                • createAgent()
                • updateAgent()
                • deleteAgent()`"]
                model_manage_handler["`模型管理
                model_management.js
                • createModel()
                • updateModel()
                • 密钥加密`"]
                package_manage_handler["`套餐管理
                package_management.js
                • createPackage()
                • updatePackage()
                • 价格配置`"]
                order_manage_handler["`订单管理
                order_management.js
                • getOrderList()
                • orderStatistics()
                • 退款处理`"]
            end
        end

        subgraph PaymentCallback ["支付回调云函数 (exePaymentCallback)"]
            payment_callback["`支付回调处理
            index.js
            • 富友支付回调
            • 签名验证
            • 订单状态更新
            • 算力充值`"]
        end
    end

    %% 管理后台应用
    subgraph AdminApp ["管理后台应用 (numerology_ai_chat_new_admin)"]
        subgraph AdminScreens ["管理页面 (Screens)"]
            admin_login["`管理员登录
            LoginScreen
            • 管理员认证
            • 权限验证`"]
            dashboard["`仪表板
            DashboardScreen
            • 数据统计
            • 系统概览`"]
            user_management["`用户管理
            UserManagementScreen
            • 用户列表
            • 额度管理
            • 状态控制`"]
            agent_management["`智能体管理
            AgentManagementScreen
            • 智能体配置
            • 提示词编辑
            • 档次关联`"]
            model_management["`模型管理
            ModelManagementScreen
            • 模型配置
            • API密钥
            • 参数设置`"]
            package_management["`套餐管理
            PackageManagementScreen
            • 套餐配置
            • 价格设置
            • 状态管理`"]
            order_management["`订单管理
            OrderManagementScreen
            • 订单查询
            • 状态跟踪
            • 统计分析`"]
        end

        subgraph AdminServices ["管理服务 (Services)"]
            admin_auth_service["`管理认证服务
            • 管理员登录
            • Token管理
            • 权限验证`"]
            admin_api_service["`管理API服务
            • 云函数调用
            • 数据管理
            • 批量操作`"]
        end
    end

    %% 数据库层
    subgraph Database ["腾讯云开发数据库 (MongoDB)"]
        subgraph CoreTables ["核心业务表"]
            db_users["`用户表 (exe_users)
            • _id, username, password
            • availableCount, membership
            • refreshToken, createdAt
            • 索引: username(唯一)`"]
            db_agents["`智能体表 (exe_agents)
            • agentName, agentPrompt
            • agentType, pricingTierId
            • isActive, sortOrder
            • 索引: isActive, sortOrder`"]
            db_models["`模型表 (exe_models)
            • modelName, modelApiKey
            • modelLevel, maxTokens
            • temperature, isActive
            • 索引: isActive, modelLevel`"]
            db_admins["`管理员表 (exe_admins)
            • username, password
            • role, permissions
            • lastLoginAt, isActive
            • 索引: username(唯一)`"]
        end

        subgraph ConfigTables ["业务配置表"]
            db_pricing_tiers["`算力档次表 (exe_pricing_tiers)
            • tierName, tierDescription
            • basicModelCost, advancedModelCost
            • isActive, sortOrder
            • 索引: isActive, sortOrder`"]
            db_payment_packages["`套餐表 (exe_payment_packages)
            • packageName, price
            • quotaCount, description
            • isActive, sortOrder
            • 索引: isActive, sortOrder`"]
            db_pages["`页面配置表 (exe_pages)
            • pageTitle, pageContent
            • pageType, slug
            • isActive, sortOrder
            • 索引: pageType, slug`"]
            db_system_config["`系统配置表 (exe_system_config)
            • configKey, configValue
            • configType, category
            • isActive, description
            • 索引: configKey(唯一)`"]
        end

        subgraph TransactionTables ["交易记录表"]
            db_purchase_orders["`购买订单表 (exe_purchase_orders)
            • userId, orderNo
            • packageName, orderAmount
            • status, payTime
            • 索引: userId, orderNo(唯一)`"]
            db_payment_logs["`支付日志表 (exe_payment_logs)
            • orderNo, paymentMethod
            • amount, status
            • transactionId, payTime
            • 索引: orderNo, transactionId`"]
            db_usage_history["`消费历史表 (exe_usage_history)
            • userId, agentId, modelId
            • powerCost, balanceBefore
            • balanceAfter, consumeTime
            • 索引: userId, consumeTime`"]
        end

        subgraph VersionTables ["版本管理表"]
            db_app_versions["`版本表 (exe_app_versions)
            • versionNumber, releaseNotes
            • isForceUpdate, isAvailable
            • publishedAt, downloadUrl
            • 索引: versionNumber(唯一)`"]
        end
    end

    %% 外部服务层
    subgraph ExternalServices ["外部服务与API"]
        subgraph LLMAPIs ["大语言模型API"]
            api_deepseek["`DeepSeek API
            • 聊天完成
            • 流式响应
            • Token计费`"]
            api_gemini["`Gemini API
            • 多模态支持
            • 图像理解
            • 文本生成`"]
            api_other_llm["`其他LLM API
            • 可扩展支持
            • 统一接口
            • 负载均衡`"]
        end

        subgraph PaymentServices ["支付服务"]
            api_fuiou["`富友支付API
            • 统一下单
            • 支付回调
            • 签名验证
            • 微信/支付宝`"]
        end

        subgraph ThirdPartyServices ["第三方服务"]
            api_address["`地址服务API
            • 省市区数据
            • 地理坐标
            • 时区信息`"]
            api_lunar["`农历服务
            lunar-javascript
            • 八字计算
            • 真太阳时
            • 节气查询`"]
        end
    end

    %% === 数据流连接关系 ===

    %% Flutter客户端 -> 服务层连接
    splash --> auth_service
    login --> auth_service
    register --> auth_service
    chat --> go_proxy_service
    chat --> storage_service
    bazi --> bazi_service
    bazi --> cloud_service
    purchase --> cloud_service
    profile --> cloud_service

    %% 状态管理连接
    auth_provider --> auth_service
    chat_provider --> go_proxy_service
    chat_provider --> storage_service
    agent_provider --> cloud_service
    model_provider --> cloud_service
    version_provider --> cloud_service

    %% Flutter服务层 -> 后端连接
    auth_service --> exe_index
    cloud_service --> exe_index
    go_proxy_service --> router_go
    bazi_service --> exe_index

    %% Go代理服务内部连接
    router_go --> auth_middleware
    auth_middleware --> chat_handler
    chat_handler --> llm_proxy
    llm_proxy --> cloudfunction_client
    llm_proxy --> database_client

    %% Go代理 -> 云函数连接
    cloudfunction_client --> exe_index

    %% Go代理 -> 外部API连接
    llm_proxy --> api_deepseek
    llm_proxy --> api_gemini
    llm_proxy --> api_other_llm

    %% 云函数内部连接
    exe_index --> exe_auth_middleware
    exe_auth_middleware --> auth_handler
    exe_auth_middleware --> agents_handler
    exe_auth_middleware --> models_handler
    exe_auth_middleware --> user_info_handler
    exe_auth_middleware --> payment_handler
    exe_index --> bazi_handler
    exe_index --> version_handler

    %% 云函数 -> 数据库连接
    auth_handler --> db_users
    agents_handler --> db_agents
    models_handler --> db_models
    user_info_handler --> db_users
    user_info_handler --> db_usage_history
    payment_handler --> db_purchase_orders
    payment_handler --> db_payment_packages
    payment_handler --> db_users
    bazi_handler --> api_lunar
    version_handler --> db_app_versions

    %% 管理后台连接
    admin_login --> admin_auth_service
    dashboard --> admin_api_service
    user_management --> admin_api_service
    agent_management --> admin_api_service
    model_management --> admin_api_service
    package_management --> admin_api_service
    order_management --> admin_api_service

    %% 管理后台 -> 云函数连接
    admin_auth_service --> admin_index
    admin_api_service --> admin_index

    %% 管理云函数内部连接
    admin_index --> admin_auth_handler
    admin_index --> user_manage_handler
    admin_index --> agent_manage_handler
    admin_index --> model_manage_handler
    admin_index --> package_manage_handler
    admin_index --> order_manage_handler

    %% 管理云函数 -> 数据库连接
    admin_auth_handler --> db_admins
    user_manage_handler --> db_users
    agent_manage_handler --> db_agents
    model_manage_handler --> db_models
    package_manage_handler --> db_payment_packages
    order_manage_handler --> db_purchase_orders

    %% 支付回调连接
    api_fuiou --> payment_callback
    payment_callback --> exe_index
    payment_handler --> api_fuiou

    %% === 样式定义 ===
    classDef flutter fill:#e0f7fa,stroke:#00796b,stroke-width:2px,color:#00796b;
    classDef go fill:#e8eaf6,stroke:#303f9f,stroke-width:2px,color:#303f9f;
    classDef nodejs fill:#fbe9e7,stroke:#d84315,stroke-width:2px,color:#d84315;
    classDef db fill:#f9fbe7,stroke:#f9a825,stroke-width:2px,color:#f9a825;
    classDef external fill:#f1f8e9,stroke:#558b2f,stroke-width:2px,color:#558b2f;
    classDef admin fill:#fce4ec,stroke:#c2185b,stroke-width:2px,color:#c2185b;

    %% Flutter客户端应用样式
    class splash,login,register,home,chat,bazi,purchase,profile,settings flutter;
    class auth_provider,chat_provider,agent_provider,model_provider,version_provider flutter;
    class auth_service,cloud_service,go_proxy_service,storage_service,bazi_service flutter;
    class user_model,chat_model,agent_model,model_model flutter;

    %% Go代理服务样式
    class main_go,router_go,chat_handler,health_handler go;
    class auth_middleware,llm_proxy go;
    class cloudfunction_client,database_client,config_go go;

    %% 云函数服务样式
    class exe_index,auth_handler,agents_handler,models_handler nodejs;
    class user_info_handler,payment_handler,bazi_handler,version_handler nodejs;
    class exe_auth_middleware,error_handler nodejs;
    class db_utils,validate_utils,logger_utils nodejs;
    class admin_index,admin_auth_handler,user_manage_handler nodejs;
    class agent_manage_handler,model_manage_handler,package_manage_handler,order_manage_handler nodejs;
    class payment_callback nodejs;

    %% 管理后台样式
    class admin_login,dashboard,user_management,agent_management admin;
    class model_management,package_management,order_management admin;
    class admin_auth_service,admin_api_service admin;

    %% 数据库样式
    class db_users,db_agents,db_models,db_admins db;
    class db_pricing_tiers,db_payment_packages,db_pages,db_system_config db;
    class db_purchase_orders,db_payment_logs,db_usage_history,db_app_versions db;

    %% 外部服务样式
    class api_deepseek,api_gemini,api_other_llm external;
    class api_fuiou,api_address,api_lunar external;
            </div>
        </div>

        <div class="legend">
            <h3>图例说明</h3>
            <div class="legend-item flutter">Flutter客户端</div>
            <div class="legend-item go">Go代理服务</div>
            <div class="legend-item nodejs">Node.js云函数</div>
            <div class="legend-item admin">管理后台</div>
            <div class="legend-item db">数据库</div>
            <div class="legend-item external">外部服务</div>

        <h3>项目特点</h3>
        <ul>
            <li><strong>三层架构</strong>：Flutter客户端 + Go代理服务 + Node.js云函数</li>
            <li><strong>敏感数据保护</strong>：API密钥等敏感信息仅在云端处理，不下发到客户端</li>
            <li><strong>流式响应</strong>：支持大语言模型的实时流式输出</li>
            <li><strong>算力计费</strong>：基于智能体档次和模型等级的精确计费系统</li>
            <li><strong>完整支付</strong>：集成富友支付，支持微信/支付宝</li>
            <li><strong>版本管理</strong>：支持强制更新和可选更新</li>
            <li><strong>八字排盘</strong>：集成lunar-javascript，支持真太阳时计算</li>
            <li><strong>管理后台</strong>：独立的管理系统，支持用户、智能体、模型等全面管理</li>
            <li><strong>数据持久化</strong>：聊天记录本地存储，支持图片消息</li>
            <li><strong>状态管理</strong>：使用Riverpod进行响应式状态管理</li>
        </ul>

        <h3>技术栈</h3>
        <ul>
            <li><strong>前端</strong>：Flutter 3.7.2, Riverpod, Go Router, Hive</li>
            <li><strong>后端</strong>：Go 1.19+, Gin, Node.js 16.13</li>
            <li><strong>数据库</strong>：腾讯云开发MongoDB</li>
            <li><strong>部署</strong>：腾讯云函数 + 云开发</li>
            <li><strong>支付</strong>：富友支付API</li>
            <li><strong>AI模型</strong>：DeepSeek, Gemini等多模型支持</li>
        </ul>
    </div>

    <!-- 加载指示器 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <div>正在生成图片，请稍候...</div>
        </div>
    </div>

    <script src="https://unpkg.com/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script>
        // 缩放和平移变量
        let scale = 1;
        let translateX = 0;
        let translateY = 0;
        let isDragging = false;
        let lastX = 0;
        let lastY = 0;

        // 初始化Mermaid
        document.addEventListener('DOMContentLoaded', function() {
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default',
                flowchart: {
                    useMaxWidth: false,
                    htmlLabels: true,
                    curve: 'basis'
                },
                securityLevel: 'loose',
                fontFamily: 'Microsoft YaHei, sans-serif'
            });

            // 添加事件监听器
            const diagram = document.getElementById('mermaidDiagram');

            // 鼠标滚轮缩放
            diagram.addEventListener('wheel', function(e) {
                e.preventDefault();
                const delta = e.deltaY > 0 ? 0.9 : 1.1;
                const rect = diagram.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                // 计算缩放中心点
                const newScale = Math.max(0.1, Math.min(5, scale * delta));
                const scaleChange = newScale / scale;

                translateX = x - (x - translateX) * scaleChange;
                translateY = y - (y - translateY) * scaleChange;
                scale = newScale;

                updateTransform();
            });

            // 鼠标拖拽
            diagram.addEventListener('mousedown', function(e) {
                isDragging = true;
                lastX = e.clientX;
                lastY = e.clientY;
                diagram.style.cursor = 'grabbing';
            });

            document.addEventListener('mousemove', function(e) {
                if (!isDragging) return;

                const deltaX = e.clientX - lastX;
                const deltaY = e.clientY - lastY;

                translateX += deltaX;
                translateY += deltaY;

                lastX = e.clientX;
                lastY = e.clientY;

                updateTransform();
            });

            document.addEventListener('mouseup', function() {
                isDragging = false;
                document.getElementById('mermaidDiagram').style.cursor = 'grab';
            });

            // 全屏模式ESC键退出
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    exitFullscreen();
                }
            });
        });

        // 更新变换
        function updateTransform() {
            const diagram = document.getElementById('mermaidDiagram');
            diagram.style.transform = `translate(${translateX}px, ${translateY}px) scale(${scale})`;
            document.getElementById('zoomInfo').textContent = `缩放: ${Math.round(scale * 100)}%`;
        }

        // 放大
        function zoomIn() {
            scale = Math.min(5, scale * 1.2);
            updateTransform();
        }

        // 缩小
        function zoomOut() {
            scale = Math.max(0.1, scale / 1.2);
            updateTransform();
        }

        // 重置缩放
        function resetZoom() {
            scale = 1;
            translateX = 0;
            translateY = 0;
            updateTransform();
        }

        // 切换全屏
        function toggleFullscreen() {
            const container = document.querySelector('.diagram-container');
            if (!container.classList.contains('fullscreen')) {
                container.classList.add('fullscreen');
            } else {
                exitFullscreen();
            }
        }

        // 退出全屏
        function exitFullscreen() {
            const container = document.querySelector('.diagram-container');
            container.classList.remove('fullscreen');
        }

        // 切换导出菜单
        function toggleExportMenu() {
            const dropdown = document.getElementById('exportDropdown');
            dropdown.classList.toggle('show');
        }

        // 点击外部关闭导出菜单
        document.addEventListener('click', function(e) {
            const exportMenu = document.querySelector('.export-menu');
            if (!exportMenu.contains(e.target)) {
                document.getElementById('exportDropdown').classList.remove('show');
            }
        });

        // 显示加载指示器
        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }

        // 隐藏加载指示器
        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }

        // 导出图片功能
        async function exportImage(format, scale) {
            // 关闭导出菜单
            document.getElementById('exportDropdown').classList.remove('show');

            // 显示加载指示器
            showLoading();

            try {
                // 等待一小段时间确保Mermaid完全渲染
                await new Promise(resolve => setTimeout(resolve, 500));

                const diagram = document.getElementById('mermaidDiagram');
                const svgElement = diagram.querySelector('svg');

                if (!svgElement) {
                    throw new Error('未找到图表元素，请确保图表已完全加载');
                }

                console.log('找到SVG元素:', svgElement);

                if (format === 'svg') {
                    await exportSVG(svgElement);
                } else if (format === 'png') {
                    await exportPNG(svgElement, scale);
                } else if (format === 'pdf') {
                    await exportPDF(svgElement, scale);
                }
            } catch (error) {
                console.error('导出失败:', error);
                alert('导出失败: ' + error.message);
            } finally {
                hideLoading();
            }
        }

        // 导出SVG
        async function exportSVG(svgElement) {
            try {
                // 克隆SVG元素以避免修改原始元素
                const clonedSvg = svgElement.cloneNode(true);

                // 确保SVG有正确的命名空间和属性
                clonedSvg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
                clonedSvg.setAttribute('xmlns:xlink', 'http://www.w3.org/1999/xlink');

                // 获取SVG的尺寸
                const bbox = svgElement.getBBox();
                clonedSvg.setAttribute('viewBox', `0 0 ${bbox.width} ${bbox.height}`);
                clonedSvg.setAttribute('width', bbox.width);
                clonedSvg.setAttribute('height', bbox.height);

                // 添加样式
                const styleElement = document.createElementNS('http://www.w3.org/2000/svg', 'style');
                styleElement.textContent = `
                    .node rect, .node circle, .node ellipse, .node polygon, .node path {
                        fill: #f9f9f9;
                        stroke: #333;
                        stroke-width: 1px;
                    }
                    .edgePath path {
                        stroke: #333;
                        stroke-width: 1.5px;
                        fill: none;
                    }
                    text {
                        font-family: 'Microsoft YaHei', sans-serif;
                        font-size: 14px;
                        fill: #333;
                    }
                `;
                clonedSvg.insertBefore(styleElement, clonedSvg.firstChild);

                const svgData = new XMLSerializer().serializeToString(clonedSvg);
                const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
                const url = URL.createObjectURL(svgBlob);

                const link = document.createElement('a');
                link.href = url;
                link.download = 'numerology-ai-architecture.svg';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);

                console.log('SVG导出成功');
            } catch (error) {
                console.error('SVG导出失败:', error);
                throw error;
            }
        }

        // 导出PNG - 使用html2canvas方法
        async function exportPNG(svgElement, scale) {
            try {
                console.log('开始PNG导出，缩放:', scale);

                // 重置图表变换，确保导出完整内容
                const originalTransform = document.getElementById('mermaidDiagram').style.transform;
                document.getElementById('mermaidDiagram').style.transform = 'none';

                // 等待DOM更新
                await new Promise(resolve => setTimeout(resolve, 100));

                // 使用html2canvas截取整个图表容器
                const diagramContainer = document.getElementById('mermaidDiagram');

                const canvas = await html2canvas(diagramContainer, {
                    scale: scale,
                    backgroundColor: '#ffffff',
                    useCORS: true,
                    allowTaint: false,
                    logging: false,
                    width: diagramContainer.scrollWidth,
                    height: diagramContainer.scrollHeight,
                    x: 0,
                    y: 0,
                    scrollX: 0,
                    scrollY: 0,
                    windowWidth: window.innerWidth,
                    windowHeight: window.innerHeight
                });

                // 恢复原始变换
                document.getElementById('mermaidDiagram').style.transform = originalTransform;

                console.log('Canvas生成成功，尺寸:', canvas.width, 'x', canvas.height);

                // 验证canvas内容
                if (canvas.width === 0 || canvas.height === 0) {
                    throw new Error('生成的图片尺寸无效');
                }

                // 使用toDataURL方法生成PNG
                const dataURL = canvas.toDataURL('image/png', 1.0);

                if (!dataURL || dataURL === 'data:,' || dataURL.length < 100) {
                    throw new Error('生成的图片数据无效');
                }

                console.log('PNG数据生成成功，大小:', Math.round(dataURL.length / 1024), 'KB');

                // 创建下载链接
                const link = document.createElement('a');
                link.download = `architecture-diagram-${scale}x.png`;
                link.href = dataURL;

                // 触发下载
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                console.log('PNG导出完成');

            } catch (error) {
                console.error('PNG导出失败:', error);

                // 恢复变换
                const originalTransform = document.getElementById('mermaidDiagram').style.transform;
                if (originalTransform) {
                    document.getElementById('mermaidDiagram').style.transform = originalTransform;
                }

                throw error;
            }
        }

        // 导出PDF
        async function exportPDF(svgElement, scale = 2) {
            try {
                console.log('开始PDF导出');

                // 重置图表变换
                const originalTransform = document.getElementById('mermaidDiagram').style.transform;
                document.getElementById('mermaidDiagram').style.transform = 'none';

                // 等待DOM更新
                await new Promise(resolve => setTimeout(resolve, 100));

                // 使用html2canvas生成高分辨率图片
                const diagramContainer = document.getElementById('mermaidDiagram');

                const canvas = await html2canvas(diagramContainer, {
                    scale: scale,
                    backgroundColor: '#ffffff',
                    useCORS: true,
                    allowTaint: false,
                    logging: false,
                    width: diagramContainer.scrollWidth,
                    height: diagramContainer.scrollHeight
                });

                // 恢复变换
                document.getElementById('mermaidDiagram').style.transform = originalTransform;

                // 获取图片数据
                const imgData = canvas.toDataURL('image/png', 1.0);

                if (!imgData || imgData === 'data:,' || imgData.length < 100) {
                    throw new Error('无法生成有效的图片数据');
                }

                console.log('图片数据生成成功');

                // 创建PDF
                const { jsPDF } = window.jspdf;

                // 计算PDF尺寸
                const imgWidth = canvas.width;
                const imgHeight = canvas.height;
                const aspectRatio = imgWidth / imgHeight;

                // 选择页面方向
                let orientation = aspectRatio > 1 ? 'landscape' : 'portrait';

                const pdf = new jsPDF(orientation, 'mm', 'a4');
                const pdfWidth = pdf.internal.pageSize.getWidth();
                const pdfHeight = pdf.internal.pageSize.getHeight();

                // 计算适合的尺寸（保持宽高比，留边距）
                const margin = 10; // 10mm边距
                const availableWidth = pdfWidth - 2 * margin;
                const availableHeight = pdfHeight - 2 * margin;

                let finalWidth, finalHeight;

                if (availableWidth / availableHeight > aspectRatio) {
                    // 以高度为准
                    finalHeight = availableHeight;
                    finalWidth = finalHeight * aspectRatio;
                } else {
                    // 以宽度为准
                    finalWidth = availableWidth;
                    finalHeight = finalWidth / aspectRatio;
                }

                // 居中放置
                const x = (pdfWidth - finalWidth) / 2;
                const y = (pdfHeight - finalHeight) / 2;

                pdf.addImage(imgData, 'PNG', x, y, finalWidth, finalHeight);
                pdf.save('architecture-diagram.pdf');

                console.log('PDF导出完成');

            } catch (error) {
                console.error('PDF导出失败:', error);

                // 恢复变换
                const originalTransform = document.getElementById('mermaidDiagram').style.transform;
                if (originalTransform) {
                    document.getElementById('mermaidDiagram').style.transform = originalTransform;
                }

                throw error;
            }
        }
    </script>
</body>
</html>
