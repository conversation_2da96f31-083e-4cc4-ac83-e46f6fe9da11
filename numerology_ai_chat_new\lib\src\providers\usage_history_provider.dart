import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/usage_history_model.dart';
import '../services/usage_history_service.dart';
import 'auth_provider.dart';

/// 消耗历史状态
class UsageHistoryState {
  final List<UsageHistoryModel> history;
  final bool isLoading;
  final bool isLoadingMore;
  final String? error;
  final int currentPage;
  final int totalPages;
  final int total;
  final bool hasMore;
  final DateTime? lastRefreshTime;

  const UsageHistoryState({
    this.history = const [],
    this.isLoading = false,
    this.isLoadingMore = false,
    this.error,
    this.currentPage = 0,
    this.totalPages = 0,
    this.total = 0,
    this.hasMore = false,
    this.lastRefreshTime,
  });

  UsageHistoryState copyWith({
    List<UsageHistoryModel>? history,
    bool? isLoading,
    bool? isLoadingMore,
    String? error,
    int? currentPage,
    int? totalPages,
    int? total,
    bool? hasMore,
    DateTime? lastRefreshTime,
  }) {
    return UsageHistoryState(
      history: history ?? this.history,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      error: error,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      total: total ?? this.total,
      hasMore: hasMore ?? this.hasMore,
      lastRefreshTime: lastRefreshTime ?? this.lastRefreshTime,
    );
  }

  /// 是否为空状态
  bool get isEmpty => history.isEmpty && !isLoading;

  /// 是否有数据
  bool get hasData => history.isNotEmpty;

  @override
  String toString() {
    return 'UsageHistoryState(historyCount: ${history.length}, isLoading: $isLoading, currentPage: $currentPage, total: $total)';
  }
}

/// 消耗历史状态管理
class UsageHistoryNotifier extends StateNotifier<UsageHistoryState> {
  final Ref _ref;
  static const int _pageSize = 20;

  UsageHistoryNotifier(this._ref) : super(const UsageHistoryState());

  /// 加载消耗历史（首次加载或刷新）
  Future<void> loadUsageHistory({bool refresh = false}) async {
    try {
      final token = _ref.read(authProvider).token;
      if (token == null) {
        state = state.copyWith(error: '用户未登录');
        return;
      }

      // 如果是刷新，重置状态
      if (refresh) {
        state = state.copyWith(
          isLoading: true,
          error: null,
          currentPage: 0,
          history: [],
        );
      } else if (state.history.isEmpty) {
        // 首次加载
        state = state.copyWith(isLoading: true, error: null);
      }

      final result = refresh
          ? await UsageHistoryService.refreshUsageHistory(
              token: token,
              page: 1,
              limit: _pageSize,
            )
          : await UsageHistoryService.getUserUsageHistory(
              token: token,
              page: 1,
              limit: _pageSize,
            );

      state = state.copyWith(
        history: result.history,
        isLoading: false,
        currentPage: result.page,
        totalPages: result.totalPages,
        total: result.total,
        hasMore: result.hasMore,
        lastRefreshTime: DateTime.now(),
        error: null,
      );

      print('UsageHistoryProvider: 加载消耗历史成功 - 总数: ${result.total}, 当前页: ${result.page}');
    } catch (e) {
      print('UsageHistoryProvider: 加载消耗历史失败: $e');
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 加载更多消耗历史
  Future<void> loadMoreUsageHistory() async {
    if (state.isLoadingMore || !state.hasMore) {
      return;
    }

    try {
      final token = _ref.read(authProvider).token;
      if (token == null) {
        state = state.copyWith(error: '用户未登录');
        return;
      }

      state = state.copyWith(isLoadingMore: true, error: null);

      final nextPage = state.currentPage + 1;
      final result = await UsageHistoryService.getUserUsageHistory(
        token: token,
        page: nextPage,
        limit: _pageSize,
      );

      // 合并数据
      final updatedHistory = [...state.history, ...result.history];

      state = state.copyWith(
        history: updatedHistory,
        isLoadingMore: false,
        currentPage: result.page,
        totalPages: result.totalPages,
        total: result.total,
        hasMore: result.hasMore,
        error: null,
      );

      print('UsageHistoryProvider: 加载更多成功 - 当前页: ${result.page}, 总条数: ${updatedHistory.length}');
    } catch (e) {
      print('UsageHistoryProvider: 加载更多失败: $e');
      state = state.copyWith(
        isLoadingMore: false,
        error: e.toString(),
      );
    }
  }

  /// 按日期范围加载消耗历史
  Future<void> loadUsageHistoryByDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final token = _ref.read(authProvider).token;
      if (token == null) {
        state = state.copyWith(error: '用户未登录');
        return;
      }

      state = state.copyWith(
        isLoading: true,
        error: null,
        history: [],
        currentPage: 0,
      );

      final result = await UsageHistoryService.getUsageHistoryByDateRange(
        token: token,
        startDate: startDate,
        endDate: endDate,
        page: 1,
        limit: _pageSize,
      );

      state = state.copyWith(
        history: result.history,
        isLoading: false,
        currentPage: result.page,
        totalPages: result.totalPages,
        total: result.total,
        hasMore: result.hasMore,
        lastRefreshTime: DateTime.now(),
        error: null,
      );

      print('UsageHistoryProvider: 按日期范围加载成功 - 总数: ${result.total}');
    } catch (e) {
      print('UsageHistoryProvider: 按日期范围加载失败: $e');
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 刷新消耗历史
  Future<void> refresh() async {
    await loadUsageHistory(refresh: true);
  }

  /// 清除错误状态
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// 重置状态
  void reset() {
    state = const UsageHistoryState();
    UsageHistoryService.clearCache();
  }

  /// 获取指定ID的消耗记录
  UsageHistoryModel? getHistoryById(String id) {
    try {
      return state.history.firstWhere((item) => item.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 获取今日消耗总计
  int getTodayTotalCost() {
    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);
    final todayEnd = todayStart.add(const Duration(days: 1));

    return state.history
        .where((item) => 
            item.consumeTime.isAfter(todayStart) && 
            item.consumeTime.isBefore(todayEnd))
        .fold(0, (sum, item) => sum + item.powerCost);
  }

  /// 获取本周消耗总计
  int getWeekTotalCost() {
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    final weekStartDay = DateTime(weekStart.year, weekStart.month, weekStart.day);

    return state.history
        .where((item) => item.consumeTime.isAfter(weekStartDay))
        .fold(0, (sum, item) => sum + item.powerCost);
  }

  /// 获取本月消耗总计
  int getMonthTotalCost() {
    final now = DateTime.now();
    final monthStart = DateTime(now.year, now.month, 1);

    return state.history
        .where((item) => item.consumeTime.isAfter(monthStart))
        .fold(0, (sum, item) => sum + item.powerCost);
  }
}

/// 消耗历史状态提供者
final usageHistoryProvider = StateNotifierProvider<UsageHistoryNotifier, UsageHistoryState>((ref) {
  return UsageHistoryNotifier(ref);
});

/// 今日消耗总计提供者
final todayUsageCostProvider = Provider<int>((ref) {
  final usageHistory = ref.watch(usageHistoryProvider);
  final today = DateTime.now();
  final todayStart = DateTime(today.year, today.month, today.day);
  final todayEnd = todayStart.add(const Duration(days: 1));

  return usageHistory.history
      .where((item) => 
          item.consumeTime.isAfter(todayStart) && 
          item.consumeTime.isBefore(todayEnd))
      .fold(0, (sum, item) => sum + item.powerCost);
});

/// 本周消耗总计提供者
final weekUsageCostProvider = Provider<int>((ref) {
  final usageHistory = ref.watch(usageHistoryProvider);
  final now = DateTime.now();
  final weekStart = now.subtract(Duration(days: now.weekday - 1));
  final weekStartDay = DateTime(weekStart.year, weekStart.month, weekStart.day);

  return usageHistory.history
      .where((item) => item.consumeTime.isAfter(weekStartDay))
      .fold(0, (sum, item) => sum + item.powerCost);
});

/// 本月消耗总计提供者
final monthUsageCostProvider = Provider<int>((ref) {
  final usageHistory = ref.watch(usageHistoryProvider);
  final now = DateTime.now();
  final monthStart = DateTime(now.year, now.month, 1);

  return usageHistory.history
      .where((item) => item.consumeTime.isAfter(monthStart))
      .fold(0, (sum, item) => sum + item.powerCost);
});
