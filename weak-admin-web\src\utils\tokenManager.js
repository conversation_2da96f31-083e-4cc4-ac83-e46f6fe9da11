/**
 * Token管理器
 * 负责token的自动续签、过期检查和生命周期管理
 */
class TokenManager {
  constructor(options = {}) {
    this.apiService = options.apiService
    this.onTokenRefreshed = options.onTokenRefreshed
    this.onRefreshFailed = options.onRefreshFailed
    
    // 内部状态
    this._tokenExpiryTime = null
    this._refreshTimer = null
    this._isRefreshing = false
    this._refreshPromise = null
    
    // 配置
    this.refreshThresholdMinutes = 10 // 在过期前10分钟续签
    this.checkIntervalSeconds = 300 // 每5分钟检查一次
    
    console.log('TokenManager: 初始化完成')
  }

  /**
   * 启动自动续签
   * @param {Date} tokenExpiryTime Token过期时间
   */
  startAutoRefresh(tokenExpiryTime) {
    this._tokenExpiryTime = tokenExpiryTime
    
    // 停止现有定时器
    this.stopAutoRefresh()
    
    console.log('TokenManager: 启动自动续签，Token过期时间:', tokenExpiryTime)
    
    // 设置定期检查定时器
    this._refreshTimer = setInterval(() => {
      this._periodicTokenCheck()
    }, this.checkIntervalSeconds * 1000)
    
    // 立即检查一次
    setTimeout(() => this._periodicTokenCheck(), 1000)
  }

  /**
   * 停止自动续签
   */
  stopAutoRefresh() {
    if (this._refreshTimer) {
      clearInterval(this._refreshTimer)
      this._refreshTimer = null
      console.log('TokenManager: 停止自动续签')
    }
  }

  /**
   * 检查token是否需要续签
   */
  shouldRefreshToken() {
    if (!this._tokenExpiryTime) return false

    const now = new Date()
    const timeUntilExpiry = this._tokenExpiryTime.getTime() - now.getTime()
    const minutesUntilExpiry = Math.floor(timeUntilExpiry / (1000 * 60))

    // 如果已经过期，需要续签
    if (minutesUntilExpiry <= 0) {
      return true
    }

    // 如果剩余时间少于阈值，需要续签
    return minutesUntilExpiry < this.refreshThresholdMinutes
  }

  /**
   * 检查token是否已过期
   */
  isTokenExpired() {
    if (!this._tokenExpiryTime) return true
    
    const now = new Date()
    return now.getTime() > this._tokenExpiryTime.getTime()
  }

  /**
   * 定期token检查
   */
  async _periodicTokenCheck() {
    const now = new Date()
    
    console.log('TokenManager: 定期检查 - 当前时间:', now, 'Token过期时间:', this._tokenExpiryTime)
    
    if (!this._tokenExpiryTime) {
      console.log('TokenManager: Token过期时间未设置')
      return
    }

    // 检查token是否已过期
    if (this.isTokenExpired()) {
      console.log('TokenManager: Token已过期，尝试续签')
      const refreshSuccess = await this._performRefresh()
      if (!refreshSuccess) {
        console.log('TokenManager: Token过期后续签失败，触发失败回调')
        this.onRefreshFailed?.()
      }
      return
    }

    // 检查是否需要续签
    if (this.shouldRefreshToken()) {
      console.log('TokenManager: Token即将过期，开始续签')
      const refreshSuccess = await this.refreshTokenIfNeeded()
      if (!refreshSuccess) {
        console.log('TokenManager: Token续签失败')
      }
    } else {
      const timeUntilExpiry = this._tokenExpiryTime.getTime() - now.getTime()
      const minutesUntilExpiry = Math.floor(timeUntilExpiry / (1000 * 60))
      console.log(`TokenManager: Token仍有效，剩余时间: ${minutesUntilExpiry}分钟`)
    }
  }

  /**
   * 执行token续签（如果需要）
   */
  async refreshTokenIfNeeded() {
    // 如果正在续签，等待完成
    if (this._isRefreshing) {
      console.log('TokenManager: 续签正在进行中，等待完成')
      return await this._refreshPromise
    }

    // 检查是否需要续签
    if (!this.shouldRefreshToken()) {
      const now = new Date()
      const timeUntilExpiry = this._tokenExpiryTime.getTime() - now.getTime()
      const minutesUntilExpiry = Math.floor(timeUntilExpiry / (1000 * 60))
      console.log(`TokenManager: Token尚未到期，无需续签。剩余时间: ${minutesUntilExpiry}分钟`)
      return true
    }

    console.log('TokenManager: Token需要续签，开始执行')
    return await this._performRefresh()
  }

  /**
   * 强制执行token续签
   */
  async forceRefreshToken() {
    // 如果正在续签，等待完成
    if (this._isRefreshing) {
      return await this._refreshPromise
    }
    
    return await this._performRefresh()
  }

  /**
   * 执行实际的续签操作
   */
  async _performRefresh() {
    this._isRefreshing = true
    this._refreshPromise = this._doRefresh()
    
    try {
      const result = await this._refreshPromise
      return result
    } finally {
      this._isRefreshing = false
      this._refreshPromise = null
    }
  }

  /**
   * 执行续签请求
   */
  async _doRefresh() {
    console.log('TokenManager: 开始执行token续签')
    
    try {
      // 获取refresh token
      const refreshToken = localStorage.getItem('weak_admin_refresh_token')
      if (!refreshToken) {
        console.log('TokenManager: Refresh token不存在')
        this.onRefreshFailed?.()
        return false
      }
      
      // 调用续签接口
      console.log('TokenManager: 正在调用续签接口...')
      const result = await this.apiService.post('/exeWeakAdmin', {
        action: 'refreshToken',
        refreshToken: refreshToken
      })
      
      console.log('TokenManager: 续签接口返回结果:', result)
      
      if (result.data.success) {
        const { tokens } = result.data.data
        const newToken = tokens.accessToken
        const newRefreshToken = tokens.refreshToken
        
        // 计算新的过期时间
        let newExpiryTime
        if (tokens.expiresAt) {
          newExpiryTime = new Date(tokens.expiresAt)
        } else {
          newExpiryTime = new Date()
          newExpiryTime.setTime(newExpiryTime.getTime() + 60 * 60 * 1000) // 默认1小时
        }
        
        this._tokenExpiryTime = newExpiryTime
        
        // 保存新的tokens
        localStorage.setItem('weak_admin_token', newToken)
        localStorage.setItem('weak_admin_refresh_token', newRefreshToken)
        
        console.log('TokenManager: Token续签成功，新Token过期时间:', this._tokenExpiryTime)
        
        // 通知回调
        this.onTokenRefreshed?.(newToken)
        
        return true
      } else {
        console.log('TokenManager: Token续签失败:', result.data.error?.message)
        this.onRefreshFailed?.()
        return false
      }
    } catch (e) {
      console.log('TokenManager: Token续签异常:', e)
      this.onRefreshFailed?.()
      return false
    }
  }

  /**
   * 更新token过期时间
   */
  updateTokenExpiryTime(expiryTime) {
    this._tokenExpiryTime = expiryTime
    console.log('TokenManager: 更新Token过期时间:', expiryTime)
  }

  /**
   * 获取token过期时间
   */
  get tokenExpiryTime() {
    return this._tokenExpiryTime
  }

  /**
   * 获取续签状态
   */
  get isRefreshing() {
    return this._isRefreshing
  }

  /**
   * 清理资源
   */
  dispose() {
    this.stopAutoRefresh()
    this.onTokenRefreshed = null
    this.onRefreshFailed = null
    console.log('TokenManager: 资源已清理')
  }
}

export default TokenManager
