import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';

import '../widgets/bazi_panel.dart';
import '../widgets/bazi_history_list.dart';

/// 八字排盘页面
class BaziScreen extends ConsumerWidget {
  const BaziScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          // 左侧：排盘输入
          Expanded(
            flex: 2,
            child: BaziPanel(
              showTitle: true,
              onHelpPressed: () => _showHelp(context),
            ),
          ),

          const Gap(16),

          // 右侧：历史生成列表
          const Expanded(
            flex: 1,
            child: BaziHistoryList(),
          ),
        ],
      ),
    );
  }

  Widget _buildHelpContent(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHelpSection(
            context,
            '基本信息',
            [
              '• 姓名：输入完整姓名',
              '• 性别：选择男性或女性',
              '• 出生时间：精确到分钟',
              '• 出生地点：影响时区计算',
            ],
          ),
          const Gap(16),
          _buildHelpSection(
            context,
            '历法选择',
            [
              '• 公历：现代通用历法',
              '• 农历：传统中国历法',
              '• 闰月：农历特殊月份',
            ],
          ),
          const Gap(16),
          _buildHelpSection(
            context,
            '排盘结果',
            [
              '• 年柱：出生年份干支',
              '• 月柱：出生月份干支',
              '• 日柱：出生日期干支',
              '• 时柱：出生时辰干支',
            ],
          ),
          const Gap(16),
          _buildHelpSection(
            context,
            '注意事项',
            [
              '• 时间越精确，结果越准确',
              '• 出生地影响真太阳时',
              '• 可保存多个排盘结果',
              '• 支持导出分享功能',
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHelpSection(BuildContext context, String title, List<String> items) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
        ),
        const Gap(8),
        ...items.map((item) => Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Text(
            item,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.8),
            ),
          ),
        )),
      ],
    );
  }

  void _showHelp(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('八字排盘说明'),
        content: SizedBox(
          width: 400,
          height: 300,
          child: _buildHelpContent(context),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('知道了'),
          ),
        ],
      ),
    );
  }
}
