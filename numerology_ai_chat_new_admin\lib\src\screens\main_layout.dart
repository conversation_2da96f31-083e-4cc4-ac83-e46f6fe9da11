
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:numerology_ai_chat_admin/src/providers/auth_provider.dart';

class MainLayout extends ConsumerStatefulWidget {
  final Widget child;
  const MainLayout({super.key, required this.child});

  @override
  ConsumerState<MainLayout> createState() => _MainLayoutState();
}

class _MainLayoutState extends ConsumerState<MainLayout> {
  int _selectedIndex = 0;

  void _onDestinationSelected(int index) {
    setState(() {
      _selectedIndex = index;
    });
    switch (index) {
      case 0:
        context.go('/home');
        break;
      case 1:
        context.go('/users');
        break;
      case 2:
        context.go('/agents');
        break;
      case 3:
        context.go('/models');
        break;
      case 4:
        context.go('/pages');
        break;
      case 5:
        context.go('/config');
        break;
      case 6:
        context.go('/packages');
        break;
      case 7:
        context.go('/orders');
        break;
      case 8:
        context.go('/payment-logs');
        break;
      case 9:
        context.go('/usage-history');
        break;
    }
  }

  int _calculateSelectedIndex(BuildContext context) {
    final location = GoRouterState.of(context).matchedLocation;
    if (location.startsWith('/home')) {
      return 0;
    } else if (location.startsWith('/users')) {
      return 1;
    } else if (location.startsWith('/agents')) {
      return 2;
    } else if (location.startsWith('/models')) {
      return 3;
    } else if (location.startsWith('/pages')) {
      return 4;
    } else if (location.startsWith('/config')) {
      return 5;
    } else if (location.startsWith('/packages')) {
      return 6;
    } else if (location.startsWith('/orders')) {
      return 7;
    } else if (location.startsWith('/payment-logs')) {
      return 8;
    } else if (location.startsWith('/usage-history')) {
      return 9;
    }
    return 0;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('命理AI管理后台'),
        automaticallyImplyLeading: false,
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(LucideIcons.user),
            onSelected: (value) {
              if (value == 'logout') {
                _showLogoutDialog();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'logout',
                child: Row(
                  children: [
                    Icon(LucideIcons.logOut, size: 16),
                    SizedBox(width: 8),
                    Text('退出登录'),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: Row(
        children: [
          NavigationRail(
            selectedIndex: _calculateSelectedIndex(context),
            onDestinationSelected: _onDestinationSelected,
            labelType: NavigationRailLabelType.all,
            destinations: const [
              NavigationRailDestination(
                icon: Icon(LucideIcons.layoutDashboard),
                label: Text('仪表盘'),
              ),
              NavigationRailDestination(
                icon: Icon(LucideIcons.users),
                label: Text('用户管理'),
              ),
              NavigationRailDestination(
                icon: Icon(LucideIcons.bot),
                label: Text('智能体管理'),
              ),
              NavigationRailDestination(
                icon: Icon(LucideIcons.cpu),
                label: Text('模型管理'),
              ),
              NavigationRailDestination(
                icon: Icon(LucideIcons.fileText),
                label: Text('页面管理'),
              ),
              NavigationRailDestination(
                icon: Icon(LucideIcons.settings),
                label: Text('系统配置'),
              ),
              NavigationRailDestination(
                icon: Icon(LucideIcons.creditCard),
                label: Text('算力套餐'),
              ),
              NavigationRailDestination(
                icon: Icon(LucideIcons.shoppingCart),
                label: Text('订单管理'),
              ),
              NavigationRailDestination(
                icon: Icon(LucideIcons.fileText),
                label: Text('支付日志'),
              ),
              NavigationRailDestination(
                icon: Icon(LucideIcons.activity),
                label: Text('使用历史'),
              ),
            ],
          ),
          const VerticalDivider(thickness: 1, width: 1),
          Expanded(
            child: widget.child,
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认退出'),
        content: const Text('确定要退出登录吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await ref.read(authProvider.notifier).logout();
              if (mounted) {
                context.go('/login');
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('退出'),
          ),
        ],
      ),
    );
  }
}
