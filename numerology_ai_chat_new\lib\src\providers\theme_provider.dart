import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/constants/app_constants.dart';
import '../core/storage/storage_service.dart';

/// 主题管理提供者
class ThemeProvider extends ChangeNotifier {
  final StorageService _storageService;

  ThemeMode _themeMode = ThemeMode.system;

  ThemeProvider(this._storageService);

  ThemeMode get themeMode => _themeMode;
  
  /// 是否为暗色主题
  bool get isDarkMode {
    if (_themeMode == ThemeMode.system) {
      return WidgetsBinding.instance.platformDispatcher.platformBrightness == Brightness.dark;
    }
    return _themeMode == ThemeMode.dark;
  }
  
  /// 初始化主题设置
  Future<void> initTheme() async {
    final result = await _storageService.get<int>(AppConstants.themeKey);
    result.when(
      success: (themeIndex) {
        if (themeIndex != null && themeIndex < ThemeMode.values.length) {
          _themeMode = ThemeMode.values[themeIndex];
          notifyListeners();
        }
      },
      failure: (error) {
        // 使用默认主题
        _themeMode = ThemeMode.system;
      },
    );
  }
  
  /// 切换主题模式
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode == mode) return;

    _themeMode = mode;
    notifyListeners();

    await _storageService.set(AppConstants.themeKey, mode.index);
  }
  
  /// 切换明暗主题
  Future<void> toggleTheme() async {
    switch (_themeMode) {
      case ThemeMode.system:
      case ThemeMode.light:
        await setThemeMode(ThemeMode.dark);
        break;
      case ThemeMode.dark:
        await setThemeMode(ThemeMode.light);
        break;
    }
  }

  /// 获取主题模式的显示名称
  String get themeModeDisplayName {
    switch (_themeMode) {
      case ThemeMode.system:
      case ThemeMode.light:
        return '明亮模式';
      case ThemeMode.dark:
        return '暗色模式';
    }
  }

  /// 获取主题模式的图标
  IconData get themeModeIcon {
    switch (_themeMode) {
      case ThemeMode.system:
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
    }
  }
}

/// 主题提供者
final themeProviderNotifier = ChangeNotifierProvider<ThemeProvider>((ref) {
  final storageService = ref.read(storageServiceProvider);
  final provider = ThemeProvider(storageService);
  // 异步初始化主题
  provider.initTheme();
  return provider;
});

/// 主题提供者（兼容旧代码）
final themeProvider = themeProviderNotifier;
