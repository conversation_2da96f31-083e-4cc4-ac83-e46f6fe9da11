import 'dart:convert';
import 'package:http/http.dart' as http;
import '../core/constants/app_constants.dart';
import '../models/api_response.dart';
import '../models/agent_model.dart';
import '../models/ai_model.dart';
import '../models/user_model.dart';
import '../models/pricing_tier.dart';

/// 云函数服务
class CloudFunctionService {
  static const String _baseUrl = AppConstants.exeFunctionUrl;

  /// 用户注册
  Future<ApiResponse<CloudFunctionData<Map<String, dynamic>>>> register({
    required String username,
    required String password,
  }) async {
    try {
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'action': AppConstants.actionRegister,
          'username': username,
          'password': password,
        }),
      ).timeout(AppConstants.connectTimeout);

      final jsonData = jsonDecode(response.body) as Map<String, dynamic>;
      
      return ApiResponse.fromJson(
        jsonData,
        (data) => CloudFunctionData.fromJson(
          data as Map<String, dynamic>,
          (innerData) => innerData as Map<String, dynamic>,
        ),
      );
    } catch (e) {
      return ApiResponse<CloudFunctionData<Map<String, dynamic>>>(
        code: -1,
        message: '注册失败: $e',
        data: null,
      );
    }
  }

  /// 用户登录
  Future<ApiResponse<CloudFunctionData<LoginResponseData>>> login({
    required String username,
    required String password,
  }) async {
    try {
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'action': AppConstants.actionLogin,
          'username': username,
          'password': password,
        }),
      ).timeout(AppConstants.connectTimeout);

      final jsonData = jsonDecode(response.body) as Map<String, dynamic>;
      
      return ApiResponse.fromJson(
        jsonData,
        (data) => CloudFunctionData.fromJson(
          data as Map<String, dynamic>,
          (innerData) => LoginResponseData.fromJson(innerData as Map<String, dynamic>),
        ),
      );
    } catch (e) {
      return ApiResponse<CloudFunctionData<LoginResponseData>>(
        code: -1,
        message: '登录失败: $e',
        data: null,
      );
    }
  }

  /// 刷新Token
  Future<ApiResponse<CloudFunctionData<TokenInfo>>> refreshToken({
    required String refreshToken,
  }) async {
    try {
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'action': AppConstants.actionRefreshToken,
          'refreshToken': refreshToken,
        }),
      ).timeout(AppConstants.connectTimeout);

      final jsonData = jsonDecode(response.body) as Map<String, dynamic>;
      
      return ApiResponse.fromJson(
        jsonData,
        (data) => CloudFunctionData.fromJson(
          data as Map<String, dynamic>,
          (innerData) => TokenInfo.fromJson(innerData as Map<String, dynamic>),
        ),
      );
    } catch (e) {
      return ApiResponse<CloudFunctionData<TokenInfo>>(
        code: -1,
        message: '刷新Token失败: $e',
        data: null,
      );
    }
  }

  /// 获取智能体列表
  Future<ApiResponse<CloudFunctionData<AgentListResponse>>> getAgents({
    required String token,
  }) async {
    try {
      print('CloudFunctionService: 开始获取智能体列表, token: ${token.substring(0, 20)}...');
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'action': AppConstants.actionGetAgents,
          'token': token,
        }),
      ).timeout(AppConstants.connectTimeout);

      print('CloudFunctionService: 获取智能体列表响应状态: ${response.statusCode}');
      print('CloudFunctionService: 获取智能体列表响应内容: ${response.body}');

      final jsonData = jsonDecode(response.body) as Map<String, dynamic>;

      return ApiResponse.fromJson(
        jsonData,
        (data) => CloudFunctionData.fromJson(
          data as Map<String, dynamic>,
          (innerData) => AgentListResponse.fromJson(innerData as Map<String, dynamic>),
        ),
      );
    } catch (e) {
      print('CloudFunctionService: 获取智能体列表异常: $e');
      return ApiResponse<CloudFunctionData<AgentListResponse>>(
        code: -1,
        message: '获取智能体列表失败: $e',
        data: null,
      );
    }
  }

  /// 获取模型列表
  Future<ApiResponse<CloudFunctionData<ModelListResponse>>> getModels({
    required String token,
  }) async {
    try {
      print('CloudFunctionService: 开始获取模型列表, token: ${token.substring(0, 20)}...');
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'action': AppConstants.actionGetModels,
          'token': token,
        }),
      ).timeout(AppConstants.connectTimeout);

      print('CloudFunctionService: 获取模型列表响应状态: ${response.statusCode}');
      print('CloudFunctionService: 获取模型列表响应内容: ${response.body}');

      final jsonData = jsonDecode(response.body) as Map<String, dynamic>;

      return ApiResponse.fromJson(
        jsonData,
        (data) => CloudFunctionData.fromJson(
          data as Map<String, dynamic>,
          (innerData) => ModelListResponse.fromJson(innerData as Map<String, dynamic>),
        ),
      );
    } catch (e) {
      print('CloudFunctionService: 获取模型列表异常: $e');
      return ApiResponse<CloudFunctionData<ModelListResponse>>(
        code: -1,
        message: '获取模型列表失败: $e',
        data: null,
      );
    }
  }

  /// 获取用户信息
  Future<ApiResponse<CloudFunctionData<Map<String, dynamic>>>> getUserInfo({
    required String token,
  }) async {
    try {
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'action': AppConstants.actionGetUserInfo,
          'token': token,
        }),
      ).timeout(AppConstants.connectTimeout);

      final jsonData = jsonDecode(response.body) as Map<String, dynamic>;
      
      return ApiResponse.fromJson(
        jsonData,
        (data) => CloudFunctionData.fromJson(
          data as Map<String, dynamic>,
          (innerData) => innerData as Map<String, dynamic>,
        ),
      );
    } catch (e) {
      return ApiResponse<CloudFunctionData<Map<String, dynamic>>>(
        code: -1,
        message: '获取用户信息失败: $e',
        data: null,
      );
    }
  }

  /// 更新使用次数
  Future<ApiResponse<CloudFunctionData<Map<String, dynamic>>>> updateUsage({
    required String token,
  }) async {
    try {
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'action': AppConstants.actionUpdateUsage,
          'token': token,
        }),
      ).timeout(AppConstants.connectTimeout);

      final jsonData = jsonDecode(response.body) as Map<String, dynamic>;
      
      return ApiResponse.fromJson(
        jsonData,
        (data) => CloudFunctionData.fromJson(
          data as Map<String, dynamic>,
          (innerData) => innerData as Map<String, dynamic>,
        ),
      );
    } catch (e) {
      return ApiResponse<CloudFunctionData<Map<String, dynamic>>>(
        code: -1,
        message: '更新使用次数失败: $e',
        data: null,
      );
    }
  }

  /// 获取档次配置列表
  Future<ApiResponse<CloudFunctionData<PricingTierListResponse>>> getPricingTiers({
    required String token,
  }) async {
    try {
      print('CloudFunctionService: 开始获取档次配置列表, token: ${token.substring(0, 20)}...');
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'action': 'getPricingTiers',
          'token': token,
        }),
      ).timeout(AppConstants.connectTimeout);

      print('CloudFunctionService: 获取档次配置列表响应状态: ${response.statusCode}');
      print('CloudFunctionService: 获取档次配置列表响应内容: ${response.body}');

      final jsonData = jsonDecode(response.body) as Map<String, dynamic>;

      return ApiResponse.fromJson(
        jsonData,
        (data) => CloudFunctionData.fromJson(
          data as Map<String, dynamic>,
          (innerData) => PricingTierListResponse.fromJson(innerData as Map<String, dynamic>),
        ),
      );
    } catch (e) {
      print('CloudFunctionService: 获取档次配置列表异常: $e');
      return ApiResponse<CloudFunctionData<PricingTierListResponse>>(
        code: -1,
        message: '获取档次配置列表失败: $e',
        data: null,
      );
    }
  }
}
