import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/portrait_mode_provider.dart';

/// 竖屏模式切换按钮
class PortraitModeButton extends ConsumerStatefulWidget {
  const PortraitModeButton({super.key});

  @override
  ConsumerState<PortraitModeButton> createState() => _PortraitModeButtonState();
}

class _PortraitModeButtonState extends ConsumerState<PortraitModeButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isToggling = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _togglePortraitMode() async {
    if (_isToggling) return;

    setState(() {
      _isToggling = true;
    });

    try {
      // 播放按钮动画
      await _animationController.forward();
      await _animationController.reverse();

      final portraitModeNotifier = ref.read(portraitModeProvider.notifier);
      final isCurrentlyEnabled = ref.read(portraitModeProvider).isPortraitModeEnabled;

      if (isCurrentlyEnabled) {
        // 退出竖屏模式
        await portraitModeNotifier.exitPortraitMode();
        if (mounted) {
          _showSnackBar('已退出竖屏模式', Icons.desktop_windows, Colors.blue);
        }
      } else {
        // 显示确认对话框
        final confirmed = await _showConfirmDialog();
        if (confirmed == true) {
          await portraitModeNotifier.enterPortraitMode();
          if (mounted) {
            _showSnackBar('已进入竖屏模式', Icons.phone_android, Colors.green);
          }
        }
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar('切换失败: $e', Icons.error, Colors.red);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isToggling = false;
        });
      }
    }
  }

  Future<bool?> _showConfirmDialog() async {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.phone_android, color: Colors.orange),
            SizedBox(width: 8),
            Text('进入竖屏模式'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('即将进入手机竖屏模式，将会：'),
            SizedBox(height: 8),
            Text('• 调整窗口为手机比例 (9:16)'),
            Text('• 隐藏侧边栏和左侧面板'),
            Text('• 放大字体以适应手机显示'),
            Text('• 保持所有聊天功能正常'),
            SizedBox(height: 12),
            Text(
              '提示：再次点击此按钮可退出竖屏模式',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showSnackBar(String message, IconData icon, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: color,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isPortraitModeEnabled = ref.watch(portraitModeStateProvider);

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Tooltip(
            message: isPortraitModeEnabled ? '退出竖屏模式' : '进入竖屏模式',
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: _isToggling ? null : _togglePortraitMode,
                borderRadius: BorderRadius.circular(20),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: isPortraitModeEnabled
                        ? theme.colorScheme.primary.withOpacity(0.1)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isPortraitModeEnabled
                          ? theme.colorScheme.primary
                          : theme.colorScheme.outline.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (_isToggling)
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: theme.colorScheme.primary,
                          ),
                        )
                      else
                        Icon(
                          isPortraitModeEnabled
                              ? Icons.desktop_windows
                              : Icons.phone_android,
                          size: 16,
                          color: isPortraitModeEnabled
                              ? theme.colorScheme.primary
                              : theme.colorScheme.onSurface.withOpacity(0.6),
                        ),
                      const SizedBox(width: 6),
                      Text(
                        isPortraitModeEnabled ? '桌面模式' : '竖屏模式',
                        style: theme.textTheme.labelMedium?.copyWith(
                          color: isPortraitModeEnabled
                              ? theme.colorScheme.primary
                              : theme.colorScheme.onSurface.withOpacity(0.8),
                          fontWeight: isPortraitModeEnabled
                              ? FontWeight.bold
                              : FontWeight.normal,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// 简化版竖屏模式按钮（仅图标）
class PortraitModeIconButton extends ConsumerWidget {
  const PortraitModeIconButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isPortraitModeEnabled = ref.watch(portraitModeStateProvider);

    return IconButton(
      onPressed: () => ref.read(portraitModeProvider.notifier).togglePortraitMode(),
      tooltip: isPortraitModeEnabled ? '退出竖屏模式' : '进入竖屏模式',
      icon: Icon(
        isPortraitModeEnabled ? Icons.desktop_windows : Icons.phone_android,
        color: isPortraitModeEnabled
            ? theme.colorScheme.primary
            : theme.colorScheme.onSurface.withOpacity(0.6),
      ),
      style: IconButton.styleFrom(
        backgroundColor: isPortraitModeEnabled
            ? theme.colorScheme.primary.withOpacity(0.1)
            : Colors.transparent,
        foregroundColor: isPortraitModeEnabled
            ? theme.colorScheme.primary
            : theme.colorScheme.onSurface.withOpacity(0.6),
      ),
    );
  }
}
