const { createBusinessError, ERROR_CODES, formatSuccessResponse } = require('../utils/error_handler')
const { checkPermission } = require('../middleware/auth')
const logger = require('../utils/logger')
const cloud = require('wx-server-sdk')

// 初始化数据库
const db = cloud.database()
const configCollection = db.collection('exe_system_config')

/**
 * 获取系统配置列表
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 系统配置列表
 */
async function getSystemConfigs(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'system_config_read')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限查看系统配置', 403)
    }

    const { category, isActive, page = 1, pageSize = 20 } = event || {}

    logger.info('管理员获取系统配置列表', {
      adminId: adminAuth.adminId,
      category,
      isActive,
      page,
      pageSize
    })

    // 构建查询条件
    const where = {}
    if (category) {
      where.category = category
    }
    if (typeof isActive === 'boolean') {
      where.isActive = isActive
    }

    // 分页查询
    const skip = (page - 1) * pageSize
    const { data: configs } = await configCollection
      .where(where)
      .orderBy('category', 'asc')
      .orderBy('configKey', 'asc')
      .skip(skip)
      .limit(pageSize)
      .get()

    const { total } = await configCollection.where(where).count()

    logger.info('管理员获取系统配置列表成功', {
      adminId: adminAuth.adminId,
      total,
      currentPage: page,
      pageSize
    })

    return formatSuccessResponse({
      configs,
      pagination: {
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    }, '获取系统配置列表成功')

  } catch (error) {
    logger.error('获取系统配置列表失败', { error: error.message, adminId: adminAuth.adminId })
    throw error
  }
}

/**
 * 创建系统配置
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 创建结果
 */
async function createSystemConfig(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'system_config_write')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限创建系统配置', 403)
    }

    const { configKey, configValue, configType, description, isActive = true, category } = event

    // 参数验证
    if (!configKey || !configValue || !configType || !description || !category) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '缺少必要参数', 400)
    }

    logger.info('管理员创建系统配置', {
      adminId: adminAuth.adminId,
      configKey,
      category
    })

    // 检查配置键是否已存在
    const { data: existingConfigs } = await configCollection.where({ configKey }).get()
    if (existingConfigs.length > 0) {
      throw createBusinessError(ERROR_CODES.RESOURCE_EXISTS, '配置键已存在', 400)
    }

    // 创建配置
    const now = new Date()
    const configData = {
      configKey,
      configValue,
      configType,
      description,
      isActive,
      category,
      createdAt: now,
      updatedAt: now,
      createdBy: adminAuth.adminId,
      updatedBy: adminAuth.adminId
    }

    const { _id } = await configCollection.add(configData)

    logger.info('管理员创建系统配置成功', {
      adminId: adminAuth.adminId,
      configId: _id,
      configKey
    })

    return formatSuccessResponse({
      configId: _id,
      configKey,
      category,
      isActive,
      createdAt: now
    }, '系统配置创建成功')

  } catch (error) {
    logger.error('创建系统配置失败', { error: error.message, adminId: adminAuth.adminId })
    throw error
  }
}

/**
 * 更新系统配置
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 更新结果
 */
async function updateSystemConfig(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'system_config_write')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限更新系统配置', 403)
    }

    const { configId, configValue, configType, description, isActive, category } = event

    // 参数验证
    if (!configId) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '缺少配置ID', 400)
    }

    logger.info('管理员更新系统配置', {
      adminId: adminAuth.adminId,
      configId
    })

    // 检查配置是否存在
    const { data: existingConfigs } = await configCollection.doc(configId).get()
    if (!existingConfigs) {
      throw createBusinessError(ERROR_CODES.RESOURCE_NOT_FOUND, '配置不存在', 404)
    }

    // 构建更新数据
    const updateData = {
      updatedAt: new Date(),
      updatedBy: adminAuth.adminId
    }

    if (configValue !== undefined) updateData.configValue = configValue
    if (configType !== undefined) updateData.configType = configType
    if (description !== undefined) updateData.description = description
    if (isActive !== undefined) updateData.isActive = isActive
    if (category !== undefined) updateData.category = category

    // 更新配置
    await configCollection.doc(configId).update(updateData)

    logger.info('管理员更新系统配置成功', {
      adminId: adminAuth.adminId,
      configId
    })

    return formatSuccessResponse({
      configId,
      updatedAt: updateData.updatedAt
    }, '系统配置更新成功')

  } catch (error) {
    logger.error('更新系统配置失败', { error: error.message, adminId: adminAuth.adminId })
    throw error
  }
}

/**
 * 删除系统配置
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 删除结果
 */
async function deleteSystemConfig(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'system_config_write')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限删除系统配置', 403)
    }

    const { configId } = event

    // 参数验证
    if (!configId) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '缺少配置ID', 400)
    }

    logger.info('管理员删除系统配置', {
      adminId: adminAuth.adminId,
      configId
    })

    // 检查配置是否存在
    const { data: existingConfig } = await configCollection.doc(configId).get()
    if (!existingConfig) {
      throw createBusinessError(ERROR_CODES.RESOURCE_NOT_FOUND, '配置不存在', 404)
    }

    // 删除配置
    await configCollection.doc(configId).remove()

    logger.info('管理员删除系统配置成功', {
      adminId: adminAuth.adminId,
      configId
    })

    return formatSuccessResponse({
      configId,
      deletedAt: new Date()
    }, '系统配置删除成功')

  } catch (error) {
    logger.error('删除系统配置失败', { error: error.message, adminId: adminAuth.adminId })
    throw error
  }
}

module.exports = {
  getSystemConfigs,
  createSystemConfig,
  updateSystemConfig,
  deleteSystemConfig
}
