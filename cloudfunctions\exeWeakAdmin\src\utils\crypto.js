const CryptoJS = require('crypto-js')
const { systemConfigCollection } = require('./db')
const logger = require('./logger')

/**
 * 获取加密密钥
 * @returns {Promise<string>} 加密密钥
 */
async function getEncryptionKey() {
  try {
    const config = await systemConfigCollection.getConfig()
    if (!config || !config.encryption_key) {
      throw new Error('系统配置中未找到加密密钥')
    }
    return config.encryption_key
  } catch (error) {
    logger.error('获取加密密钥失败', { error: error.message })
    throw error
  }
}

/**
 * 生成激活码
 * @param {number} quotaAmount 算力数量
 * @param {string} creatorUsername 创建者用户名
 * @returns {Promise<string>} 加密后的激活码
 */
async function generateActivationCode(quotaAmount, creatorUsername) {
  try {
    const encryptionKey = await getEncryptionKey()
    
    // 构造激活码数据
    const codeData = {
      timestamp: Date.now(),
      quota: quotaAmount,
      creator: creatorUsername,
      version: '1.0',
      random: Math.random().toString(36).substring(2, 15) // 增加随机性
    }
    
    // 转换为JSON字符串
    const jsonString = JSON.stringify(codeData)
    
    // AES加密
    const encrypted = CryptoJS.AES.encrypt(jsonString, encryptionKey).toString()
    
    logger.info('激活码生成成功', {
      creator: creatorUsername,
      quota: quotaAmount,
      timestamp: codeData.timestamp
    })
    
    return encrypted
    
  } catch (error) {
    logger.error('激活码生成失败', {
      error: error.message,
      creator: creatorUsername,
      quota: quotaAmount
    })
    throw error
  }
}

/**
 * 验证并解析激活码
 * @param {string} activationCode 激活码
 * @returns {Promise<object>} 解析后的激活码数据
 */
async function validateActivationCode(activationCode) {
  try {
    const encryptionKey = await getEncryptionKey()
    
    // AES解密
    const decryptedBytes = CryptoJS.AES.decrypt(activationCode, encryptionKey)
    const decryptedString = decryptedBytes.toString(CryptoJS.enc.Utf8)
    
    if (!decryptedString) {
      throw new Error('激活码格式无效')
    }
    
    // 解析JSON
    const codeData = JSON.parse(decryptedString)
    
    // 验证必要字段
    if (!codeData.timestamp || !codeData.quota || !codeData.creator || !codeData.version) {
      throw new Error('激活码数据不完整')
    }
    
    // 验证版本
    if (codeData.version !== '1.0') {
      throw new Error('激活码版本不支持')
    }
    
    // 验证时间戳（可选：检查是否过期）
    const now = Date.now()
    const codeAge = now - codeData.timestamp
    const maxAge = 365 * 24 * 60 * 60 * 1000 // 1年有效期
    
    if (codeAge > maxAge) {
      throw new Error('激活码已过期')
    }
    
    logger.info('激活码验证成功', {
      creator: codeData.creator,
      quota: codeData.quota,
      timestamp: codeData.timestamp,
      age: Math.floor(codeAge / (24 * 60 * 60 * 1000)) + '天'
    })
    
    return codeData
    
  } catch (error) {
    logger.error('激活码验证失败', {
      error: error.message,
      code: activationCode.substring(0, 20) + '...' // 只记录前20个字符
    })
    throw error
  }
}

module.exports = {
  generateActivationCode,
  validateActivationCode,
  getEncryptionKey
}
