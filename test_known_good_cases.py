#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试已知的成功案例，验证八字排盘修复效果
"""

import requests
import json
import time

# 云函数API地址
API_URL = "https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction"

def test_bazi_case(name, description, date, time_str, expected_results, zi_time_handling=0):
    """测试单个八字案例"""
    print(f"\n=== {name} ===")
    print(f"描述：{description}")
    print(f"测试时间：{date} {time_str}")
    
    request_data = {
        "action": "baziAnalyze",
        "name": "测试者",  # 使用简单的姓名，避免特殊字符
        "gender": "男",
        "calendarType": "公历",
        "birthDate": date,
        "birthTime": time_str,
        "isLeapMonth": False,
        "birthPlace": "北京",
        "ziTimeHandling": zi_time_handling
    }
    
    print(f"子时处理：{'晚子时（当天）' if zi_time_handling == 0 else '早子时（第二天）'}")
    
    try:
        response = requests.post(
            API_URL,
            headers={'Content-Type': 'application/json'},
            json=request_data,
            timeout=30
        )
        
        if response.status_code != 200:
            print(f"❌ HTTP错误：{response.status_code}")
            return False
            
        result = response.json()
        
        if result.get('code') != 0:
            print(f"❌ API错误：{result.get('message', '未知错误')}")
            return False
            
        data = result.get('data', {})
        bazi_str = data.get('baziStr', '')
        
        print(f"✅ 请求成功")
        print(f"八字结果：{bazi_str}")
        
        # 验证预期结果
        success = True
        for key, expected_value in expected_results.items():
            actual_value = data.get(key, {})
            if isinstance(actual_value, dict) and 'ganZhi' in actual_value:
                actual_value = actual_value['ganZhi']
            
            if actual_value == expected_value:
                print(f"✅ {key}：{actual_value} （正确）")
            else:
                print(f"❌ {key}：{actual_value} （预期：{expected_value}）")
                success = False
        
        return success
        
    except Exception as e:
        print(f"❌ 请求异常：{str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始已知成功案例测试")
    print("验证八字排盘修复效果")
    print("=" * 60)
    
    test_cases = [
        # 原始修复验证案例
        {
            "name": "年柱修复验证",
            "description": "2024年2月4日16:26，立春前1分钟，年柱应为癸卯",
            "date": "2024-02-04",
            "time": "16:26",
            "expected": {"year": "癸卯"},
            "zi_time_handling": 0
        },
        {
            "name": "子时晚子时",
            "description": "1990年12月15日23:30，晚子时规则，日柱应为甲寅",
            "date": "1990-12-15",
            "time": "23:30",
            "expected": {"day": "甲寅"},
            "zi_time_handling": 0
        },
        {
            "name": "子时早子时",
            "description": "1990年12月15日23:30，早子时规则，日柱应为乙卯",
            "date": "1990-12-15",
            "time": "23:30",
            "expected": {"day": "乙卯"},
            "zi_time_handling": 1
        },
        {
            "name": "月柱节气验证",
            "description": "1990年11月8日23:30，立冬后，月柱应为丁亥",
            "date": "1990-11-08",
            "time": "23:30",
            "expected": {"month": "丁亥"},
            "zi_time_handling": 0
        },
        # 新增的边界案例测试
        {
            "name": "近期立春测试",
            "description": "2023年2月4日10:42立春，测试近期立春",
            "date": "2023-02-04",
            "time": "10:30",  # 立春前12分钟
            "expected": {"year": "壬寅"},  # 应该还是前一年
            "zi_time_handling": 0
        },
        {
            "name": "近期立春后",
            "description": "2023年2月4日10:42立春，测试立春后",
            "date": "2023-02-04",
            "time": "11:00",  # 立春后18分钟
            "expected": {"year": "癸卯"},  # 应该是新年
            "zi_time_handling": 0
        },
        {
            "name": "普通时间测试1",
            "description": "2020年6月15日14:30，普通时间测试",
            "date": "2020-06-15",
            "time": "14:30",
            "expected": {"year": "庚子"},  # 验证年柱
            "zi_time_handling": 0
        },
        {
            "name": "普通时间测试2",
            "description": "2018年9月23日09:15，普通时间测试",
            "date": "2018-09-23",
            "time": "09:15",
            "expected": {"year": "戊戌"},  # 验证年柱
            "zi_time_handling": 0
        }
    ]
    
    # 执行所有测试
    total_tests = len(test_cases)
    passed_tests = 0
    failed_cases = []
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n[{i}/{total_tests}] ", end="")
        success = test_bazi_case(
            case["name"],
            case["description"],
            case["date"],
            case["time"],
            case["expected"],
            case.get("zi_time_handling", 0)
        )
        
        if success:
            passed_tests += 1
        else:
            failed_cases.append(case["name"])
        
        # 避免请求过快
        if i < total_tests:
            time.sleep(0.5)
    
    # 汇总测试结果
    print("\n" + "=" * 60)
    print("📊 已知成功案例测试结果汇总")
    print("=" * 60)
    
    print(f"总测试数：{total_tests}")
    print(f"通过测试：{passed_tests}")
    print(f"失败测试：{total_tests - passed_tests}")
    print(f"通过率：{passed_tests/total_tests*100:.1f}%")
    
    if failed_cases:
        print(f"\n❌ 失败的测试案例：")
        for case_name in failed_cases:
            print(f"  - {case_name}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有已知成功案例测试通过！八字排盘修复效果确认！")
        print("✅ 年柱节气交接问题已修复")
        print("✅ 子时处理规则正确实现")
        print("✅ 月柱节气交接问题已修复")
        print("✅ 系统在常见情况下表现稳定")
    else:
        print(f"\n⚠️  有 {total_tests - passed_tests} 个已知案例测试失败，需要检查")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
