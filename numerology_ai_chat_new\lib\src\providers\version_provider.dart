import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/version_model.dart';
import '../services/version_service.dart';
import '../core/constants/app_constants.dart';
import '../core/storage/storage_service.dart';

/// 版本状态
class VersionState {
  final VersionCheckResult result;
  final bool isChecking;
  final DateTime? lastCheckedAt;
  final bool hasShownUpdateDialog;

  const VersionState({
    required this.result,
    this.isChecking = false,
    this.lastCheckedAt,
    this.hasShownUpdateDialog = false,
  });

  /// 初始状态
  factory VersionState.initial() {
    return const VersionState(
      result: VersionCheckResult(status: VersionCheckStatus.notChecked),
    );
  }

  /// 复制状态
  VersionState copyWith({
    VersionCheckResult? result,
    bool? isChecking,
    DateTime? lastCheckedAt,
    bool? hasShownUpdateDialog,
  }) {
    return VersionState(
      result: result ?? this.result,
      isChecking: isChecking ?? this.isChecking,
      lastCheckedAt: lastCheckedAt ?? this.lastCheckedAt,
      hasShownUpdateDialog: hasShownUpdateDialog ?? this.hasShownUpdateDialog,
    );
  }

  /// 是否有更新
  bool get hasUpdate => result.hasUpdate;

  /// 是否需要强制更新
  bool get needsForceUpdate => result.needsForceUpdate;

  /// 是否检查成功
  bool get isCheckSuccess => result.isSuccess;

  /// 是否检查失败
  bool get isCheckFailure => result.isFailure;

  /// 获取更新信息
  UpdateInfo? get updateInfo {
    if (result.response != null) {
      return UpdateInfo.fromVersionCheckResponse(result.response!);
    }
    return null;
  }

  @override
  String toString() {
    return 'VersionState(result: $result, isChecking: $isChecking, lastCheckedAt: $lastCheckedAt)';
  }
}

/// 版本状态管理器
class VersionNotifier extends StateNotifier<VersionState> {
  final VersionService _versionService;
  final StorageService _storageService;

  VersionNotifier(this._versionService, this._storageService) 
      : super(VersionState.initial()) {
    _loadCachedResult();
  }

  /// 加载缓存的检查结果
  Future<void> _loadCachedResult() async {
    try {
      final result = await _storageService.get<String>(AppConstants.lastVersionCheckKey);
      result.when(
        success: (data) {
          if (data != null) {
            final lastCheckedAt = DateTime.parse(data);
            state = state.copyWith(lastCheckedAt: lastCheckedAt);
          }
        },
        failure: (error) {
          print('VersionNotifier: 加载缓存结果失败: ${error.message}');
        },
      );
    } catch (e) {
      print('VersionNotifier: 加载缓存结果失败: $e');
    }
  }

  /// 检查版本更新
  Future<VersionCheckResult> checkVersion({bool force = false}) async {
    // 如果正在检查，直接返回当前结果
    if (state.isChecking && !force) {
      return state.result;
    }

    // 检查是否需要重新检查（距离上次检查超过指定间隔）
    if (!force && state.lastCheckedAt != null) {
      final timeSinceLastCheck = DateTime.now().difference(state.lastCheckedAt!);
      if (timeSinceLastCheck < AppConstants.versionCheckInterval) {
        print('VersionNotifier: 距离上次检查时间过短，跳过检查');
        return state.result;
      }
    }

    // 开始检查
    state = state.copyWith(
      isChecking: true,
      result: VersionCheckResult.checking(),
    );

    try {
      final result = await _versionService.checkVersion();
      final now = DateTime.now();

      // 更新状态
      state = state.copyWith(
        result: result,
        isChecking: false,
        lastCheckedAt: now,
      );

      // 缓存检查时间
      await _storageService.set(
        AppConstants.lastVersionCheckKey,
        now.toIso8601String(),
      );

      print('VersionNotifier: 版本检查完成: $result');
      return result;

    } catch (e) {
      final errorResult = VersionCheckResult.failure('版本检查失败: $e');
      
      state = state.copyWith(
        result: errorResult,
        isChecking: false,
        lastCheckedAt: DateTime.now(),
      );

      print('VersionNotifier: 版本检查失败: $e');
      return errorResult;
    }
  }

  /// 标记已显示更新对话框
  void markUpdateDialogShown() {
    state = state.copyWith(hasShownUpdateDialog: true);
  }

  /// 重置更新对话框显示状态
  void resetUpdateDialogShown() {
    state = state.copyWith(hasShownUpdateDialog: false);
  }

  /// 获取当前版本号
  String getCurrentVersion() {
    return _versionService.getCurrentVersion();
  }

  /// 获取最新版本号
  String? getLatestVersion() {
    return state.result.response?.latestVersion;
  }

  /// 获取版本更新类型
  VersionUpdateType getUpdateType() {
    final response = state.result.response;
    if (response == null) return VersionUpdateType.none;
    
    return _versionService.getUpdateType(
      response.currentVersion,
      response.latestVersion,
    );
  }

  /// 是否应该显示更新提示
  bool shouldShowUpdatePrompt() {
    return state.hasUpdate && 
           !state.hasShownUpdateDialog && 
           !state.isChecking;
  }

  /// 是否应该阻止应用使用（强制更新）
  bool shouldBlockApp() {
    return state.needsForceUpdate && state.isCheckSuccess;
  }

  /// 清除状态
  void clear() {
    state = VersionState.initial();
  }

  /// 重新检查版本
  Future<VersionCheckResult> recheckVersion() async {
    return await checkVersion(force: true);
  }
}

/// 版本服务提供者
final versionServiceProvider = Provider<VersionService>((ref) {
  return VersionService();
});

/// 版本状态提供者
final versionProvider = StateNotifierProvider<VersionNotifier, VersionState>((ref) {
  final versionService = ref.read(versionServiceProvider);
  final storageService = ref.read(storageServiceProvider);
  return VersionNotifier(versionService, storageService);
});

/// 版本检查结果提供者
final versionCheckResultProvider = Provider<VersionCheckResult>((ref) {
  return ref.watch(versionProvider).result;
});

/// 是否有更新提供者
final hasUpdateProvider = Provider<bool>((ref) {
  return ref.watch(versionProvider).hasUpdate;
});

/// 是否需要强制更新提供者
final needsForceUpdateProvider = Provider<bool>((ref) {
  return ref.watch(versionProvider).needsForceUpdate;
});

/// 更新信息提供者
final updateInfoProvider = Provider<UpdateInfo?>((ref) {
  return ref.watch(versionProvider).updateInfo;
});

/// 是否正在检查版本提供者
final isCheckingVersionProvider = Provider<bool>((ref) {
  return ref.watch(versionProvider).isChecking;
});

/// 是否应该显示更新提示提供者
final shouldShowUpdatePromptProvider = Provider<bool>((ref) {
  return ref.watch(versionProvider.notifier).shouldShowUpdatePrompt();
});

/// 是否应该阻止应用使用提供者
final shouldBlockAppProvider = Provider<bool>((ref) {
  return ref.watch(versionProvider.notifier).shouldBlockApp();
});

/// 版本更新类型提供者
final versionUpdateTypeProvider = Provider<VersionUpdateType>((ref) {
  return ref.watch(versionProvider.notifier).getUpdateType();
});
