@echo off
chcp 65001 >nul
echo ════════════════════════════════════════════
echo    命理AI聊天应用 - 开发运行脚本
echo ════════════════════════════════════════════
echo.

echo 🔄 步骤1: 生成超高质量图标...
python generate_high_quality_icon.py
if %errorlevel% neq 0 (
    echo ⚠️ 自定义图标生成失败，使用Flutter默认方式...
)

echo 🔄 步骤1.5: 运行Flutter图标生成器...
dart run flutter_launcher_icons
if %errorlevel% neq 0 (
    echo ❌ Flutter图标生成失败！
    pause
    exit /b 1
)
echo ✅ 图标生成完成

echo.
echo 🔄 步骤2: 获取依赖包...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ 获取依赖包失败！
    pause
    exit /b 1
)
echo ✅ 依赖包获取完成

echo.
echo 🔄 步骤3: 运行应用（开发模式）...
echo 💡 应用将在开发模式下运行，支持热重载
echo 📝 按 Ctrl+C 停止应用
echo.
flutter run -d windows
