import 'package:flutter_test/flutter_test.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';
import 'dart:io';
import 'dart:convert';

import 'package:numerology_ai_chat_new/src/services/chat_storage_service.dart';
import 'package:numerology_ai_chat_new/src/core/storage/storage_service.dart';
import 'package:numerology_ai_chat_new/src/models/conversation_model.dart';
import 'package:numerology_ai_chat_new/src/models/chat_message.dart';
import 'package:numerology_ai_chat_new/src/models/agent_model.dart';
import 'package:numerology_ai_chat_new/src/models/ai_model.dart';
import 'package:numerology_ai_chat_new/src/models/image_attachment.dart';

/// Mock PathProviderPlatform for testing
class MockPathProviderPlatform extends Fake
    with MockPlatformInterfaceMixin
    implements PathProviderPlatform {
  
  @override
  Future<String?> getApplicationDocumentsPath() async {
    return Directory.systemTemp.path;
  }
  
  @override
  Future<String?> getTemporaryPath() async {
    return Directory.systemTemp.path;
  }
}

void main() {
  group('ChatStorageService Tests', () {
    late ChatStorageService storageService;
    late HiveStorageService hiveService;
    late Directory tempDir;

    setUpAll(() async {
      // 设置mock path provider
      PathProviderPlatform.instance = MockPathProviderPlatform();
      
      // 创建临时目录
      tempDir = await Directory.systemTemp.createTemp('chat_storage_test');
      
      // 初始化Hive
      Hive.init(tempDir.path);
      
      // 创建存储服务
      hiveService = HiveStorageService();
      await hiveService.init();
      
      storageService = ChatStorageService(hiveService);
    });

    tearDownAll(() async {
      await hiveService.close();
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
      }
    });

    test('应该能够保存和加载对话', () async {
      // 创建测试对话
      final agent = AgentModel(
        id: 'test_agent',
        agentName: '测试智能体',
        description: '测试用智能体',
        agentType: AgentType.general,
        pricingTierId: 'test_tier',
        isActive: true,
        sortOrder: 1,
      );

      final model = AIModel(
        id: 'test_model',
        modelName: 'test-model',
        modelDisplayName: '测试模型',
        modelApiUrl: 'https://test.com/api',
        temperature: 0.7,
        maxTokens: 2000,
        isActive: true,
        sortOrder: 1,
        description: '测试模型描述',
      );

      final conversation = ConversationModel.create(
        agent: agent,
        model: model,
      );

      // 添加一些测试消息
      final userMessage = ChatMessage.user(content: '你好');
      final assistantMessage = ChatMessage.assistant(content: '你好！我是测试智能体。');
      
      final conversationWithMessages = conversation
          .addMessage(userMessage)
          .addMessage(assistantMessage);

      // 保存对话
      await storageService.saveConversation(conversationWithMessages);

      // 加载对话
      final loadedConversation = await storageService.loadConversation(conversation.id);

      // 验证
      expect(loadedConversation, isNotNull);
      expect(loadedConversation!.id, equals(conversation.id));
      expect(loadedConversation.title, equals(conversation.title));
      expect(loadedConversation.messages.length, equals(2));
      expect(loadedConversation.messages[0].content, equals('你好'));
      expect(loadedConversation.messages[1].content, equals('你好！我是测试智能体。'));
      expect(loadedConversation.selectedAgent?.id, equals('test_agent'));
      expect(loadedConversation.selectedModel?.id, equals('test_model'));
    });

    test('应该能够加载对话摘要', () async {
      // 创建多个测试对话
      final conversations = <ConversationModel>[];
      
      for (int i = 0; i < 3; i++) {
        final conversation = ConversationModel.create();
        final userMessage = ChatMessage.user(content: '测试消息 $i');
        final conversationWithMessage = conversation.addMessage(userMessage);
        conversations.add(conversationWithMessage);
        
        await storageService.saveConversation(conversationWithMessage);
        
        // 添加延迟确保时间戳不同
        await Future.delayed(const Duration(milliseconds: 10));
      }

      // 加载对话摘要
      final summaries = await storageService.loadConversationSummaries();

      // 验证
      expect(summaries.length, greaterThanOrEqualTo(3));
      
      // 验证按时间倒序排列
      for (int i = 0; i < summaries.length - 1; i++) {
        expect(
          summaries[i].updatedAt.isAfter(summaries[i + 1].updatedAt) ||
          summaries[i].updatedAt.isAtSameMomentAs(summaries[i + 1].updatedAt),
          isTrue,
        );
      }
    });

    test('应该能够删除对话', () async {
      // 创建测试对话
      final conversation = ConversationModel.create();
      await storageService.saveConversation(conversation);

      // 验证对话存在
      var loadedConversation = await storageService.loadConversation(conversation.id);
      expect(loadedConversation, isNotNull);

      // 删除对话
      await storageService.deleteConversation(conversation.id);

      // 验证对话已删除
      loadedConversation = await storageService.loadConversation(conversation.id);
      expect(loadedConversation, isNull);

      // 验证索引中也已删除
      final summaries = await storageService.loadConversationSummaries();
      final deletedSummary = summaries.where((s) => s.id == conversation.id);
      expect(deletedSummary, isEmpty);
    });

    test('应该能够处理图片附件', () async {
      // 创建带图片的消息
      final imageAttachment = ImageAttachment(
        id: 'test_image',
        base64Data: base64Encode([1, 2, 3, 4, 5]), // 模拟图片数据
        mimeType: 'image/png',
        fileSize: 5,
        width: 100,
        height: 100,
      );

      final userMessage = ChatMessage.userWithImages(
        images: [imageAttachment],
        content: '这是一张图片',
      );

      final conversation = ConversationModel.create().addMessage(userMessage);

      // 保存对话
      await storageService.saveConversation(conversation);

      // 加载对话
      final loadedConversation = await storageService.loadConversation(conversation.id);

      // 验证
      expect(loadedConversation, isNotNull);
      expect(loadedConversation!.messages.length, equals(1));
      expect(loadedConversation.messages[0].hasImages, isTrue);
      expect(loadedConversation.messages[0].images!.length, equals(1));
      expect(loadedConversation.messages[0].images![0].id, equals('test_image'));
      expect(loadedConversation.messages[0].images![0].base64Data, isNotNull);
    });

    test('应该能够限制对话数量', () async {
      // 这个测试需要修改常量，在实际应用中会自动限制
      // 这里只验证基本功能
      final summaries = await storageService.loadConversationSummaries();
      final initialCount = summaries.length;

      // 创建一个新对话
      final conversation = ConversationModel.create();
      await storageService.saveConversation(conversation);

      final newSummaries = await storageService.loadConversationSummaries();
      expect(newSummaries.length, equals(initialCount + 1));
    });

    test('应该能够处理空对话', () async {
      // 创建空对话
      final conversation = ConversationModel.create();

      // 保存空对话
      await storageService.saveConversation(conversation);

      // 加载对话
      final loadedConversation = await storageService.loadConversation(conversation.id);

      // 验证
      expect(loadedConversation, isNotNull);
      expect(loadedConversation!.messages, isEmpty);
    });

    test('应该能够处理不存在的对话', () async {
      // 尝试加载不存在的对话
      final loadedConversation = await storageService.loadConversation('non_existent_id');

      // 验证
      expect(loadedConversation, isNull);
    });
  });
}
