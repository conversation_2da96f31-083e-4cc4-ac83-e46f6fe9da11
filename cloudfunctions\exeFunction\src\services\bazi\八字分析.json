[{"name": "八字分析", "description": "根据用户提供的生辰八字信息进行分析。", "action": "baziAnalyze", "params": {"name": "string (姓名)", "gender": "string (性别: 男/女)", "birthDate": "string (出生日期: YYYY-MM-DD HH:MM:SS 或 YYYY-MM-DD HH:MM 或 YYYY-MM-DD)", "isLunar": "boolean (是否农历)"}, "example": {"action": "baziAnalyze", "params": {"name": "张三", "gender": "男", "birthDate": "1990-01-01 10:30:00", "isLunar": false}}}, {"action": "baziAnalyze", "params": {"name": "命主", "gender": "男", "calendarType": "农历", "birthDate": "1995-09-25", "birthTime": "17:55", "isLeapMonth": false, "birthPlace": "北京市 朝阳区"}}, {"action": "baziAnalyze", "params": {"name": "朱明", "gender": "男", "calendarType": "农历", "birthDate": "1985-09-16", "birthTime": "12:00", "isLeapMonth": false, "birthPlace": "北京市 朝阳区"}}]