import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:numerology_ai_chat_admin/src/models/config_model.dart';
import 'package:numerology_ai_chat_admin/src/services/admin_api_service.dart';
import 'package:numerology_ai_chat_admin/src/providers/auth_provider.dart';

class ConfigState {
  final List<SystemConfig>? configs;
  final bool isLoading;
  final String? error;
  final int total;
  final int currentPage;
  final int pageSize;

  ConfigState({
    this.configs,
    this.isLoading = false,
    this.error,
    this.total = 0,
    this.currentPage = 1,
    this.pageSize = 20,
  });

  ConfigState copyWith({
    List<SystemConfig>? configs,
    bool? isLoading,
    String? error,
    int? total,
    int? currentPage,
    int? pageSize,
  }) {
    return ConfigState(
      configs: configs ?? this.configs,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      total: total ?? this.total,
      currentPage: currentPage ?? this.currentPage,
      pageSize: pageSize ?? this.pageSize,
    );
  }
}

class ConfigNotifier extends StateNotifier<ConfigState> {
  final AdminApiService _apiService;
  final Ref _ref;

  ConfigNotifier(this._apiService, this._ref) : super(ConfigState());

  Future<void> fetchConfigs({
    String? category,
    bool? isActive,
    int page = 1,
    int pageSize = 20,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final authState = _ref.read(authProvider);
      if (authState.token == null) {
        throw Exception('未登录');
      }

      final response = await _apiService.getSystemConfigs(
        authState.token!,
        category: category,
        isActive: isActive,
        page: page,
        pageSize: pageSize,
      );

      if (response['code'] == 0) {
        final data = response['data']['data'];
        final List<dynamic> configList = data['configs'] ?? [];
        // 过滤掉不完整的数据记录
        final validConfigs = configList.where((json) {
          return json is Map<String, dynamic> &&
                 json.containsKey('configKey') &&
                 json.containsKey('configValue');
        }).toList();
        final configs = validConfigs.map((json) => SystemConfig.fromJson(json)).toList();

        state = state.copyWith(
          configs: configs,
          isLoading: false,
          total: data['pagination']['total'] ?? 0,
          currentPage: page,
          pageSize: pageSize,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response['message'] ?? '获取系统配置列表失败',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<bool> createConfig(CreateConfigRequest request) async {
    try {
      final authState = _ref.read(authProvider);
      if (authState.token == null) {
        throw Exception('未登录');
      }

      final response = await _apiService.createSystemConfig(
        authState.token!,
        request.toJson(),
      );

      if (response['code'] == 0) {
        // 刷新配置列表
        await fetchConfigs(
          page: state.currentPage,
          pageSize: state.pageSize,
        );
        return true;
      } else {
        state = state.copyWith(error: response['message'] ?? '创建系统配置失败');
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  Future<bool> updateConfig(String configId, UpdateConfigRequest request) async {
    try {
      final authState = _ref.read(authProvider);
      if (authState.token == null) {
        throw Exception('未登录');
      }

      final response = await _apiService.updateSystemConfig(
        authState.token!,
        configId,
        request.toJson(),
      );

      if (response['code'] == 0) {
        // 刷新配置列表
        await fetchConfigs(
          page: state.currentPage,
          pageSize: state.pageSize,
        );
        return true;
      } else {
        state = state.copyWith(error: response['message'] ?? '更新系统配置失败');
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  Future<bool> deleteConfig(String configId) async {
    try {
      final authState = _ref.read(authProvider);
      if (authState.token == null) {
        throw Exception('未登录');
      }

      final response = await _apiService.deleteSystemConfig(authState.token!, configId);

      if (response['code'] == 0) {
        // 刷新配置列表
        await fetchConfigs(
          page: state.currentPage,
          pageSize: state.pageSize,
        );
        return true;
      } else {
        state = state.copyWith(error: response['message'] ?? '删除系统配置失败');
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

final configProvider = StateNotifierProvider<ConfigNotifier, ConfigState>((ref) {
  return ConfigNotifier(AdminApiService(), ref);
});
