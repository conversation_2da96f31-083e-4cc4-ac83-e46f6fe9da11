import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:numerology_ai_chat_admin/src/models/overall_stats_model.dart';
import 'package:numerology_ai_chat_admin/src/services/admin_api_service.dart';
import 'package:numerology_ai_chat_admin/src/providers/auth_provider.dart';

class StatsState {
  final OverallStatsModel? stats;
  final bool isLoading;
  final String? error;
  final DateTime? lastUpdated;

  StatsState({
    this.stats,
    this.isLoading = false,
    this.error,
    this.lastUpdated,
  });

  StatsState copyWith({
    OverallStatsModel? stats,
    bool? isLoading,
    String? error,
    DateTime? lastUpdated,
  }) {
    return StatsState(
      stats: stats ?? this.stats,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

class StatsNotifier extends StateNotifier<StatsState> {
  final AdminApiService _apiService;
  final Ref _ref;
  Timer? _refreshTimer;

  StatsNotifier(this._apiService, this._ref) : super(StatsState()) {
    // 启动自动刷新
    _startAutoRefresh();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  void _startAutoRefresh() {
    // 每30秒自动刷新一次
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      fetchStats();
    });
  }

  Future<void> fetchStats() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final authState = _ref.read(authProvider);
      if (authState.token == null) {
        throw Exception('未登录');
      }

      final response = await _apiService.getOverallStats(authState.token!);

      if (response['code'] == 0) {
        final data = response['data']['data'];
        final stats = OverallStatsModel.fromJson(data);

        state = state.copyWith(
          stats: stats,
          isLoading: false,
          lastUpdated: DateTime.now(),
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response['message'] ?? '获取统计数据失败',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  void refreshStats() {
    fetchStats();
  }
}

final statsProvider = StateNotifierProvider<StatsNotifier, StatsState>((ref) {
  return StatsNotifier(AdminApiService(), ref);
});
