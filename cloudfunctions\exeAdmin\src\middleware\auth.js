const jwt = require('jsonwebtoken')
const { createBusinessError, ERROR_CODES } = require('../utils/error_handler')
const { adminCollection } = require('../utils/db')
const logger = require('../utils/logger')

// JWT密钥，实际使用时应从环境变量获取
const JWT_SECRET = process.env.JWT_SECRET || 'your-admin-secret-key'
const ACCESS_TOKEN_EXPIRES_IN = '2h' // 管理员token有效期较短
const REFRESH_TOKEN_EXPIRES_IN = '7d'

/**
 * 生成访问令牌
 * @param {object} payload 载荷数据
 * @returns {string} JWT令牌
 */
function generateAccessToken(payload) {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: ACCESS_TOKEN_EXPIRES_IN,
    issuer: 'exeAdmin'
  })
}

/**
 * 生成刷新令牌
 * @param {object} payload 载荷数据
 * @returns {string} JWT令牌
 */
function generateRefreshToken(payload) {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: REFRESH_TOKEN_EXPIRES_IN,
    issuer: 'exeAdmin'
  })
}

/**
 * 验证访问令牌
 * @param {string} token JWT令牌
 * @returns {object} 解码后的载荷
 */
function verifyAccessToken(token) {
  try {
    return jwt.verify(token, JWT_SECRET, {
      issuer: 'exeAdmin'
    })
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw createBusinessError(ERROR_CODES.TOKEN_EXPIRED, 'Token已过期', 401)
    } else if (error.name === 'JsonWebTokenError') {
      throw createBusinessError(ERROR_CODES.TOKEN_INVALID, 'Token无效', 401)
    } else {
      throw createBusinessError(ERROR_CODES.TOKEN_INVALID, 'Token验证失败', 401)
    }
  }
}

/**
 * 验证刷新令牌
 * @param {string} token JWT令牌
 * @returns {object} 解码后的载荷
 */
function verifyRefreshToken(token) {
  try {
    return jwt.verify(token, JWT_SECRET, {
      issuer: 'exeAdmin'
    })
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw createBusinessError(ERROR_CODES.TOKEN_EXPIRED, 'Refresh Token已过期', 401)
    } else if (error.name === 'JsonWebTokenError') {
      throw createBusinessError(ERROR_CODES.TOKEN_INVALID, 'Refresh Token无效', 401)
    } else {
      throw createBusinessError(ERROR_CODES.TOKEN_INVALID, 'Refresh Token验证失败', 401)
    }
  }
}

/**
 * 管理员鉴权中间件
 * @param {object} event 云函数事件对象
 * @returns {object} 鉴权结果
 */
async function adminAuthMiddleware(event) {
  try {
    // 从请求头或参数获取token
    let token = event.token; // 直接从参数获取

    if (!token) {
      // 如果参数中没有，尝试从请求头获取
      const authorization = event.headers?.authorization || event.headers?.Authorization
      if (authorization && authorization.startsWith('Bearer ')) {
        token = authorization.substring(7);
      }
    }

    if (!token) {
      throw createBusinessError(ERROR_CODES.UNAUTHORIZED, '缺少token参数或Authorization头', 401)
    }

    
    // 验证token
    const decoded = verifyAccessToken(token)
    
    // 检查管理员是否存在且状态正常
    const admin = await adminCollection.findById(decoded.adminId)
    if (!admin) {
      throw createBusinessError(ERROR_CODES.ADMIN_NOT_FOUND, '管理员不存在', 401)
    }
    
    if (!admin.isActive) {
      throw createBusinessError(ERROR_CODES.UNAUTHORIZED, '管理员账号已被禁用', 401)
    }
    
    // 记录访问日志
    logger.info('管理员访问', {
      adminId: admin._id,
      adminAccount: admin.adminAccount,
      action: event.action || 'unknown',
      ip: event.headers?.['x-real-ip'] || event.headers?.['x-forwarded-for'] || 'unknown'
    })
    
    // 返回管理员信息
    return {
      adminId: admin._id,
      adminAccount: admin.adminAccount,
      adminRole: admin.adminRole,
      adminPermissions: admin.adminPermissions || []
    }
    
  } catch (error) {
    logger.error('管理员鉴权失败', {
      error: error.message,
      action: event.action || 'unknown'
    })
    throw error
  }
}

/**
 * 生成Token对
 * @param {object} adminInfo 管理员信息
 * @returns {object} Token对象
 */
function generateTokenPair(adminInfo) {
  const payload = {
    adminId: adminInfo._id,
    adminAccount: adminInfo.adminAccount,
    adminRole: adminInfo.adminRole
  }
  
  const accessToken = generateAccessToken(payload)
  const refreshToken = generateRefreshToken({ adminId: adminInfo._id })
  
  return {
    accessToken,
    refreshToken,
    expiresIn: ACCESS_TOKEN_EXPIRES_IN
  }
}

/**
 * 检查管理员权限
 * @param {array|string} adminPermissionsOrRole 管理员权限列表或角色
 * @param {string} requiredPermission 需要的权限
 * @returns {boolean} 是否有权限
 */
function checkPermission(adminPermissionsOrRole, requiredPermission) {
  // 如果是字符串，说明传入的是角色
  if (typeof adminPermissionsOrRole === 'string') {
    // 超级管理员拥有所有权限
    return adminPermissionsOrRole === '超级管理员'
  }

  // 如果是数组，检查权限列表
  if (Array.isArray(adminPermissionsOrRole)) {
    // 超级管理员拥有所有权限
    if (adminPermissionsOrRole.includes('super_admin')) {
      return true
    }
    // 检查具体权限
    return adminPermissionsOrRole.includes(requiredPermission)
  }

  return false
}

module.exports = {
  generateAccessToken,
  generateRefreshToken,
  verifyAccessToken,
  verifyRefreshToken,
  adminAuthMiddleware,
  generateTokenPair,
  checkPermission
}