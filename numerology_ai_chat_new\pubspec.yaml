name: numerology_ai_chat
description: "命理AI聊天桌面应用 - 专业的命理分析与AI对话平台"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.2+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI & Theme
  cupertino_icons: ^1.0.8

  # State Management
  provider: ^6.1.1

  # HTTP & Network
  http: ^1.1.0
  dio: ^5.4.0

  # Window Management
  window_manager: ^0.3.7

  # Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.4
  path: ^1.9.0

  # Date & Time
  intl: ^0.19.0

  # Icons
  lucide_icons: ^0.257.0

  # UUID
  uuid: ^4.2.1

  # Routing
  go_router: ^14.2.7

  # State Management (Enhanced)
  riverpod: ^2.5.1
  flutter_riverpod: ^2.5.1

  # UI Enhancements
  flutter_animate: ^4.5.0
  gap: ^3.0.1
  responsive_framework: ^1.4.0

  # Utilities
  freezed_annotation: ^2.4.4
  json_annotation: ^4.9.0

  # Markdown Rendering
  flutter_markdown: ^0.7.3

  # Logging
  logger: ^2.4.0

  # URL Launcher
  url_launcher: ^6.3.1

  # Collection Algorithms
  collection: ^1.18.0

  # Image Processing
  image_picker: ^1.0.4
  image: ^4.1.3
  mime: ^1.0.4
  qr_flutter: ^4.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

  # Icon Generation
  flutter_launcher_icons: ^0.13.1

  # Code Generation
  build_runner: ^2.4.12
  freezed: ^2.5.7
  json_serializable: ^6.8.0
  hive_generator: ^2.0.1

  # Testing
  mockito: ^5.4.4
  integration_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/fonts/
    - assets/images/
    - assets/data/china_address.json

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # 霞鹜文楷字体配置
  fonts:
    - family: LXGWWenKai
      fonts:
        - asset: assets/fonts/LXGWWenKai-Light.ttf
          weight: 300
        - asset: assets/fonts/LXGWWenKai-Regular.ttf
          weight: 400
        - asset: assets/fonts/LXGWWenKai-Medium.ttf
          weight: 500

# Flutter Launcher Icons 配置
# 自动生成超高质量应用图标，支持多平台和高DPI
flutter_launcher_icons:
  android: false  # 不生成Android图标
  ios: false      # 不生成iOS图标
  windows:
    generate: true
    image_path: "assets/images/logo.png"
    icon_size: 512 # 超高质量Windows图标大小（支持4K显示器）

  # 通用配置（作为备用）
  image_path: "assets/images/logo.png"
