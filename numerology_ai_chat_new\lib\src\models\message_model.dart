import 'package:freezed_annotation/freezed_annotation.dart';

part 'message_model.freezed.dart';
part 'message_model.g.dart';

/// 消息角色枚举
enum MessageRole {
  user,
  assistant,
  system,
}

/// 消息状态枚举
enum MessageStatus {
  sending,
  sent,
  failed,
  typing,
}

/// 消息模型
@freezed
class MessageModel with _$MessageModel {
  const factory MessageModel({
    required String id,
    required MessageRole role,
    required String content,
    @Default(MessageStatus.sent) MessageStatus status,
    @Default(false) bool isTyping,
    DateTime? createdAt,
    String? agentId,
    String? baziData,
    Map<String, dynamic>? metadata,
  }) = _MessageModel;

  factory MessageModel.fromJson(Map<String, dynamic> json) =>
      _$MessageModelFromJson(json);

  /// 创建用户消息
  factory MessageModel.user({
    required String content,
    String? agentId,
    String? baziData,
  }) {
    return MessageModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      role: MessageRole.user,
      content: content,
      status: MessageStatus.sent,
      createdAt: DateTime.now(),
      agentId: agentId,
      baziData: baziData,
    );
  }

  /// 创建助手消息
  factory MessageModel.assistant({
    required String content,
    MessageStatus status = MessageStatus.sent,
    bool isTyping = false,
    String? agentId,
  }) {
    return MessageModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      role: MessageRole.assistant,
      content: content,
      status: status,
      isTyping: isTyping,
      createdAt: DateTime.now(),
      agentId: agentId,
    );
  }

  /// 创建系统消息
  factory MessageModel.system({
    required String content,
  }) {
    return MessageModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      role: MessageRole.system,
      content: content,
      status: MessageStatus.sent,
      createdAt: DateTime.now(),
    );
  }

  /// 创建正在输入的消息
  factory MessageModel.typing({
    String? agentId,
  }) {
    return MessageModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      role: MessageRole.assistant,
      content: '',
      status: MessageStatus.typing,
      isTyping: true,
      createdAt: DateTime.now(),
      agentId: agentId,
    );
  }
}

/// 消息模型扩展
extension MessageModelExtension on MessageModel {
  /// 是否为用户消息
  bool get isUser => role == MessageRole.user;

  /// 是否为助手消息
  bool get isAssistant => role == MessageRole.assistant;

  /// 是否为系统消息
  bool get isSystem => role == MessageRole.system;

  /// 是否正在发送
  bool get isSending => status == MessageStatus.sending;

  /// 是否发送成功
  bool get isSent => status == MessageStatus.sent;

  /// 是否发送失败
  bool get isFailed => status == MessageStatus.failed;

  /// 是否为空消息
  bool get isEmpty => content.trim().isEmpty;

  /// 获取显示时间
  String get displayTime {
    if (createdAt == null) return '';
    
    final now = DateTime.now();
    final diff = now.difference(createdAt!);
    
    if (diff.inMinutes < 1) {
      return '刚刚';
    } else if (diff.inHours < 1) {
      return '${diff.inMinutes}分钟前';
    } else if (diff.inDays < 1) {
      return '${diff.inHours}小时前';
    } else if (diff.inDays < 7) {
      return '${diff.inDays}天前';
    } else {
      return '${createdAt!.month}月${createdAt!.day}日';
    }
  }

  /// 获取格式化时间
  String get formattedTime {
    if (createdAt == null) return '';
    
    final time = createdAt!;
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  /// 获取消息预览（用于对话列表）
  String get preview {
    if (content.length <= 50) return content;
    return '${content.substring(0, 50)}...';
  }

  /// 获取角色显示名称
  String get roleDisplayName {
    switch (role) {
      case MessageRole.user:
        return '用户';
      case MessageRole.assistant:
        return '助手';
      case MessageRole.system:
        return '系统';
    }
  }

  /// 获取状态显示名称
  String get statusDisplayName {
    switch (status) {
      case MessageStatus.sending:
        return '发送中';
      case MessageStatus.sent:
        return '已发送';
      case MessageStatus.failed:
        return '发送失败';
      case MessageStatus.typing:
        return '正在输入';
    }
  }

  /// 是否可以重试
  bool get canRetry => isFailed && isUser;

  /// 是否可以复制
  bool get canCopy => content.isNotEmpty && !isTyping;

  /// 是否可以删除
  bool get canDelete => !isTyping;

  /// 获取消息字数
  int get wordCount => content.length;

  /// 是否为长消息
  bool get isLongMessage => content.length > 200;

  /// 获取消息类型图标
  String get typeIcon {
    switch (role) {
      case MessageRole.user:
        return '👤';
      case MessageRole.assistant:
        return '🤖';
      case MessageRole.system:
        return 'ℹ️';
    }
  }
}
