# 经销商渠道系统实现方案

## 一、需求概述

为了引入经销商渠道，需要对现有的用户注册和算力管理流程进行重大调整：

1. **经销商管理**：经销商通过线下付款批量采购算力，后台手动为其渠道账户增加额度
2. **邀请码注册制**：用户必须使用邀请码才能注册，邀请码包含渠道归属信息
3. **渠道绑定**：用户注册后自动绑定对应渠道，显示服务到期时间
4. **算力划转**：经销商通过专门的渠道端程序将自己的算力划转给渠道下的用户
5. **充值权限控制**：根据渠道类型和经销商余额动态控制用户的官方充值权限
6. **多端架构**：包含用户端、管理端、渠道端三个独立的应用程序

## 二、数据库设计调整

### 2.1 新增集合

#### exe_channels（渠道表）
仅存储经销商渠道信息：
- channelId：渠道唯一标识
- channelName：渠道名称（如"张三代理商"）
- channelAccount：渠道登录账号
- channelPassword：渠道登录密码（加密存储）
- contactInfo：联系信息（经销商姓名、电话、微信等）
- availableQuota：渠道可用算力余额
- totalPurchased：累计采购算力
- status：渠道状态（"active"激活、"suspended"暂停、"disabled"禁用）
- autoOpenRechargeThreshold：自动开放官方充值的阈值（默认8000）
- refreshToken：渠道端登录刷新令牌
- refreshTokenExpiresAt：刷新令牌过期时间
- lastLoginAt：最后登录时间
- createdAt、updatedAt：创建和更新时间
- createdBy：创建者（管理员账号）

**说明**：
- 此表只存储经销商渠道，不存储官方渠道
- 官方渠道用固定字符串"official"表示

#### exe_used_invitation_codes（已核销邀请码表）
存储已核销的邀请码记录：
- inviteCode：邀请码字符串
- channelId：关联的渠道ID（经销商渠道ID或"official"表示官方渠道）
- userId：使用该邀请码注册的用户ID
- usedAt：核销时间
- userIP：用户注册时的IP地址（防刷用）
- createdBy：邀请码创建者（经销商账号或"admin"表示管理员）

#### exe_quota_transfers（算力划转记录表）
记录算力划转历史：
- transferId：划转记录ID
- fromChannelId：源渠道ID（经销商）
- toUserId：目标用户ID
- quotaAmount：划转算力数量
- transferType：划转类型（"channel_to_user"渠道到用户、"user_to_user"用户间划转）
- transferReason：划转原因
- status：划转状态（"pending"待处理、"completed"已完成、"failed"失败）
- transferredAt：划转时间
- operatorId：操作者ID（经销商或管理员）

### 2.2 现有集合调整

#### exe_users（用户表）
新增字段：
- channelId：关联的渠道ID（经销商渠道ID或"official"表示官方渠道）
- inviteCode：注册时使用的邀请码
- serviceExpiresAt：服务到期时间（根据渠道政策计算）
- allowOfficialRecharge：是否允许使用官方充值（官方渠道用户默认true，经销商渠道用户根据经销商余额动态调整）
- registrationSource：注册来源（"invite_code"邀请码注册）

#### exe_admins（管理员表）
保持现有结构，不需要修改。经销商账号独立存储在渠道表中，不与管理员表混合。

## 三、系统架构设计

### 3.1 多端应用架构

#### 用户端（numerology_ai_chat_new）
- 现有的Flutter桌面应用
- 功能：用户注册（邀请码）、AI聊天、个人中心、购买套餐
- 用户群体：终端用户

#### 管理端（numerology_ai_chat_admin）
- 新开发的Flutter桌面应用
- 功能：用户管理、渠道管理、智能体管理、模型管理、系统配置、官方邀请码生成、用户算力直接赠送/修改
- 用户群体：平台管理员

#### 渠道端（numerology_ai_chat_dealer）
- 新开发的Flutter桌面应用
- 功能：渠道登录、用户列表查看、算力划转、邀请码生成、统计报表
- 用户群体：经销商（仅限有算力余额的渠道）

### 3.2 云函数架构调整

#### exeFunction（用户端云函数）
- 保持现有功能
- 新增邀请码验证和注册功能

#### exeAdmin（管理端云函数）
- 保持现有功能
- 新增渠道管理功能
- 新增官方邀请码生成功能
- 新增用户算力直接赠送/修改功能

#### exeDealer（渠道端云函数）
- 新增云函数，专门服务渠道端
- 功能：经销商渠道登录、算力划转、用户管理、邀请码生成、统计查询

## 四、功能实现方案

### 4.1 邀请码生成与管理

#### 邀请码生成规则
- 官方和经销商都可以创建任意格式的邀请码（建议8-12位）
- 邀请码通过云函数中的加密算法验证有效性
- 使用渠道ID+时间戳+秘钥生成签名，确保邀请码真实性
- 邀请码格式：渠道标识+随机码+签名（如"OF001ABC123XYZ"官方、"DL001ABC123XYZ"经销商）

#### 邀请码验证机制
- 云函数接收邀请码后，解析渠道ID和签名
- 使用服务端秘钥验证签名有效性
- 检查渠道是否存在且状态为激活
- 检查邀请码是否已被核销（查询已核销表）
- 验证通过后允许注册，并将邀请码记录到已核销表

#### 邀请码生成权限
- 官方邀请码：管理员通过管理端生成，用于官方渠道用户注册
- 经销商邀请码：经销商通过渠道端程序自主生成，用于经销商渠道用户注册
- 管理员可以查看所有渠道的邀请码使用情况
- 支持邀请码使用统计和查询
- 可以禁用特定渠道的邀请码（通过渠道状态控制）

### 4.2 渠道账号管理

#### 渠道管理规则
- 官方渠道：用固定字符串"official"表示，不在渠道表中存储
- 经销商渠道：管理员创建，存储在渠道表中，有账号密码和算力余额

#### 经销商渠道登录认证
- 渠道端程序独立的登录界面
- 使用渠道账号和密码进行认证
- 支持token自动续签机制
- 登录状态管理和安全控制

#### 官方渠道管理
- 官方渠道不需要登录渠道端
- 管理员通过管理端直接管理官方渠道用户
- 管理员可以直接给任何用户赠送/修改算力（不受算力余额限制）

### 4.3 用户注册流程改造

#### 注册页面调整
- 移除原有的直接注册功能
- 新增邀请码输入框（必填）
- 邀请码验证：检查签名有效性、渠道状态、是否已核销
- 显示邀请码关联的渠道信息和服务说明

#### 注册逻辑调整
- 验证邀请码有效性（签名验证+渠道检查）
- 检查邀请码是否已被使用
- 获取邀请码关联的渠道信息
- 创建用户时自动绑定渠道ID和相关权限
- 用户初始算力为0，需要经销商后续划转
- 将邀请码记录到已核销表中
- 计算并设置服务到期时间

### 4.4 渠道端功能设计（仅限经销商渠道）

#### 主要功能模块
- 经销商渠道登录和认证
- 渠道信息查看和基本设置
- 下属用户列表查看
- 算力划转操作（从渠道余额转给用户）
- 邀请码生成工具
- 统计报表查看

#### 用户管理功能
- 查看本渠道下所有注册用户
- 查看用户基本信息和算力余额
- 查看用户使用情况统计
- 搜索和筛选用户

#### 算力管理功能
- 查看渠道算力余额
- 向指定用户划转算力（扣除渠道余额）
- 查看算力划转历史
- 算力使用统计分析

**注意**：官方渠道不使用渠道端，官方的所有操作都在管理端完成

### 4.5 管理端功能扩展

#### 渠道管理功能
- 创建官方渠道和经销商渠道
- 设置渠道基本信息和联系方式
- 配置经销商渠道的账号密码和算力余额
- 渠道状态管理（激活、暂停、禁用）
- 重置经销商渠道密码

#### 算力管理功能
- 为经销商渠道增加算力余额（线下付款确认后）
- 直接给任何用户赠送/修改算力（官方权限，不受余额限制）
- 记录所有算力变动历史
- 查看所有渠道的算力余额和使用情况
- 支持算力有效期管理

#### 邀请码管理功能
- 生成官方邀请码
- 查看所有渠道的邀请码使用记录
- 邀请码使用统计分析

### 4.6 算力操作功能

#### 经销商算力划转（渠道端）
- 经销商通过渠道端程序登录
- 查看渠道下的所有用户列表
- 选择用户进行算力划转
- 输入划转数量和原因
- 系统验证渠道余额是否充足
- 执行划转（扣除渠道余额，增加用户算力）并记录操作日志

#### 官方算力赠送（管理端）
- 管理员通过管理端直接操作
- 可以给任何用户赠送/增加/扣除算力
- 不受算力余额限制（官方权限）
- 记录操作日志和原因

#### 操作记录查询
- 用户可在个人中心查看算力到账记录
- 经销商可在渠道端查看自己的划转历史
- 管理员可在管理端查看全平台所有算力操作记录

### 4.7 充值权限动态控制

#### 权限判断逻辑
- 官方渠道用户（channelId="official"）：始终允许官方充值（allowOfficialRecharge=true）
- 经销商渠道用户（channelId为具体渠道ID）：
  - 默认情况：不允许官方充值（allowOfficialRecharge=false）
  - 特殊情况：经销商算力余额低于阈值时，自动开放官方充值（allowOfficialRecharge=true）
  - 手动控制：管理员可手动开放/关闭特定用户的充值权限

#### 实时权限更新
- 经销商算力变动时，自动检查是否需要调整下属用户的充值权限
- 权限变更时发送通知给相关用户
- 在购买页面实时显示当前可用的充值方式

#### 权限控制规则
- 用户的allowOfficialRecharge字段决定是否能使用官方充值
- 官方渠道用户（channelId="official"）注册时自动设置为true
- 经销商渠道用户注册时自动设置为false
- 系统定期检查经销商余额，自动调整下属用户权限

## 五、技术实现要点

### 5.1 云函数接口扩展

#### exeFunction新增接口
- validateInviteCode：验证邀请码有效性（签名验证+渠道检查）
- registerWithInviteCode：使用邀请码注册
- getUserChannelInfo：获取用户渠道信息
- getQuotaTransferHistory：获取算力划转记录

#### exeAdmin新增接口
- createChannel：创建经销商渠道
- manageChannel：管理经销商渠道信息
- getChannelList：获取经销商渠道列表
- getUsedInviteCodes：查看已核销邀请码记录
- getChannelStatistics：获取渠道统计信息
- addChannelQuota：为经销商渠道增加算力
- generateOfficialInviteCode：生成官方邀请码
- grantUserQuota：直接给用户赠送/修改算力（官方权限）

#### exeDealer新增云函数
- dealerLogin：经销商渠道登录认证
- refreshDealerToken：刷新渠道token
- getDealerInfo：获取渠道信息
- getChannelUsers：获取渠道下用户列表
- transferQuotaToUser：划转算力给用户（扣除渠道余额）
- generateDealerInviteCode：生成经销商邀请码
- getDealerStatistics：获取渠道统计数据

### 5.2 前端应用开发

#### 用户端（numerology_ai_chat_new）调整
- 注册页面：添加邀请码输入和验证
- 个人中心：显示渠道归属信息和服务到期时间
- 购买页面：根据用户权限显示可用充值方式
- 新增算力到账记录查询功能

#### 管理端（numerology_ai_chat_admin）新增功能
- 经销商渠道管理模块：创建、编辑经销商渠道
- 经销商渠道算力管理：查看余额、增加算力、使用统计
- 官方邀请码生成工具
- 用户算力直接赠送/修改功能（官方权限，不受余额限制）
- 邀请码使用记录查询（包括官方和经销商邀请码）
- 全平台统计报表功能

#### 渠道端（numerology_ai_chat_dealer）全新开发
- 经销商渠道登录界面
- 渠道信息展示和基本设置
- 下属用户列表查看和搜索
- 算力划转操作界面（从渠道余额转给用户）
- 经销商邀请码生成工具
- 统计报表查看

**注意**：官方渠道不使用渠道端程序

### 5.3 数据安全考虑

#### 权限控制
- 渠道端只能管理自己渠道下的用户
- 严格的API权限验证和身份认证
- 敏感操作需要二次确认
- 渠道间数据完全隔离

#### 数据完整性
- 算力划转的原子性操作
- 邀请码使用次数的并发控制
- 关键操作的审计日志

## 六、实施步骤建议

### 第一阶段：数据库设计
1. 创建新的数据库集合
2. 为现有集合添加新字段
3. 创建必要的索引
4. 数据迁移脚本

### 第二阶段：后端接口开发
1. 开发exeDealer云函数（渠道端专用）
2. 扩展exeAdmin云函数（渠道管理功能）
3. 扩展exeFunction云函数（邀请码注册）
4. 开发算力划转接口
5. 开发邀请码验证和生成接口

### 第三阶段：前端应用开发
1. 改造用户端注册页面（添加邀请码输入）
2. 更新用户端个人中心（显示渠道信息）
3. 调整用户端购买页面（权限控制）
4. 开发管理端渠道管理功能
5. 全新开发渠道端应用程序

### 第四阶段：测试与优化
1. 功能测试
2. 性能测试
3. 安全测试
4. 用户体验优化

## 七、风险评估与应对

### 技术风险
- 数据迁移可能影响现有用户
- 多端应用开发和维护复杂度增加
- 复杂的权限逻辑可能出现漏洞
- 并发操作可能导致数据不一致

### 业务风险
- 经销商管理复杂度大幅增加
- 用户体验可能受到影响
- 算力划转可能被滥用
- 渠道端程序的安全性要求更高

### 应对措施
- 充分的测试和灰度发布
- 完善的监控和告警机制
- 详细的操作日志和审计功能
- 用户教育和客服支持

这个方案通过三端分离的架构设计，实现了完整的经销商渠道管理体系。虽然需要开发新的应用程序，但各端职责清晰，便于维护和扩展，能够满足复杂的业务需求。
