/// 购买历史记录模型
class PurchaseHistoryModel {
  final String id;
  final String orderNo;
  final String packageName;
  final int packageQuota;
  final int orderAmount;
  final String status;
  final DateTime createTime;
  final DateTime? payTime;
  final String? transactionId;
  final bool isAdminRecharge; // 是否为管理员充值
  final String? operator; // 操作者
  final String? reason; // 充值原因

  const PurchaseHistoryModel({
    required this.id,
    required this.orderNo,
    required this.packageName,
    required this.packageQuota,
    required this.orderAmount,
    required this.status,
    required this.createTime,
    this.payTime,
    this.transactionId,
    this.isAdminRecharge = false,
    this.operator,
    this.reason,
  });

  /// 从JSON创建购买历史模型
  factory PurchaseHistoryModel.fromJson(Map<String, dynamic> json) {
    return PurchaseHistoryModel(
      id: json['_id'] as String? ?? json['id'] as String,
      orderNo: json['orderNo'] as String,
      packageName: json['packageName'] as String,
      packageQuota: json['packageQuota'] as int,
      orderAmount: json['orderAmount'] as int,
      status: json['status'] as String,
      createTime: DateTime.parse(json['createTime'] as String),
      payTime: json['payTime'] != null
          ? DateTime.parse(json['payTime'] as String)
          : null,
      transactionId: json['transactionId'] as String?,
      isAdminRecharge: json['isAdminRecharge'] as bool? ?? false,
      operator: json['operator'] as String?,
      reason: json['reason'] as String?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'orderNo': orderNo,
      'packageName': packageName,
      'packageQuota': packageQuota,
      'orderAmount': orderAmount,
      'status': status,
      'createTime': createTime.toIso8601String(),
      'payTime': payTime?.toIso8601String(),
      'transactionId': transactionId,
      'isAdminRecharge': isAdminRecharge,
      'operator': operator,
      'reason': reason,
    };
  }

  /// 获取状态显示文本
  String get statusDisplayText {
    switch (status) {
      case 'PENDING':
        return '待支付';
      case 'PAID':
        return '已支付';
      case 'COMPLETED':
        return '已完成';
      case 'CANCELLED':
        return '已取消';
      case 'REFUNDED':
        return '已退款';
      default:
        return '未知状态';
    }
  }

  /// 获取状态颜色
  String get statusColor {
    switch (status) {
      case 'PENDING':
        return 'orange';
      case 'PAID':
      case 'COMPLETED':
        return 'green';
      case 'CANCELLED':
      case 'REFUNDED':
        return 'red';
      default:
        return 'grey';
    }
  }

  /// 格式化金额显示（分转元）
  String get formattedAmount {
    return '¥${(orderAmount / 100).toStringAsFixed(2)}';
  }

  /// 格式化创建时间
  String get formattedCreateTime {
    return '${createTime.year}-${createTime.month.toString().padLeft(2, '0')}-${createTime.day.toString().padLeft(2, '0')} ${createTime.hour.toString().padLeft(2, '0')}:${createTime.minute.toString().padLeft(2, '0')}';
  }

  /// 格式化支付时间
  String? get formattedPayTime {
    if (payTime == null) return null;
    return '${payTime!.year}-${payTime!.month.toString().padLeft(2, '0')}-${payTime!.day.toString().padLeft(2, '0')} ${payTime!.hour.toString().padLeft(2, '0')}:${payTime!.minute.toString().padLeft(2, '0')}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PurchaseHistoryModel &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PurchaseHistoryModel{id: $id, orderNo: $orderNo, packageName: $packageName, status: $status}';
  }
}
