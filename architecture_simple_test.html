<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>架构图测试</title>
    <style>
        body { 
            font-family: 'Microsoft YaHei', sans-serif; 
            margin: 20px;
            background-color: #f5f5f5;
        }
        .mermaid { 
            text-align: center; 
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <h1>Mermaid测试</h1>
    <div class="mermaid">
graph TD
    A[Flutter客户端] --> B[Go代理服务]
    B --> C[云函数]
    C --> D[数据库]
    A --> C
    
    classDef flutter fill:#e0f7fa,stroke:#00796b,stroke-width:2px;
    classDef go fill:#e8eaf6,stroke:#303f9f,stroke-width:2px;
    classDef nodejs fill:#fbe9e7,stroke:#d84315,stroke-width:2px;
    classDef db fill:#f9fbe7,stroke:#f9a825,stroke-width:2px;
    
    class A flutter;
    class B go;
    class C nodejs;
    class D db;
    </div>

    <script src="https://unpkg.com/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose'
        });
    </script>
</body>
</html>
