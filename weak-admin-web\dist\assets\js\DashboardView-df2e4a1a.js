import{u as m,a as b,b as w,o as h,c as _,d as A,e as y,f as s,t as r,i,g as o,w as e,q as C,j as k,v as Q}from"./index-0bf154dd.js";import{w as v}from"./weakAdminService-c6c1aace.js";import{_ as $}from"./_plugin-vue_export-helper-c27b6911.js";const g={class:"dashboard"},S={class:"dashboard-content"},V={class:"welcome-section"},q={class:"feature-cards"},D={class:"card-content"},O={class:"card-content"},B={class:"card-content"},R={class:"stats-section"},x={class:"stats-cards"},N={class:"stat-content"},j={class:"stat-number"},E={class:"stat-content"},H={class:"stat-number"},I={class:"stat-content"},L={class:"stat-number"},M={__name:"DashboardView",setup(T){m();const p=b(),l=w({totalActivationCodes:0,totalQuotaOperations:0,totalQuotaAmount:0}),f=async()=>{try{const n=await v.getActivationHistory(1,1);n.success&&(l.totalActivationCodes=n.data.total);const t=await v.getQuotaOperations(1,100);t.success&&(l.totalQuotaOperations=t.data.total,l.totalQuotaAmount=t.data.data.reduce((d,a)=>d+(a.operationType==="increase"?a.quotaAmount:0),0))}catch(n){console.error("Load stats error:",n)}};return h(()=>{f()}),(n,t)=>{var u;const d=_("el-icon"),a=_("el-card");return A(),y("div",g,[s("div",S,[s("div",V,[s("h2",null,"欢迎，"+r((u=i(p).user)==null?void 0:u.username),1),t[3]||(t[3]=s("p",null,"您可以使用以下功能管理激活码和用户算力",-1))]),s("div",q,[o(a,{class:"feature-card",shadow:"hover",onClick:t[0]||(t[0]=c=>n.$router.push("/activation-codes"))},{default:e(()=>[s("div",D,[o(d,{class:"card-icon",color:"#409eff"},{default:e(()=>[o(i(C))]),_:1}),t[4]||(t[4]=s("h3",null,"激活码管理",-1)),t[5]||(t[5]=s("p",null,"生成激活码，查看核销历史",-1))])]),_:1}),o(a,{class:"feature-card",shadow:"hover",onClick:t[1]||(t[1]=c=>n.$router.push("/user-quota"))},{default:e(()=>[s("div",O,[o(d,{class:"card-icon",color:"#67c23a"},{default:e(()=>[o(i(k))]),_:1}),t[6]||(t[6]=s("h3",null,"用户算力管理",-1)),t[7]||(t[7]=s("p",null,"查询用户信息，修改算力余额",-1))])]),_:1}),o(a,{class:"feature-card",shadow:"hover",onClick:t[2]||(t[2]=c=>n.$router.push("/history"))},{default:e(()=>[s("div",B,[o(d,{class:"card-icon",color:"#e6a23c"},{default:e(()=>[o(i(Q))]),_:1}),t[8]||(t[8]=s("h3",null,"操作历史",-1)),t[9]||(t[9]=s("p",null,"查看算力操作和激活码核销记录",-1))])]),_:1})]),s("div",R,[t[13]||(t[13]=s("h3",null,"快速统计",-1)),s("div",x,[o(a,{class:"stat-card"},{default:e(()=>[s("div",N,[s("div",j,r(l.totalActivationCodes),1),t[10]||(t[10]=s("div",{class:"stat-label"},"已生成激活码",-1))])]),_:1}),o(a,{class:"stat-card"},{default:e(()=>[s("div",E,[s("div",H,r(l.totalQuotaOperations),1),t[11]||(t[11]=s("div",{class:"stat-label"},"算力操作次数",-1))])]),_:1}),o(a,{class:"stat-card"},{default:e(()=>[s("div",I,[s("div",L,r(l.totalQuotaAmount),1),t[12]||(t[12]=s("div",{class:"stat-label"},"累计分配算力",-1))])]),_:1})])])])])}}},J=$(M,[["__scopeId","data-v-9fe063b5"]]);export{J as default};
