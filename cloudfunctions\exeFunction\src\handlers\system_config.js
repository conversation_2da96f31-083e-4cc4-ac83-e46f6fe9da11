const cloud = require('wx-server-sdk')
const logger = require('../utils/logger')

const db = cloud.database()

/**
 * 获取系统配置
 * @param {Object} params - 参数对象
 * @param {string} params.configField - 配置字段名（可选，不传则返回所有配置）
 * @returns {Object} 系统配置信息
 */
async function getSystemConfig(params) {
  try {
    const { configField } = params

    logger.info('获取系统配置', { configField })

    const result = await db.collection('exe_system_config').get()

    if (result.data.length === 0) {
      throw new Error('系统配置不存在')
    }

    const config = result.data[0]

    if (configField) {
      // 返回特定字段
      return {
        configField: configField,
        configValue: config[configField] || null
      }
    } else {
      // 返回所有配置
      return {
        configValue: config
      }
    }

  } catch (error) {
    logger.error('获取系统配置失败', { error: error.message, params })
    throw error
  }
}

/**
 * 获取Go代理API地址（专用方法）
 * @returns {Object} Go代理API配置
 */
async function getGoProxyApiUrl() {
  try {
    logger.info('获取Go代理API地址')

    const result = await db.collection('exe_system_config').get()

    if (result.data.length === 0) {
      throw new Error('数据库中未找到系统配置')
    }

    const config = result.data[0]
    const apiUrl = config.go_proxy_api_url

    if (!apiUrl) {
      throw new Error('系统配置中未找到Go代理API地址')
    }

    return {
      configValue: apiUrl
    }

  } catch (error) {
    logger.error('获取Go代理API地址失败', { error: error.message })
    throw error
  }
}

module.exports = {
  getSystemConfig,
  getGoProxyApiUrl
}
