import 'package:json_annotation/json_annotation.dart';

part 'payment_log_model.g.dart';

@JsonSerializable()
class PaymentLogModel {
  @JsonKey(name: '_id')
  final String id;
  
  final String operationType;
  final String orderId;
  final String userId;
  final Map<String, dynamic> requestData;
  final Map<String, dynamic> responseData;
  final String status;
  final DateTime timestamp;

  const PaymentLogModel({
    required this.id,
    required this.operationType,
    required this.orderId,
    required this.userId,
    required this.requestData,
    required this.responseData,
    required this.status,
    required this.timestamp,
  });

  factory PaymentLogModel.fromJson(Map<String, dynamic> json) {
    // 处理时间字段的特殊格式
    DateTime parseDateTime(dynamic value) {
      if (value == null) return DateTime.now();
      if (value is Map && value.containsKey('\$date')) {
        return DateTime.fromMillisecondsSinceEpoch(value['\$date']);
      } else if (value is String) {
        try {
          return DateTime.parse(value);
        } catch (e) {
          return DateTime.now();
        }
      } else if (value is int) {
        return DateTime.fromMillisecondsSinceEpoch(value);
      }
      return DateTime.now();
    }

    return PaymentLogModel(
      id: json['_id'] as String,
      operationType: json['operationType'] as String,
      orderId: json['orderId'] as String,
      userId: json['userId'] as String,
      requestData: json['requestData'] as Map<String, dynamic>? ?? {},
      responseData: json['responseData'] as Map<String, dynamic>? ?? {},
      status: json['status'] as String,
      timestamp: parseDateTime(json['timestamp']),
    );
  }

  Map<String, dynamic> toJson() => _$PaymentLogModelToJson(this);

  String get operationTypeText {
    switch (operationType) {
      case 'CREATE_ORDER':
        return '创建订单';
      case 'FUIOU_UNIFIED_ORDER':
        return '富友统一下单';
      case 'PAYMENT_CALLBACK':
        return '支付回调';
      case 'REFUND':
        return '退款';
      case 'CANCEL_ORDER':
        return '取消订单';
      default:
        return operationType;
    }
  }

  String get statusText {
    switch (status) {
      case 'SUCCESS':
        return '成功';
      case 'FAILED':
        return '失败';
      case 'PENDING':
        return '处理中';
      case 'TIMEOUT':
        return '超时';
      default:
        return status;
    }
  }

  bool get isSuccess => status == 'SUCCESS';
  bool get isFailed => status == 'FAILED';
  bool get isPending => status == 'PENDING';
}
