const { weakAdminLogin, refreshToken } = require('./auth')
const { generateActivationCodes, getActivationHistory } = require('./activation_codes')
const { getUserInfo, modifyUserQuota, getQuotaOperations } = require('./user_quota')
const { createBusinessError, ERROR_CODES } = require('../middleware/error_handler')
const logger = require('../utils/logger')

/**
 * 处理器映射
 */
const handlers = {
  // 认证相关
  weakAdminLogin,
  refreshToken,

  // 激活码管理
  generateActivationCodes,
  getActivationHistory,

  // 用户算力管理
  getUserInfo,
  modifyUserQuota,
  getQuotaOperations
}

/**
 * 请求处理器
 * @param {object} event 事件对象
 * @returns {object} 处理结果
 */
async function handleRequest(event) {
  const { action } = event
  
  if (!action) {
    throw createBusinessError(
      ERROR_CODES.VALIDATION_ERROR,
      '缺少action参数',
      400
    )
  }
  
  const handler = handlers[action]
  if (!handler) {
    throw createBusinessError(
      ERROR_CODES.VALIDATION_ERROR,
      `不支持的操作: ${action}`,
      400
    )
  }
  
  logger.info('处理请求', {
    action,
    hasToken: !!event.token
  })
  
  // 调用对应的处理器
  return await handler(event)
}

module.exports = {
  handleRequest
}
