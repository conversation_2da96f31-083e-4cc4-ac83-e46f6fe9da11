
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:numerology_ai_chat_admin/src/models/model_model.dart';
import 'package:numerology_ai_chat_admin/src/providers/model_provider.dart';
import 'package:numerology_ai_chat_admin/src/widgets/edit_model_dialog.dart';

class ModelManagementScreen extends ConsumerStatefulWidget {
  const ModelManagementScreen({super.key});

  @override
  ConsumerState<ModelManagementScreen> createState() => _ModelManagementScreenState();
}

class _ModelManagementScreenState extends ConsumerState<ModelManagementScreen> {
  bool? _selectedIsActive;

  @override
  void initState() {
    super.initState();
    // 初始加载模型列表
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(modelProvider.notifier).fetchModels();
    });
  }

  @override
  Widget build(BuildContext context) {
    final modelState = ref.watch(modelProvider);

    // 监听错误状态
    ref.listen<ModelListState>(modelProvider, (previous, next) {
      if (next.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.error!),
            backgroundColor: Colors.red,
          ),
        );
      }
    });

    return Scaffold(
      body: Column(
        children: [
          // 页面标题和操作栏
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Text(
                  '模型管理',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () => _refreshModels(),
                  tooltip: '刷新',
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: () => _showCreateModelDialog(),
                  icon: const Icon(Icons.add),
                  label: const Text('新建模型'),
                ),
              ],
            ),
          ),
          // 筛选条件
          _buildFilterBar(),
          const Divider(height: 1),
          // 模型列表
          Expanded(
            child: modelState.isLoading
                ? const Center(child: CircularProgressIndicator())
                : modelState.models == null || modelState.models!.isEmpty
                    ? const Center(child: Text('暂无模型数据'))
                    : _buildModelList(modelState.models!),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // 状态筛选
          SizedBox(
            width: 120,
            child: DropdownButtonFormField<bool>(
              value: _selectedIsActive,
              decoration: const InputDecoration(
                labelText: '状态',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: const [
                DropdownMenuItem(value: null, child: Text('全部状态')),
                DropdownMenuItem(value: true, child: Text('启用')),
                DropdownMenuItem(value: false, child: Text('禁用')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedIsActive = value;
                });
                _refreshModels();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModelList(List<Model> models) {
    // 根据筛选条件过滤模型
    final filteredModels = models.where((model) {
      if (_selectedIsActive != null && model.isActive != _selectedIsActive) {
        return false;
      }
      return true;
    }).toList();

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('模型名称')),
          DataColumn(label: Text('显示名称')),
          DataColumn(label: Text('API地址')),
          DataColumn(label: Text('最大Token')),
          DataColumn(label: Text('温度')),
          DataColumn(label: Text('状态')),
          DataColumn(label: Text('排序')),
          DataColumn(label: Text('更新时间')),
          DataColumn(label: Text('操作')),
        ],
        rows: filteredModels.map((model) => _buildModelRow(model)).toList(),
      ),
    );
  }

  DataRow _buildModelRow(Model model) {
    return DataRow(
      cells: [
        DataCell(
          SizedBox(
            width: 150,
            child: Text(
              model.modelName,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        DataCell(
          SizedBox(
            width: 120,
            child: Text(
              model.modelDisplayName,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        DataCell(
          SizedBox(
            width: 200,
            child: Text(
              model.modelApiUrl,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        DataCell(Text(model.maxTokens.toString())),
        DataCell(Text(model.temperature.toString())),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: model.isActive ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              model.isActive ? '启用' : '禁用',
              style: TextStyle(
                color: model.isActive ? Colors.green : Colors.red,
                fontSize: 12,
              ),
            ),
          ),
        ),
        DataCell(Text(model.sortOrder.toString())),
        DataCell(Text(_formatDateTime(model.updatedAt))),
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: const Icon(Icons.edit, size: 18),
                onPressed: () => _showEditModelDialog(model),
                tooltip: '编辑',
              ),
              IconButton(
                icon: const Icon(Icons.delete, size: 18),
                onPressed: () => _showDeleteConfirmDialog(model),
                tooltip: '删除',
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _refreshModels() {
    ref.read(modelProvider.notifier).fetchModels();
  }

  void _showCreateModelDialog() {
    showDialog(
      context: context,
      builder: (context) => const EditModelDialog(),
    );
  }

  void _showEditModelDialog(Model model) {
    showDialog(
      context: context,
      builder: (context) => EditModelDialog(model: model),
    );
  }

  void _showDeleteConfirmDialog(Model model) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除模型"${model.modelDisplayName}"吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final success = await ref.read(modelProvider.notifier).deleteModel(model.modelId);
              if (success) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('模型删除成功')),
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }
}
