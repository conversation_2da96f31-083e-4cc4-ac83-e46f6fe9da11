import{G as s}from"./index-0bf154dd.js";class o{async generateActivationCodes(e,t){return(await s.post("/exeWeakAdmin",{action:"generateActivationCodes",quantity:e,quotaAmount:t})).data}async getActivationHistory(e=1,t=20){return(await s.post("/exeWeakAdmin",{action:"getActivationHistory",page:e,pageSize:t})).data}async getUserInfo(e){return(await s.post("/exeWeakAdmin",{action:"getUserInfo",username:e})).data}async modifyUserQuota(e,t,a,n=""){return(await s.post("/exeWeakAdmin",{action:"modifyUserQuota",targetUsername:e,operationType:t,quotaAmount:a,reason:n})).data}async getQuotaOperations(e=1,t=20,a=""){return(await s.post("/exeWeakAdmin",{action:"getQuotaOperations",page:e,pageSize:t,targetUsername:a})).data}}const p=new o;export{p as w};
