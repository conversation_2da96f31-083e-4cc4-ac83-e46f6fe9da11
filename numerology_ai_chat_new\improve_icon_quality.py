#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进图标质量，解决模糊和默认图标问题
"""

from PIL import Image
import os

def create_high_quality_ico(jpg_path, ico_path):
    """
    创建高质量的ICO文件，解决模糊问题
    
    Args:
        jpg_path: 源JPG文件路径
        ico_path: 目标ICO文件路径
    """
    try:
        # 打开JPG图片
        with Image.open(jpg_path) as img:
            print(f"📏 原始图片尺寸: {img.size}")
            print(f"🎨 原始图片模式: {img.mode}")
            
            # 转换为RGBA模式（支持透明度）
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            # 调整图片为正方形（取较小的边作为基准）
            min_size = min(img.size)
            # 从中心裁剪为正方形
            left = (img.size[0] - min_size) // 2
            top = (img.size[1] - min_size) // 2
            right = left + min_size
            bottom = top + min_size
            img_square = img.crop((left, top, right, bottom))
            
            print(f"✂️ 裁剪后尺寸: {img_square.size}")
            
            # Windows图标标准尺寸（包含高DPI支持）
            sizes = [
                (16, 16),   # 小图标
                (20, 20),   # 小图标 125% DPI
                (24, 24),   # 小图标 150% DPI
                (32, 32),   # 中等图标
                (40, 40),   # 中等图标 125% DPI
                (48, 48),   # 中等图标 150% DPI
                (64, 64),   # 大图标
                (96, 96),   # 大图标 150% DPI
                (128, 128), # 超大图标
                (256, 256)  # 超大图标
            ]
            
            # 创建不同尺寸的高质量图标
            icon_images = []
            for size in sizes:
                # 使用LANCZOS重采样算法获得最佳质量
                resized = img_square.resize(size, Image.Resampling.LANCZOS)
                
                # 对小尺寸图标进行锐化处理
                if size[0] <= 48:
                    from PIL import ImageFilter
                    resized = resized.filter(ImageFilter.SHARPEN)
                
                icon_images.append(resized)
                print(f"✅ 生成 {size[0]}x{size[1]} 图标")
            
            # 保存为ICO文件
            icon_images[0].save(
                ico_path,
                format='ICO',
                sizes=sizes,
                append_images=icon_images[1:]
            )
            
            print(f"✅ 成功创建高质量图标: {ico_path}")
            print(f"📏 包含 {len(sizes)} 种尺寸")
            
            # 获取文件大小
            size = os.path.getsize(ico_path)
            print(f"📦 文件大小: {size} 字节")
            
    except Exception as e:
        print(f"❌ 创建图标失败: {e}")
        return False
    
    return True

def update_resource_file():
    """
    确保资源文件正确引用图标
    """
    resource_file = "windows/runner/Runner.rc"
    
    if not os.path.exists(resource_file):
        print(f"❌ 资源文件不存在: {resource_file}")
        return False
    
    try:
        with open(resource_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查图标引用是否正确
        if 'IDI_APP_ICON            ICON                    "resources\\\\app_icon.ico"' in content:
            print("✅ 资源文件图标引用正确")
            return True
        else:
            print("⚠️ 资源文件图标引用可能有问题")
            print("当前内容包含:")
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'ICON' in line and 'app_icon' in line:
                    print(f"第{i+1}行: {line}")
            return False
            
    except Exception as e:
        print(f"❌ 检查资源文件失败: {e}")
        return False

def main():
    print("🔧 改进图标质量，解决模糊和默认图标问题...")
    
    # 定义文件路径
    jpg_path = "assets/images/logo.png"
    ico_path = "windows/runner/resources/app_icon.ico"
    
    # 检查源文件是否存在
    if not os.path.exists(jpg_path):
        print(f"❌ 源文件不存在: {jpg_path}")
        return
    
    # 确保目标目录存在
    os.makedirs(os.path.dirname(ico_path), exist_ok=True)
    
    # 备份原有的ico文件
    if os.path.exists(ico_path):
        backup_path = ico_path + ".old"
        try:
            import shutil
            shutil.copy2(ico_path, backup_path)
            print(f"📦 已备份当前图标到: {backup_path}")
        except Exception as e:
            print(f"⚠️ 备份图标文件失败: {e}")
    
    # 创建高质量图标
    print(f"🔄 开始创建高质量图标...")
    success = create_high_quality_ico(jpg_path, ico_path)
    
    if success:
        print("✅ 高质量图标创建完成！")
        
        # 检查资源文件
        print("🔍 检查资源文件配置...")
        update_resource_file()
        
        print("\n📝 接下来的步骤:")
        print("1. 清理构建缓存: flutter clean")
        print("2. 重新构建应用: flutter build windows --release")
        print("3. 如果问题仍然存在，可能需要重启Windows资源管理器")
        print("   - 打开任务管理器")
        print("   - 找到 'Windows资源管理器' 进程")
        print("   - 右键选择 '重新启动'")
        
        print("\n💡 提示:")
        print("- 新图标包含多种尺寸，支持高DPI显示")
        print("- 小尺寸图标已进行锐化处理，减少模糊")
        print("- 如果任务栏图标仍然模糊，请检查Windows显示缩放设置")
        
    else:
        print("💥 图标创建失败，请检查错误信息。")

if __name__ == "__main__":
    main()
