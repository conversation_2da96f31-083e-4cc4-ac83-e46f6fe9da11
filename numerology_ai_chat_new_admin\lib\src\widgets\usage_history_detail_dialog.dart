import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../models/usage_history_model.dart';

class UsageHistoryDetailDialog extends StatelessWidget {
  final UsageHistoryModel history;

  const UsageHistoryDetailDialog({
    super.key,
    required this.history,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 600,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '使用历史详情',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSection('基本信息', [
                      _buildInfoRow('记录ID', history.id, copyable: true),
                      _buildInfoRow('用户ID', history.userId, copyable: true),
                      _buildInfoRow('描述', history.description),
                    ]),
                    const SizedBox(height: 24),
                    _buildSection('智能体信息', [
                      _buildInfoRow('智能体ID', history.agentId, copyable: true),
                      _buildInfoRow('智能体名称', history.agentName),
                    ]),
                    const SizedBox(height: 24),
                    _buildSection('模型信息', [
                      _buildInfoRow('模型ID', history.modelId, copyable: true),
                      _buildInfoRow('模型名称', history.modelName),
                      _buildInfoRow('模型等级', history.modelLevel),
                    ]),
                    const SizedBox(height: 24),
                    _buildSection('档次信息', [
                      _buildInfoRow('档次ID', history.pricingTierId, copyable: true),
                      _buildInfoRow('档次名称', history.tierName),
                    ]),
                    const SizedBox(height: 24),
                    _buildSection('算力消耗', [
                      _buildInfoRow('消耗算力', '${history.powerCost}'),
                      _buildInfoRow('消耗前余额', '${history.balanceBefore}'),
                      _buildInfoRow('消耗后余额', '${history.balanceAfter}'),
                      _buildInfoRow('实际消耗', '${history.balanceBefore - history.balanceAfter}'),
                    ]),
                    const SizedBox(height: 24),
                    _buildSection('时间信息', [
                      _buildInfoRow('使用时间', DateFormat('yyyy-MM-dd HH:mm:ss').format(history.consumeTime)),
                      _buildInfoRow('创建时间', DateFormat('yyyy-MM-dd HH:mm:ss').format(history.createdAt)),
                    ]),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('关闭'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value, {bool copyable = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    value,
                    style: TextStyle(
                      fontFamily: copyable ? 'monospace' : null,
                    ),
                  ),
                ),
                if (copyable)
                  IconButton(
                    icon: const Icon(Icons.copy, size: 16),
                    onPressed: () {
                      Clipboard.setData(ClipboardData(text: value));
                    },
                    tooltip: '复制',
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
