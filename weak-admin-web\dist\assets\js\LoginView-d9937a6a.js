import{u as v,a as b,r as x,b as V,o as y,c as t,d as k,e as L,f as l,g as o,w as r,h as R,i as n,j as S,l as z,k as B,m as C,t as E,E as g}from"./index-0bf154dd.js";import{_ as F}from"./_plugin-vue_export-helper-c27b6911.js";const M={class:"login-container"},N={class:"login-card"},q={__name:"LoginView",setup(A){const p=v(),a=b(),i=x(),s=V({username:"",password:""}),f={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:1,max:50,message:"用户名长度在 1 到 50 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:100,message:"密码长度在 6 到 100 个字符",trigger:"blur"}]},u=async()=>{if(i.value)try{if(!await i.value.validate())return;const e=await a.login(s);e.success?(g.success("登录成功"),p.push("/dashboard")):g.error(e.message)}catch(d){console.error("Login validation error:",d)}};return y(()=>{a.checkAuth()&&p.push("/dashboard")}),(d,e)=>{const _=t("el-input"),c=t("el-form-item"),w=t("el-button"),h=t("el-form");return k(),L("div",M,[l("div",N,[e[2]||(e[2]=l("div",{class:"login-header"},[l("h2",null,"管理员登录"),l("p",null,"请使用您的管理员账号登录")],-1)),o(h,{ref_key:"loginFormRef",ref:i,model:s,rules:f,class:"login-form",onSubmit:R(u,["prevent"])},{default:r(()=>[o(c,{prop:"username"},{default:r(()=>[o(_,{modelValue:s.username,"onUpdate:modelValue":e[0]||(e[0]=m=>s.username=m),placeholder:"请输入用户名",size:"large","prefix-icon":n(S),clearable:""},null,8,["modelValue","prefix-icon"])]),_:1}),o(c,{prop:"password"},{default:r(()=>[o(_,{modelValue:s.password,"onUpdate:modelValue":e[1]||(e[1]=m=>s.password=m),type:"password",placeholder:"请输入密码",size:"large","prefix-icon":n(z),"show-password":"",clearable:"",onKeyup:B(u,["enter"])},null,8,["modelValue","prefix-icon"])]),_:1}),o(c,null,{default:r(()=>[o(w,{type:"primary",size:"large",class:"login-button",loading:n(a).loading,onClick:u},{default:r(()=>[C(E(n(a).loading?"登录中...":"登录"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])])])}}},j=F(q,[["__scopeId","data-v-1047fe27"]]);export{j as default};
