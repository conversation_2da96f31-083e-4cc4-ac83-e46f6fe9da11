import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/chat_message.dart';
import '../models/agent_model.dart';
import '../models/ai_model.dart';
import '../models/bazi_model.dart';
import '../models/conversation_model.dart';
import '../services/ai_service.dart';
import '../services/go_proxy_service.dart';
import '../services/power_calculation_service.dart';
import '../services/chat_storage_service.dart';
import '../core/storage/storage_service.dart';
import 'settings_provider.dart';
import 'agent_provider.dart';
import 'model_provider.dart';
import '../models/chat_state.dart';
import 'auth_provider.dart';
import 'service_providers.dart';


final chatProvider = StateNotifierProvider<ChatNotifier, ChatState>((ref) {
  final settings = ref.watch(settingsProvider);
  final storageService = ref.watch(storageServiceProvider);
  final aiService = ref.watch(aiServiceProvider);
  return ChatNotifier(ref, settings, storageService, aiService);
});


class ChatNotifier extends StateNotifier<ChatState> {
  final AIService _aiService;
  final SettingsProvider _settingsProvider;
  final Ref _ref;
  late final ChatStorageService _storageService;

  ChatNotifier(this._ref, this._settingsProvider, StorageService storageService, this._aiService) : super(const ChatState()) {
    _storageService = ChatStorageService(storageService);
  }

  // 用于取消正在进行的AI响应
  StreamSubscription? _streamSubscription;

  /// 初始化，加载历史对话或创建新对话
  void init() {
    _initializeStorageAndLoadConversations();
  }

  /// 初始化存储服务并加载对话
  Future<void> _initializeStorageAndLoadConversations() async {
    try {
      // 确保存储服务已初始化
      await _ref.read(storageInitProvider.future);

      // 加载对话
      await _loadConversationsFromStorage();
    } catch (e) {
      print('初始化存储服务失败: $e');
      // 初始化失败，创建新对话
      createNewConversation();
    }
  }

  /// 从存储加载对话
  Future<void> _loadConversationsFromStorage() async {
    try {
      state = state.copyWith(isLoading: true);

      // 输出存储调试信息
      final debugInfo = await _storageService.getStorageDebugInfo();
      print('=== 聊天存储调试信息 ===');
      print('存储路径: ${debugInfo['storage_path']}');
      print('对话目录存在: ${debugInfo['conversations_dir_exists']}');
      print('索引文件存在: ${debugInfo['index_file_exists']}');
      print('索引中的对话数: ${debugInfo['summaries_count']}');
      print('文件系统中的对话文件数: ${debugInfo['conversation_files_count']}');
      if (debugInfo['error'] != null) {
        print('存储错误: ${debugInfo['error']}');
      }

      // 定期清理过期对话（异步执行，不阻塞加载）
      _cleanupOldConversationsAsync();

      // 加载对话摘要
      final summaries = await _storageService.loadConversationSummaries();
      print('成功加载 ${summaries.length} 个对话摘要');

      if (summaries.isEmpty) {
        print('没有历史对话，创建新对话');
        // 没有历史对话，创建新对话
        createNewConversation();
        return;
      }

      // 从摘要创建轻量级对话对象（用于显示列表）
      final conversations = summaries.map((summary) => ConversationModel(
        id: summary.id,
        title: summary.title, // 现在摘要中已包含正确的显示标题
        createdAt: summary.createdAt,
        updatedAt: summary.updatedAt,
        messages: [], // 暂时为空，点击时再加载
        selectedAgent: null, // 暂时为空，点击时再加载
        selectedModel: null, // 暂时为空，点击时再加载
        baziData: null,
      )).toList();

      if (conversations.isNotEmpty) {
        print('成功创建 ${conversations.length} 个对话摘要');
        print('对话列表: ${conversations.map((c) => '${c.displayTitle}(${c.updatedAt})').join(', ')}');

        // 加载第一个对话的完整内容
        final firstConversation = await _storageService.loadConversation(conversations.first.id);
        if (firstConversation != null) {
          conversations[0] = firstConversation;
          print('加载第一个对话完整内容成功: ${firstConversation.displayTitle}');
        }

        state = state.copyWith(
          conversations: conversations,
          currentSessionId: conversations.first.id,
          isLoading: false,
        );

        // 调试：对比内存与存储状态
        _compareStateAsync();
      } else {
        print('没有找到对话摘要，创建新对话');
        // 没有对话，创建新对话
        createNewConversation();
      }
    } catch (e) {
      print('加载历史对话失败: $e');
      // 加载失败，创建新对话
      createNewConversation();
    }
  }

  /// 异步清理过期对话
  void _cleanupOldConversationsAsync() {
    Future.microtask(() async {
      try {
        await _storageService.cleanupOldConversations();
        print('过期对话清理完成');
      } catch (e) {
        print('清理过期对话失败: $e');
      }
    });
  }

  /// 创建一个新的对话会话
  void createNewConversation() {
    // 设置加载状态
    state = state.copyWith(isLoading: true, error: null);

    try {
      // 触发数据加载
      _ref.read(autoLoadAgentsProvider);
      _ref.read(autoLoadModelsProvider);

      // 获取默认智能体和模型（可能为null）
      final defaultAgent = _ref.read(defaultAgentProvider);
      final defaultModel = _ref.read(defaultModelProvider);

      // 直接创建对话，不管是否有默认值
      _createConversationWithDefaults(defaultAgent, defaultModel);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: '创建对话失败: $e',
      );
    }
  }



  /// 使用默认智能体和模型创建对话（可能为null）
  void _createConversationWithDefaults(AgentModel? agent, AIModel? model) {
    final conversation = ConversationModel.create(
      agent: agent,
      model: model,
    );

    // 不添加任何欢迎消息，保持对话界面简洁
    // 智能体信息只在后台使用，不在聊天中显示
    final conversationWithMessage = conversation;

    // 按更新时间排序插入，保持与存储排序一致
    final newConversations = List<ConversationModel>.from(state.conversations);
    newConversations.insert(0, conversationWithMessage);
    // 重新排序以保持一致性
    newConversations.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

    state = state.copyWith(
      conversations: newConversations,
      currentSessionId: conversationWithMessage.id,
      isLoading: false,
    );

    // 同步保存新创建的对话，确保不丢失
    _saveConversationSync(conversationWithMessage);
  }



  /// 切换当前对话
  void switchConversation(String sessionId) async {
    if (state.currentSessionId == sessionId) return;

    // 先切换到目标对话
    state = state.copyWith(currentSessionId: sessionId);

    // 检查目标对话是否已加载完整内容
    final targetConversation = state.conversations.firstWhere((c) => c.id == sessionId);

    // 如果消息为空，说明只是摘要，需要加载完整内容
    if (targetConversation.messages.isEmpty) {
      print('加载对话完整内容: ${targetConversation.id}');

      try {
        final fullConversation = await _storageService.loadConversation(sessionId);
        if (fullConversation != null) {
          // 更新对话列表中的对话内容
          final conversations = List<ConversationModel>.from(state.conversations);
          final index = conversations.indexWhere((c) => c.id == sessionId);
          if (index != -1) {
            conversations[index] = fullConversation;
            state = state.copyWith(conversations: conversations);
            print('对话内容加载成功: ${fullConversation.displayTitle}, 消息数: ${fullConversation.messages.length}');
          }
        } else {
          print('加载对话内容失败: $sessionId');
        }
      } catch (e) {
        print('加载对话内容异常: $e');
      }
    }
  }

  /// 删除对话
  void deleteConversation(String sessionId) {
    final newConversations = List<ConversationModel>.from(state.conversations)
      ..removeWhere((c) => c.id == sessionId);

    String? newCurrentId = state.currentSessionId;
    if (newCurrentId == sessionId) {
      newCurrentId = newConversations.isNotEmpty ? newConversations.first.id : null;
    }

    state = state.copyWith(
      conversations: newConversations,
      currentSessionId: newCurrentId,
    );

    // 异步删除存储中的对话
    _deleteConversationAsync(sessionId);

    if (newCurrentId == null) {
      createNewConversation();
    }
  }

  /// 异步删除对话存储
  void _deleteConversationAsync(String conversationId) {
    Future.microtask(() async {
      try {
        await _storageService.deleteConversation(conversationId);
      } catch (e) {
        print('删除对话存储失败: $e');
      }
    });
  }

  /// 重命名对话
  void renameConversation(String sessionId, String newTitle) {
    _updateConversation(sessionId, (conv) => conv.copyWith(title: newTitle));
  }

  /// 选择智能体
  void selectAgent(AgentModel agent) {
    if (state.currentConversation == null || !state.canSwitchAgent) return;
    _updateConversation(state.currentSessionId!, (conv) => conv.copyWith(selectedAgent: agent));
  }

  /// 选择AI模型
  void selectModel(AIModel model) {
    if (state.currentConversation == null || !state.canSwitchModel) return;
    _updateConversation(state.currentSessionId!, (conv) => conv.copyWith(selectedModel: model));
  }

  /// 设置八字结果
  void setBaziResult(BaziResultModel baziResult) {
    if (state.currentConversation == null) return;

    // 只设置八字数据，不在聊天中显示任何消息
    _updateConversation(state.currentSessionId!, (conv) => conv.copyWith(baziData: baziResult));
  }

  /// 清除八字结果
  void clearBaziResult() {
    if (state.currentConversation == null) return;

    // 只清除八字数据，不需要移除消息（因为现在不会创建八字相关的系统消息）
    _updateConversation(state.currentSessionId!, (conv) => conv.copyWith(
      baziData: null,
      clearBaziData: true,
    ));
  }


  /// 发送消息（兼容旧接口）
  Future<void> sendMessage(String text) async {
    final userMessage = ChatMessage.user(content: text);
    await sendMessageWithImages(userMessage);
  }

  /// 发送包含图片的消息
  Future<void> sendMessageWithImages(ChatMessage userMessage) async {
    final conversation = state.currentConversation;
    if (conversation == null || (userMessage.content.trim().isEmpty && !userMessage.hasImages)) return;

    // 检查是否可以发送消息
    if (!state.canSendMessage) {
      // 这里不做任何操作，让UI层处理提示
      return;
    }

    // 检查用户登录状态
    final authState = _ref.read(authProvider);
    final user = authState.user;
    if (user == null) {
      throw Exception('用户未登录');
    }

    if (conversation.selectedAgent == null || conversation.selectedModel == null) {
      throw Exception('请先选择智能体和模型');
    }

    // 获取token
    final token = _ref.read(authProvider).token;
    if (token == null) throw Exception('未登录');

    // 计算本次对话需要的算力（仅用于UI显示）
    final requiredPower = await PowerCalculationService.calculatePowerCost(
      agent: conversation.selectedAgent!,
      model: conversation.selectedModel!,
      token: token,
    );

    // 前端预检查算力是否足够（最终以后端为准）
    if (user.availableCount < requiredPower) {
      throw Exception('算力不足，需要 $requiredPower 算力，当前仅有 ${user.availableCount} 算力');
    }

    // 检查是否需要八字数据
    String? baziDataText;
    if (conversation.selectedAgent?.requiresBazi == true && conversation.baziData != null) {
      final baziResult = conversation.baziData!;
      baziDataText = '''
姓名：${baziResult.input.name}
性别：${baziResult.input.gender.displayName}
出生时间：${baziResult.input.birthDateTime.year}年${baziResult.input.birthDateTime.month}月${baziResult.input.birthDateTime.day}日${baziResult.input.birthDateTime.hour}时${baziResult.input.birthDateTime.minute}分
出生地：${baziResult.input.birthPlace}
历法：${baziResult.input.calendarType.displayName}

四柱八字：${baziResult.fourPillars}

详细排盘信息：
${baziResult.baziText}
''';
    }

    // 更新用户消息的八字数据和智能体ID
    final finalUserMessage = userMessage.copyWith(
      agentId: conversation.selectedAgent?.id,
      baziData: baziDataText,
    );

    // 创建一个AI助手的"正在输入"占位消息
    final typingMessage = ChatMessage.typing(agentId: conversation.selectedAgent?.id);

    // 更新状态，加入用户消息和正在输入消息，并设置流式输出状态
    _updateConversation(conversation.id, (conv) => conv
        .addMessage(finalUserMessage)
        .addMessage(typingMessage)
        .copyWith(isStreaming: true, streamingMessageId: typingMessage.id));

    // 开始调用AI服务
    try {

      // 获取更新后的对话对象
      final updatedConversation = state.currentConversation!;

      // 获取有效的消息历史（排除正在输入的消息和大白话显示消息）。
      // 如果过滤后列表为空（理论上不应该为空，但为安全起见），
      // 兜底至少发送当前的用户消息，避免 Go 代理返回 "messages cannot be empty" 错误。
      final validMessages = updatedConversation.messages.where((m) =>
        (m.isUser || m.isAssistant) && !m.isTyping && m.content.isNotEmpty && !m.isLaymanDisplay
      ).toList();

      if (validMessages.isEmpty) {
        // 兜底：直接使用刚刚发送的用户消息
        validMessages.add(userMessage.copyWith(status: MessageStatus.sent));
      }

      print('发送聊天请求:');
      print('总消息数: ${updatedConversation.messages.length}');
      print('有效消息数: ${validMessages.length}');
      for (int i = 0; i < updatedConversation.messages.length; i++) {
        final msg = updatedConversation.messages[i];
        print('消息$i: sender=${msg.sender}, isUser=${msg.isUser}, isAssistant=${msg.isAssistant}, isTyping=${msg.isTyping}, content="${msg.content}"');
      }

      // 检查是否需要大白话版本
      if (updatedConversation.selectedAgent!.needLaymanVersion) {
        // 大白话版本：使用非流式API
        await _handleLaymanVersionResponse(
          token: token,
          agentId: updatedConversation.selectedAgent!.id,
          modelId: updatedConversation.selectedModel!.id,
          messages: validMessages,
          conversationId: conversation.id,
          typingMessage: typingMessage,
        );
      } else {
        // 普通版本：使用流式API
        final stream = _aiService.getChatCompletionStream(
          token: token,
          agentId: updatedConversation.selectedAgent!.id,
          modelId: updatedConversation.selectedModel!.id,
          messages: validMessages,
        );

        final buffer = StringBuffer();
        _streamSubscription = stream.listen(
          (chunk) {
            buffer.write(chunk);
            _updateConversation(conversation.id, (conv) => conv.updateLastMessage(
              typingMessage.copyWith(
                content: buffer.toString(),
                status: MessageStatus.sent, // 正在接收
              )
            ));
          },
          onDone: () async {
            _streamSubscription = null;
            final updatedConv = state.currentConversation!
                .updateLastMessage(state.currentConversation!.messages.last.copyWith(status: MessageStatus.sent))
                .copyWith(isStreaming: false, streamingMessageId: null);

            _updateConversationState(conversation.id, updatedConv);

            // 同步保存完整对话，确保AI回复不丢失
            await _saveConversationSync(updatedConv);

            // AI响应完成后，刷新用户信息以同步算力
            _refreshUserInfoAfterChat();
          },
          onError: (error) {
            _streamSubscription = null;
            _updateConversation(conversation.id, (conv) => conv
                .updateLastMessage(conv.messages.last.copyWith(
                  content: '获取AI回复失败: $error',
                  status: MessageStatus.failed,
                ))
                .copyWith(isStreaming: false, streamingMessageId: null));
          },
          cancelOnError: true,
        );
      }
    } catch (e) {
      _streamSubscription = null;
      _updateConversation(conversation.id, (conv) => conv
          .updateLastMessage(conv.messages.last.copyWith(
            content: '发送消息失败: $e',
            status: MessageStatus.failed,
          ))
          .copyWith(isStreaming: false, streamingMessageId: null));
    }
  }

  /// 重新发送失败的消息
  Future<void> resendMessage(String messageId) async {
    if (state.currentConversation == null) return;

    // 检查是否已经在重试中
    if (state.isMessageRetrying(messageId)) return;

    final conversation = state.currentConversation!;
    final messageIndex = conversation.messages.indexWhere((m) => m.id == messageId);

    if (messageIndex == -1) return;

    final failedMessage = conversation.messages[messageIndex];

    // 添加到重试状态
    final newRetryingIds = Set<String>.from(state.retryingMessageIds)..add(messageId);
    state = state.copyWith(retryingMessageIds: newRetryingIds);

    try {
      // 如果是用户消息失败，直接重新发送该消息
      if (failedMessage.isUser) {
        final messageContent = failedMessage.content;

        // 移除从这条失败消息之后的所有消息
        final newMessages = conversation.messages.sublist(0, messageIndex);

        _updateConversation(conversation.id, (conv) => conv.copyWith(messages: newMessages));

        // 重新发送，但不创建新的用户消息，而是重用原来的消息
        await _resendUserMessage(messageContent, conversation.id);
        return;
      }

      // 如果是AI消息失败，找到触发这条AI回复的用户消息并重新发送
      if (failedMessage.isAssistant) {
        // 向前查找最近的用户消息
        String? userMessageContent;
        int userMessageIndex = -1;

        for (int i = messageIndex - 1; i >= 0; i--) {
          if (conversation.messages[i].isUser) {
            userMessageContent = conversation.messages[i].content;
            userMessageIndex = i;
            break;
          }
        }

        if (userMessageContent != null && userMessageIndex != -1) {
          // 移除从失败的AI回复开始的所有消息，保留用户消息
          final newMessages = conversation.messages.sublist(0, messageIndex);

          _updateConversation(conversation.id, (conv) => conv.copyWith(messages: newMessages));

          // 重新发送AI请求，不创建新的用户消息
          await _resendUserMessage(userMessageContent, conversation.id);
        }
      }
    } finally {
      // 移除重试状态
      final updatedRetryingIds = Set<String>.from(state.retryingMessageIds)..remove(messageId);
      state = state.copyWith(retryingMessageIds: updatedRetryingIds);
    }
  }

  /// 重新发送用户消息的内部方法，不创建新的用户消息
  Future<void> _resendUserMessage(String content, String conversationId) async {
    final conversation = state.conversations.firstWhere((c) => c.id == conversationId);

    // 检查是否可以发送消息
    if (!state.canSendMessage) {
      return;
    }

    // 注意：这里不需要处理八字数据，因为重试时使用的是原始消息历史

    // 创建一个AI助手的"正在输入"占位消息
    final typingMessage = ChatMessage.typing(agentId: conversation.selectedAgent?.id);

    // 更新状态，加入正在输入消息，并设置流式输出状态
    _updateConversation(conversation.id, (conv) => conv
        .addMessage(typingMessage)
        .copyWith(isStreaming: true, streamingMessageId: typingMessage.id));

    // 开始调用AI服务
    try {
      final token = _ref.read(authProvider).token;
      if (token == null) throw Exception('未登录');

      // 获取更新后的对话对象
      final updatedConversation = state.currentConversation!;

      // 获取有效的消息历史（排除正在输入的消息和大白话显示消息）
      final validMessages = updatedConversation.messages.where((m) =>
        (m.isUser || m.isAssistant) && !m.isTyping && m.content.isNotEmpty && !m.isLaymanDisplay
      ).toList();

      print('重试发送聊天请求:');
      print('总消息数: ${updatedConversation.messages.length}');
      print('有效消息数: ${validMessages.length}');
      for (int i = 0; i < updatedConversation.messages.length; i++) {
        final msg = updatedConversation.messages[i];
        print('消息$i: sender=${msg.sender}, isUser=${msg.isUser}, isAssistant=${msg.isAssistant}, isTyping=${msg.isTyping}, content="${msg.content}"');
      }

      final stream = _aiService.getChatCompletionStream(
        token: token,
        agentId: updatedConversation.selectedAgent!.id,
        modelId: updatedConversation.selectedModel!.id,
        messages: validMessages,
      );

      final buffer = StringBuffer();
      _streamSubscription = stream.listen(
        (chunk) {
          buffer.write(chunk);
          _updateConversation(conversation.id, (conv) => conv.updateLastMessage(
            typingMessage.copyWith(
              content: buffer.toString(),
              status: MessageStatus.sent, // 正在接收
            )
          ));
        },
        onDone: () async {
          _streamSubscription = null;
          final updatedConv = state.currentConversation!
              .updateLastMessage(state.currentConversation!.messages.last.copyWith(status: MessageStatus.sent))
              .copyWith(isStreaming: false, streamingMessageId: null);

          _updateConversationState(conversation.id, updatedConv);

          // 同步保存重试后的对话
          await _saveConversationSync(updatedConv);

          // 重试完成后也刷新用户信息
          _refreshUserInfoAfterChat();
        },
        onError: (error) {
          _streamSubscription = null;
          _updateConversation(conversation.id, (conv) => conv
              .updateLastMessage(conv.messages.last.copyWith(
                content: '获取AI回复失败: $error',
                status: MessageStatus.failed,
              ))
              .copyWith(isStreaming: false, streamingMessageId: null));
        },
        cancelOnError: true,
      );
    } catch (e) {
      _updateConversation(conversation.id, (conv) => conv
          .updateLastMessage(conv.messages.last.copyWith(
            content: '发送消息失败: $e',
            status: MessageStatus.failed,
          ))
          .copyWith(isStreaming: false, streamingMessageId: null));
    }
  }

  /// 停止AI响应
  void stopAIResponse() {
    _streamSubscription?.cancel();
    _streamSubscription = null;

    if(state.currentConversation != null && state.isStreaming) {
      _updateConversation(state.currentSessionId!, (conv) {
        // 如果最后一条消息是正在输入，则更新其状态并清除流式状态
        if(conv.messages.last.isTyping) {
          return conv
              .updateLastMessage(conv.messages.last.copyWith(status: MessageStatus.sent))
              .copyWith(isStreaming: false, streamingMessageId: null);
        }
        return conv.copyWith(isStreaming: false, streamingMessageId: null);
      });
    }
  }

  /// 获取当前对话的算力消耗
  Future<int> getCurrentPowerCost() async {
    final conversation = state.currentConversation;
    if (conversation?.selectedAgent == null || conversation?.selectedModel == null) {
      return 0;
    }

    final token = _ref.read(authProvider).token;
    if (token == null) return 0;

    return await PowerCalculationService.calculatePowerCost(
      agent: conversation!.selectedAgent!,
      model: conversation.selectedModel!,
      token: token,
    );
  }

  /// 获取当前对话的算力消耗详情
  Future<Map<String, dynamic>> getCurrentPowerCostDetails() async {
    final conversation = state.currentConversation;
    if (conversation?.selectedAgent == null || conversation?.selectedModel == null) {
      return {
        'cost': 0,
        'description': '请先选择智能体和模型',
      };
    }

    final token = _ref.read(authProvider).token;
    if (token == null) {
      return {
        'cost': 0,
        'description': '未登录',
      };
    }

    return await PowerCalculationService.getPowerCostDetails(
      agent: conversation!.selectedAgent!,
      model: conversation.selectedModel!,
      token: token,
    );
  }

  /// 辅助方法：更新指定ID的对话
  void _updateConversation(
      String conversationId, ConversationModel Function(ConversationModel) updater) {
    final conversations = List<ConversationModel>.from(state.conversations);
    final index = conversations.indexWhere((c) => c.id == conversationId);
    if (index != -1) {
      final oldConversation = conversations[index];
      final newConversation = updater(oldConversation);
      conversations[index] = newConversation;

      // 重新排序以保持一致性
      conversations.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

      state = state.copyWith(conversations: conversations);

      // 异步保存对话，避免阻塞UI
      _saveConversationAsync(newConversation);
    }
  }

  /// 辅助方法：更新对话状态但不自动保存
  void _updateConversationState(String conversationId, ConversationModel newConversation) {
    final conversations = List<ConversationModel>.from(state.conversations);
    final index = conversations.indexWhere((c) => c.id == conversationId);
    if (index != -1) {
      conversations[index] = newConversation;
      state = state.copyWith(conversations: conversations);
    }
  }

  /// 同步保存对话（确保数据不丢失）
  Future<void> _saveConversationSync(ConversationModel conversation) async {
    try {
      await _storageService.saveConversation(conversation);
      print('对话保存成功: ${conversation.id}');
    } catch (e) {
      print('保存对话失败: $e');
      // 不抛出异常，避免影响用户体验
    }
  }

  /// 异步保存对话（用于非关键场景）
  void _saveConversationAsync(ConversationModel conversation) {
    Future.microtask(() async {
      await _saveConversationSync(conversation);
    });
  }

  /// 异步对比内存与存储状态
  void _compareStateAsync() {
    Future.microtask(() async {
      try {
        final comparison = await _storageService.compareMemoryWithStorage(state.conversations);
        if (comparison['inconsistencies'] != null &&
            (comparison['inconsistencies'] as List).isNotEmpty) {
          print('=== 发现内存与存储状态不一致 ===');
          for (final inconsistency in comparison['inconsistencies']) {
            print('不一致类型: ${inconsistency['type']}');
            print('对话ID: ${inconsistency['id']}');
            if (inconsistency['memory'] != null) {
              print('内存状态: ${inconsistency['memory']}');
            }
            if (inconsistency['storage'] != null) {
              print('存储状态: ${inconsistency['storage']}');
            }
          }
        } else {
          print('内存与存储状态一致');
        }
      } catch (e) {
        print('状态对比失败: $e');
      }
    });
  }

  /// 聊天完成后刷新用户信息以同步算力
  void _refreshUserInfoAfterChat() {
    // 异步刷新用户信息，减少延迟提高响应速度
    Future.delayed(const Duration(milliseconds: 200), () async {
      try {
        await _ref.read(authProvider.notifier).refreshUser();
        print('算力同步成功');
      } catch (e) {
        print('刷新用户信息失败: $e');
        // 如果第一次失败，延迟重试一次
        Future.delayed(const Duration(seconds: 2), () async {
          try {
            await _ref.read(authProvider.notifier).refreshUser();
            print('算力同步重试成功');
          } catch (retryError) {
            print('算力同步重试失败: $retryError');
            // 不抛出异常，避免影响用户体验
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _streamSubscription?.cancel();
    super.dispose();
  }

  // ===== 兼容旧代码的别名方法 =====

  /// 兼容旧代码：重命名会话【renameSession重命名会话】
  void renameSession(String sessionId, String newTitle) {
    renameConversation(sessionId, newTitle);
  }

  /// 兼容旧代码：删除会话【deleteSession删除会话】
  void deleteSession(String sessionId) {
    deleteConversation(sessionId);
  }

  /// 清除所有对话
  Future<void> clearAllConversations() async {
    try {
      // 1. 清除内存中的所有对话
      state = state.copyWith(
        conversations: [],
        currentSessionId: null,
      );

      // 2. 清除存储中的所有对话
      await _storageService.clearAllConversations();

      // 3. 创建新对话
      createNewConversation();

      print('所有对话已清除');
    } catch (e) {
      print('清除所有对话失败: $e');
      rethrow;
    }
  }

  /// 处理大白话版本响应（流式）- 创建两个独立的消息气泡
  Future<void> _handleLaymanVersionResponse({
    required String token,
    required String agentId,
    required String modelId,
    required List<ChatMessage> messages,
    required String conversationId,
    required ChatMessage typingMessage,
  }) async {
    try {
      // 获取流式响应
      final stream = _aiService.getChatCompletionWithLaymanVersionStream(
        token: token,
        agentId: agentId,
        modelId: modelId,
        messages: messages,
      );

      String currentStage = '';
      final professionalBuffer = StringBuffer();
      final laymanBuffer = StringBuffer();
      bool professionalCompleted = false;
      String? professionalFinalContent; // 保存专业版本的最终内容

      // 大白话消息ID
      String? laymanMessageId;

      // 添加调试日志
      print('开始处理大白话版本流式响应');

      _streamSubscription = stream.listen(
        (chunk) {
          if (chunk.isStage) {
            // 阶段切换
            currentStage = chunk.content;
            print('切换到阶段: $currentStage');
            if (currentStage == 'professional') {
              _updateConversation(conversationId, (conv) => conv.updateLastMessage(
                typingMessage.copyWith(
                  content: '正在生成专业版本...',
                  status: MessageStatus.sent,
                )
              ));
            } else if (currentStage == 'layman') {
              // 大白话阶段开始时，不更新专业版本消息，而是创建大白话消息
              if (laymanMessageId == null) {
                final laymanMessage = ChatMessage.laymanDisplay(
                  content: '正在生成大白话版本...',
                  agentId: agentId,
                  status: MessageStatus.sent,
                );
                laymanMessageId = laymanMessage.id;
                _updateConversation(conversationId, (conv) => conv.addMessage(laymanMessage));
              }
            }
          } else if (chunk.isTransition) {
            // 阶段转换消息 - 严格控制，防止覆盖已完成的专业版本
            print('收到transition消息: "${chunk.content}", 当前阶段: $currentStage, 专业版本已完成: $professionalCompleted');

            // 检查是否为大白话相关的transition消息
            bool isLaymanTransition = chunk.content.contains('大白话') || chunk.content.contains('正在生成大白话');

            if (isLaymanTransition) {
              // 所有大白话相关的transition消息都路由到大白话消息，绝不更新专业版本
              print('检测到大白话transition消息，路由到大白话消息');

              // 如果大白话消息还未创建，创建它
              if (laymanMessageId == null) {
                final laymanMessage = ChatMessage.laymanDisplay(
                  content: chunk.content,
                  agentId: agentId,
                  status: MessageStatus.sent,
                );
                laymanMessageId = laymanMessage.id;
                _updateConversation(conversationId, (conv) => conv.addMessage(laymanMessage));
                print('创建大白话消息显示transition内容');
              } else {
                // 更新大白话消息
                _updateConversation(conversationId, (conv) => conv.updateMessage(
                  laymanMessageId!,
                  (msg) => msg.copyWith(content: chunk.content),
                ));
              }
            } else if (currentStage == 'professional' && !professionalCompleted) {
              // 只在专业版本未完成且不是大白话消息时才更新专业版本消息
              _updateConversation(conversationId, (conv) => conv.updateLastMessage(
                typingMessage.copyWith(
                  content: chunk.content,
                  status: MessageStatus.sent,
                )
              ));
            } else if (currentStage == 'layman' && laymanMessageId != null) {
              // 大白话阶段的转换消息更新大白话消息
              _updateConversation(conversationId, (conv) => conv.updateMessage(
                laymanMessageId!,
                (msg) => msg.copyWith(content: chunk.content),
              ));
            } else {
              // 其他情况，记录日志但不处理，避免意外覆盖
              print('忽略transition消息，避免意外覆盖: "${chunk.content}"');
            }
          } else if (chunk.isStageComplete) {
            // 阶段完成，包含完整内容
            print('收到阶段完成: ${chunk.stage}, 内容长度: ${chunk.content.length}');
            if (chunk.stage == 'professional') {
              // 专业版本完成，固定专业版本内容
              professionalFinalContent = chunk.content; // 保存最终内容
              _updateConversation(conversationId, (conv) => conv.updateLastMessage(
                typingMessage.copyWith(
                  content: chunk.content,
                  status: MessageStatus.sent,
                )
              ));
              professionalCompleted = true;
              // 立即切换到大白话阶段，防止后续transition消息覆盖专业版本
              currentStage = 'layman';
              print('专业版本已完成并固定，内容长度: ${chunk.content.length}，切换到大白话阶段');
            } else if (chunk.stage == 'layman') {
              // 大白话版本完成
              if (laymanMessageId != null) {
                _updateConversation(conversationId, (conv) => conv.updateMessage(
                  laymanMessageId!,
                  (msg) => msg.copyWith(content: chunk.content),
                ));
              }
              print('大白话版本已完成');
            }
          } else if (chunk.isContent) {
            // 内容数据
            print('收到内容数据，当前阶段: $currentStage, 内容长度: ${chunk.content.length}');
            if (currentStage == 'professional' && !professionalCompleted) {
              professionalBuffer.write(chunk.content);
              // 实时显示专业版本内容
              _updateConversation(conversationId, (conv) => conv.updateLastMessage(
                typingMessage.copyWith(
                  content: professionalBuffer.toString(),
                  status: MessageStatus.sent,
                )
              ));
            } else if (professionalCompleted && currentStage == 'professional') {
              // 专业版本已完成，但仍收到professional阶段的内容，这可能是延迟的数据，忽略
              print('专业版本已完成，忽略延迟的professional内容数据');
            } else if (currentStage == 'layman') {
              laymanBuffer.write(chunk.content);
              // 如果大白话消息还未创建，先创建它
              if (laymanMessageId == null) {
                final laymanMessage = ChatMessage.laymanDisplay(
                  content: laymanBuffer.toString(),
                  agentId: agentId,
                  status: MessageStatus.sent,
                );
                laymanMessageId = laymanMessage.id;
                _updateConversation(conversationId, (conv) => conv.addMessage(laymanMessage));
              } else {
                // 更新大白话消息内容
                _updateConversation(conversationId, (conv) => conv.updateMessage(
                  laymanMessageId!,
                  (msg) => msg.copyWith(content: laymanBuffer.toString()),
                ));
              }
            }
          } else if (chunk.isComplete) {
            // 最终完成标记
            print('收到最终完成标记');
          } else if (chunk.isError) {
            // 错误处理
            if (laymanMessageId != null) {
              // 如果大白话消息已创建，更新其错误状态
              _updateConversation(conversationId, (conv) => conv.updateMessage(
                laymanMessageId!,
                (msg) => msg.copyWith(
                  content: '获取大白话版本失败: ${chunk.content}',
                  status: MessageStatus.failed,
                ),
              ));
            } else {
              // 否则更新专业版本消息
              _updateConversation(conversationId, (conv) => conv
                  .updateLastMessage(conv.messages.last.copyWith(
                    content: '获取大白话版本失败: ${chunk.content}',
                    status: MessageStatus.failed,
                  )));
            }
            _updateConversation(conversationId, (conv) => conv.copyWith(isStreaming: false, streamingMessageId: null));
            return;
          }
        },
        onDone: () async {
          _streamSubscription = null;

          // 最终保护：确保专业版本内容正确
          if (professionalCompleted && professionalFinalContent != null) {
            final currentConv = state.currentConversation!;
            final lastMessage = currentConv.messages.last;

            // 检查最后一条消息是否为专业版本消息，且内容是否正确
            if (!lastMessage.isLaymanDisplay && lastMessage.content != professionalFinalContent) {
              print('检测到专业版本内容被覆盖，恢复正确内容');
              print('当前内容: "${lastMessage.content}"');
              print('正确内容: "$professionalFinalContent"');

              // 恢复正确的专业版本内容
              _updateConversation(conversationId, (conv) => conv.updateLastMessage(
                lastMessage.copyWith(
                  content: professionalFinalContent!,
                  status: MessageStatus.sent,
                )
              ));
            }
          }

          // 结束流式状态
          _updateConversation(conversationId, (conv) => conv.copyWith(isStreaming: false, streamingMessageId: null));

          // 同步保存完整对话
          await _saveConversationSync(state.currentConversation!);

          // AI响应完成后，刷新用户信息以同步算力
          _refreshUserInfoAfterChat();
        },
        onError: (error) {
          _streamSubscription = null;
          if (laymanMessageId != null) {
            // 如果大白话消息已创建，更新其错误状态
            _updateConversation(conversationId, (conv) => conv.updateMessage(
              laymanMessageId!,
              (msg) => msg.copyWith(
                content: '获取大白话版本失败: $error',
                status: MessageStatus.failed,
              ),
            ));
          } else {
            // 否则更新专业版本消息
            _updateConversation(conversationId, (conv) => conv
                .updateLastMessage(conv.messages.last.copyWith(
                  content: '获取大白话版本失败: $error',
                  status: MessageStatus.failed,
                )));
          }
          _updateConversation(conversationId, (conv) => conv.copyWith(isStreaming: false, streamingMessageId: null));
        },
        cancelOnError: true,
      );

    } catch (error) {
      // 处理错误
      _streamSubscription = null;
      _updateConversation(conversationId, (conv) => conv
          .updateLastMessage(conv.messages.last.copyWith(
            content: '获取大白话版本失败: $error',
            status: MessageStatus.failed,
          ))
          .copyWith(isStreaming: false, streamingMessageId: null));
    }
  }
}