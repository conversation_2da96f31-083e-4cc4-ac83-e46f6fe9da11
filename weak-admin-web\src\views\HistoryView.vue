<template>
  <div class="history">
    <div class="page-header">
      <h2>操作历史</h2>
      <p>查看算力操作和激活码核销历史</p>
    </div>

    <!-- 标签页 -->
    <el-tabs v-model="activeTab" class="history-tabs">
      <!-- 算力操作历史 -->
      <el-tab-pane label="算力操作历史" name="quota">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>算力操作记录</span>
              <div class="header-actions">
                <el-input
                  v-model="quotaFilter.targetUsername"
                  placeholder="搜索用户名"
                  clearable
                  style="width: 200px; margin-right: 10px"
                  @clear="loadQuotaHistory"
                  @keyup.enter="loadQuotaHistory"
                />
                <el-button @click="loadQuotaHistory">搜索</el-button>
                <el-button @click="loadQuotaHistory">刷新</el-button>
              </div>
            </div>
          </template>
          
          <el-table
            v-loading="quotaLoading"
            :data="quotaData"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="targetUsername" label="目标用户" width="150" />
            <el-table-column prop="operationType" label="操作类型" width="120">
              <template #default="{ row }">
                <el-tag :type="row.operationType === 'increase' ? 'success' : 'warning'">
                  {{ row.operationType === 'increase' ? '增加' : '减少' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="quotaAmount" label="算力数量" width="120" />
            <el-table-column prop="quotaBefore" label="操作前" width="120" />
            <el-table-column prop="quotaAfter" label="操作后" width="120" />
            <el-table-column prop="reason" label="操作原因" min-width="200">
              <template #default="{ row }">
                <span>{{ row.reason || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="createdAt" label="操作时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.createdAt) }}
              </template>
            </el-table-column>
          </el-table>
          
          <div class="pagination">
            <el-pagination
              v-model:current-page="quotaPagination.page"
              v-model:page-size="quotaPagination.pageSize"
              :total="quotaPagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="loadQuotaHistory"
              @current-change="loadQuotaHistory"
            />
          </div>
        </el-card>
      </el-tab-pane>

      <!-- 激活码核销历史 -->
      <el-tab-pane label="激活码核销历史" name="activation">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>激活码核销记录</span>
              <el-button @click="loadActivationHistory">刷新</el-button>
            </div>
          </template>
          
          <el-table
            v-loading="activationLoading"
            :data="activationData"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="activationCode" label="激活码" width="300">
              <template #default="{ row }">
                <span class="code-display">{{ row.activationCode.substring(0, 20) }}...</span>
              </template>
            </el-table-column>
            <el-table-column prop="quotaAmount" label="算力数量" width="120" />
            <el-table-column prop="usedBy" label="使用者" width="150" />
            <el-table-column prop="usedAt" label="使用时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.usedAt) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag type="success">{{ row.status === 'used' ? '已使用' : '未使用' }}</el-tag>
              </template>
            </el-table-column>
          </el-table>
          
          <div class="pagination">
            <el-pagination
              v-model:current-page="activationPagination.page"
              v-model:page-size="activationPagination.pageSize"
              :total="activationPagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="loadActivationHistory"
              @current-change="loadActivationHistory"
            />
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import weakAdminService from '@/services/weakAdminService'

// 当前活动标签
const activeTab = ref('quota')

// 算力操作相关状态
const quotaLoading = ref(false)
const quotaData = ref([])
const quotaPagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})
const quotaFilter = reactive({
  targetUsername: ''
})

// 激活码核销相关状态
const activationLoading = ref(false)
const activationData = ref([])
const activationPagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 加载算力操作历史
const loadQuotaHistory = async () => {
  try {
    quotaLoading.value = true
    const response = await weakAdminService.getQuotaOperations(
      quotaPagination.page,
      quotaPagination.pageSize,
      quotaFilter.targetUsername
    )

    if (response.success) {
      quotaData.value = response.data.data
      quotaPagination.total = response.data.total
    } else {
      ElMessage.error(response.error?.message || '加载算力操作历史失败')
    }
  } catch (error) {
    console.error('Load quota history error:', error)
    ElMessage.error('加载算力操作历史失败')
  } finally {
    quotaLoading.value = false
  }
}

// 加载激活码核销历史
const loadActivationHistory = async () => {
  try {
    activationLoading.value = true
    const response = await weakAdminService.getActivationHistory(
      activationPagination.page,
      activationPagination.pageSize
    )

    if (response.success) {
      activationData.value = response.data.data
      activationPagination.total = response.data.total
    } else {
      ElMessage.error(response.error?.message || '加载激活码历史失败')
    }
  } catch (error) {
    console.error('Load activation history error:', error)
    ElMessage.error('加载激活码历史失败')
  } finally {
    activationLoading.value = false
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// 监听标签切换
watch(activeTab, (newTab) => {
  if (newTab === 'quota') {
    loadQuotaHistory()
  } else if (newTab === 'activation') {
    loadActivationHistory()
  }
})

// 组件挂载时加载数据
onMounted(() => {
  loadQuotaHistory()
})
</script>

<style scoped>
.history {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 5px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.history-tabs {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.code-display {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-tab-pane) {
  padding: 0;
}
</style>
