# Token续签短期测试快速指南

## 🎯 测试目标
快速验证Token自动续签功能的完整工作流程，包括定时续签、被动续签、应用生命周期处理等。

## ⚡ 快速执行步骤

### 第一步：调整配置为测试模式

#### 1.1 修改云函数配置
**文件：** `cloudfunctions/exeFunction/src/config/auth.js`
```javascript
// 将 JWT_EXPIRES_IN 从 '2h' 改为 '2m'
const JWT_EXPIRES_IN = '2m'  // 测试用：2分钟
```

#### 1.2 修改前端TokenManager
**文件：** `lib/src/services/token_manager.dart`
```dart
// 找到续签提前时间配置，将30分钟改为30秒
const Duration(seconds: 30)  // 测试用：过期前30秒续签
```

#### 1.3 修改前端AuthProvider
**文件：** `lib/src/providers/auth_provider.dart`
```dart
// 找到token过期时间设置，将2小时改为2分钟
DateTime.now().add(const Duration(minutes: 2))  // 测试用：2分钟有效期
```

### 第二步：部署测试配置
```bash
# 1. 部署云函数更新
updateFunctionCode_cloudbase-mcp

# 2. 重新编译前端应用
flutter run -d windows
```

### 第三步：执行测试用例

#### 测试用例1：定时续签（2分钟测试）
1. 登录应用，记录当前时间
2. 观察控制台日志，寻找 "TokenManager" 相关输出
3. 等待1分30秒，观察是否触发自动续签
4. 验证续签成功后应用功能正常

**预期结果：**
```
flutter: TokenManager: 启动自动续签，Token过期时间: [时间]
flutter: TokenManager: 开始执行token续签
flutter: TokenManager: Token续签成功
flutter: AuthProvider: Token续签成功
```

#### 测试用例2：被动续签（网络中断测试）
1. 登录应用
2. 断网2分钟以上（让token过期）
3. 恢复网络
4. 尝试发送消息或刷新用户信息
5. 观察是否触发401错误和自动续签

**预期结果：**
```
flutter: HttpClient: 收到401错误，尝试刷新token
flutter: HttpClient: Token刷新成功
flutter: [原请求重试成功]
```

#### 测试用例3：应用生命周期（后台测试）
1. 登录应用
2. 最小化应用2分钟以上
3. 恢复应用到前台
4. 观察是否自动检查并续签token

**预期结果：**
```
flutter: AuthProvider: 应用恢复，检查token状态
flutter: TokenManager: Token需要续签
flutter: TokenManager: Token续签成功
```

### 第四步：恢复正常配置

#### 4.1 恢复云函数配置
```javascript
const JWT_EXPIRES_IN = '2h'  // 恢复正常：2小时
```

#### 4.2 恢复前端配置
```dart
// TokenManager
const Duration(minutes: 30)  // 恢复正常：过期前30分钟续签

// AuthProvider
DateTime.now().add(const Duration(hours: 2))  // 恢复正常：2小时有效期
```

#### 4.3 重新部署
```bash
# 部署云函数更新
updateFunctionCode_cloudbase-mcp

# 重新编译前端应用
flutter run -d windows
```

## 📋 测试检查清单

### 配置修改检查
- [ ] 云函数JWT_EXPIRES_IN改为'2m'
- [ ] TokenManager续签提前时间改为30秒
- [ ] AuthProvider过期时间改为2分钟
- [ ] 云函数已重新部署
- [ ] 前端应用已重新编译

### 功能测试检查
- [ ] 定时续签：1分30秒后自动触发
- [ ] 被动续签：401错误触发续签
- [ ] 生命周期：应用恢复时检查token
- [ ] 错误处理：续签失败正确处理
- [ ] 日志输出：TokenManager日志完整

### 恢复配置检查
- [ ] 云函数JWT_EXPIRES_IN恢复为'2h'
- [ ] TokenManager续签提前时间恢复为30分钟
- [ ] AuthProvider过期时间恢复为2小时
- [ ] 云函数已重新部署
- [ ] 前端应用已重新编译
- [ ] 正常配置下功能验证通过

## 🔍 关键日志监控

### TokenManager日志
```
TokenManager: 启动自动续签
TokenManager: 开始执行token续签
TokenManager: Token续签成功/失败
```

### AuthProvider日志
```
AuthProvider: Token续签成功
AuthProvider: Token续签失败，需要重新登录
```

### HttpClient日志
```
HttpClient: 收到401错误，尝试刷新token
HttpClient: Token刷新成功/失败
```

## ⚠️ 注意事项

1. **测试期间用户体验**：2分钟的token有效期会导致频繁续签，属于正常现象
2. **网络环境**：确保测试环境网络稳定
3. **数据备份**：测试前备份重要数据
4. **时间控制**：严格按照时间节点观察现象
5. **日志记录**：保存完整的测试日志用于分析

## 🎯 成功标准

- ✅ 定时续签在过期前30秒自动触发
- ✅ 401错误能触发被动续签并重试成功
- ✅ 应用恢复时能自动检查token状态
- ✅ 续签失败时能正确处理并提示
- ✅ 所有续签操作都有完整的日志记录
- ✅ 恢复正常配置后功能正常

## 📞 问题排查

### 如果定时续签没有触发
1. 检查TokenManager是否正确启动
2. 检查token过期时间是否正确设置
3. 检查续签提前时间配置

### 如果被动续签失败
1. 检查HttpClientService回调设置
2. 检查401错误是否正确捕获
3. 检查refresh token是否有效

### 如果应用恢复时没有检查
1. 检查应用生命周期监听是否正确
2. 检查onAppResumed方法是否被调用
3. 检查token状态判断逻辑

---

**下次对话时，直接按照这个指南执行即可快速完成Token续签功能的短期测试验证！**
