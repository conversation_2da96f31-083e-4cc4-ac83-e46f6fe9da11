#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
八字排盘边界案例全面测试
基于万年历数据库中的关键边界时刻进行测试
"""

import requests
import json
import time

# 云函数API地址
API_URL = "https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction"

def test_bazi_case(name, description, date, time_str, expected_results, zi_time_handling=0):
    """测试单个八字边界案例"""
    print(f"\n=== {name} ===")
    print(f"描述：{description}")
    print(f"测试时间：{date} {time_str}")
    
    request_data = {
        "action": "baziAnalyze",
        "name": "测试者",  # 使用简单的姓名，避免特殊字符
        "gender": "男",
        "calendarType": "公历",
        "birthDate": date,
        "birthTime": time_str,
        "isLeapMonth": False,
        "birthPlace": "北京",
        "ziTimeHandling": zi_time_handling
    }
    
    print(f"子时处理：{'晚子时（当天）' if zi_time_handling == 0 else '早子时（第二天）'}")
    
    try:
        response = requests.post(
            API_URL,
            headers={'Content-Type': 'application/json'},
            json=request_data,
            timeout=30
        )
        
        if response.status_code != 200:
            print(f"❌ HTTP错误：{response.status_code}")
            return False
            
        result = response.json()
        
        if result.get('code') != 0:
            print(f"❌ API错误：{result.get('message', '未知错误')}")
            return False
            
        data = result.get('data', {})
        bazi_str = data.get('baziStr', '')
        
        print(f"✅ 请求成功")
        print(f"八字结果：{bazi_str}")
        
        # 验证预期结果
        success = True
        for key, expected_value in expected_results.items():
            actual_value = data.get(key, {})
            if isinstance(actual_value, dict) and 'ganZhi' in actual_value:
                actual_value = actual_value['ganZhi']
            
            if actual_value == expected_value:
                print(f"✅ {key}：{actual_value} （正确）")
            else:
                print(f"❌ {key}：{actual_value} （预期：{expected_value}）")
                success = False
        
        return success
        
    except Exception as e:
        print(f"❌ 请求异常：{str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始八字排盘边界案例全面测试")
    print("基于万年历数据库中的关键边界时刻")
    print("=" * 80)
    
    test_cases = []
    
    # 1. 立春节气边界案例（子时时段）- 使用近期年份
    test_cases.extend([
        {
            "name": "立春边界1",
            "description": "2005年2月4日01:43立春，测试立春前后的年柱变化",
            "date": "2005-02-04",
            "time": "01:40",  # 立春前3分钟
            "expected": {"year": "甲申"},  # 应该还是前一年
            "zi_time_handling": 0
        },
        {
            "name": "立春边界2",
            "description": "2005年2月4日01:43立春，测试立春后的年柱",
            "date": "2005-02-04",
            "time": "01:45",  # 立春后2分钟
            "expected": {"year": "乙酉"},  # 应该是新年
            "zi_time_handling": 0
        },
        {
            "name": "立春边界3",
            "description": "2009年2月4日00:49立春，测试子时立春",
            "date": "2009-02-04",
            "time": "00:45",  # 立春前4分钟
            "expected": {"year": "戊子"},  # 应该还是前一年
            "zi_time_handling": 0
        },
        {
            "name": "立春边界4",
            "description": "2009年2月4日00:49立春，测试子时立春后",
            "date": "2009-02-04",
            "time": "00:55",  # 立春后6分钟
            "expected": {"year": "己丑"},  # 应该是新年
            "zi_time_handling": 0
        }
    ])
    
    # 2. 立冬节气边界案例（子时时段）- 使用近期年份
    test_cases.extend([
        {
            "name": "立冬边界1",
            "description": "1990年11月8日00:23立冬，测试立冬前的月柱",
            "date": "1990-11-08",
            "time": "00:20",  # 立冬前3分钟
            "expected": {"month": "丙戌"},  # 应该还是前一月
            "zi_time_handling": 0
        },
        {
            "name": "立冬边界2",
            "description": "1990年11月8日00:23立冬，测试立冬后的月柱",
            "date": "1990-11-08",
            "time": "00:25",  # 立冬后2分钟
            "expected": {"month": "丁亥"},  # 应该是新月
            "zi_time_handling": 0
        },
        {
            "name": "立冬边界3",
            "description": "2015年11月8日01:58立冬，测试深夜立冬前",
            "date": "2015-11-08",
            "time": "01:55",  # 立冬前3分钟
            "expected": {"month": "丙戌"},  # 应该还是前一月
            "zi_time_handling": 0
        },
        {
            "name": "立冬边界4",
            "description": "2015年11月8日01:58立冬，测试深夜立冬后",
            "date": "2015-11-08",
            "time": "02:05",  # 立冬后7分钟
            "expected": {"month": "丁亥"},  # 应该是新月
            "zi_time_handling": 0
        }
    ])
    
    # 3. 冬至节气边界案例（子时时段）- 使用近期年份
    test_cases.extend([
        {
            "name": "冬至边界1",
            "description": "2021年12月21日23:59冬至，测试冬至前的月柱",
            "date": "2021-12-21",
            "time": "23:55",  # 冬至前4分钟
            "expected": {"month": "庚子"},  # 应该还是前一月
            "zi_time_handling": 0
        },
        {
            "name": "冬至边界2",
            "description": "2021年12月21日23:59冬至，测试跨日冬至后",
            "date": "2021-12-22",
            "time": "00:05",  # 冬至后6分钟（跨日）
            "expected": {"month": "庚子"},  # 冬至不换月柱，应该还是同一月
            "zi_time_handling": 0
        }
    ])
    
    # 4. 子时处理规则测试（使用已知的子时案例）
    test_cases.extend([
        {
            "name": "子时规则1",
            "description": "1990年12月15日23:30，晚子时规则测试",
            "date": "1990-12-15",
            "time": "23:30",
            "expected": {"day": "甲寅"},  # 晚子时，日柱不变
            "zi_time_handling": 0
        },
        {
            "name": "子时规则2",
            "description": "1990年12月15日23:30，早子时规则测试",
            "date": "1990-12-15",
            "time": "23:30",
            "expected": {"day": "乙卯"},  # 早子时，日柱推进
            "zi_time_handling": 1
        }
    ])
    
    # 5. 极端边界案例（节气交接的精确时刻）
    test_cases.extend([
        {
            "name": "极端边界1",
            "description": "2021年12月21日23:59冬至，测试秒级精度",
            "date": "2021-12-21",
            "time": "23:59",  # 冬至当时
            "expected": {"month": "庚子"},  # 验证月柱计算
            "zi_time_handling": 0
        },
        {
            "name": "极端边界2",
            "description": "2017年2月3日23:34立春，测试深夜立春",
            "date": "2017-02-03",
            "time": "23:30",  # 立春前4分钟
            "expected": {"year": "丙申"},  # 应该还是前一年
            "zi_time_handling": 0
        },
        {
            "name": "极端边界3",
            "description": "2017年2月3日23:34立春，测试深夜立春后",
            "date": "2017-02-03",
            "time": "23:40",  # 立春后6分钟
            "expected": {"year": "丁酉"},  # 应该是新年
            "zi_time_handling": 0
        }
    ])
    
    # 执行所有测试
    total_tests = len(test_cases)
    passed_tests = 0
    failed_cases = []
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n[{i}/{total_tests}] ", end="")
        success = test_bazi_case(
            case["name"],
            case["description"],
            case["date"],
            case["time"],
            case["expected"],
            case.get("zi_time_handling", 0)
        )
        
        if success:
            passed_tests += 1
        else:
            failed_cases.append(case["name"])
        
        # 避免请求过快
        if i < total_tests:
            time.sleep(0.5)
    
    # 汇总测试结果
    print("\n" + "=" * 80)
    print("📊 边界案例测试结果汇总")
    print("=" * 80)
    
    print(f"总测试数：{total_tests}")
    print(f"通过测试：{passed_tests}")
    print(f"失败测试：{total_tests - passed_tests}")
    print(f"通过率：{passed_tests/total_tests*100:.1f}%")
    
    if failed_cases:
        print(f"\n❌ 失败的测试案例：")
        for case_name in failed_cases:
            print(f"  - {case_name}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有边界案例测试通过！八字排盘系统在关键边界时刻表现完美！")
        print("✅ 节气交接时刻处理正确")
        print("✅ 子时处理规则正确")
        print("✅ 年月日柱计算精确")
        print("✅ 极端边界情况稳定")
    else:
        print(f"\n⚠️  有 {total_tests - passed_tests} 个边界案例测试失败，需要进一步优化")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
