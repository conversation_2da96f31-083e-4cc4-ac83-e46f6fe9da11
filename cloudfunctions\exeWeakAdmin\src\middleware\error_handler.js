/**
 * 错误代码常量
 */
const ERROR_CODES = {
  // 通用错误
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  
  // 认证相关错误
  TOKEN_MISSING: 'TOKEN_MISSING',
  TOKEN_INVALID: 'TOKEN_INVALID',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  
  // 用户相关错误
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  USER_DISABLED: 'USER_DISABLED',
  
  // 业务相关错误
  ACTIVATION_CODE_INVALID: 'ACTIVATION_CODE_INVALID',
  ACTIVATION_CODE_USED: 'ACTIVATION_CODE_USED',
  INSUFFICIENT_QUOTA: 'INSUFFICIENT_QUOTA'
}

/**
 * 业务错误类
 */
class BusinessError extends Error {
  constructor(code, message, statusCode = 400, details = null) {
    super(message)
    this.name = 'BusinessError'
    this.code = code
    this.statusCode = statusCode
    this.details = details
  }
}

/**
 * 创建业务错误
 * @param {string} code 错误代码
 * @param {string} message 错误消息
 * @param {number} statusCode HTTP状态码
 * @param {object} details 错误详情
 * @returns {BusinessError} 业务错误实例
 */
function createBusinessError(code, message, statusCode = 400, details = null) {
  return new BusinessError(code, message, statusCode, details)
}

/**
 * 成功响应
 * @param {object} data 响应数据
 * @param {string} message 响应消息
 * @returns {object} 成功响应对象
 */
function successResponse(data = null, message = '操作成功') {
  return {
    success: true,
    message,
    data,
    timestamp: new Date().toISOString()
  }
}

/**
 * 错误响应
 * @param {string} code 错误代码
 * @param {string} message 错误消息
 * @param {number} statusCode HTTP状态码
 * @param {object} details 错误详情
 * @returns {object} 错误响应对象
 */
function errorResponse(code, message, statusCode = 400, details = null) {
  return {
    success: false,
    error: {
      code,
      message,
      statusCode,
      details
    },
    timestamp: new Date().toISOString()
  }
}

/**
 * 错误处理器
 * @param {Error} error 错误对象
 * @returns {object} 错误响应
 */
function errorHandler(error) {
  // 业务错误
  if (error instanceof BusinessError) {
    return errorResponse(error.code, error.message, error.statusCode, error.details)
  }
  
  // JWT相关错误
  if (error.name === 'TokenExpiredError') {
    return errorResponse(ERROR_CODES.TOKEN_EXPIRED, 'Token已过期', 401)
  }
  
  if (error.name === 'JsonWebTokenError') {
    return errorResponse(ERROR_CODES.TOKEN_INVALID, 'Token无效', 401)
  }
  
  // 数据库相关错误
  if (error.message && error.message.includes('collection')) {
    return errorResponse(ERROR_CODES.INTERNAL_ERROR, '数据库操作失败', 500)
  }
  
  // 其他未知错误
  console.error('Unhandled error:', error)
  return errorResponse(
    ERROR_CODES.INTERNAL_ERROR,
    '服务器内部错误',
    500,
    process.env.NODE_ENV === 'development' ? error.stack : null
  )
}

module.exports = {
  ERROR_CODES,
  BusinessError,
  createBusinessError,
  successResponse,
  errorResponse,
  errorHandler
}
