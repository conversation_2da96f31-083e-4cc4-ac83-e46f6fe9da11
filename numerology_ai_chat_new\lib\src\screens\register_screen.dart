import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../core/constants/app_constants.dart';
import '../providers/auth_provider.dart';
import '../providers/theme_provider.dart';
import '../core/router/routes.dart';
import '../utils/validators.dart';
import '../widgets/custom_title_bar.dart';

/// 注册页面
class RegisterScreen extends ConsumerStatefulWidget {
  const RegisterScreen({super.key});
  
  @override
  ConsumerState<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends ConsumerState<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _activationCodeController = TextEditingController();

  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _agreeToTerms = false;

  // 激活码验证相关状态
  bool _isValidatingCode = false;
  bool? _isCodeValid;
  String? _codeValidationMessage;
  int? _codeQuota;
  
  @override
  void dispose() {
    _usernameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _activationCodeController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    final themeProvider = ref.watch(themeProviderNotifier);
    final theme = Theme.of(context);

    return Scaffold(
      body: Column(
        children: [
          // 自定义标题栏
          CustomTitleBar(
            title: Row(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(6),
                    child: Image.asset(
                      'assets/images/logo.png',
                      width: 24,
                      height: 24,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        // 如果图片加载失败，显示默认图标
                        return Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                theme.colorScheme.primary,
                                theme.colorScheme.secondary,
                              ],
                            ),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: const Icon(
                            Icons.auto_awesome,
                            size: 16,
                            color: Colors.white,
                          ),
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(AppConstants.appName),
              ],
            ),
          ),

          // 主体内容
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: themeProvider.isDarkMode
                      ? [
                          const Color(0xFF0F172A),
                          const Color(0xFF1E293B),
                        ]
                      : [
                          const Color(0xFFF8FAFC),
                          const Color(0xFFE2E8F0),
                        ],
                ),
              ),
              child: Center(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(32.0),
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(maxWidth: 400),
                    child: Card(
                      elevation: 8,
                      child: Padding(
                        padding: const EdgeInsets.all(32.0),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              // 返回按钮
                              Row(
                                children: [
                                  IconButton(
                                    icon: const Icon(Icons.arrow_back),
                                    onPressed: () => context.go(Routes.login),
                                  ),
                                  const Spacer(),
                                ],
                              ),

                              // 头部信息
                              _buildHeader(theme),
                              const SizedBox(height: 32),

                              // 注册表单
                              _buildRegisterForm(theme),
                              const SizedBox(height: 16),

                              // 服务条款同意
                              _buildTermsAgreement(theme),
                              const SizedBox(height: 24),

                              // 注册按钮
                              _buildRegisterButton(),
                              const SizedBox(height: 16),

                              // 登录链接
                              _buildLoginLink(theme),
                              const SizedBox(height: 16),

                              // 主题切换按钮
                              _buildThemeToggle(themeProvider),

                              // 错误信息显示
                              if (authState.errorMessage != null) ...[
                                const SizedBox(height: 16),
                                _buildErrorMessage(theme, authState.errorMessage!),
                              ],
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildHeader(ThemeData theme) {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Image.asset(
              'assets/images/logo.png',
              width: 80,
              height: 80,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                // 如果图片加载失败，显示默认图标
                return Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        theme.colorScheme.primary,
                        theme.colorScheme.secondary,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(
                    Icons.auto_awesome,
                    size: 40,
                    color: Colors.white,
                  ),
                );
              },
            ),
          ),
        ),
        const SizedBox(height: 16),
        Text(
          '创建账号',
          style: theme.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '加入鲸准大师，开启智能命理之旅',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
  
  Widget _buildRegisterForm(ThemeData theme) {
    return Column(
      children: [
        TextFormField(
          controller: _usernameController,
          decoration: const InputDecoration(
            labelText: '用户名（8-30位数字和字母）',
            prefixIcon: Icon(Icons.person_outline),
            hintText: '请输入用户名（建议使用手机号）',
          ),
          inputFormatters: [
            // 只允许数字和大小写英文字母
            FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9]')),
          ],
          validator: Validators.validateUsername,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _emailController,
          decoration: const InputDecoration(
            labelText: '邮箱（可选）',
            prefixIcon: Icon(Icons.email_outlined),
            hintText: '请输入邮箱地址',
          ),
          keyboardType: TextInputType.emailAddress,
          validator: (value) {
            if (value != null && value.isNotEmpty && !value.contains('@')) {
              return '请输入有效的邮箱地址';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _passwordController,
          obscureText: !_isPasswordVisible,
          decoration: InputDecoration(
            labelText: '密码（8-50位字母数字符号）',
            prefixIcon: const Icon(Icons.lock_outline),
            hintText: '请输入密码',
            suffixIcon: IconButton(
              icon: Icon(
                _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
              ),
              onPressed: () {
                setState(() {
                  _isPasswordVisible = !_isPasswordVisible;
                });
              },
            ),
          ),
          inputFormatters: [
            // 禁止输入中文字符
            FilteringTextInputFormatter.deny(RegExp(r'[\u4e00-\u9fa5]')),
          ],
          validator: Validators.validatePassword,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _confirmPasswordController,
          obscureText: !_isConfirmPasswordVisible,
          decoration: InputDecoration(
            labelText: '确认密码',
            prefixIcon: const Icon(Icons.lock_outline),
            hintText: '请再次输入密码',
            suffixIcon: IconButton(
              icon: Icon(
                _isConfirmPasswordVisible ? Icons.visibility_off : Icons.visibility,
              ),
              onPressed: () {
                setState(() {
                  _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
                });
              },
            ),
          ),
          inputFormatters: [
            // 禁止输入中文字符
            FilteringTextInputFormatter.deny(RegExp(r'[\u4e00-\u9fa5]')),
          ],
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '请确认密码';
            }
            // 先验证密码格式
            final passwordError = Validators.validatePassword(value);
            if (passwordError != null) {
              return passwordError;
            }
            // 再验证是否一致
            if (value != _passwordController.text) {
              return '两次输入的密码不一致';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        // 激活码输入框
        _buildActivationCodeField(theme),
      ],
    );
  }
  
  Widget _buildTermsAgreement(ThemeData theme) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Checkbox(
          value: _agreeToTerms,
          onChanged: (value) {
            setState(() {
              _agreeToTerms = value ?? false;
            });
          },
        ),
        Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _agreeToTerms = !_agreeToTerms;
              });
            },
            child: Padding(
              padding: const EdgeInsets.only(top: 12),
              child: RichText(
                text: TextSpan(
                  style: theme.textTheme.bodySmall,
                  children: [
                    const TextSpan(text: '我已阅读并同意'),
                    TextSpan(
                      text: '《用户协议》',
                      style: TextStyle(
                        color: theme.colorScheme.primary,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                    const TextSpan(text: '和'),
                    TextSpan(
                      text: '《隐私政策》',
                      style: TextStyle(
                        color: theme.colorScheme.primary,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRegisterButton() {
    final authState = ref.watch(authProvider);

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: (authState.isLoading || !_agreeToTerms) ? null : _handleRegister,
        child: authState.isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : const Text('注册'),
      ),
    );
  }

  Widget _buildLoginLink(ThemeData theme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          '已有账号？',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
        TextButton(
          onPressed: () {
            context.go(Routes.login);
          },
          child: const Text('立即登录'),
        ),
      ],
    );
  }

  Widget _buildThemeToggle(ThemeProvider themeProvider) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          themeProvider.themeModeIcon,
          size: 20,
          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
        ),
        const SizedBox(width: 8),
        TextButton(
          onPressed: () => ref.read(themeProviderNotifier.notifier).toggleTheme(),
          child: Text(themeProvider.themeModeDisplayName),
        ),
      ],
    );
  }

  Widget _buildErrorMessage(ThemeData theme, String error) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.error.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: theme.colorScheme.error,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              error,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onErrorContainer,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleRegister() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!_agreeToTerms) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请先同意用户协议和隐私政策'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final username = _usernameController.text.trim();
    final email = _emailController.text.trim();
    final password = _passwordController.text;
    final activationCode = _activationCodeController.text.trim();

    // 额外检查激活码是否为空（双重保险）
    if (activationCode.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请输入激活码后再注册'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    try {
      final success = await ref.read(authProvider.notifier).register(
        username,
        email,
        password,
        activationCode, // 直接传递激活码，不再判断是否为空
      );

      if (mounted && success) {
        final authState = ref.read(authProvider);

        // 构建成功消息
        String successMessage = '注册成功';
        if (_codeQuota != null && _codeQuota! > 0) {
          successMessage += '，获得${_codeQuota}算力';
        }

        if (authState.isAuthenticated) {
          // 注册成功并自动登录，跳转到主页
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('$successMessage并已自动登录！'),
              backgroundColor: Colors.green,
            ),
          );
          context.go(Routes.home);
        } else {
          // 注册成功但需要手动登录
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('$successMessage！请登录您的账号'),
              backgroundColor: Colors.green,
            ),
          );
          context.go(Routes.login);
        }
      }
    } catch (e) {
      // 错误已经在authProvider中处理，这里不需要额外处理
    }
  }

  /// 构建激活码输入框
  Widget _buildActivationCodeField(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _activationCodeController,
          decoration: InputDecoration(
            labelText: '激活码（必填）',
            prefixIcon: const Icon(Icons.card_giftcard_outlined),
            hintText: '请输入激活码获得算力',
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 清除按钮（当有内容时显示）
                if (_activationCodeController.text.isNotEmpty)
                  IconButton(
                    icon: const Icon(Icons.clear, size: 20),
                    onPressed: _clearActivationCode,
                    tooltip: '清除激活码',
                  ),
                // 粘贴按钮
                IconButton(
                  icon: const Icon(Icons.content_paste, size: 20),
                  onPressed: _pasteActivationCode,
                  tooltip: '粘贴激活码',
                ),
                // 验证状态图标
                if (_buildActivationCodeSuffix() != null)
                  _buildActivationCodeSuffix()!,
              ],
            ),
            helperText: '💡 激活码为必填项，输入后可获得相应算力。请联系管理员获取激活码，客服：18595672129（微信同号）',
            helperMaxLines: 3,
          ),
          onChanged: _onActivationCodeChanged,
          validator: _validateActivationCode,
          maxLines: 1,
          textInputAction: TextInputAction.done,
        ),
        if (_codeValidationMessage != null) ...[
          const SizedBox(height: 4),
          Text(
            _codeValidationMessage!,
            style: TextStyle(
              fontSize: 12,
              color: _isCodeValid == true
                  ? Colors.green
                  : theme.colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  /// 构建激活码输入框后缀图标
  Widget? _buildActivationCodeSuffix() {
    if (_isValidatingCode) {
      return const Padding(
        padding: EdgeInsets.all(12.0),
        child: SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      );
    }

    if (_isCodeValid == true) {
      return const Icon(Icons.check_circle, color: Colors.green);
    }

    if (_isCodeValid == false) {
      return const Icon(Icons.error, color: Colors.red);
    }

    return null;
  }

  /// 激活码输入变化处理
  void _onActivationCodeChanged(String value) {
    // 更新UI状态（用于显示/隐藏清除按钮）
    setState(() {});

    if (value.trim().isEmpty) {
      setState(() {
        _isCodeValid = null;
        _codeValidationMessage = null;
        _codeQuota = null;
      });
      return;
    }

    // 防抖处理，避免频繁验证
    Future.delayed(const Duration(milliseconds: 500), () {
      if (_activationCodeController.text.trim() == value.trim()) {
        _validateActivationCodeAsync(value.trim());
      }
    });
  }

  /// 激活码表单验证
  String? _validateActivationCode(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '请输入激活码'; // 必填字段，不允许为空
    }

    // 基本格式检查
    if (value.trim().length < 10) {
      return '激活码长度不足，请检查输入是否完整';
    }

    // 检查是否包含非法字符（激活码应该是Base64格式）
    if (!RegExp(r'^[A-Za-z0-9+/=]+$').hasMatch(value.trim())) {
      return '激活码包含非法字符，请重新输入';
    }

    return null;
  }

  /// 异步验证激活码
  Future<void> _validateActivationCodeAsync(String code) async {
    if (code.isEmpty) return;

    setState(() {
      _isValidatingCode = true;
      _isCodeValid = null;
      _codeValidationMessage = null;
    });

    try {
      // 调用云函数验证激活码
      final result = await ref.read(authProvider.notifier).validateActivationCode(code);

      if (mounted) {
        setState(() {
          _isValidatingCode = false;
          if (result['valid'] == true) {
            _isCodeValid = true;
            _codeQuota = result['quota'] as int?;
            _codeValidationMessage = _codeQuota != null
                ? '✓ 有效激活码，可获得${_codeQuota}算力'
                : '✓ 激活码有效';
          } else {
            _isCodeValid = false;
            final errorMsg = result['error'] as String? ?? '激活码无效';
            _codeValidationMessage = _formatErrorMessage(errorMsg);
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isValidatingCode = false;
          _isCodeValid = false;
          _codeValidationMessage = '网络错误，请检查网络连接后重试';
        });
      }
    }
  }

  /// 粘贴激活码
  Future<void> _pasteActivationCode() async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      if (clipboardData?.text != null && clipboardData!.text!.isNotEmpty) {
        final pastedText = clipboardData.text!.trim();
        _activationCodeController.text = pastedText;

        // 触发验证
        _onActivationCodeChanged(pastedText);

        // 显示粘贴成功提示
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('激活码已粘贴'),
              duration: Duration(seconds: 1),
            ),
          );
        }
      } else {
        // 剪贴板为空
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('剪贴板中没有内容'),
              duration: Duration(seconds: 1),
            ),
          );
        }
      }
    } catch (e) {
      // 粘贴失败
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('粘贴失败'),
            duration: Duration(seconds: 1),
          ),
        );
      }
    }
  }

  /// 清除激活码
  void _clearActivationCode() {
    _activationCodeController.clear();
    setState(() {
      _isCodeValid = null;
      _codeValidationMessage = null;
      _codeQuota = null;
    });
  }

  /// 格式化错误消息，使其更用户友好
  String _formatErrorMessage(String errorMsg) {
    // 常见错误消息的友好化处理
    if (errorMsg.contains('激活码格式无效') || errorMsg.contains('激活码数据不完整')) {
      return '✗ 激活码格式不正确，请检查后重新输入';
    } else if (errorMsg.contains('激活码已被使用')) {
      return '✗ 此激活码已被使用，请使用其他激活码';
    } else if (errorMsg.contains('激活码已过期')) {
      return '✗ 激活码已过期，请联系管理员获取新的激活码';
    } else if (errorMsg.contains('激活码版本不支持')) {
      return '✗ 激活码版本过旧，请联系管理员获取新的激活码';
    } else if (errorMsg.contains('网络') || errorMsg.contains('连接')) {
      return '✗ 网络连接失败，请检查网络后重试';
    } else {
      return '✗ $errorMsg';
    }
  }
}
