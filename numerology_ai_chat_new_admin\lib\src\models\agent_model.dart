
import 'package:json_annotation/json_annotation.dart';

part 'agent_model.g.dart';

@JsonSerializable()
class Agent {
  final String agentId;
  final String agentName;
  final String agentDisplayName;
  final String agentType;
  final String description;
  final bool isActive;
  final int sortOrder;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool hasPrompt;

  Agent({
    required this.agentId,
    required this.agentName,
    required this.agentDisplayName,
    required this.agentType,
    required this.description,
    required this.isActive,
    required this.sortOrder,
    required this.createdAt,
    required this.updatedAt,
    required this.hasPrompt,
  });

  factory Agent.fromJson(Map<String, dynamic> json) => _$AgentFromJson(json);
  Map<String, dynamic> toJson() => _$AgentToJson(this);
}
