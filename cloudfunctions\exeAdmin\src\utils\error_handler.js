const logger = require('./logger')

/**
 * 错误码定义
 */
const ERROR_CODES = {
  // 通用错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  INVALID_PARAMS: 'INVALID_PARAMS',
  
  // 认证相关错误
  ADMIN_NOT_FOUND: 'ADMIN_NOT_FOUND',
  INVALID_PASSWORD: 'INVALID_PASSWORD',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',
  UNAUTHORIZED: 'UNAUTHORIZED',
  
  // 用户管理错误
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  USER_ALREADY_EXISTS: 'USER_ALREADY_EXISTS',
  INVALID_USER_STATUS: 'INVALID_USER_STATUS',
  
  // 智能体管理错误
  AGENT_NOT_FOUND: 'AGENT_NOT_FOUND',
  AGENT_ALREADY_EXISTS: 'AGENT_ALREADY_EXISTS',
  
  // 模型管理错误
  MODEL_NOT_FOUND: 'MODEL_NOT_FOUND',
  MODEL_ALREADY_EXISTS: 'MODEL_ALREADY_EXISTS',
  
  // 页面配置错误
  PAGE_NOT_FOUND: 'PAGE_NOT_FOUND',
  SLUG_ALREADY_EXISTS: 'SLUG_ALREADY_EXISTS',

  // 智能体错误
  AGENT_NOT_FOUND: 'AGENT_NOT_FOUND',
  AGENT_ALREADY_EXISTS: 'AGENT_ALREADY_EXISTS',
  
  // 数据库错误
  DATABASE_ERROR: 'DATABASE_ERROR',
  
  // 权限错误
  PERMISSION_DENIED: 'PERMISSION_DENIED'
}

/**
 * 自定义业务异常类
 */
class BusinessError extends Error {
  constructor(code, message, statusCode = 400) {
    super(message)
    this.name = 'BusinessError'
    this.code = code
    this.statusCode = statusCode
  }
}

/**
 * 统一错误处理函数
 * @param {Error} error 错误对象
 * @param {string} requestId 请求ID
 * @returns {object} 错误响应
 */
function errorHandler(error, requestId = '') {
  let response = {
    success: false,
    code: ERROR_CODES.UNKNOWN_ERROR,
    message: '系统内部错误',
    data: null,
    requestId
  }
  
  // 记录错误日志
  const logMeta = {
    requestId,
    errorName: error.name,
    errorMessage: error.message,
    stack: error.stack
  }
  
  if (error instanceof BusinessError) {
    // 业务异常
    response.code = error.code
    response.message = error.message
    logger.warn('业务异常', logMeta)
  } else if (error.name === 'ValidationError') {
    // 参数校验错误
    response.code = ERROR_CODES.INVALID_PARAMS
    response.message = error.message
    logger.warn('参数校验失败', logMeta)
  } else if (error.name === 'JsonWebTokenError') {
    // JWT相关错误
    response.code = ERROR_CODES.TOKEN_INVALID
    response.message = 'Token无效'
    logger.warn('Token验证失败', logMeta)
  } else if (error.name === 'TokenExpiredError') {
    // Token过期错误
    response.code = ERROR_CODES.TOKEN_EXPIRED
    response.message = 'Token已过期'
    logger.warn('Token已过期', logMeta)
  } else if (error.message && error.message.includes('数据库')) {
    // 数据库错误
    response.code = ERROR_CODES.DATABASE_ERROR
    response.message = '数据库操作失败'
    logger.error('数据库错误', logMeta)
  } else {
    // 未知错误
    logger.error('未知错误', logMeta)
  }
  
  return response
}

/**
 * 创建业务异常
 * @param {string} code 错误码
 * @param {string} message 错误消息
 * @param {number} statusCode HTTP状态码
 * @returns {BusinessError} 业务异常对象
 */
function createBusinessError(code, message, statusCode = 400) {
  return new BusinessError(code, message, statusCode)
}

/**
 * 格式化成功响应
 * @param {any} data 响应数据
 * @param {string} message 响应消息
 * @param {string} requestId 请求ID
 * @returns {object} 成功响应
 */
function formatSuccessResponse(data = null, message = '操作成功', requestId = '') {
  return {
    success: true,
    code: 'SUCCESS',
    message,
    data,
    requestId
  }
}

module.exports = {
  ERROR_CODES,
  BusinessError,
  errorHandler,
  createBusinessError,
  formatSuccessResponse
}