import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';

import '../widgets/chat_panel.dart';
import '../widgets/conversation_selector.dart';
import '../widgets/agent_panel.dart';
import '../providers/chat_provider.dart';
import '../providers/portrait_mode_provider.dart';
import '../core/storage/storage_service.dart';

/// 聊天页面
class ChatScreen extends ConsumerStatefulWidget {
  const ChatScreen({super.key});

  @override
  ConsumerState<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends ConsumerState<ChatScreen> {
  @override
  void initState() {
    super.initState();
    // 延迟初始化聊天状态，确保存储服务已初始化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeChatWithStorage();
    });
  }

  /// 确保存储服务初始化后再初始化聊天
  Future<void> _initializeChatWithStorage() async {
    try {
      // 等待存储服务初始化完成
      await ref.read(storageInitProvider.future);

      // 初始化聊天
      ref.read(chatProvider.notifier).init();
    } catch (e) {
      print('初始化聊天失败: $e');
      // 即使失败也要初始化聊天，确保基本功能可用
      ref.read(chatProvider.notifier).init();
    }
  }

  @override
  Widget build(BuildContext context) {
    final chatState = ref.watch(chatProvider);

    // 如果正在加载且没有对话，显示加载指示器
    if (chatState.isLoading && chatState.conversations.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在初始化聊天...'),
          ],
        ),
      );
    }

    // 如果有错误且没有对话，显示错误信息
    if (chatState.error != null && chatState.conversations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              '初始化失败',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              chatState.error!,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.read(chatProvider.notifier).init(),
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    final isPortraitMode = ref.watch(portraitModeStateProvider);
    final portraitModeNotifier = ref.watch(portraitModeProvider);

    if (isPortraitMode) {
      // 竖屏模式：只显示聊天面板
      return Container(
        padding: portraitModeNotifier.getPortraitModePadding(const EdgeInsets.all(8.0)),
        child: const ChatPanel(),
      );
    }

    // 桌面模式：显示完整布局
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          // 左侧：智能体选择、模型选择和对话历史
          SizedBox(
            width: 320,
            child: Column(
              children: [
                // 智能体和模型选择区域（固定内容高度，不可滚动）
                const AgentPanel(),

                const Gap(8),

                // 对话历史区域（占用剩余空间，可滚动）
                const Expanded(
                  child: ConversationSelector(),
                ),
              ],
            ),
          ),

          const Gap(16),

          // 右侧：聊天面板
          const Expanded(
            child: ChatPanel(),
          ),
        ],
      ),
    );
  }
}
