import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:numerology_ai_chat_admin/src/models/config_model.dart';
import 'package:numerology_ai_chat_admin/src/providers/config_provider.dart';
import 'package:numerology_ai_chat_admin/src/widgets/config_edit_dialog.dart';

class SystemConfigScreen extends ConsumerStatefulWidget {
  const SystemConfigScreen({super.key});

  @override
  ConsumerState<SystemConfigScreen> createState() => _SystemConfigScreenState();
}

class _SystemConfigScreenState extends ConsumerState<SystemConfigScreen> {
  String? _selectedCategory;
  bool? _selectedIsActive;

  @override
  void initState() {
    super.initState();
    // 初始加载配置列表
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(configProvider.notifier).fetchConfigs();
    });
  }

  @override
  Widget build(BuildContext context) {
    final configState = ref.watch(configProvider);

    // 监听错误状态
    ref.listen<ConfigState>(configProvider, (previous, next) {
      if (next.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.error!),
            backgroundColor: Colors.red,
          ),
        );
        ref.read(configProvider.notifier).clearError();
      }
    });

    return Scaffold(
      body: Column(
        children: [
          // 页面标题和操作栏
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Text(
                  '系统配置管理',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () => _refreshConfigs(),
                  tooltip: '刷新',
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: () => _showCreateConfigDialog(),
                  icon: const Icon(Icons.add),
                  label: const Text('新建配置'),
                ),
              ],
            ),
          ),
          // 筛选条件
          _buildFilterBar(),
          const Divider(height: 1),
          // 配置列表
          Expanded(
            child: configState.isLoading
                ? const Center(child: CircularProgressIndicator())
                : configState.configs == null || configState.configs!.isEmpty
                    ? const Center(child: Text('暂无配置数据'))
                    : _buildConfigList(configState.configs!),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // 配置分类筛选
          SizedBox(
            width: 150,
            child: DropdownButtonFormField<String>(
              value: _selectedCategory,
              decoration: const InputDecoration(
                labelText: '配置分类',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: const [
                DropdownMenuItem(value: null, child: Text('全部分类')),
                DropdownMenuItem(value: 'API配置', child: Text('API配置')),
                DropdownMenuItem(value: '系统参数', child: Text('系统参数')),
                DropdownMenuItem(value: '业务配置', child: Text('业务配置')),
                DropdownMenuItem(value: '界面配置', child: Text('界面配置')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedCategory = value;
                });
                _refreshConfigs();
              },
            ),
          ),
          const SizedBox(width: 16),
          // 状态筛选
          SizedBox(
            width: 120,
            child: DropdownButtonFormField<bool>(
              value: _selectedIsActive,
              decoration: const InputDecoration(
                labelText: '状态',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: const [
                DropdownMenuItem(value: null, child: Text('全部状态')),
                DropdownMenuItem(value: true, child: Text('启用')),
                DropdownMenuItem(value: false, child: Text('禁用')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedIsActive = value;
                });
                _refreshConfigs();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfigList(List<SystemConfig> configs) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('配置键')),
          DataColumn(label: Text('配置值')),
          DataColumn(label: Text('类型')),
          DataColumn(label: Text('分类')),
          DataColumn(label: Text('描述')),
          DataColumn(label: Text('状态')),
          DataColumn(label: Text('更新时间')),
          DataColumn(label: Text('操作')),
        ],
        rows: configs.map((config) => _buildConfigRow(config)).toList(),
      ),
    );
  }

  DataRow _buildConfigRow(SystemConfig config) {
    return DataRow(
      cells: [
        DataCell(
          SizedBox(
            width: 150,
            child: Text(
              config.configKey,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        DataCell(
          SizedBox(
            width: 200,
            child: Text(
              config.configValue,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ),
        DataCell(Text(config.configType)),
        DataCell(Text(config.category)),
        DataCell(
          SizedBox(
            width: 200,
            child: Text(
              config.description,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: config.isActive ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              config.isActive ? '启用' : '禁用',
              style: TextStyle(
                color: config.isActive ? Colors.green : Colors.red,
                fontSize: 12,
              ),
            ),
          ),
        ),
        DataCell(Text(_formatDateTime(config.updatedAt))),
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: const Icon(Icons.edit, size: 18),
                onPressed: () => _showEditConfigDialog(config),
                tooltip: '编辑',
              ),
              IconButton(
                icon: const Icon(Icons.delete, size: 18),
                onPressed: () => _showDeleteConfirmDialog(config),
                tooltip: '删除',
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _refreshConfigs() {
    ref.read(configProvider.notifier).fetchConfigs(
          category: _selectedCategory,
          isActive: _selectedIsActive,
        );
  }

  void _showCreateConfigDialog() {
    showDialog(
      context: context,
      builder: (context) => ConfigEditDialog(
        onSave: (request) async {
          final success = await ref.read(configProvider.notifier).createConfig(request);
          if (success) {
            Navigator.of(context).pop();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('配置创建成功')),
            );
          }
        },
      ),
    );
  }

  void _showEditConfigDialog(SystemConfig config) {
    showDialog(
      context: context,
      builder: (context) => ConfigEditDialog(
        config: config,
        onSave: (request) async {
          final success = await ref.read(configProvider.notifier).updateConfig(config.id, request);
          if (success) {
            Navigator.of(context).pop();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('配置更新成功')),
            );
          }
        },
      ),
    );
  }

  void _showDeleteConfirmDialog(SystemConfig config) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除配置"${config.configKey}"吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final success = await ref.read(configProvider.notifier).deleteConfig(config.id);
              if (success) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('配置删除成功')),
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }
}
