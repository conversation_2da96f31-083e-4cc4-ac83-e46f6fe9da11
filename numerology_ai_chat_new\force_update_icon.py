#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制更新应用图标
直接从logo.png生成高质量ico文件并替换
"""

from PIL import Image
import os
import shutil

def create_ico_from_jpg(jpg_path, ico_path):
    """
    直接从JPG创建ICO文件
    
    Args:
        jpg_path: 源JPG文件路径
        ico_path: 目标ICO文件路径
    """
    try:
        print(f"🔄 正在处理: {jpg_path}")
        
        # 打开JPG图片
        with Image.open(jpg_path) as img:
            print(f"📏 原始尺寸: {img.size[0]}x{img.size[1]} 像素")
            
            # 转换为RGBA模式
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            # 调整为正方形
            min_size = min(img.size)
            left = (img.size[0] - min_size) // 2
            top = (img.size[1] - min_size) // 2
            right = left + min_size
            bottom = top + min_size
            img_square = img.crop((left, top, right, bottom))
            
            print(f"✂️ 裁剪后: {img_square.size[0]}x{img_square.size[1]} 像素")
            
            # Windows图标标准尺寸
            sizes = [
                (16, 16),
                (32, 32), 
                (48, 48),
                (64, 64),
                (128, 128),
                (256, 256)
            ]
            
            # 创建不同尺寸的图标
            icon_images = []
            for size in sizes:
                resized = img_square.resize(size, Image.Resampling.LANCZOS)
                
                # 小尺寸图标锐化处理
                if size[0] <= 48:
                    from PIL import ImageFilter
                    resized = resized.filter(ImageFilter.SHARPEN)
                
                icon_images.append(resized)
                print(f"✅ 生成 {size[0]}x{size[1]} 图标")
            
            # 保存为ICO文件
            icon_images[0].save(
                ico_path,
                format='ICO',
                sizes=sizes,
                append_images=icon_images[1:]
            )
            
            print(f"✅ 成功创建图标: {ico_path}")
            
            # 获取文件大小
            size_bytes = os.path.getsize(ico_path)
            print(f"📦 文件大小: {size_bytes} 字节")
            
            return True
            
    except Exception as e:
        print(f"❌ 创建图标失败: {e}")
        return False

def backup_current_icon(ico_path):
    """备份当前图标"""
    if os.path.exists(ico_path):
        backup_path = ico_path + ".force_backup"
        try:
            shutil.copy2(ico_path, backup_path)
            print(f"📦 已备份当前图标: {backup_path}")
            return True
        except Exception as e:
            print(f"⚠️ 备份失败: {e}")
            return False
    return True

def main():
    print("🔧 强制更新应用图标")
    print("=" * 50)
    
    # 文件路径
    jpg_path = "assets/images/logo.png"
    ico_path = "windows/runner/resources/app_icon.ico"
    
    # 检查源文件
    if not os.path.exists(jpg_path):
        print(f"❌ 源文件不存在: {jpg_path}")
        return False
    
    # 确保目标目录存在
    os.makedirs(os.path.dirname(ico_path), exist_ok=True)
    
    # 备份当前图标
    backup_current_icon(ico_path)
    
    # 强制创建新图标
    print("🔄 开始强制创建图标...")
    success = create_ico_from_jpg(jpg_path, ico_path)
    
    if success:
        print("\n🎉 图标强制更新完成!")
        print("=" * 50)
        print("📝 接下来的步骤:")
        print("1. 清理构建缓存: flutter clean")
        print("2. 重新构建应用: flutter build windows --release")
        print("3. 如果图标仍未更新，请重启Windows资源管理器")
        print("   - 按 Ctrl+Shift+Esc 打开任务管理器")
        print("   - 找到 'Windows资源管理器' 进程")
        print("   - 右键选择 '重新启动'")
        
        print("\n💡 提示:")
        print("- 新图标包含6种标准尺寸")
        print("- 小尺寸图标已进行锐化处理")
        print("- 如果问题仍然存在，可能需要清理Windows图标缓存")
        
        return True
    else:
        print("💥 图标更新失败")
        return False

if __name__ == "__main__":
    main()
