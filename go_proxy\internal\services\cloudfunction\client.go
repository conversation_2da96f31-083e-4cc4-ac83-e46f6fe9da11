package cloudfunction

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"go_proxy/internal/config"
)

// Client 云函数客户端
type Client struct {
	baseURL    string
	httpClient *http.Client
}

// UserInfo 用户信息结构
type UserInfo struct {
	UserID             string    `json:"userId"`
	Username           string    `json:"username"`
	MembershipType     string    `json:"membershipType"`
	MembershipExpiresAt *time.Time `json:"membershipExpiresAt"`
	AvailableCount     int       `json:"availableCount"`
	Status             string    `json:"status"`
}

// CloudFunctionRequest 云函数请求结构
type CloudFunctionRequest struct {
	Action  string `json:"action"`
	Token   string `json:"token,omitempty"`
	AgentID string `json:"agentId,omitempty"`
	ModelID string `json:"modelId,omitempty"`
}

// CloudFunctionResponse 云函数响应结构
type CloudFunctionResponse struct {
	Code    interface{} `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// NewClient 创建新的云函数客户端
func NewClient(cfg *config.Config) *Client {
	return &Client{
		baseURL: cfg.CloudFuncBaseURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// GetUserInfo 获取用户信息
func (c *Client) GetUserInfo(accessToken string) (*UserInfo, error) {
	req := CloudFunctionRequest{
		Action: "getUserInfo",
		Token:  accessToken,
	}

	resp, err := c.callCloudFunction(req)
	if err != nil {
		return nil, fmt.Errorf("failed to call cloud function: %w", err)
	}

	// 检查响应状态 - 云函数返回code=0表示成功
	codeFloat, ok := resp.Code.(float64)
	if !ok {
		codeStr, ok := resp.Code.(string)
		if !ok || (codeStr != "SUCCESS" && codeStr != "0") {
			return nil, fmt.Errorf("cloud function error: %s", resp.Message)
		}
	} else if codeFloat != 0 {
		return nil, fmt.Errorf("cloud function error: %s", resp.Message)
	}

	// 解析响应数据 - 根据实际的云函数响应格式
	dataMap, ok := resp.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid response data format")
	}

	// 获取嵌套的data字段
	innerDataMap, ok := dataMap["data"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid inner data format")
	}

	// 获取用户信息
	userDataMap, ok := innerDataMap["user"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid user data format")
	}

	// 映射字段到UserInfo结构
	userInfo := &UserInfo{
		UserID:         userDataMap["id"].(string),
		Username:       userDataMap["username"].(string),
		AvailableCount: int(userDataMap["availableCount"].(float64)),
		Status:         userDataMap["status"].(string),
	}

	// 处理会员信息
	if membership, ok := userDataMap["membership"].(map[string]interface{}); ok {
		if memberType, ok := membership["type"].(string); ok {
			userInfo.MembershipType = memberType
		}
	}

	return userInfo, nil
}

// UpdateUsage 更新用户使用次数
func (c *Client) UpdateUsage(accessToken, userID string) error {
	return c.UpdateUsageWithPowerCalculation(accessToken, userID, "", "")
}

// UpdateUsageWithPowerCalculation 更新用户使用次数（支持算力计算）
func (c *Client) UpdateUsageWithPowerCalculation(accessToken, userID, agentID, modelID string) error {
	req := CloudFunctionRequest{
		Action:  "updateUsage",
		Token:   accessToken,
		AgentID: agentID,
		ModelID: modelID,
	}

	resp, err := c.callCloudFunction(req)
	if err != nil {
		return fmt.Errorf("failed to call cloud function: %w", err)
	}

	// 检查响应状态 - 云函数返回code=0表示成功
	codeFloat, ok := resp.Code.(float64)
	if !ok {
		codeStr, ok := resp.Code.(string)
		if !ok || (codeStr != "SUCCESS" && codeStr != "0") {
			return fmt.Errorf("cloud function error: %s", resp.Message)
		}
	} else if codeFloat != 0 {
		return fmt.Errorf("cloud function error: %s", resp.Message)
	}

	return nil
}

// callCloudFunction 调用云函数的通用方法
func (c *Client) callCloudFunction(req CloudFunctionRequest) (*CloudFunctionResponse, error) {
	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequest("POST", c.baseURL, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")

	httpResp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer httpResp.Body.Close()

	respBody, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if httpResp.StatusCode != http.StatusOK {
		// 添加调试信息
		fmt.Printf("Cloud function request: %s\n", string(reqBody))
		fmt.Printf("Cloud function response status: %d\n", httpResp.StatusCode)
		fmt.Printf("Cloud function response body: %s\n", string(respBody))
		return nil, fmt.Errorf("cloud function returned status %d: %s", httpResp.StatusCode, string(respBody))
	}

	var resp CloudFunctionResponse
	if err := json.Unmarshal(respBody, &resp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &resp, nil
}