import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/system_config_service.dart';
import '../services/go_proxy_service.dart';
import '../services/ai_service.dart';
import '../services/agent_service.dart';
import '../services/model_service.dart';
import '../core/storage/storage_service.dart';

/// 系统配置服务提供者
final systemConfigServiceProvider = Provider<SystemConfigService>((ref) {
  final storageService = ref.read(storageServiceProvider);
  return SystemConfigService.getInstance(storageService);
});

/// Go代理服务提供者
final goProxyServiceProvider = Provider<GoProxyService>((ref) {
  final systemConfigService = ref.read(systemConfigServiceProvider);
  return GoProxyService(systemConfigService);
});

/// 智能体服务提供者
final agentServiceProvider = Provider<AgentService>((ref) {
  return AgentService();
});

/// 模型服务提供者
final modelServiceProvider = Provider<ModelService>((ref) {
  return ModelService();
});

/// AI服务提供者
final aiServiceProvider = Provider<AIService>((ref) {
  final goProxyService = ref.read(goProxyServiceProvider);
  final agentService = ref.read(agentServiceProvider);
  final modelService = ref.read(modelServiceProvider);

  return AIService(
    goProxyService: goProxyService,
    agentService: agentService,
    modelService: modelService,
  );
});
