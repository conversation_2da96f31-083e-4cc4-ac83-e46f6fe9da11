import 'package:json_annotation/json_annotation.dart';

part 'usage_history_model.g.dart';

@JsonSerializable()
class UsageHistoryModel {
  @JsonKey(name: '_id')
  final String id;
  
  final String userId;
  final String agentId;
  final String agentName;
  final String modelId;
  final String modelName;
  final String modelLevel;
  final String pricingTierId;
  final String tierName;
  final int powerCost;
  final int balanceBefore;
  final int balanceAfter;
  final String description;
  final DateTime consumeTime;
  final DateTime createdAt;

  const UsageHistoryModel({
    required this.id,
    required this.userId,
    required this.agentId,
    required this.agentName,
    required this.modelId,
    required this.modelName,
    required this.modelLevel,
    required this.pricingTierId,
    required this.tierName,
    required this.powerCost,
    required this.balanceBefore,
    required this.balanceAfter,
    required this.description,
    required this.consumeTime,
    required this.createdAt,
  });

  factory UsageHistoryModel.fromJson(Map<String, dynamic> json) {
    // 处理时间字段的特殊格式
    DateTime parseDateTime(dynamic value) {
      if (value == null) return DateTime.now();
      if (value is Map && value.containsKey('\$date')) {
        return DateTime.fromMillisecondsSinceEpoch(value['\$date']);
      } else if (value is String) {
        try {
          return DateTime.parse(value);
        } catch (e) {
          return DateTime.now();
        }
      } else if (value is int) {
        return DateTime.fromMillisecondsSinceEpoch(value);
      }
      return DateTime.now();
    }

    return UsageHistoryModel(
      id: json['_id'] as String,
      userId: json['userId'] as String,
      agentId: json['agentId'] as String,
      agentName: json['agentName'] as String,
      modelId: json['modelId'] as String,
      modelName: json['modelName'] as String,
      modelLevel: json['modelLevel'] as String,
      pricingTierId: json['pricingTierId'] as String,
      tierName: json['tierName'] as String,
      powerCost: json['powerCost'] as int,
      balanceBefore: json['balanceBefore'] as int,
      balanceAfter: json['balanceAfter'] as int,
      description: json['description'] as String,
      consumeTime: parseDateTime(json['consumeTime']),
      createdAt: parseDateTime(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() => _$UsageHistoryModelToJson(this);

  String get modelLevelText {
    switch (modelLevel) {
      case '初级':
        return '初级';
      case '中级':
        return '中级';
      case '高级':
        return '高级';
      case '顶级':
        return '顶级';
      default:
        return modelLevel;
    }
  }

  String get tierNameText {
    switch (tierName) {
      case '基础档次':
        return '基础档次';
      case '标准档次':
        return '标准档次';
      case '高级档次':
        return '高级档次';
      case '专业档次':
        return '专业档次';
      default:
        return tierName;
    }
  }

  // 计算消耗的算力
  int get consumedPower => balanceBefore - balanceAfter;
}
