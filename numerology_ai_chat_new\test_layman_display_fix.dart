import 'dart:convert';
import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'lib/src/models/chat_message.dart';
import 'lib/src/services/chat_storage_service.dart';

/// 测试大白话显示消息的序列化和反序列化
void main() {
  group('大白话显示消息序列化测试', () {
    test('ChatMessage.toJson() 应该正确保存 isLaymanDisplay 属性', () {
      // 创建大白话显示消息
      final laymanMessage = ChatMessage.laymanDisplay(
        content: '这是一个大白话版本的回答',
        agentId: 'test_agent',
      );

      // 转换为JSON
      final json = laymanMessage.toJson();

      // 验证JSON包含正确的属性
      expect(json['is_layman_display'], true);
      expect(json['content'], '这是一个大白话版本的回答');
      expect(json['agent_id'], 'test_agent');
    });

    test('ChatMessage.fromJson() 应该正确恢复 isLaymanDisplay 属性', () {
      // 创建包含大白话属性的JSON
      final json = {
        'id': 'layman_123456789',
        'content': '这是一个大白话版本的回答',
        'sender': 'assistant',
        'status': 'sent',
        'timestamp': DateTime.now().toIso8601String(),
        'agent_id': 'test_agent',
        'bazi_data': null,
        'metadata': null,
        'message_type': 'text',
        'images': null,
        'layman_version': null,
        'is_layman_display': true,
      };

      // 从JSON恢复消息
      final message = ChatMessage.fromJson(json);

      // 验证属性正确恢复
      expect(message.isLaymanDisplay, true);
      expect(message.content, '这是一个大白话版本的回答');
      expect(message.agentId, 'test_agent');
    });

    test('ChatStorageService._messageFromJson() 应该正确处理 isLaymanDisplay 属性', () {
      // 模拟存储服务的JSON数据
      final json = {
        'id': 'layman_123456789',
        'content': '这是一个大白话版本的回答',
        'sender': 'assistant',
        'status': 'sent',
        'timestamp': DateTime.now().toIso8601String(),
        'agent_id': 'test_agent',
        'bazi_data': null,
        'metadata': null,
        'message_type': 'text',
        'images': null,
        'layman_version': null,
        'is_layman_display': true,
      };

      // 创建存储服务实例（这里我们需要模拟私有方法的行为）
      // 由于 _messageFromJson 是私有方法，我们通过反射或者创建测试版本来验证
      
      // 手动模拟 _messageFromJson 的逻辑
      final message = ChatMessage(
        id: json['id'] as String,
        content: json['content'] as String,
        sender: MessageSender.values.firstWhere((e) => e.name == json['sender']),
        status: MessageStatus.values.firstWhere((e) => e.name == json['status']),
        timestamp: DateTime.parse(json['timestamp'] as String),
        agentId: json['agent_id'] as String?,
        baziData: json['bazi_data'] as String?,
        metadata: json['metadata'] as Map<String, dynamic>?,
        messageType: json['message_type'] != null
            ? MessageType.values.firstWhere((e) => e.name == json['message_type'])
            : MessageType.text,
        images: null,
        laymanVersion: json['layman_version'] as String?,
        isLaymanDisplay: json['is_layman_display'] as bool? ?? false,
      );

      // 验证修复后的逻辑正确处理了 isLaymanDisplay
      expect(message.isLaymanDisplay, true);
      expect(message.content, '这是一个大白话版本的回答');
    });

    test('完整的序列化-反序列化循环应该保持 isLaymanDisplay 属性', () {
      // 创建大白话显示消息
      final originalMessage = ChatMessage.laymanDisplay(
        content: '这是一个大白话版本的回答',
        agentId: 'test_agent',
      );

      // 序列化
      final json = originalMessage.toJson();
      final jsonString = jsonEncode(json);

      // 反序列化
      final decodedJson = jsonDecode(jsonString) as Map<String, dynamic>;
      final restoredMessage = ChatMessage.fromJson(decodedJson);

      // 验证属性保持不变
      expect(restoredMessage.isLaymanDisplay, originalMessage.isLaymanDisplay);
      expect(restoredMessage.content, originalMessage.content);
      expect(restoredMessage.agentId, originalMessage.agentId);
      expect(restoredMessage.isLaymanDisplay, true);
    });

    test('普通消息的 isLaymanDisplay 应该为 false', () {
      // 创建普通助手消息
      final normalMessage = ChatMessage.assistant(
        content: '这是一个普通的回答',
        agentId: 'test_agent',
      );

      // 验证 isLaymanDisplay 为 false
      expect(normalMessage.isLaymanDisplay, false);

      // 序列化-反序列化循环
      final json = normalMessage.toJson();
      final restoredMessage = ChatMessage.fromJson(json);

      // 验证属性保持不变
      expect(restoredMessage.isLaymanDisplay, false);
    });
  });
}
