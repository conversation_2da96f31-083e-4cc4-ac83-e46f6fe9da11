import 'package:json_annotation/json_annotation.dart';

part 'stats_model.g.dart';

@JsonSerializable()
class SystemStats {
  final OverviewStats overview;
  final TrendsData? trends;
  final DateTime lastUpdated;

  SystemStats({
    required this.overview,
    this.trends,
    required this.lastUpdated,
  });

  factory SystemStats.fromJson(Map<String, dynamic> json) => _$SystemStatsFromJson(json);
  Map<String, dynamic> toJson() => _$SystemStatsToJson(this);
}

@JsonSerializable()
class OverviewStats {
  final UserStats users;
  final ModelStats models;
  final AgentStats agents;
  final PageStats pages;
  final UsageStats? usage;

  OverviewStats({
    required this.users,
    required this.models,
    required this.agents,
    required this.pages,
    this.usage,
  });

  factory OverviewStats.fromJson(Map<String, dynamic> json) => _$OverviewStatsFromJson(json);
  Map<String, dynamic> toJson() => _$OverviewStatsToJson(this);
}

@JsonSerializable()
class UserStats {
  final int total;
  final int active;
  final int inactive;
  final int withMembership;
  final int totalQuota;
  final int totalUsage;

  UserStats({
    required this.total,
    required this.active,
    required this.inactive,
    required this.withMembership,
    required this.totalQuota,
    required this.totalUsage,
  });

  factory UserStats.fromJson(Map<String, dynamic> json) => _$UserStatsFromJson(json);
  Map<String, dynamic> toJson() => _$UserStatsToJson(this);
}

@JsonSerializable()
class ModelStats {
  final int total;
  final int active;
  final int inactive;
  final int withApiKey;

  ModelStats({
    required this.total,
    required this.active,
    required this.inactive,
    required this.withApiKey,
  });

  factory ModelStats.fromJson(Map<String, dynamic> json) => _$ModelStatsFromJson(json);
  Map<String, dynamic> toJson() => _$ModelStatsToJson(this);
}

@JsonSerializable()
class AgentStats {
  final int total;
  final int active;
  final int inactive;
  @JsonKey(name: 'byType')
  final Map<String, int> typeDistribution;

  AgentStats({
    required this.total,
    required this.active,
    required this.inactive,
    required this.typeDistribution,
  });

  factory AgentStats.fromJson(Map<String, dynamic> json) => _$AgentStatsFromJson(json);
  Map<String, dynamic> toJson() => _$AgentStatsToJson(this);
}

@JsonSerializable()
class PageStats {
  final int total;
  final int active;
  final int inactive;
  @JsonKey(name: 'byType')
  final Map<String, int> typeDistribution;

  PageStats({
    required this.total,
    required this.active,
    required this.inactive,
    required this.typeDistribution,
  });

  factory PageStats.fromJson(Map<String, dynamic> json) => _$PageStatsFromJson(json);
  Map<String, dynamic> toJson() => _$PageStatsToJson(this);
}

@JsonSerializable()
class UsageStats {
  final int todayRequests;
  final int todayUsers;
  final int avgResponseTime;
  final double errorRate;
  final String peakHour;
  final List<PopularItem> popularModels;
  final List<PopularItem> popularAgents;

  UsageStats({
    required this.todayRequests,
    required this.todayUsers,
    required this.avgResponseTime,
    required this.errorRate,
    required this.peakHour,
    required this.popularModels,
    required this.popularAgents,
  });

  factory UsageStats.fromJson(Map<String, dynamic> json) => _$UsageStatsFromJson(json);
  Map<String, dynamic> toJson() => _$UsageStatsToJson(this);
}

@JsonSerializable()
class PopularItem {
  final String name;
  final int usage;

  PopularItem({
    required this.name,
    required this.usage,
  });

  factory PopularItem.fromJson(Map<String, dynamic> json) => _$PopularItemFromJson(json);
  Map<String, dynamic> toJson() => _$PopularItemToJson(this);
}

@JsonSerializable()
class TrendsData {
  final List<int> dailyUsers;
  final List<int> dailyRequests;
  final List<int> dailyErrors;
  final List<String> dates;

  TrendsData({
    required this.dailyUsers,
    required this.dailyRequests,
    required this.dailyErrors,
    required this.dates,
  });

  factory TrendsData.fromJson(Map<String, dynamic> json) => _$TrendsDataFromJson(json);
  Map<String, dynamic> toJson() => _$TrendsDataToJson(this);
}
