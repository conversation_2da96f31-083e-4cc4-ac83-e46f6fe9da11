// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'usage_history_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UsageHistoryModel _$UsageHistoryModelFromJson(Map<String, dynamic> json) =>
    UsageHistoryModel(
      id: json['_id'] as String,
      userId: json['userId'] as String,
      agentId: json['agentId'] as String,
      agentName: json['agentName'] as String,
      modelId: json['modelId'] as String,
      modelName: json['modelName'] as String,
      modelLevel: json['modelLevel'] as String,
      pricingTierId: json['pricingTierId'] as String,
      tierName: json['tierName'] as String,
      powerCost: (json['powerCost'] as num).toInt(),
      balanceBefore: (json['balanceBefore'] as num).toInt(),
      balanceAfter: (json['balanceAfter'] as num).toInt(),
      description: json['description'] as String,
      consumeTime: DateTime.parse(json['consumeTime'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$UsageHistoryModelToJson(UsageHistoryModel instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'userId': instance.userId,
      'agentId': instance.agentId,
      'agentName': instance.agentName,
      'modelId': instance.modelId,
      'modelName': instance.modelName,
      'modelLevel': instance.modelLevel,
      'pricingTierId': instance.pricingTierId,
      'tierName': instance.tierName,
      'powerCost': instance.powerCost,
      'balanceBefore': instance.balanceBefore,
      'balanceAfter': instance.balanceAfter,
      'description': instance.description,
      'consumeTime': instance.consumeTime.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
    };
