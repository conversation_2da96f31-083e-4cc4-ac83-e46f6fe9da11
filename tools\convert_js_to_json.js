#!/usr/bin/env node

/**
 * 将JavaScript对象格式的地址数据转换为标准JSON格式
 */

const fs = require('fs');
const path = require('path');

const INPUT_FILE = 'cloudfunctions/exeFunction/src/services/bazi/国内地址.txt';
const OUTPUT_FILE = 'lib/assets/data/china_address.json';

function convertJsToJson() {
  try {
    console.log('读取JavaScript对象文件...');
    let content = fs.readFileSync(INPUT_FILE, 'utf-8');
    
    // 将JavaScript对象格式转换为JSON格式
    // 1. 为属性名添加双引号
    content = content.replace(/(\w+):/g, '"$1":');
    
    // 2. 处理特殊情况，确保是有效的JSON
    content = content.replace(/'/g, '"'); // 单引号转双引号
    
    console.log('解析JavaScript对象...');
    
    // 使用eval来解析JavaScript对象（在受控环境中）
    const data = eval('(' + content + ')');
    
    console.log('转换为JSON格式...');
    const jsonContent = JSON.stringify(data, null, 2);
    
    // 确保输出目录存在
    const outputDir = path.dirname(OUTPUT_FILE);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    console.log('写入JSON文件...');
    fs.writeFileSync(OUTPUT_FILE, jsonContent, 'utf-8');
    
    console.log('✅ 转换完成！');
    console.log(`📁 输入文件: ${INPUT_FILE}`);
    console.log(`📁 输出文件: ${OUTPUT_FILE}`);
    console.log(`📊 文件大小: ${(fs.statSync(OUTPUT_FILE).size / 1024 / 1024).toFixed(2)} MB`);
    
    // 验证数据结构
    console.log('\n📋 数据统计:');
    let provinceCount = 0;
    let cityCount = 0;
    let districtCount = 0;
    
    data.forEach(province => {
      provinceCount++;
      if (province.children) {
        province.children.forEach(city => {
          cityCount++;
          if (city.children) {
            districtCount += city.children.length;
          }
        });
      }
    });
    
    console.log(`- 省份数量: ${provinceCount}`);
    console.log(`- 城市数量: ${cityCount}`);
    console.log(`- 区县数量: ${districtCount}`);
    
  } catch (error) {
    console.error('❌ 转换失败:', error.message);
    process.exit(1);
  }
}

// 运行转换
convertJsToJson();
