#!/bin/bash

# Go Proxy 部署脚本
# 使用方法: ./deploy.sh

set -e

echo "=== Go Proxy 部署脚本 ==="

# 检查是否存在可执行文件
if [ ! -f "go_proxy_linux" ]; then
    echo "错误: 找不到 go_proxy_linux 可执行文件"
    echo "请先运行编译命令: GOOS=linux GOARCH=amd64 go build -o go_proxy_linux ./cmd/server"
    exit 1
fi

# 检查是否存在配置文件
if [ ! -f ".env" ]; then
    echo "警告: 找不到 .env 配置文件，将使用默认配置"
    echo "建议复制 .env.example 为 .env 并修改相应配置"
fi

# 设置可执行权限
chmod +x go_proxy_linux

# 检查端口是否被占用
PORT=${SERVER_PORT:-8080}
if command -v netstat >/dev/null 2>&1; then
    if netstat -tuln | grep ":$PORT " >/dev/null; then
        echo "警告: 端口 $PORT 已被占用，请检查是否有其他服务在运行"
    fi
fi

echo "=== 配置信息 ==="
echo "服务端口: $PORT"
echo "可执行文件: go_proxy_linux"
echo "配置文件: .env"

echo ""
echo "=== 部署完成 ==="
echo "启动服务命令: ./go_proxy_linux"
echo "后台运行命令: nohup ./go_proxy_linux > go_proxy.log 2>&1 &"
echo "查看日志命令: tail -f go_proxy.log"
echo "停止服务命令: pkill -f go_proxy_linux"

echo ""
echo "=== 系统服务配置 (可选) ==="
echo "如需配置为系统服务，请参考 go_proxy.service 文件"
