# 手机端直播模式功能测试指南

## 测试环境准备

### 1. 编译运行应用
```bash
cd numerology_ai_chat_new
flutter run -d windows
```

### 2. 登录应用
- 使用有效的用户账号登录
- 确保能正常进入聊天界面

## 功能测试步骤

### 测试1: 直播模式按钮显示
**目标**: 验证直播模式按钮是否正确显示

**步骤**:
1. 进入AI对话页面
2. 查看聊天面板右上角是否有"直播模式"按钮
3. 按钮应显示手机图标和"直播模式"文字

**预期结果**: 
- ✅ 按钮正确显示
- ✅ 鼠标悬停显示工具提示"进入直播模式"

### 测试2: 进入直播模式
**目标**: 验证进入直播模式的完整流程

**步骤**:
1. 点击"直播模式"按钮
2. 确认对话框应该弹出，显示功能说明
3. 点击"确定"按钮

**预期结果**:
- ✅ 弹出确认对话框，内容包括功能说明
- ✅ 窗口尺寸自动调整为450x800像素
- ✅ 窗口居中显示
- ✅ 侧边栏完全隐藏
- ✅ 标题栏完全隐藏
- ✅ 只显示聊天面板
- ✅ 按钮文字变为"桌面模式"，图标变为桌面图标
- ✅ 显示"已进入直播模式"提示

### 测试3: 直播模式UI适配
**目标**: 验证直播模式下的UI适配效果

**步骤**:
1. 在直播模式下观察聊天界面
2. 发送一条测试消息
3. 观察字体大小、间距等变化

**预期结果**:
- ✅ 字体大小明显放大（约1.8倍）
- ✅ 消息气泡间距增大
- ✅ 输入框字体放大
- ✅ 按钮和图标尺寸放大
- ✅ 整体布局适合手机端显示

### 测试4: 功能完整性
**目标**: 验证直播模式下所有聊天功能正常

**步骤**:
1. 发送文字消息
2. 上传图片（如果支持）
3. 查看消息历史
4. 测试停止按钮（如果AI正在回复）

**预期结果**:
- ✅ 文字消息正常发送和接收
- ✅ 图片上传和显示正常
- ✅ 消息历史正常显示
- ✅ 所有交互功能正常工作

### 测试5: 退出直播模式
**目标**: 验证退出直播模式的功能

**步骤**:
1. 在直播模式下点击"桌面模式"按钮
2. 观察界面变化

**预期结果**:
- ✅ 窗口尺寸恢复到原始大小
- ✅ 窗口位置恢复（如果之前不是居中）
- ✅ 侧边栏重新显示
- ✅ 标题栏重新显示
- ✅ 字体大小恢复正常
- ✅ 按钮文字变回"直播模式"
- ✅ 显示"已退出直播模式"提示

### 测试6: 状态保持
**目标**: 验证模式切换时状态保持

**步骤**:
1. 选择特定的智能体和模型
2. 发送几条消息
3. 进入直播模式
4. 再退出直播模式
5. 检查状态是否保持

**预期结果**:
- ✅ 智能体选择保持不变
- ✅ 模型选择保持不变
- ✅ 聊天历史完整保持
- ✅ 用户设置保持不变

### 测试7: 边界情况
**目标**: 测试各种边界情况

**步骤**:
1. 在直播模式下最小化窗口
2. 在直播模式下尝试最大化窗口
3. 快速多次切换模式
4. 在AI回复过程中切换模式

**预期结果**:
- ✅ 最小化/最大化操作正常
- ✅ 快速切换不会导致错误
- ✅ AI回复过程不受影响
- ✅ 无内存泄漏或性能问题

## 问题排查

### 常见问题及解决方案

**问题1**: 按钮不显示
- 检查是否正确导入了LiveModeButton组件
- 确认ChatPanel中是否正确添加了按钮

**问题2**: 窗口尺寸不变
- 检查window_manager权限
- 确认LiveModeProvider中的窗口管理代码

**问题3**: 字体没有放大
- 检查LiveModeProvider的字体缩放逻辑
- 确认各组件是否正确使用了getLiveModeTextSize方法

**问题4**: UI元素没有隐藏
- 检查HomeScreen中的条件渲染逻辑
- 确认liveModeStateProvider的状态更新

## 性能测试

### 内存使用
- 进入/退出直播模式前后内存使用对比
- 长时间使用直播模式的内存稳定性

### 响应速度
- 模式切换的响应时间
- UI重绘的流畅性

### 兼容性
- 不同屏幕分辨率下的显示效果
- 不同Windows版本的兼容性

## 测试报告模板

```
测试日期: ____
测试人员: ____
应用版本: ____

功能测试结果:
[ ] 测试1: 直播模式按钮显示
[ ] 测试2: 进入直播模式
[ ] 测试3: 直播模式UI适配
[ ] 测试4: 功能完整性
[ ] 测试5: 退出直播模式
[ ] 测试6: 状态保持
[ ] 测试7: 边界情况

发现问题:
1. ____
2. ____

建议改进:
1. ____
2. ____

总体评价: ____
```

---

通过以上测试步骤，可以全面验证手机端直播模式功能的正确性和稳定性。
