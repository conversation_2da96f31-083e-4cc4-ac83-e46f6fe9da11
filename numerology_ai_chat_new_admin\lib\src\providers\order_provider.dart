import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/order_model.dart';
import '../services/admin_api_service.dart';
import 'auth_provider.dart';

class OrderState {
  final List<OrderModel> orders;
  final bool isLoading;
  final String? error;
  final int currentPage;
  final int totalPages;
  final int totalCount;
  final String searchQuery;
  final String statusFilter;
  final String paymentMethodFilter;

  const OrderState({
    this.orders = const [],
    this.isLoading = false,
    this.error,
    this.currentPage = 1,
    this.totalPages = 1,
    this.totalCount = 0,
    this.searchQuery = '',
    this.statusFilter = '',
    this.paymentMethodFilter = '',
  });

  OrderState copyWith({
    List<OrderModel>? orders,
    bool? isLoading,
    String? error,
    int? currentPage,
    int? totalPages,
    int? totalCount,
    String? searchQuery,
    String? statusFilter,
    String? paymentMethodFilter,
  }) {
    return OrderState(
      orders: orders ?? this.orders,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      totalCount: totalCount ?? this.totalCount,
      searchQuery: searchQuery ?? this.searchQuery,
      statusFilter: statusFilter ?? this.statusFilter,
      paymentMethodFilter: paymentMethodFilter ?? this.paymentMethodFilter,
    );
  }
}

class OrderNotifier extends StateNotifier<OrderState> {
  final AdminApiService _apiService;
  final Ref _ref;

  OrderNotifier(this._apiService, this._ref) : super(const OrderState());

  Future<void> loadOrders({
    int page = 1,
    int limit = 20,
    String? search,
    String? status,
    String? paymentMethod,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final authState = _ref.read(authProvider);
      if (authState.token == null) {
        state = state.copyWith(isLoading: false, error: '未登录');
        return;
      }

      final response = await _apiService.getOrderList(
        authState.token!,
        page: page,
        limit: limit,
        search: search,
        status: status,
        paymentMethod: paymentMethod,
      );

      if (response['code'] == 0) {
        final outerData = response['data'] as Map<String, dynamic>;
        final innerData = outerData['data'] as Map<String, dynamic>;
        final ordersData = innerData['orders'] as List;
        final orders = ordersData.map((json) => OrderModel.fromJson(json)).toList();

        state = state.copyWith(
          orders: orders,
          isLoading: false,
          currentPage: page,
          totalCount: innerData['total'] ?? 0,
          totalPages: ((innerData['total'] ?? 0) / limit).ceil(),
          searchQuery: search ?? '',
          statusFilter: status ?? '',
          paymentMethodFilter: paymentMethod ?? '',
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response['message'] ?? '加载订单列表失败',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: '网络错误: $e',
      );
    }
  }

  Future<void> refreshOrders() async {
    await loadOrders(
      page: state.currentPage,
      search: state.searchQuery.isNotEmpty ? state.searchQuery : null,
      status: state.statusFilter.isNotEmpty ? state.statusFilter : null,
      paymentMethod: state.paymentMethodFilter.isNotEmpty ? state.paymentMethodFilter : null,
    );
  }

  Future<void> searchOrders(String query) async {
    await loadOrders(
      page: 1,
      search: query.isNotEmpty ? query : null,
      status: state.statusFilter.isNotEmpty ? state.statusFilter : null,
      paymentMethod: state.paymentMethodFilter.isNotEmpty ? state.paymentMethodFilter : null,
    );
  }

  Future<void> filterByStatus(String status) async {
    await loadOrders(
      page: 1,
      search: state.searchQuery.isNotEmpty ? state.searchQuery : null,
      status: status.isNotEmpty ? status : null,
      paymentMethod: state.paymentMethodFilter.isNotEmpty ? state.paymentMethodFilter : null,
    );
  }

  Future<void> filterByPaymentMethod(String paymentMethod) async {
    await loadOrders(
      page: 1,
      search: state.searchQuery.isNotEmpty ? state.searchQuery : null,
      status: state.statusFilter.isNotEmpty ? state.statusFilter : null,
      paymentMethod: paymentMethod.isNotEmpty ? paymentMethod : null,
    );
  }

  Future<void> loadPage(int page) async {
    await loadOrders(
      page: page,
      search: state.searchQuery.isNotEmpty ? state.searchQuery : null,
      status: state.statusFilter.isNotEmpty ? state.statusFilter : null,
      paymentMethod: state.paymentMethodFilter.isNotEmpty ? state.paymentMethodFilter : null,
    );
  }

  Future<bool> updateOrderStatus(String orderId, String newStatus) async {
    try {
      final authState = _ref.read(authProvider);
      if (authState.token == null) {
        state = state.copyWith(error: '未登录');
        return false;
      }

      final response = await _apiService.updateOrderStatus(authState.token!, orderId, newStatus);

      if (response['code'] == 0) {
        // 更新本地状态
        final updatedOrders = state.orders.map((order) {
          if (order.id == orderId) {
            return OrderModel.fromJson({
              ...order.toJson(),
              'status': newStatus,
              'updatedAt': {'\$date': DateTime.now().millisecondsSinceEpoch},
            });
          }
          return order;
        }).toList();

        state = state.copyWith(orders: updatedOrders);
        return true;
      } else {
        state = state.copyWith(error: response['message'] ?? '更新订单状态失败');
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: '网络错误: $e');
      return false;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

final orderProvider = StateNotifierProvider<OrderNotifier, OrderState>((ref) {
  final apiService = ref.read(adminApiServiceProvider);
  return OrderNotifier(apiService, ref);
});
