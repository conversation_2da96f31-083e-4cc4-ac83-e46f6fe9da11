# ==========================================================================================
# FuiouPay Unified Order API - Test Script
# ==========================================================================================

# --- Step 1: Set Security Protocol to TLS 1.2 ---
[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12

# --- Step 2: Define Request Parameters ---
$mchnt_cd = "0002900F1503036"
$mchnt_key = "f00dac5077ea11e754e14c9541bc0170"
$appid = "wxfa089da95020ba1a"

# Create a unique merchant order number (using the current time)
$timestamp = Get-Date -Format "yyyyMMddHHmmss"
$mchnt_order_no = "1066_$timestamp"

$params = @{
    "version"        = "2.0";
    "mchnt_cd"       = $mchnt_cd;
    "mchnt_order_no" = $mchnt_order_no;
    "appid"          = $appid;
    "order_amt"      = "1";
    "trade_type"     = "WECHAT_NATIVE";
    "order_desc"     = "Test Payment";
    "notify_url"     = "http://example.com/notify";
    "term_ip"        = "127.0.0.1";
}

# --- Step 3: Generate Signature ---

# 1. Sort parameter names alphabetically
$sorted_keys = $params.Keys | Sort-Object

# 2. Concatenate into key1=value1&key2=value2... format
$string_to_sign_parts = @()
foreach ($key in $sorted_keys) {
    if (-not [string]::IsNullOrEmpty($params[$key])) {
        $string_to_sign_parts += "$key=$($params[$key])"
    }
}
$string_to_sign = $string_to_sign_parts -join '&'

# 3. Append the merchant key
$string_to_sign += "&key=$mchnt_key"

Write-Host "String to be signed:"
Write-Host $string_to_sign

# 4. Calculate MD5 hash
$md5 = New-Object -TypeName System.Security.Cryptography.MD5CryptoServiceProvider
$utf8 = New-Object -TypeName System.Text.UTF8Encoding
$sign = [System.BitConverter]::ToString($md5.ComputeHash($utf8.GetBytes($string_to_sign))).Replace("-", "").ToLower()

Write-Host "Generated Signature: $sign"

# --- Step 4: Build XML Request Body ---
$xml_parts = @("<xml>")
foreach ($key in $params.Keys) {
    $xml_parts += "<$key>$($params[$key])</$key>"
}
$xml_parts += "<sign>$sign</sign>"
$xml_parts += "</xml>"
$xml_body = $xml_parts -join ''

Write-Host "Generated XML Request Body:"
Write-Host $xml_body

# --- Step 5: Send HTTP POST Request ---
$uri = "https://fht-api.fuioupay.com/req.do"
$headers = @{ "Content-Type" = "application/x-www-form-urlencoded" }

# The body should be a form with two fields: mchntCd and req (which contains the xml)
$form_body = "mchntCd=$($params.mchnt_cd)&req=$xml_body"

try {
    Write-Host "Sending request to $uri..."
    $response = Invoke-WebRequest -Uri $uri -Method POST -Headers $headers -Body $form_body -UseBasicParsing
    
    Write-Host "`n--- Request Successful ---"
    Write-Host "HTTP Status Code: $($response.StatusCode)"
    Write-Host "Server Response Content:"
    
    # Check if response looks like XML before trying to parse
    if ($response.Content.Trim().StartsWith("<")) {
        # Beautify XML output
        $xml_response = [xml]$response.Content
        $string_writer = New-Object System.IO.StringWriter
        $xml_writer = New-Object System.Xml.XmlTextWriter $string_writer
        $xml_writer.Formatting = 'Indented'
        $xml_response.WriteContentTo($xml_writer)
        $xml_writer.Flush()
        $string_writer.Flush()
        Write-Output $string_writer.ToString()
    } else {
        # Just print the raw content if it's not XML
        Write-Output $response.Content
    }

} catch {
    Write-Host "`n--- Request Failed ---" -ForegroundColor Red
    $error_response = $_.Exception.Response
    if ($null -ne $error_response) {
        $error_stream = $error_response.GetResponseStream()
        $stream_reader = New-Object System.IO.StreamReader($error_stream)
        $error_body = $stream_reader.ReadToEnd()
    } else {
        $error_body = ""
    }
    
    Write-Host "Error Type: $($_.Exception.GetType().FullName)"
    Write-Host "Error Message: $($_.Exception.Message)"
    if (-not [string]::IsNullOrEmpty($error_body)) {
        Write-Host "Error Response Body: $error_body"
    }
} 