# 系统配置动态化改造总结

## 改造目标

将前端硬编码的Go代理服务API地址 `http://go.ziyuanit.com/api` 改为从数据库动态获取，提高系统的灵活性和可维护性。

## 核心改动

### 1. 数据库层面
- 新增 `exe_system_config` 集合
- 存储配置项：`go_proxy_api_url = http://go.ziyuanit.com/api`
- 建立必要的索引（configKey唯一索引等）

### 2. 云函数层面
- 新增 `system_config.js` 处理器
- 提供 `getSystemConfig` 和 `getGoProxyApiUrl` 接口
- 支持配置获取失败时的默认值兜底

### 3. 前端层面
- 新增 `SystemConfigService` 服务
- 实现配置缓存机制（内存+本地存储）
- 重构 `GoProxyService` 和 `AIService` 使用依赖注入
- 在应用启动时预加载配置

## 技术特性

### 缓存策略
- **内存缓存**：30分钟有效期
- **本地存储**：持久化缓存
- **预加载**：应用启动时主动获取

### 容错机制
- 配置获取失败时使用默认值
- 确保系统在任何情况下都能正常运行
- 不阻塞应用启动流程

### 依赖注入
- 统一的服务Provider管理
- 松耦合的服务架构
- 便于测试和维护

## 使用方式

### 前端获取配置
```dart
// 获取系统配置服务
final systemConfigService = ref.read(systemConfigServiceProvider);

// 获取Go代理API地址
final apiUrl = await systemConfigService.getGoProxyApiUrl();
```

### 云函数接口
```javascript
// 获取Go代理API地址
POST /exeFunction
{
  "action": "getGoProxyApiUrl"
}

// 获取所有系统配置
POST /exeFunction
{
  "action": "getSystemConfig"
}
```

## 测试验证

所有功能已通过测试：
- ✅ 云函数接口正常工作
- ✅ 前端配置获取正常
- ✅ Go代理服务健康检查通过
- ✅ 应用启动流程正常

## 后续扩展

该架构支持添加更多系统配置项：
- API超时时间
- 功能开关
- 业务参数
- 第三方服务配置

只需在数据库中添加新的配置记录即可，无需修改代码。

## 维护说明

### 修改配置
直接在数据库 `exe_system_config` 集合中修改 `configValue` 字段即可，前端会在缓存过期后自动获取新配置。

### 添加新配置
在数据库中插入新的配置记录，前端通过 `getSystemConfig` 接口即可获取。

### 清除缓存
如需立即生效，可调用 `SystemConfigService.clearCache()` 方法清除缓存。
