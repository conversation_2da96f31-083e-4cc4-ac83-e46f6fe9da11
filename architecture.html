<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目架构图</title>
    <style>
        body {
            font-family: sans-serif;
            background-color: #f0f0f0;
            color: #333;
        }
        .container {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            padding: 20px;
            gap: 20px;
            flex-wrap: wrap;
        }
        .component {
            background-color: #fff;
            border: 1px solid #ccc;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            min-width: 300px;
        }
        .component h2 {
            margin-top: 0;
            color: #007bff;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .module {
            margin-bottom: 15px;
            padding-left: 20px;
            border-left: 2px solid #eee;
        }
        .module h3 {
            margin: 5px 0;
            color: #17a2b8;
        }
        .module p {
            margin: 5px 0;
            font-size: 0.9em;
            color: #666;
        }
        .arrow {
            position: relative;
            align-self: center;
            font-size: 2em;
            color: #6c757d;
            margin: 0 10px;
        }
    </style>
</head>
<body>

    <div class="container">
        <div class="component">
            <h2>前端 (Flutter App)</h2>
            <div class="module">
                <h3>核心功能</h3>
                <p><b>main.dart:</b> 应用入口，初始化窗口和路由</p>
                <p><b>pubspec.yaml:</b> 定义了Riverpod (状态管理), GoRouter (路由), Dio/HTTP (网络请求) 等依赖</p>
            </div>
            <div class="module">
                <h3>UI (Screens)</h3>
                <p><b>chat_screen.dart:</b> 聊天界面</p>
                <p><b>bazi_screen.dart:</b> 八字排盘</p>
                <p><b>login_screen.dart:</b> 登录</p>
                <p><b>purchase_screen.dart:</b> 购买套餐</p>
            </div>
            <div class="module">
                <h3>服务 (Services)</h3>
                <p><b>go_proxy_service.dart:</b> 与Go代理进行通信</p>
                <p><b>cloud_function_service.dart:</b> 直接调用云函数</p>
                <p><b>auth_service.dart:</b> 用户认证</p>
                <p><b>bazi_service.dart:</b> 八字排盘服务</p>
                <p><b>payment_package_service.dart:</b> 套餐和支付</p>
            </div>
        </div>

        <div class="arrow">&rarr;</div>

        <div class="component">
            <h2>Go 代理</h2>
            <div class="module">
                <h3>路由 (API)</h3>
                <p><b>router.go:</b> 定义 /v1/chat/completions 等API端点</p>
                <p><b>middleware:</b> AuthMiddleware 进行鉴权</p>
            </div>
            <div class="module">
                <h3>代理逻辑 (Proxy)</h3>
                <p><b>llm_proxy.go:</b></p>
                <p>- 接收前端请求</p>
                <p>- 从数据库获取Agent和Model信息</p>
                <p>- 构造对LLM的请求</p>
                <p>- 将请求转发到LLM API (如Gemini, GPT)</p>
                <p>- 支持流式和非流式响应</p>
            </div>
        </div>

        <div class="arrow">&rarr;</div>

        <div class="component">
            <h2>云函数 (Node.js)</h2>
            <div class="module">
                <h3>用户功能 (exeFunction)</h3>
                <p><b>auth.js:</b> 登录、注册、刷新Token</p>
                <p><b>bazi.js:</b> 八字分析</p>
                <p><b>user_info.js:</b> 获取用户信息、更新额度</p>
                <p><b>payment_packages.js:</b> 处理套餐购买和支付</p>
                <p><b>agents.js, models.js:</b> 提供给Go代理所需的信息</p>
            </div>
            <div class="module">
                <h3>管理功能 (exeAdmin)</h3>
                <p><b>admin_auth.js:</b> 管理员登录</p>
                <p><b>user_management.js:</b> 用户管理</p>
                <p><b>agent_manage.js:</b> 智能体管理</p>
                <p><b>model_management.js:</b> 模型管理</p>
            </div>
             <div class="module">
                <h3>支付回调 (paymentCallback)</h3>
                <p>处理来自支付网关的回调</p>
            </div>
        </div>
    </div>

</body>
</html>