@echo off
chcp 65001 >nul
echo ════════════════════════════════════════════
echo    测试图标生成流程
echo ════════════════════════════════════════════
echo.

echo 🔍 步骤1: 检查当前图标文件状态...
if exist "windows\runner\resources\app_icon.ico" (
    echo ✅ 图标文件存在: windows\runner\resources\app_icon.ico
    for %%A in ("windows\runner\resources\app_icon.ico") do echo 📦 文件大小: %%~zA 字节
) else (
    echo ❌ 图标文件不存在
)

echo.
echo 🔍 步骤2: 检查源图片文件...
if exist "assets\images\logo.png" (
    echo ✅ 源图片存在: assets\images\logo.png
    for %%A in ("assets\images\logo.png") do echo 📦 文件大小: %%~zA 字节
) else (
    echo ❌ 源图片不存在
)

echo.
echo 🔍 步骤3: 检查pubspec.yaml配置...
findstr /C:"flutter_launcher_icons" pubspec.yaml >nul
if %errorlevel% equ 0 (
    echo ✅ 找到flutter_launcher_icons配置
    echo 📄 相关配置:
    findstr /A:0C "flutter_launcher_icons" pubspec.yaml
    findstr /A:0C "icon_size" pubspec.yaml
    findstr /A:0C "image_path" pubspec.yaml
) else (
    echo ❌ 未找到flutter_launcher_icons配置
)

echo.
echo 🔄 步骤4: 尝试运行图标生成器...
echo 正在运行: dart run flutter_launcher_icons
dart run flutter_launcher_icons
if %errorlevel% equ 0 (
    echo ✅ 图标生成器运行成功
) else (
    echo ❌ 图标生成器运行失败
    echo 💡 尝试安装插件: flutter pub add dev:flutter_launcher_icons
)

echo.
echo 🔍 步骤5: 检查生成后的图标文件...
if exist "windows\runner\resources\app_icon.ico" (
    echo ✅ 图标文件存在
    for %%A in ("windows\runner\resources\app_icon.ico") do echo 📦 文件大小: %%~zA 字节
    
    echo 📅 文件修改时间:
    forfiles /m app_icon.ico /p "windows\runner\resources" /c "cmd /c echo @fdate @ftime" 2>nul
) else (
    echo ❌ 图标文件仍然不存在
)

echo.
echo 🔍 步骤6: 检查资源文件配置...
if exist "windows\runner\Runner.rc" (
    echo ✅ 资源文件存在: windows\runner\Runner.rc
    echo 📄 图标引用:
    findstr /C:"app_icon.ico" "windows\runner\Runner.rc"
) else (
    echo ❌ 资源文件不存在
)

echo.
echo 📝 诊断结果:
echo ════════════════════════════════════════════
if exist "windows\runner\resources\app_icon.ico" (
    for %%A in ("windows\runner\resources\app_icon.ico") do (
        if %%~zA gtr 10000 (
            echo ✅ 图标文件大小正常 ^(%%~zA 字节^)
        ) else (
            echo ⚠️ 图标文件可能太小 ^(%%~zA 字节^)
        )
    )
) else (
    echo ❌ 图标文件缺失
)

echo.
echo 💡 建议的解决方案:
echo 1. 如果图标文件存在但exe仍显示默认图标，运行: fix_icon_completely.bat
echo 2. 如果图标文件不存在，检查flutter_launcher_icons插件安装
echo 3. 如果文件太小，可能需要手动生成图标
echo.
pause
