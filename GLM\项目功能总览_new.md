# 命理AI聊天系统项目功能总览

## 项目概述

命理AI聊天系统是一个综合性的命理分析与AI对话平台，由三个主要项目组成：
1. **numerology_ai_chat_new** - 基于Flutter的跨平台前端应用
2. **go_proxy** - Go语言编写的后端代理服务
3. **cloudfunctions** - 云函数集合，提供核心业务逻辑

## 1. numerology_ai_chat_new项目功能

### 基本功能
- 基于Flutter 3.7.2+开发的跨平台桌面应用
- 支持Windows、macOS和Linux系统
- 提供美观的用户界面和交互体验
- 支持命理分析与AI对话功能
- 自定义窗口管理，支持窗口大小调整和居中显示

### 核心模块

#### 用户认证模块
- 用户注册：创建新用户账户，支持用户名和密码认证
- 用户登录：验证用户身份并返回token
- Token管理：自动刷新过期token，支持应用生命周期内的token续签
- 用户信息管理：获取和更新用户个人信息

#### 命理分析模块
- **八字排盘功能**：
  - 支持公历和农历日期输入
  - 支持闰月处理
  - 支持性别选择
  - 支持出生地输入和经纬度坐标
  - 真太阳时计算选项
  - 夏令时考虑选项
  - 子时处理设置（早子时/晚子时）
  - 八字结果本地文件保存
  - 八字历史记录管理

#### AI对话模块
- **智能体系统**：
  - 支持多种类型的AI助手（八字、紫微、通用）
  - 智能体配置和选择
  - 大白话版本命理解析（将专业术语转化为通俗解释）
  - 智能体排序和过滤

- **模型系统**：
  - 支持多种AI模型
  - 模型参数配置（温度、最大Token等）
  - 模型选择和切换

- **对话功能**：
  - 流式对话响应
  - 多轮对话支持
  - 对话历史保存
  - 对话内容导出
  - Markdown渲染支持
  - 图片上传与分析

#### 聊天记录管理
- 本地聊天记录存储
- 聊天历史查看和管理
- 对话内容搜索
- 数据备份和恢复

#### 系统设置模块
- 主题设置：支持亮色/暗色主题切换
- 界面设置：字体大小、界面布局等
- 数据管理：缓存清理、数据导出
- 直播模式：适配直播场景的界面模式
- 版本更新：自动检测和更新应用

### 技术特点
- 使用Flutter 3.7.2+开发
- 采用Provider和Riverpod进行状态管理
- 使用Hive进行本地数据存储
- 支持Markdown渲染
- 响应式UI设计，适应不同屏幕尺寸
- 自定义霞鹜文楷字体，提供优美的中文显示效果
- 窗口管理器集成，支持自定义窗口行为
- 应用生命周期管理，支持token自动续签
- 图片处理和上传功能
- 本地文件系统访问

### 特色功能
- **真太阳时计算**：精确的命理时间计算，考虑地理位置和经纬度
- **大白话版命理解析**：将专业命理术语转化为通俗易懂的解释
- **多智能体支持**：提供不同专业领域的AI助手，包括八字、紫微等
- **图片上传与分析**：支持上传图片进行命理分析
- **直播模式**：适配直播场景的界面模式，优化显示效果
- **版本自动更新**：支持应用自动检测和更新
- **流式对话响应**：提供更流畅的对话体验
- **多轮对话支持**：保持上下文连贯性
- **本地数据持久化**：聊天记录和设置本地保存
- **响应式设计**：适应不同屏幕尺寸和窗口大小

## 2. go_proxy项目功能

### 基本功能
- 使用Go语言开发的高性能后端代理服务
- 作为前端应用和云函数之间的中间层
- 提供API请求转发和响应处理
- 支持负载均衡和请求缓存
- 处理流式响应

### 核心模块

#### API代理模块
- 请求路由：根据请求路径转发到相应的处理程序
- 请求转发：将前端请求转发到云函数
- 响应处理：处理云函数返回的响应并转发给前端
- 流式响应处理：支持AI对话的流式响应

#### 请求处理模块
- 请求解析：解析前端发送的请求参数
- 参数验证：验证请求参数的合法性
- 数据转换：转换数据格式以适配云函数接口
- 响应格式化：格式化云函数返回的响应数据

#### 安全认证模块
- JWT Token验证：验证前端请求中的Token
- 用户身份验证：确认用户身份和权限
- 请求签名验证：验证请求的合法性
- 敏感数据处理：加密处理敏感数据

#### 日志记录模块
- 请求日志：记录所有请求的详细信息
- 响应日志：记录响应内容和状态
- 错误日志：记录系统错误和异常
- 性能监控：监控请求处理时间和资源使用

#### 健康检查模块
- 服务状态监控：监控服务的运行状态
- 资源使用监控：监控CPU、内存等资源使用情况
- 健康检查端点：提供HTTP健康检查接口

### 技术特点
- 高性能的Go语言实现
- 支持高并发请求处理
- 提供RESTful API接口
- 支持跨域请求处理
- 配置化管理，支持环境变量
- 完善的错误处理和日志记录
- 流式响应处理
- 中间件架构，支持功能扩展

### 部署特性
- 支持Linux系统部署
- 提供系统服务配置
- 支持Docker容器化部署
- 提供自动化部署脚本
- 支持Nginx反向代理配置
- 支持环境变量配置
- 提供启动和停止脚本
- 支持日志轮转和管理

## 3. cloudfunctions项目功能

### 基本功能
- 云函数集合，提供核心业务逻辑
- 分为不同功能的云函数模块
- 支持用户管理、内容管理、支付处理等功能
- 提供RESTful API接口
- 支持数据库操作和第三方服务集成

### 核心云函数模块

#### exeFunction云函数

**用户认证功能**
- 用户注册：创建新用户账户，分配初始使用额度
- 用户登录：验证用户身份并返回accessToken和refreshToken
- Token刷新：使用refreshToken更新过期的访问令牌
- 激活码验证：验证激活码的有效性

**数据获取功能**
- 获取智能体列表：返回启用的AI助手（不含敏感信息）
- 获取模型列表：返回启用的AI模型（不含API密钥）
- 获取用户信息：返回用户详细信息（包含会员状态、可用次数等）
- 获取价格档次：返回不同功能的价格档次信息
- 获取套餐信息：返回可购买的套餐信息
- 获取系统配置：返回系统配置信息
- 获取Go代理API地址：返回Go代理服务的API地址

**使用次数管理**
- 更新使用次数：记录用户功能使用情况，根据智能体和模型类型计算消耗的算力
- 获取用户使用历史：返回用户的使用历史记录
- 算力计算：根据智能体和模型类型计算消耗的算力

**八字分析功能**
- 八字分析：根据用户输入的出生信息进行八字分析
- 真太阳时计算：根据出生地经纬度计算真太阳时
- 夏令时处理：处理夏令时对出生时间的影响

**版本管理功能**
- 版本检查：检查应用是否有新版本
- 版本信息获取：获取最新版本的信息和下载地址

**购买功能**
- 创建购买订单：创建套餐购买订单
- 模拟支付：模拟支付流程（测试环境）
- 查询支付状态：查询订单的支付状态
- 获取用户订单：获取用户的订单列表
- 获取订单详情：获取订单的详细信息
- 重试支付：重试失败的支付
- 取消订单：取消未支付的订单

**内部接口**
- 处理支付成功：处理支付成功的回调，更新用户会员状态和额度

#### exeAdmin云函数

**管理员认证功能**
- 管理员登录：验证管理员身份并返回token
- 管理员密码更新：更新管理员密码

**用户管理功能**
- 获取用户列表：分页获取用户列表
- 创建用户：创建新用户并设置初始额度
- 更新用户会员状态：更新用户的会员类型和过期时间
- 更新用户额度：增加或减少用户的使用额度
- 更新用户状态：启用或禁用用户账户

**智能体管理功能**
- 获取智能体列表：获取所有智能体（包含敏感信息）
- 创建智能体：创建新的AI助手，配置提示词和参数
- 更新智能体：修改智能体的配置和参数
- 删除智能体：删除不需要的智能体

**模型管理功能**
- 获取模型列表：获取所有AI模型（包含API密钥）
- 创建模型：创建新的AI模型，配置API地址和参数
- 更新模型：修改模型的配置和参数
- 删除模型：删除不需要的模型

**页面配置管理功能**
- 获取页面列表：获取所有内容页面
- 创建页面：创建新的内容页面
- 更新页面：修改页面的内容和配置
- 删除页面：删除不需要的页面

**系统配置管理功能**
- 获取系统配置列表：获取所有系统配置
- 创建系统配置：创建新的系统配置项
- 更新系统配置：修改系统配置的值
- 删除系统配置：删除不需要的系统配置

**套餐管理功能**
- 获取套餐列表：获取所有套餐
- 创建套餐：创建新的套餐
- 更新套餐：修改套餐的配置
- 删除套餐：删除不需要的套餐

**订单管理功能**
- 获取订单列表：获取所有订单
- 获取订单详情：获取订单的详细信息
- 更新订单状态：更新订单的状态
- 获取订单统计：获取订单的统计数据

**支付日志管理功能**
- 获取支付日志列表：获取所有支付日志
- 获取支付日志详情：获取支付日志的详细信息
- 获取支付日志统计：获取支付日志的统计数据
- 删除支付日志：删除不需要的支付日志

**使用历史管理功能**
- 获取使用历史列表：获取所有使用历史
- 获取使用历史详情：获取使用历史的详细信息
- 获取使用历史统计：获取使用历史的统计数据
- 删除使用历史：删除不需要的使用历史

**分析统计功能**
- 获取总体统计：获取系统的总体统计数据
- 获取趋势数据：获取系统使用的趋势数据

**日志和统计功能**
- 查看日志：查看系统运行日志
- 系统统计：获取系统使用的统计数据

#### exeWeakAdmin云函数

**弱管理员功能**
- 弱管理员登录：验证弱管理员身份
- 有限权限的管理操作：执行有限权限的管理操作
- 激活码系统管理：管理激活码的生成和使用

#### paymentCallback云函数

**支付处理功能**
- 富友支付回调处理：处理富友支付的异步回调通知
- 签名验证：验证支付回调的签名
- 订单状态更新：更新订单的支付状态
- 会员权限更新：根据支付结果更新用户会员状态和额度
- 支付成功处理：处理支付成功后的业务逻辑

### 技术特点
- 云函数架构，无需管理服务器
- 支持多种触发方式（HTTP调用、定时触发等）
- 数据库集成，支持数据持久化
- 安全认证和授权机制
- 完善的错误处理和日志记录
- 支持多种第三方服务集成
- 支持富友支付系统集成
- 支持内部云函数调用
- 支持跨域请求处理
- 支持请求和响应数据加密

## 系统整体架构

### 数据流
1. 用户通过Flutter前端应用进行交互
2. 前端应用将请求发送到Go代理服务
3. Go代理服务验证请求并转发到相应的云函数
4. 云函数处理业务逻辑并访问数据库
5. 处理结果通过Go代理服务返回给前端应用
6. 对于支付流程，支付平台直接回调paymentCallback云函数
7. paymentCallback云函数处理支付结果并调用exeFunction云函数更新订单状态

### 安全机制
- JWT Token认证：前端和Go代理之间、Go代理和云函数之间
- API请求加密：敏感数据传输加密
- 敏感数据加密存储：API密钥等敏感信息加密存储
- 访问权限控制：基于角色的访问控制
- 操作日志记录：记录所有关键操作
- 支付签名验证：验证支付回调的签名

### 扩展性
- 模块化设计，易于添加新功能
- 支持多种AI模型和智能体
- 可扩展的云函数架构
- 灵活的配置管理系统
- 支持多种支付方式
- 支持多种数据库

## 未来发展方向

### 功能扩展
- 增加更多命理分析方法：如紫微斗数、奇门遁甲等
- 引入更多专业领域的AI助手：如风水、姓名学等
- 开发移动端应用：支持iOS和Android平台
- 增加社交功能：用户分享、评论等
- 增加多语言支持：支持英文等其他语言

### 技术优化
- 提升系统性能和响应速度：优化数据库查询、缓存策略等
- 优化用户体验：改进UI设计、增加交互动画等
- 增强系统安全性：加强数据加密、访问控制等
- 改进数据分析和统计功能：提供更详细的用户行为分析
- 优化流式响应：提升对话体验

### 商业模式
- 会员订阅服务：提供不同等级的会员服务
- 高级功能付费：提供高级命理分析功能
- 企业级解决方案：为企业提供定制化服务
- API服务开放平台：开放API接口供第三方开发者使用
- 命理师认证平台：认证专业命理师并提供服务
