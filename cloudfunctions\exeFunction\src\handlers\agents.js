const { agentCollection } = require('../utils/db')
const { successResponse } = require('../middleware/error_handler')
const logger = require('../utils/logger')

/**
 * 获取智能体列表
 * @param {object} params 请求参数
 * @returns {object} 智能体列表
 */
async function getList(params) {
  try {
    logger.info('Getting agents list')

    // 获取启用的智能体列表（不包含agentPrompt）
    const agents = await agentCollection.getActiveList()

    logger.info('Agents list retrieved successfully', {
      count: agents.length
    })

    // 返回智能体列表（前端需要基本信息，包含档次信息用于算力计算）
    return successResponse({
      agents: agents.map(agent => ({
        id: agent._id,
        agentName: agent.agentName,
        agentType: agent.agentType,
        description: agent.description,
        pricingTierId: agent.pricingTierId || '', // 添加档次ID字段
        isActive: agent.isActive,
        sortOrder: agent.sortOrder,
        needLaymanVersion: agent.needLaymanVersion || false,  // 是否需要大白话版本
        laymanPrompt: agent.laymanPrompt || '',  // 大白话润色提示词（前端可能需要显示）
        createdAt: agent.createdAt || new Date().toISOString(),
        updatedAt: agent.updatedAt || new Date().toISOString()
      }))
    }, '获取智能体列表成功')

  } catch (error) {
    logger.error('Failed to get agents list', {
      error: error.message
    })
    throw error
  }
}

/**
 * 获取智能体完整信息（包含提示词，供Go程序使用）
 * @param {object} params 请求参数
 * @returns {object} 智能体完整信息
 */
async function getFullList(params) {
  try {
    logger.info('Getting full agents list for Go proxy')

    // 获取启用的智能体列表（包含agentPrompt）
    const agents = await agentCollection.getActiveFullList()

    logger.info('Full agents list retrieved successfully', {
      count: agents.length
    })

    // 返回完整的智能体信息（包含提示词和新字段）
    return successResponse({
      agents: agents.map(agent => ({
        id: agent._id,  // 使用id字段保持与前端一致
        agentName: agent.agentName,
        agentPrompt: agent.agentPrompt || agent.systemPrompt || '',
        agentType: agent.agentType,
        description: agent.description,
        isActive: agent.isActive,
        sortOrder: agent.sortOrder,
        needLaymanVersion: agent.needLaymanVersion || false,  // 是否需要大白话版本
        laymanPrompt: agent.laymanPrompt || '',  // 大白话润色提示词
        laymanApiKey: agent.laymanApiKey || '',  // 大白话专用API密钥
        laymanApiUrl: agent.laymanApiUrl || '',  // 大白话专用API地址
        laymanModelName: agent.laymanModelName || '',  // 大白话专用模型名称
        laymanMaxTokens: agent.laymanMaxTokens || 4096,  // 大白话专用最大token数
        laymanTemperature: agent.laymanTemperature || 0.7  // 大白话专用温度参数
      }))
    }, '获取智能体完整信息成功')

  } catch (error) {
    logger.error('Failed to get full agents list', {
      error: error.message
    })
    throw error
  }
}

module.exports = {
  getList,
  getFullList
}