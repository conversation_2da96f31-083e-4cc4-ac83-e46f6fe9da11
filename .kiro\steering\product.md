# 产品概述

## 命理AI聊天应用

基于Flutter开发的专业命理分析与AI对话桌面应用，专为Windows平台设计。

### 核心功能
- **AI驱动的命理咨询** - 针对不同占卜方法的专业智能体
- **安全的云端架构** - 本地不存储任何敏感数据
- **多模态聊天** - 支持文字和图片输入
- **实时流式响应** - 来自大语言模型的实时回复
- **本地聊天记录存储** - 加密存储，保护隐私
- **算力计费系统** - 基于差异化定价档次的计费模式

### 架构组件
- **前端应用**: Flutter桌面应用 (numerology_ai_chat_new)
- **管理后台**: Flutter管理界面 (numerology_ai_chat_new_admin) 
- **后端服务**: 腾讯云函数 (Node.js 16.13)
- **代理服务**: Go中间件，用于安全的AI API调用
- **Web管理**: Vue.js管理界面 (weak-admin-web)
- **数据库**: 腾讯云开发文档型数据库

### 核心原则
- **安全第一**: 前端不存储API密钥或敏感提示词
- **隐私保护**: 聊天记录仅本地存储，绝不上传
- **可扩展架构**: 云函数配合Go代理处理AI调用
- **用户体验**: 无缝认证，支持token自动刷新