package handlers

import (
	"log"
	"net/http"

	"go_proxy/internal/config"
	"go_proxy/internal/proxy"
	"go_proxy/internal/services/cloudfunction"
	"go_proxy/internal/services/database"

	"github.com/gin-gonic/gin"
)

// HandleChatCompletion 处理聊天完成请求
func HandleChatCompletion(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从context中获取用户信息和token
		userID, exists := c.Get("userId")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "User not authenticated",
			})
			return
		}

		token, tokenExists := c.Get("token")
		if !tokenExists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Token not found",
			})
			return
		}

		// 解析请求体
		var chatReq proxy.ChatRequest
		if err := c.ShouldBindJSON(&chatReq); err != nil {
			c.<PERSON>(http.StatusBadRequest, gin.H{
				"error": "Invalid request format: " + err.Error(),
			})
			return
		}

		// 验证必要字段
		if chatReq.AgentID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "agentId is required",
			})
			return
		}

		if chatReq.ModelID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "modelId is required",
			})
			return
		}

		if len(chatReq.Messages) == 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "messages cannot be empty",
			})
			return
		}

		// 创建数据库客户端
		dbClient, err := database.NewClient(cfg)
		if err != nil {
			log.Printf("Failed to create database client: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Internal server error",
			})
			return
		}

		// 创建LLM代理
		llmProxy := proxy.NewLLMProxy(dbClient)

		// 代理请求到LLM
		if err := llmProxy.ProxyRequest(c, &chatReq, token.(string)); err != nil {
			log.Printf("Failed to proxy request: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to process chat request",
			})
			return
		}

		// 异步更新用户使用次数
		go func() {
			accessToken := c.GetHeader("Authorization")
			if accessToken != "" {
				// 移除"Bearer "前缀
				if len(accessToken) > 7 && accessToken[:7] == "Bearer " {
					accessToken = accessToken[7:]
				}

				cloudClient := cloudfunction.NewClient(cfg)
				// 使用新的算力计算方法，传递agentId和modelId
				if err := cloudClient.UpdateUsageWithPowerCalculation(accessToken, userID.(string), chatReq.AgentID, chatReq.ModelID); err != nil {
					log.Printf("Failed to update usage for user %s: %v", userID, err)
				}
			}
		}()
	}
}