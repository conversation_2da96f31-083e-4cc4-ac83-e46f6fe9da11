@echo off
echo ================================================
echo    Windows Icon Cache Cleaner
echo ================================================
echo.
echo This tool will clear Windows icon cache to fix
echo custom icon display issues.
echo.
echo WARNING: This operation will:
echo    1. Temporarily close Windows Explorer
echo    2. Clear icon cache files
echo    3. Restart Windows Explorer
echo.
echo Use case:
echo    - Custom icon visible in file properties
echo    - But default icon shown in file manager
echo.

set /p confirm="Confirm cleanup operation? (Y/N): "
if /i not "%confirm%"=="Y" (
    echo Operation cancelled
    pause
    exit /b 0
)

echo.
echo Step 1: Closing Windows Explorer...
taskkill /f /im explorer.exe >nul 2>&1
if %errorlevel% equ 0 (
    echo Explorer closed successfully
) else (
    echo Explorer close failed, continuing...
)

echo.
echo Step 2: Clearing icon cache files...
cd /d "%userprofile%\AppData\Local\Microsoft\Windows\Explorer" 2>nul
if exist "iconcache*.db" (
    del iconcache*.db /a /q >nul 2>&1
    echo Icon cache files cleared
) else (
    echo No icon cache files found
)

echo.
echo Step 3: Restarting Windows Explorer...
start explorer.exe
timeout /t 3 /nobreak >nul
echo Explorer restarted successfully

echo.
echo Icon cache cleanup completed!
echo ================================================
echo Please check:
echo    1. Desktop and taskbar display normally
echo    2. EXE file icon has been updated
echo    3. If issue persists, restart computer
echo.
echo Tips:
echo    - Icon update may take a few seconds
echo    - Some cases require computer restart
echo    - If problem continues, check icon embedding
echo.
pause
