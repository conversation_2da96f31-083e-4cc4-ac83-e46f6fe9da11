import 'dart:convert';
import 'package:http/http.dart' as http;

void main() async {
  print('🧪 开始测试输入验证功能...\n');
  
  // 测试用例
  final testCases = [
    // 用户名测试
    {'username': 'abc', 'password': 'password123', 'expected': false, 'reason': '用户名少于8位'},
    {'username': '中文用户名123', 'password': 'password123', 'expected': false, 'reason': '用户名包含中文'},
    {'username': 'user@name', 'password': 'password123', 'expected': false, 'reason': '用户名包含特殊符号'},
    {'username': 'validuser123', 'password': 'password123', 'expected': true, 'reason': '有效用户名'},
    {'username': 'VALIDUSER123', 'password': 'password123', 'expected': true, 'reason': '有效用户名（大写）'},
    {'username': 'ValidUser123', 'password': 'password123', 'expected': true, 'reason': '有效用户名（混合大小写）'},
    
    // 密码测试
    {'username': 'validuser123', 'password': 'short', 'expected': false, 'reason': '密码少于8位'},
    {'username': 'validuser123', 'password': '密码123456', 'expected': false, 'reason': '密码包含中文'},
    {'username': 'validuser123', 'password': 'password123', 'expected': true, 'reason': '有效密码（字母数字）'},
    {'username': 'validuser123', 'password': 'Password123!', 'expected': true, 'reason': '有效密码（包含符号）'},
    {'username': 'validuser123', 'password': 'P@ssw0rd!#\$%', 'expected': true, 'reason': '有效密码（复杂符号）'},
  ];
  
  for (int i = 0; i < testCases.length; i++) {
    final testCase = testCases[i];
    print('测试 ${i + 1}: ${testCase['reason']}');
    print('用户名: ${testCase['username']}');
    print('密码: ${testCase['password']}');
    
    final result = await testRegistration(
      testCase['username'] as String,
      testCase['password'] as String,
    );
    
    final expected = testCase['expected'] as bool;
    if (result == expected) {
      print('✅ 测试通过');
    } else {
      print('❌ 测试失败 - 期望: ${expected ? '成功' : '失败'}, 实际: ${result ? '成功' : '失败'}');
    }
    print('---');
  }
  
  print('\n🎯 测试完成！');
}

Future<bool> testRegistration(String username, String password) async {
  try {
    final response = await http.post(
      Uri.parse('https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'action': 'register',
        'username': username,
        'password': password,
      }),
    );

    if (response.statusCode == 200) {
      final responseData = jsonDecode(response.body);
      
      if (responseData['code'] == 0) {
        print('✅ 注册成功');
        return true;
      } else {
        print('❌ 注册失败: ${responseData['message']}');
        return false;
      }
    } else {
      print('❌ 请求失败: ${response.statusCode}');
      return false;
    }
  } catch (e) {
    print('❌ 测试异常: $e');
    return false;
  }
}
