import '../models/agent_model.dart';
import '../models/api_response.dart';
import '../core/constants/app_constants.dart';
import 'cloud_function_service.dart';
import 'storage_service.dart';

/// 智能体服务
class AgentService {
  final CloudFunctionService _cloudFunctionService = CloudFunctionService();
  final StorageService _storageService = StorageService();

  // 缓存
  List<AgentModel>? _cachedAgents;
  DateTime? _lastCacheTime;

  /// 获取智能体列表
  Future<List<AgentModel>> getAgents({
    required String token,
    bool forceRefresh = false,
  }) async {
    // 检查缓存
    if (!forceRefresh && _cachedAgents != null && _lastCacheTime != null) {
      final cacheAge = DateTime.now().difference(_lastCacheTime!);
      if (cacheAge < AppConstants.cacheExpiry) {
        return _cachedAgents!;
      }
    }

    try {
      final response = await _cloudFunctionService.getAgents(token: token);
      
      if (response.isSuccess && response.data?.success == true) {
        final agentsData = response.data?.data?.agents ?? [];
        final agents = agentsData
            .map((agentJson) => AgentModel.fromJson(agentJson as Map<String, dynamic>))
            .where((agent) => agent.isActive) // 只返回激活的智能体
            .toList();

        // 按排序权重排序
        agents.sort((a, b) => a.sortOrder.compareTo(b.sortOrder));

        // 更新缓存
        _cachedAgents = agents;
        _lastCacheTime = DateTime.now();

        // 保存到本地存储
        await _saveAgentsToStorage(agents);

        return agents;
      } else {
        // 如果云函数调用失败，尝试从本地存储加载
        return await _loadAgentsFromStorage();
      }
    } catch (e) {
      // 网络错误时从本地存储加载，如果本地也没有则抛出异常
      final localAgents = await _loadAgentsFromStorage();
      if (localAgents.isEmpty) {
        throw Exception('无法加载智能体列表，请检查网络连接');
      }
      return localAgents;
    }
  }

  /// 根据ID获取智能体
  Future<AgentModel?> getAgentById(String agentId, String token) async {
    final agents = await getAgents(token: token);
    try {
      return agents.firstWhere((agent) => agent.id == agentId);
    } catch (e) {
      return null;
    }
  }

  /// 根据类型获取智能体列表
  Future<List<AgentModel>> getAgentsByType(AgentType type, String token) async {
    final agents = await getAgents(token: token);
    return agents.where((agent) => agent.agentType == type).toList();
  }

  /// 搜索智能体
  Future<List<AgentModel>> searchAgents(String query, String token) async {
    final agents = await getAgents(token: token);
    final lowerQuery = query.toLowerCase();
    
    return agents.where((agent) {
      return agent.agentName.toLowerCase().contains(lowerQuery) ||
             agent.description.toLowerCase().contains(lowerQuery) ||
             agent.agentType.displayName.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// 获取推荐智能体（按使用频率或其他逻辑）
  Future<List<AgentModel>> getRecommendedAgents(String token) async {
    final agents = await getAgents(token: token);
    
    // 这里可以根据用户的使用历史、偏好等来推荐
    // 目前简单按照排序权重返回前几个
    return agents.take(3).toList();
  }

  /// 清除缓存
  void clearCache() {
    _cachedAgents = null;
    _lastCacheTime = null;
  }

  /// 保存智能体列表到本地存储
  Future<void> _saveAgentsToStorage(List<AgentModel> agents) async {
    try {
      final agentsJson = agents.map((agent) => agent.toJson()).toList();
      await _storageService.setJsonList(AppConstants.agentsCacheKey, agentsJson);
      await _storageService.setString(
        AppConstants.lastUpdateKey,
        DateTime.now().toIso8601String(),
      );
    } catch (e) {
      // 忽略存储错误
    }
  }

  /// 从本地存储加载智能体列表
  Future<List<AgentModel>> _loadAgentsFromStorage() async {
    try {
      final agentsJsonList = await _storageService.getJsonList(AppConstants.agentsCacheKey);
      if (agentsJsonList != null) {
        return agentsJsonList
            .map((agentJson) => AgentModel.fromJson(agentJson))
            .where((agent) => agent.isActive)
            .toList();
      }
    } catch (e) {
      // 忽略加载错误
    }

    // 不再返回硬编码的默认智能体
    return [];
  }

  /// 获取默认智能体列表（后备方案）
  List<AgentModel> _getDefaultAgents() {
    // 不再返回硬编码的智能体，强制从云函数加载
    return [];
  }
}
