import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/purchase_history_model.dart';
import '../services/purchase_history_service.dart';
import 'auth_provider.dart';

/// 购买历史状态
class PurchaseHistoryState {
  final List<PurchaseHistoryModel> orders;
  final bool isLoading;
  final String? error;
  final bool hasMore;
  final int currentPage;

  const PurchaseHistoryState({
    this.orders = const [],
    this.isLoading = false,
    this.error,
    this.hasMore = true,
    this.currentPage = 1,
  });

  PurchaseHistoryState copyWith({
    List<PurchaseHistoryModel>? orders,
    bool? isLoading,
    String? error,
    bool? hasMore,
    int? currentPage,
    bool clearError = false,
  }) {
    return PurchaseHistoryState(
      orders: orders ?? this.orders,
      isLoading: isLoading ?? this.isLoading,
      error: clearError ? null : (error ?? this.error),
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
    );
  }
}

/// 购买历史状态管理
class PurchaseHistoryNotifier extends StateNotifier<PurchaseHistoryState> {
  final PurchaseHistoryService _service;
  final Ref _ref;

  PurchaseHistoryNotifier(this._service, this._ref) : super(const PurchaseHistoryState());

  /// 加载购买历史
  Future<void> loadPurchaseHistory({bool refresh = false}) async {
    final authState = _ref.read(authProvider);
    final user = authState.user;
    final token = authState.token;

    if (user == null || token == null) {
      state = state.copyWith(error: '用户未登录');
      return;
    }

    // 如果是刷新，重置状态
    if (refresh) {
      state = const PurchaseHistoryState(isLoading: true);
    } else if (state.isLoading || !state.hasMore) {
      return;
    } else {
      state = state.copyWith(isLoading: true, clearError: true);
    }

    try {
      final page = refresh ? 1 : state.currentPage;
      final orders = await _service.getUserPurchaseHistory(
        userId: user.id,
        token: token,
        page: page,
        limit: 20,
      );

      if (refresh) {
        state = state.copyWith(
          orders: orders,
          isLoading: false,
          hasMore: orders.length >= 20,
          currentPage: 1,
          clearError: true,
        );
      } else {
        final allOrders = [...state.orders, ...orders];
        state = state.copyWith(
          orders: allOrders,
          isLoading: false,
          hasMore: orders.length >= 20,
          currentPage: page + 1,
          clearError: true,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 加载更多
  Future<void> loadMore() async {
    if (!state.hasMore || state.isLoading) return;
    await loadPurchaseHistory();
  }

  /// 刷新
  Future<void> refresh() async {
    await loadPurchaseHistory(refresh: true);
  }

  /// 重新支付订单
  Future<bool> retryPayment(String orderId) async {
    final authState = _ref.read(authProvider);
    final token = authState.token;

    if (token == null) {
      state = state.copyWith(error: '用户未登录');
      return false;
    }

    try {
      final result = await _service.retryPayment(
        orderId: orderId,
        token: token,
      );
      
      if (result != null) {
        // 重新加载购买历史
        await refresh();
        return true;
      } else {
        state = state.copyWith(error: '重新支付失败');
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// 取消订单
  Future<bool> cancelOrder(String orderId) async {
    final authState = _ref.read(authProvider);
    final token = authState.token;

    if (token == null) {
      state = state.copyWith(error: '用户未登录');
      return false;
    }

    try {
      final success = await _service.cancelOrder(
        orderId: orderId,
        token: token,
      );
      
      if (success) {
        // 重新加载购买历史
        await refresh();
        return true;
      } else {
        state = state.copyWith(error: '取消订单失败');
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// 清除错误
  void clearError() {
    state = state.copyWith(clearError: true);
  }
}

/// 购买历史服务提供者
final purchaseHistoryServiceProvider = Provider<PurchaseHistoryService>((ref) {
  return PurchaseHistoryService();
});

/// 购买历史状态提供者
final purchaseHistoryProvider = StateNotifierProvider<PurchaseHistoryNotifier, PurchaseHistoryState>((ref) {
  final service = ref.read(purchaseHistoryServiceProvider);
  return PurchaseHistoryNotifier(service, ref);
});

/// 自动加载购买历史提供者
final autoLoadPurchaseHistoryProvider = Provider<void>((ref) {
  final authState = ref.watch(authProvider);
  final purchaseHistoryNotifier = ref.read(purchaseHistoryProvider.notifier);
  
  // 当用户登录时自动加载购买历史
  if (authState.isAuthenticated && authState.user != null) {
    // 延迟执行以避免在build过程中调用
    Future.microtask(() {
      purchaseHistoryNotifier.loadPurchaseHistory(refresh: true);
    });
  }
});
