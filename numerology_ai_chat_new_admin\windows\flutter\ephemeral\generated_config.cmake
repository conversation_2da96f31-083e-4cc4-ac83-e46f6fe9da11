# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\flutter\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "F:\\suanming\\0624chat-1.0.1\\numerology_ai_chat_new_admin" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\flutter\\flutter"
  "PROJECT_DIR=F:\\suanming\\0624chat-1.0.1\\numerology_ai_chat_new_admin"
  "FLUTTER_ROOT=D:\\flutter\\flutter"
  "FLUTTER_EPHEMERAL_DIR=F:\\suanming\\0624chat-1.0.1\\numerology_ai_chat_new_admin\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=F:\\suanming\\0624chat-1.0.1\\numerology_ai_chat_new_admin"
  "FLUTTER_TARGET=F:\\suanming\\0624chat-1.0.1\\numerology_ai_chat_new_admin\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=F:\\suanming\\0624chat-1.0.1\\numerology_ai_chat_new_admin\\.dart_tool\\package_config.json"
)
