#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完美图标生成器 - 解决Windows应用图标显示问题
生成包含多种尺寸的高质量ICO文件，确保在文件管理器中正确显示
"""

import os
import sys
from PIL import Image, ImageFilter, ImageEnhance
import shutil
from datetime import datetime

def print_status(message, status="info"):
    """打印带状态的消息"""
    icons = {
        "info": "🔄",
        "success": "✅", 
        "warning": "⚠️",
        "error": "❌",
        "check": "🔍"
    }
    print(f"{icons.get(status, '📝')} {message}")

def backup_existing_icon():
    """备份现有图标文件"""
    icon_path = "windows/runner/resources/app_icon.ico"
    if os.path.exists(icon_path):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"{icon_path}.backup_{timestamp}"
        shutil.copy2(icon_path, backup_path)
        print_status(f"已备份现有图标到: {backup_path}", "success")
        return backup_path
    return None

def enhance_image_for_small_size(img, target_size):
    """为小尺寸图标增强图像质量"""
    # 如果目标尺寸很小，增强锐度和对比度
    if target_size <= 32:
        # 增强锐度
        enhancer = ImageEnhance.Sharpness(img)
        img = enhancer.enhance(1.5)
        
        # 增强对比度
        enhancer = ImageEnhance.Contrast(img)
        img = enhancer.enhance(1.2)
        
        # 轻微锐化滤镜
        img = img.filter(ImageFilter.UnsharpMask(radius=0.5, percent=150, threshold=3))
    
    return img

def create_high_quality_icon():
    """创建高质量的Windows图标文件"""
    print_status("开始创建完美的Windows应用图标...", "info")
    print("=" * 60)
    
    # 检查源图片
    source_path = "assets/images/logo.png"
    if not os.path.exists(source_path):
        print_status(f"源图片文件不存在: {source_path}", "error")
        return False
    
    # 备份现有图标
    backup_existing_icon()
    
    try:
        # 打开源图片
        with Image.open(source_path) as source_img:
            print_status(f"源图片尺寸: {source_img.size}", "check")
            print_status(f"源图片模式: {source_img.mode}", "check")
            
            # 转换为RGBA模式以支持透明度
            if source_img.mode != 'RGBA':
                source_img = source_img.convert('RGBA')
            
            # 定义Windows图标的标准尺寸（包含所有常用尺寸）
            icon_sizes = [16, 20, 24, 32, 40, 48, 64, 72, 96, 128, 256]
            
            # 创建不同尺寸的图标
            icon_images = []
            
            for size in icon_sizes:
                print_status(f"生成 {size}x{size} 图标", "info")
                
                # 高质量缩放
                resized = source_img.resize((size, size), Image.Resampling.LANCZOS)
                
                # 为小尺寸图标进行特殊处理
                if size <= 48:
                    resized = enhance_image_for_small_size(resized, size)
                
                icon_images.append(resized)
            
            # 确保输出目录存在
            output_dir = "windows/runner/resources"
            os.makedirs(output_dir, exist_ok=True)
            
            # 保存为ICO文件
            output_path = os.path.join(output_dir, "app_icon.ico")
            
            # 使用第一个图像作为主图像，其他作为附加尺寸
            icon_images[0].save(
                output_path,
                format='ICO',
                sizes=[(img.width, img.height) for img in icon_images],
                append_images=icon_images[1:]
            )
            
            # 验证生成的文件
            file_size = os.path.getsize(output_path)
            print_status(f"图标文件已生成: {output_path}", "success")
            print_status(f"文件大小: {file_size:,} 字节", "success")
            print_status(f"包含尺寸: {len(icon_sizes)} 种", "success")
            
            # 验证ICO文件
            try:
                with Image.open(output_path) as ico_img:
                    print_status(f"验证成功 - 主图标尺寸: {ico_img.size}", "success")
            except Exception as e:
                print_status(f"ICO文件验证失败: {e}", "warning")
            
            return True
            
    except Exception as e:
        print_status(f"创建图标时发生错误: {e}", "error")
        return False

def verify_windows_resources():
    """验证Windows资源文件配置"""
    print_status("验证Windows资源文件配置...", "check")
    
    rc_file = "windows/runner/Runner.rc"
    if os.path.exists(rc_file):
        with open(rc_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            if 'IDI_APP_ICON' in content and 'app_icon.ico' in content:
                print_status("Windows资源文件配置正确", "success")
                return True
            else:
                print_status("Windows资源文件配置可能有问题", "warning")
                return False
    else:
        print_status("Windows资源文件不存在", "error")
        return False

def main():
    """主函数"""
    print("🎨 完美图标生成器")
    print("=" * 60)
    print("📝 本工具将创建包含多种尺寸的高质量Windows图标")
    print("🎯 解决文件管理器中显示默认图标的问题")
    print("=" * 60)
    print()
    
    # 检查PIL库
    try:
        from PIL import Image
        print_status("PIL库检查通过", "success")
    except ImportError:
        print_status("PIL库未安装，请运行: pip install Pillow", "error")
        return False
    
    # 创建图标
    if create_high_quality_icon():
        print()
        print_status("图标创建成功！", "success")
        
        # 验证资源文件
        verify_windows_resources()
        
        print()
        print("📝 接下来的步骤:")
        print("1. 运行: flutter clean")
        print("2. 运行: flutter build windows --release")
        print("3. 检查生成的exe文件图标")
        print()
        print("💡 如果图标仍然显示为默认图标:")
        print("- 重启Windows资源管理器")
        print("- 清理Windows图标缓存")
        print("- 确保exe文件是新构建的版本")
        
        return True
    else:
        print_status("图标创建失败", "error")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
