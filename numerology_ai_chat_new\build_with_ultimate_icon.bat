@echo off
chcp 65001 >nul
echo 🎯 终极图标构建脚本
echo ================================

echo 🔄 步骤1: 生成高质量图标...
python ultimate_icon_fix.py
if %errorlevel% neq 0 (
    echo ❌ 图标生成失败！
    pause
    exit /b 1
)

echo 🔄 步骤2: 清理构建缓存...
flutter clean

echo 🔄 步骤3: 获取依赖...
flutter pub get

echo 🔄 步骤4: 构建应用...
flutter build windows --release

echo 🎉 构建完成！
echo 📁 可执行文件: build\windows\x64\runner\Release\numerology_ai_chat.exe
pause
