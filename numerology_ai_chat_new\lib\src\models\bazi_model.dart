/// 性别枚举
enum Gender {
  male('男'),
  female('女');

  const Gender(this.displayName);
  final String displayName;
}

/// 历法类型枚举
enum CalendarType {
  solar('公历'),
  lunar('农历');

  const CalendarType(this.displayName);
  final String displayName;
}

/// 排盘输入信息模型
class BaziInputModel {
  final String name;
  final DateTime birthDateTime;
  final Gender gender;
  final CalendarType calendarType;
  final bool isLeapMonth;
  final String birthPlace;
  final double? longitude;
  final double? latitude;
  final bool considerDaylightSaving;
  final bool enableSolarTimeCalculation;
  final int ziTimeHandling; // 0: 晚子时（子时算当天），1: 早子时（子时算第二天）

  const BaziInputModel({
    required this.name,
    required this.birthDateTime,
    required this.gender,
    required this.calendarType,
    this.isLeapMonth = false,
    required this.birthPlace,
    this.longitude,
    this.latitude,
    this.considerDaylightSaving = true,
    this.enableSolarTimeCalculation = true,
    this.ziTimeHandling = 0, // 默认晚子时
  });

  /// 从JSON创建排盘输入模型
  factory BaziInputModel.fromJson(Map<String, dynamic> json) {
    return BaziInputModel(
      name: json['name'] as String,
      birthDateTime: DateTime.parse(json['birth_date_time'] as String),
      gender: Gender.values.firstWhere(
        (e) => e.name == json['gender'],
        orElse: () => Gender.male,
      ),
      calendarType: CalendarType.values.firstWhere(
        (e) => e.name == json['calendar_type'],
        orElse: () => CalendarType.solar,
      ),
      isLeapMonth: json['is_leap_month'] as bool? ?? false,
      birthPlace: json['birth_place'] as String,
      longitude: json['longitude'] as double?,
      latitude: json['latitude'] as double?,
      considerDaylightSaving: json['consider_daylight_saving'] as bool? ?? true,
      enableSolarTimeCalculation: json['enable_solar_time_calculation'] as bool? ?? true,
      ziTimeHandling: json['zi_time_handling'] as int? ?? 0,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'birth_date_time': birthDateTime.toIso8601String(),
      'gender': gender.name,
      'calendar_type': calendarType.name,
      'is_leap_month': isLeapMonth,
      'birth_place': birthPlace,
      'longitude': longitude,
      'latitude': latitude,
      'consider_daylight_saving': considerDaylightSaving,
      'enable_solar_time_calculation': enableSolarTimeCalculation,
      'zi_time_handling': ziTimeHandling,
    };
  }

  /// 复制并修改部分属性
  BaziInputModel copyWith({
    String? name,
    DateTime? birthDateTime,
    Gender? gender,
    CalendarType? calendarType,
    bool? isLeapMonth,
    String? birthPlace,
    double? longitude,
    double? latitude,
    bool? considerDaylightSaving,
    bool? enableSolarTimeCalculation,
    int? ziTimeHandling,
  }) {
    return BaziInputModel(
      name: name ?? this.name,
      birthDateTime: birthDateTime ?? this.birthDateTime,
      gender: gender ?? this.gender,
      calendarType: calendarType ?? this.calendarType,
      isLeapMonth: isLeapMonth ?? this.isLeapMonth,
      birthPlace: birthPlace ?? this.birthPlace,
      longitude: longitude ?? this.longitude,
      latitude: latitude ?? this.latitude,
      considerDaylightSaving: considerDaylightSaving ?? this.considerDaylightSaving,
      enableSolarTimeCalculation: enableSolarTimeCalculation ?? this.enableSolarTimeCalculation,
      ziTimeHandling: ziTimeHandling ?? this.ziTimeHandling,
    );
  }

  @override
  String toString() {
    return 'BaziInputModel(name: $name, birthDateTime: $birthDateTime, '
        'gender: $gender, calendarType: $calendarType, birthPlace: $birthPlace)';
  }
}

/// 排盘结果模型
class BaziResultModel {
  final String id;
  final BaziInputModel input;
  final String baziText;
  final Map<String, dynamic> detailedData;
  final DateTime createdAt;

  const BaziResultModel({
    required this.id,
    required this.input,
    required this.baziText,
    required this.detailedData,
    required this.createdAt,
  });

  /// 从JSON创建排盘结果模型
  factory BaziResultModel.fromJson(Map<String, dynamic> json) {
    return BaziResultModel(
      id: json['id'] as String,
      input: BaziInputModel.fromJson(json['input'] as Map<String, dynamic>),
      baziText: json['bazi_text'] as String,
      detailedData: json['detailed_data'] as Map<String, dynamic>,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'input': input.toJson(),
      'bazi_text': baziText,
      'detailed_data': detailedData,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// 复制并修改部分属性
  BaziResultModel copyWith({
    String? id,
    BaziInputModel? input,
    String? baziText,
    Map<String, dynamic>? detailedData,
    DateTime? createdAt,
  }) {
    return BaziResultModel(
      id: id ?? this.id,
      input: input ?? this.input,
      baziText: baziText ?? this.baziText,
      detailedData: detailedData ?? this.detailedData,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// 获取简化的八字信息用于显示
  String get simplifiedBazi {
    if (baziText.length > 100) {
      return '${baziText.substring(0, 100)}...';
    }
    return baziText;
  }

  /// 获取年柱
  String? get yearPillar => detailedData['year_pillar'] as String?;

  /// 获取月柱
  String? get monthPillar => detailedData['month_pillar'] as String?;

  /// 获取日柱
  String? get dayPillar => detailedData['day_pillar'] as String?;

  /// 获取时柱
  String? get hourPillar => detailedData['hour_pillar'] as String?;

  /// 获取四柱组合
  String get fourPillars {
    final pillars = [yearPillar, monthPillar, dayPillar, hourPillar]
        .where((p) => p != null && p.isNotEmpty)
        .join(' ');
    return pillars.isNotEmpty ? pillars : '排盘数据不完整';
  }

  @override
  String toString() {
    return 'BaziResultModel(id: $id, name: ${input.name}, '
        'fourPillars: $fourPillars)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BaziResultModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  /// 兼容旧代码：判断八字信息是否包含指定关键字【contains包含】
  bool contains(String keyword) {
    if (keyword.isEmpty) return false;
    return (input.name.contains(keyword)) ||
        (fourPillars.contains(keyword)) ||
        (baziText.contains(keyword));
  }
}
