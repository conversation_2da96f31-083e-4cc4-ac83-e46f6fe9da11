import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../constants/app_constants.dart';
import '../../screens/splash_screen.dart';
import '../../screens/login_screen.dart';
import '../../screens/register_screen.dart';
import '../../screens/home_screen.dart';
import '../../screens/chat_screen.dart';
import '../../screens/bazi_screen.dart';
import '../../screens/purchase_screen.dart';

// 导入设置页面
import '../../screens/settings_screen.dart';
import '../../screens/profile_screen.dart';
import '../../providers/auth_provider.dart';

/// 路由配置提供者
final routerProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: AppRoutes.splash,
    debugLogDiagnostics: true,
    redirect: (context, state) {
      // 在重定向函数中读取AuthProvider状态，而不是在Provider级别监听
      // 这样可以避免启动阶段的无限重建
      final authState = ref.read(authProvider);
      final isAuthenticated = authState.isAuthenticated;
      final authStatus = authState.status;
      final isLoggingIn = state.matchedLocation == AppRoutes.login;
      final isRegistering = state.matchedLocation == AppRoutes.register;
      final isSplash = state.matchedLocation == AppRoutes.splash;

      debugPrint('路由重定向检查: isAuthenticated=$isAuthenticated, authStatus=$authStatus, currentLocation=${state.matchedLocation}');

      // 如果在启动页面，让启动页面自己处理跳转
      if (isSplash) {
        debugPrint('路由重定向: 在启动页面，不进行重定向');
        return null;
      }

      // 如果未登录且不在登录或注册页面，重定向到登录页面
      if (!isAuthenticated && !isLoggingIn && !isRegistering) {
        debugPrint('路由重定向: 未登录，跳转到登录页面');
        return AppRoutes.login;
      }

      // 如果已登录且在登录或注册页面，重定向到主页
      if (isAuthenticated && (isLoggingIn || isRegistering)) {
        debugPrint('路由重定向: 已登录，跳转到主页');
        return AppRoutes.home;
      }

      debugPrint('路由重定向: 无需重定向');
      return null;
    },
    routes: [
      // 启动页面
      GoRoute(
        path: AppRoutes.splash,
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),

      // 登录页面
      GoRoute(
        path: AppRoutes.login,
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),

      // 注册页面
      GoRoute(
        path: AppRoutes.register,
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),

      // 主页面及其子路由
      ShellRoute(
        builder: (context, state, child) => HomeScreen(child: child),
        routes: [
          // 聊天页面
          GoRoute(
            path: AppRoutes.chat,
            name: 'chat',
            builder: (context, state) => const ChatScreen(),
          ),

          // 八字排盘页面
          GoRoute(
            path: AppRoutes.bazi,
            name: 'bazi',
            builder: (context, state) => const BaziScreen(),
          ),

          // 充值算力页面
          GoRoute(
            path: AppRoutes.purchase,
            name: 'purchase',
            builder: (context, state) => const PurchaseScreen(),
          ),

          // 设置页面
          GoRoute(
            path: AppRoutes.settings,
            name: 'settings',
            builder: (context, state) => const SettingsScreen(),
          ),

          // 个人中心页面
          GoRoute(
            path: AppRoutes.profile,
            name: 'profile',
            builder: (context, state) => const ProfileScreen(),
          ),
        ],
      ),

      // 主页重定向
      GoRoute(
        path: AppRoutes.home,
        name: 'home',
        redirect: (context, state) => AppRoutes.chat,
      ),
    ],
    errorBuilder: (context, state) => ErrorScreen(error: state.error),
  );
});

/// 错误页面
class ErrorScreen extends StatelessWidget {
  final GoException? error;

  const ErrorScreen({super.key, this.error});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: theme.colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              '页面未找到',
              style: theme.textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              error?.toString() ?? '请检查URL是否正确',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.home),
              child: const Text('返回首页'),
            ),
          ],
        ),
      ),
    );
  }
}

/// 路由扩展
extension GoRouterExtension on GoRouter {
  /// 安全导航
  void safePush(String location) {
    try {
      push(location);
    } catch (e) {
      // 如果导航失败，回到首页
      go(AppRoutes.home);
    }
  }

  /// 安全替换
  void safeGo(String location) {
    try {
      go(location);
    } catch (e) {
      // 如果导航失败，回到首页
      go(AppRoutes.home);
    }
  }
}
