@echo off
chcp 65001 >nul
echo ════════════════════════════════════════════
echo    命理AI聊天应用 - 构建脚本
echo ════════════════════════════════════════════
echo.

echo 🔄 开始构建Windows应用...
flutter build windows --release

if %errorlevel% equ 0 (
    echo.
    echo 🎉 构建成功！
    echo 📁 可执行文件: build\windows\x64\runner\Release\numerology_ai_chat.exe
    echo 💡 图标已自动应用
) else (
    echo.
    echo ❌ 构建失败！
    echo 💡 请检查错误信息
)

echo.
pause
