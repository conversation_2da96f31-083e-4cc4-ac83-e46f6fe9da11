<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>命理AI聊天项目分层架构图</title>
    <style>
        body { 
            font-family: 'Microsoft YaHei', sans-serif; 
            margin: 20px;
            background-color: #f5f5f5;
        }
        .diagram-container { 
            background-color: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .mermaid { 
            text-align: center; 
        }
        h1, h2 {
            text-align: center;
            color: #333;
        }
        .legend {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .legend-item {
            display: inline-block;
            margin: 5px 10px;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .flutter { background-color: #e0f7fa; color: #00796b; }
        .go { background-color: #e8eaf6; color: #303f9f; }
        .nodejs { background-color: #fbe9e7; color: #d84315; }
        .db { background-color: #f9fbe7; color: #f9a825; }
        .external { background-color: #f1f8e9; color: #558b2f; }
        .admin { background-color: #fce4ec; color: #c2185b; }
    </style>
</head>
<body>
    <h1>命理AI聊天项目分层架构图</h1>
    
    <div class="legend">
        <strong>图例：</strong>
        <span class="legend-item flutter">Flutter客户端</span>
        <span class="legend-item go">Go代理服务</span>
        <span class="legend-item nodejs">Node.js云函数</span>
        <span class="legend-item admin">管理后台</span>
        <span class="legend-item db">数据库</span>
        <span class="legend-item external">外部服务</span>
    </div>

    <div class="diagram-container">
        <h2>1. 整体架构概览</h2>
        <div class="mermaid">
graph TD
    subgraph "客户端层"
        A[Flutter客户端应用<br/>numerology_ai_chat_new]
        B[管理后台应用<br/>numerology_ai_chat_new_admin]
    end
    
    subgraph "服务层"
        C[Go代理服务<br/>go_proxy]
        D[云函数服务<br/>cloudfunctions]
    end
    
    subgraph "数据层"
        E[腾讯云开发数据库<br/>MongoDB]
    end
    
    subgraph "外部服务"
        F[大语言模型API<br/>DeepSeek/Gemini]
        G[富友支付API]
        H[第三方服务<br/>地址/农历]
    end
    
    A --> C
    A --> D
    B --> D
    C --> D
    C --> F
    D --> E
    D --> G
    D --> H
    
    classDef flutter fill:#e0f7fa,stroke:#00796b,stroke-width:2px;
    classDef go fill:#e8eaf6,stroke:#303f9f,stroke-width:2px;
    classDef nodejs fill:#fbe9e7,stroke:#d84315,stroke-width:2px;
    classDef db fill:#f9fbe7,stroke:#f9a825,stroke-width:2px;
    classDef external fill:#f1f8e9,stroke:#558b2f,stroke-width:2px;
    classDef admin fill:#fce4ec,stroke:#c2185b,stroke-width:2px;
    
    class A flutter;
    class B admin;
    class C go;
    class D nodejs;
    class E db;
    class F,G,H external;
        </div>
    </div>

    <div class="diagram-container">
        <h2>2. Flutter客户端详细结构</h2>
        <div class="mermaid">
graph TD
    subgraph "页面层 (Screens)"
        S1[启动页面 SplashScreen]
        S2[登录页面 LoginScreen]
        S3[聊天页面 ChatScreen]
        S4[八字排盘 BaziScreen]
        S5[购买套餐 PurchaseScreen]
        S6[个人中心 ProfileScreen]
    end
    
    subgraph "状态管理 (Providers)"
        P1[认证状态 AuthProvider]
        P2[聊天状态 ChatProvider]
        P3[智能体状态 AgentProvider]
        P4[模型状态 ModelProvider]
    end
    
    subgraph "服务层 (Services)"
        SV1[认证服务 AuthService]
        SV2[云函数服务 CloudFunctionService]
        SV3[Go代理服务 GoProxyService]
        SV4[存储服务 StorageService]
        SV5[八字服务 BaziService]
    end
    
    S1 --> P1
    S2 --> P1
    S3 --> P2
    S4 --> SV5
    S5 --> SV2
    S6 --> SV2
    
    P1 --> SV1
    P2 --> SV3
    P3 --> SV2
    P4 --> SV2
    
    classDef flutter fill:#e0f7fa,stroke:#00796b,stroke-width:2px;
    class S1,S2,S3,S4,S5,S6,P1,P2,P3,P4,SV1,SV2,SV3,SV4,SV5 flutter;
        </div>
    </div>

    <script src="https://unpkg.com/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
