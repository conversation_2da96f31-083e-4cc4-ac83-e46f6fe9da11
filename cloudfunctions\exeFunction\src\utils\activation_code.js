const CryptoJS = require('crypto-js')
const { systemConfigCollection, activationCodeCollection } = require('./db')
const { createBusinessError, ERROR_CODES } = require('../middleware/error_handler')
const logger = require('./logger')

/**
 * 获取加密密钥
 * @returns {Promise<string>} 加密密钥
 */
async function getEncryptionKey() {
  try {
    const config = await systemConfigCollection.getConfig()
    if (!config || !config.encryption_key) {
      throw new Error('系统配置中未找到加密密钥')
    }
    return config.encryption_key
  } catch (error) {
    logger.error('获取加密密钥失败', { error: error.message })
    throw error
  }
}

/**
 * 验证并解析激活码
 * @param {string} activationCode 激活码
 * @returns {Promise<object>} 解析后的激活码数据
 */
async function validateActivationCode(activationCode) {
  try {
    if (!activationCode || typeof activationCode !== 'string') {
      throw createBusinessError(
        ERROR_CODES.ACTIVATION_CODE_INVALID,
        '激活码格式无效',
        400
      )
    }

    const encryptionKey = await getEncryptionKey()
    
    // AES解密
    const decryptedBytes = CryptoJS.AES.decrypt(activationCode, encryptionKey)
    const decryptedString = decryptedBytes.toString(CryptoJS.enc.Utf8)
    
    if (!decryptedString) {
      throw createBusinessError(
        ERROR_CODES.ACTIVATION_CODE_INVALID,
        '激活码格式无效',
        400
      )
    }
    
    // 解析JSON
    let codeData
    try {
      codeData = JSON.parse(decryptedString)
    } catch (parseError) {
      throw createBusinessError(
        ERROR_CODES.ACTIVATION_CODE_INVALID,
        '激活码格式无效',
        400
      )
    }
    
    // 验证必要字段
    if (!codeData.timestamp || !codeData.quota || !codeData.creator || !codeData.version) {
      throw createBusinessError(
        ERROR_CODES.ACTIVATION_CODE_INVALID,
        '激活码数据不完整',
        400
      )
    }
    
    // 验证版本
    if (codeData.version !== '1.0') {
      throw createBusinessError(
        ERROR_CODES.ACTIVATION_CODE_INVALID,
        '激活码版本不支持',
        400
      )
    }
    
    // 验证时间戳（可选：检查是否过期）
    const now = Date.now()
    const codeAge = now - codeData.timestamp
    const maxAge = 365 * 24 * 60 * 60 * 1000 // 1年有效期
    
    if (codeAge > maxAge) {
      throw createBusinessError(
        ERROR_CODES.ACTIVATION_CODE_INVALID,
        '激活码已过期',
        400
      )
    }
    
    // 检查激活码是否已被使用
    const isUsed = await activationCodeCollection.isCodeUsed(activationCode)
    if (isUsed) {
      throw createBusinessError(
        ERROR_CODES.ACTIVATION_CODE_USED,
        '激活码已被使用',
        400
      )
    }
    
    logger.info('激活码验证成功', {
      creator: codeData.creator,
      quota: codeData.quota,
      timestamp: codeData.timestamp,
      age: Math.floor(codeAge / (24 * 60 * 60 * 1000)) + '天'
    })
    
    return {
      quota: codeData.quota,
      creator: codeData.creator,
      timestamp: codeData.timestamp,
      version: codeData.version
    }
    
  } catch (error) {
    if (error.code) {
      // 已经是业务错误，直接抛出
      throw error
    }
    
    logger.error('激活码验证失败', {
      error: error.message,
      code: activationCode ? activationCode.substring(0, 20) + '...' : 'null'
    })
    
    throw createBusinessError(
      ERROR_CODES.ACTIVATION_CODE_INVALID,
      '激活码验证失败',
      400
    )
  }
}

/**
 * 核销激活码
 * @param {string} activationCode 激活码
 * @param {string} userId 使用者用户ID
 * @param {string} username 使用者用户名
 * @returns {Promise<object>} 核销结果
 */
async function redeemActivationCode(activationCode, userId, username) {
  try {
    // 验证激活码
    const codeData = await validateActivationCode(activationCode)
    
    // 记录核销
    await activationCodeCollection.recordUsage(
      activationCode,
      codeData.quota,
      codeData.creator,
      username
    )
    
    logger.info('激活码核销成功', {
      activationCode: activationCode.substring(0, 20) + '...',
      quota: codeData.quota,
      creator: codeData.creator,
      usedBy: username,
      userId
    })
    
    return {
      quota: codeData.quota,
      creator: codeData.creator
    }
    
  } catch (error) {
    logger.error('激活码核销失败', {
      error: error.message,
      userId,
      username
    })
    throw error
  }
}

module.exports = {
  validateActivationCode,
  redeemActivationCode,
  getEncryptionKey
}
