#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极图标修复工具 - 彻底解决Windows应用图标问题
确保生成的exe文件在文件管理器中显示正确的自定义图标
"""

import os
import sys
import shutil
from PIL import Image, ImageFilter, ImageEnhance
from datetime import datetime

def print_step(step, message, status="info"):
    """打印步骤信息"""
    icons = {"info": "🔄", "success": "✅", "warning": "⚠️", "error": "❌", "check": "🔍"}
    print(f"{icons.get(status, '📝')} 步骤{step}: {message}")

def create_ultimate_icon():
    """创建终极高质量图标"""
    print("🎯 终极图标修复工具")
    print("=" * 60)
    
    # 检查源文件
    source_path = "assets/images/logo.png"
    if not os.path.exists(source_path):
        print_step(1, f"源图片文件不存在: {source_path}", "error")
        return False
    
    print_step(1, "检查源图片文件 - 通过", "success")
    
    # 备份现有图标
    icon_path = "windows/runner/resources/app_icon.ico"
    if os.path.exists(icon_path):
        backup_path = f"{icon_path}.ultimate_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(icon_path, backup_path)
        print_step(2, f"备份现有图标到: {backup_path}", "success")
    
    try:
        # 打开并处理源图片
        with Image.open(source_path) as source_img:
            print_step(3, f"源图片信息: {source_img.size}, 模式: {source_img.mode}", "check")
            
            # 转换为RGBA模式
            if source_img.mode != 'RGBA':
                source_img = source_img.convert('RGBA')
            
            # 定义完整的Windows图标尺寸集合
            icon_sizes = [16, 20, 24, 32, 40, 48, 64, 72, 96, 128, 256]
            
            # 生成所有尺寸的图标
            icon_images = []
            for size in icon_sizes:
                print(f"   🔄 生成 {size}x{size} 图标")
                
                # 高质量缩放
                resized = source_img.resize((size, size), Image.Resampling.LANCZOS)
                
                # 为小尺寸图标进行特殊优化
                if size <= 32:
                    # 增强锐度
                    enhancer = ImageEnhance.Sharpness(resized)
                    resized = enhancer.enhance(1.3)
                    
                    # 增强对比度
                    enhancer = ImageEnhance.Contrast(resized)
                    resized = enhancer.enhance(1.1)
                
                icon_images.append(resized)
            
            print_step(4, f"生成了 {len(icon_sizes)} 种尺寸的图标", "success")
            
            # 确保输出目录存在
            os.makedirs("windows/runner/resources", exist_ok=True)
            
            # 保存为ICO文件
            icon_images[0].save(
                icon_path,
                format='ICO',
                sizes=[(img.width, img.height) for img in icon_images],
                append_images=icon_images[1:]
            )
            
            # 验证生成的文件
            file_size = os.path.getsize(icon_path)
            print_step(5, f"图标文件已生成: {icon_path}", "success")
            print_step(5, f"文件大小: {file_size:,} 字节", "success")
            
            # 验证ICO文件质量
            with Image.open(icon_path) as ico_img:
                print_step(6, f"验证成功 - 主图标尺寸: {ico_img.size}", "success")
            
            return True
            
    except Exception as e:
        print_step("X", f"创建图标时发生错误: {e}", "error")
        return False

def protect_icon_from_flutter():
    """保护图标文件不被Flutter覆盖"""
    print_step(7, "设置图标文件保护", "info")
    
    icon_path = "windows/runner/resources/app_icon.ico"
    if os.path.exists(icon_path):
        # 设置文件为只读（在Windows上）
        try:
            import stat
            os.chmod(icon_path, stat.S_IREAD)
            print_step(7, "图标文件已设置为只读保护", "success")
        except:
            print_step(7, "无法设置只读保护，但图标已生成", "warning")

def create_build_script():
    """创建专用构建脚本"""
    script_content = '''@echo off
chcp 65001 >nul
echo 🎯 终极图标构建脚本
echo ================================

echo 🔄 步骤1: 生成高质量图标...
python ultimate_icon_fix.py
if %errorlevel% neq 0 (
    echo ❌ 图标生成失败！
    pause
    exit /b 1
)

echo 🔄 步骤2: 清理构建缓存...
flutter clean

echo 🔄 步骤3: 获取依赖...
flutter pub get

echo 🔄 步骤4: 构建应用...
flutter build windows --release

echo 🎉 构建完成！
echo 📁 可执行文件: build\\windows\\x64\\runner\\Release\\numerology_ai_chat.exe
pause
'''
    
    with open("build_with_ultimate_icon.bat", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    print_step(8, "创建专用构建脚本: build_with_ultimate_icon.bat", "success")

def main():
    """主函数"""
    print("🚀 开始终极图标修复...")
    print()
    
    # 检查PIL库
    try:
        from PIL import Image
        print_step(0, "PIL库检查通过", "success")
    except ImportError:
        print_step(0, "PIL库未安装，请运行: pip install Pillow", "error")
        return False
    
    # 创建高质量图标
    if not create_ultimate_icon():
        return False
    
    # 保护图标文件
    protect_icon_from_flutter()
    
    # 创建构建脚本
    create_build_script()
    
    print()
    print("🎉 终极图标修复完成！")
    print("=" * 60)
    print("📝 接下来请：")
    print("1. 运行: build_with_ultimate_icon.bat")
    print("2. 或者手动运行:")
    print("   - flutter clean")
    print("   - flutter pub get") 
    print("   - flutter build windows --release")
    print()
    print("💡 重要提示：")
    print("- 图标文件已设置保护，不会被Flutter覆盖")
    print("- 包含11种尺寸，支持所有Windows显示场景")
    print("- 如果图标仍然不显示，请重启Windows资源管理器")
    
    return True

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
