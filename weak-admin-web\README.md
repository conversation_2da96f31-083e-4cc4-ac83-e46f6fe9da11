# 管理员Web管理系统

## 项目简介

这是一个基于Vue 3 + Element Plus的管理员Web管理系统，用于管理激活码和用户算力。

## 本地运行

### 环境要求
- Node.js 16+ 
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

项目将在 http://localhost:3000 启动

### 构建生产版本
```bash
npm run build
```

## 测试账号信息

### 管理员测试账号
- **用户名**: weakadmin001
- **密码**: 12345678
- **权限**: 管理员权限，可以生成激活码和管理用户算力

### 普通用户测试账号
- **testuser002**: 已注册用户，当前算力700
- **testuser003**: 已注册用户，当前算力2500（通过激活码获得2000 + 管理员增加500）

### 可用的激活码（未使用）
以下激活码可用于测试注册功能：
1. `U2FsdGVkX18kU3XEGplFPVGkfU2MmL8u4u9x7U073fT33wSJ7gEHpm6Q9FTVGX6+qzV5RUfBcKcTObR0n9poDOe7mw0e4+Cg3M+yaaobx+6JQ18yxPfyHdN3g9K/fBci6geYrjUeGw/IVVKEWrBA7NUEKxuWcqW4WCaNpITOBv4=` (2000算力)
2. `U2FsdGVkX19w+X7UmymUEVn7QeS63pzZwqbzVI5nLbqdMfaMeFuHc3bg2nNstBMwOTk3zEqrLldw1I2Y7kvBJQTRC2VbU8U/yvXPwN3JGvwdDz0iBBgDwZqtaGq8rdrNQvIVp5q0vBtzzUm3DYWmbSklYgpRpGC8D9hZNvganfs=` (2000算力)

## 功能说明

### 主要功能
1. **管理员登录**: 使用上述测试账号登录系统
2. **激活码管理**: 生成新的激活码，查看核销历史
3. **用户算力管理**: 查询用户信息，增加或减少用户算力
4. **操作历史**: 查看算力操作记录和激活码核销记录

### 测试流程建议
1. 使用管理员账号登录
2. 在激活码管理页面生成新的激活码
3. 在用户算力管理页面查询现有用户信息
4. 修改用户算力（增加或减少）
5. 在操作历史页面查看所有操作记录

## 云函数端点
- **exeWeakAdmin**: https://cloud1-8g3yh69faf07fd82-**********.ap-shanghai.app.tcloudbase.com/exeWeakAdmin
- **exeFunction**: https://cloud1-8g3yh69faf07fd82-**********.ap-shanghai.app.tcloudbase.com/exeFunction

## 技术栈
- Vue 3
- Element Plus
- Vue Router
- Pinia (状态管理)
- Axios (HTTP请求)
- Vite (构建工具)
