import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:window_manager/window_manager.dart';

import '../models/version_model.dart';
import '../providers/version_provider.dart';
import '../services/version_service.dart';
import '../widgets/common/primary_button.dart' as common;

/// 应用退出工具类
class AppExitHelper {
  /// 优雅地退出应用
  static Future<void> exitApp() async {
    try {
      // 优先使用 window_manager 优雅关闭
      if (await windowManager.isPreventClose()) {
        await windowManager.setPreventClose(false);
      }
      await windowManager.close();
      print('应用已通过 windowManager 关闭');
    } catch (e) {
      print('windowManager 关闭失败，使用 exit(0): $e');
      // 备选方案：强制退出
      exit(0);
    }
  }
}

/// 版本更新对话框
class VersionUpdateDialog extends ConsumerStatefulWidget {
  final UpdateInfo updateInfo;
  final bool isForced;

  const VersionUpdateDialog({
    super.key,
    required this.updateInfo,
    this.isForced = false,
  });

  @override
  ConsumerState<VersionUpdateDialog> createState() => _VersionUpdateDialogState();
}

class _VersionUpdateDialogState extends ConsumerState<VersionUpdateDialog> {

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return PopScope(
      canPop: !widget.isForced, // 强制更新时不允许返回
      child: AlertDialog(
      title: Row(
        children: [
          Icon(
            widget.isForced ? Icons.warning : Icons.system_update,
            color: widget.isForced ? theme.colorScheme.error : theme.colorScheme.primary,
            size: 24,
          ),
          const SizedBox(width: 8),
          Text(
            widget.isForced ? '强制更新' : '发现新版本',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: widget.isForced ? theme.colorScheme.error : theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
      content: SizedBox(
        width: 500,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 版本信息
            _buildVersionInfo(context, theme),

            const SizedBox(height: 16),

            // 更新说明
            if (widget.updateInfo.description.isNotEmpty) ...[
              Text(
                '更新说明',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                widget.updateInfo.description,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.8),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // 发布说明
            if (widget.updateInfo.releaseNotes.isNotEmpty) ...[
              Text(
                '版本详情',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: theme.colorScheme.outline.withOpacity(0.3),
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Markdown(
                  data: widget.updateInfo.releaseNotes,
                  styleSheet: MarkdownStyleSheet(
                    p: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.8),
                    ),
                    h1: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    h2: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    h3: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    listBullet: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
      actions: _buildActions(context),
      ),
    );
  }

  /// 构建版本信息
  Widget _buildVersionInfo(BuildContext context, ThemeData theme) {
    final versionService = VersionService();
    final currentVersion = versionService.getCurrentVersion();
    final updateType = versionService.getUpdateType(currentVersion, widget.updateInfo.version);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '当前版本：',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                currentVersion,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.8),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Text(
                '最新版本：',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '${widget.updateInfo.version} (${widget.updateInfo.versionName})',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(width: 8),
              _buildUpdateTypeBadge(context, updateType),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建更新类型标签
  Widget _buildUpdateTypeBadge(BuildContext context, VersionUpdateType updateType) {
    final theme = Theme.of(context);
    
    Color badgeColor;
    switch (updateType) {
      case VersionUpdateType.major:
        badgeColor = theme.colorScheme.error;
        break;
      case VersionUpdateType.minor:
        badgeColor = theme.colorScheme.primary;
        break;
      case VersionUpdateType.patch:
        badgeColor = theme.colorScheme.secondary;
        break;
      case VersionUpdateType.none:
        badgeColor = theme.colorScheme.outline;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: badgeColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: badgeColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Text(
        updateType.description,
        style: theme.textTheme.labelSmall?.copyWith(
          color: badgeColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 构建操作按钮
  List<Widget> _buildActions(BuildContext context) {
    if (widget.isForced) {
      // 强制更新只有一个更新按钮
      return [
        common.PrimaryButton(
          text: '立即更新',
          onPressed: () => _handleUpdate(context),
          icon: Icons.download,
        ),
      ];
    } else {
      // 可选更新有取消和更新按钮
      return [
        TextButton(
          onPressed: () => _handleCancel(context),
          child: const Text('稍后提醒'),
        ),
        const SizedBox(width: 8),
        common.PrimaryButton(
          text: '立即更新',
          onPressed: () => _handleUpdate(context),
          icon: Icons.download,
        ),
      ];
    }
  }

  /// 处理取消操作
  void _handleCancel(BuildContext context) {
    // 标记已显示更新对话框，避免重复显示
    ref.read(versionProvider.notifier).markUpdateDialogShown();
    Navigator.of(context).pop();
  }

  /// 处理更新操作
  void _handleUpdate(BuildContext context) async {
    try {
      // 打开下载链接
      final uri = Uri.parse(widget.updateInfo.downloadUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);

        // 标记已显示更新对话框
        ref.read(versionProvider.notifier).markUpdateDialogShown();

        if (context.mounted) {
          // 无论是强制更新还是可选更新，用户选择更新后都关闭应用
          print('用户选择更新：下载链接已打开，准备关闭应用');

          // 给用户一点时间看到下载开始，然后关闭应用
          await Future.delayed(const Duration(milliseconds: 500));

          // 关闭应用
          await AppExitHelper.exitApp();
        }
      } else {
        throw Exception('无法打开下载链接');
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('打开下载链接失败: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}

/// 版本更新对话框工具类
class VersionUpdateDialogHelper {
  /// 显示版本更新对话框
  static Future<void> show(
    BuildContext context,
    UpdateInfo updateInfo, {
    bool isForced = false,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: !isForced,
      builder: (context) => VersionUpdateDialog(
        updateInfo: updateInfo,
        isForced: isForced,
      ),
    );
  }
}
