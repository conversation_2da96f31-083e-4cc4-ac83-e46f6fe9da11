#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证图标更新结果
"""

import os
import shutil

def main():
    print("🔍 验证图标更新结果...")
    
    # 检查源文件
    logo_path = "assets/images/logo.png"
    if os.path.exists(logo_path):
        print(f"✅ 源图标文件存在: {logo_path}")
    else:
        print(f"❌ 源图标文件不存在: {logo_path}")
        return
    
    # 检查转换后的ico文件
    ico_path = "windows/runner/resources/app_icon.ico"
    if os.path.exists(ico_path):
        print(f"✅ Windows图标文件已更新: {ico_path}")
        
        # 获取文件大小
        size = os.path.getsize(ico_path)
        print(f"📏 图标文件大小: {size} 字节")
    else:
        print(f"❌ Windows图标文件不存在: {ico_path}")
        return
    
    # 检查备份文件
    backup_path = ico_path + ".backup"
    if os.path.exists(backup_path):
        print(f"📦 原图标已备份到: {backup_path}")
    
    # 检查构建的exe文件
    debug_exe = "build/windows/x64/runner/Debug/numerology_ai_chat.exe"
    release_exe = "build/windows/x64/runner/Release/numerology_ai_chat.exe"
    
    if os.path.exists(debug_exe):
        print(f"✅ Debug版本exe文件存在: {debug_exe}")
        size = os.path.getsize(debug_exe)
        print(f"📏 Debug exe文件大小: {size} 字节")
    
    if os.path.exists(release_exe):
        print(f"✅ Release版本exe文件存在: {release_exe}")
        size = os.path.getsize(release_exe)
        print(f"📏 Release exe文件大小: {size} 字节")
    
    print("\n🎉 图标更新完成！")
    print("\n📝 使用说明:")
    print("1. 新的应用图标已经应用到Windows程序中")
    print("2. 桌面图标和任务栏图标都会显示新的logo")
    print("3. 如果需要发布版本，请运行: flutter build windows --release")
    print("4. 可执行文件位置:")
    print(f"   - Debug版本: {debug_exe}")
    if os.path.exists(release_exe):
        print(f"   - Release版本: {release_exe}")
    
    # 询问是否清理临时文件
    print("\n🧹 是否清理临时文件？")
    print("- convert_logo_to_ico.py (图标转换脚本)")
    print("- verify_icon_update.py (本验证脚本)")
    
    response = input("输入 'y' 清理临时文件，其他键跳过: ").strip().lower()
    if response == 'y':
        try:
            if os.path.exists("convert_logo_to_ico.py"):
                os.remove("convert_logo_to_ico.py")
                print("✅ 已删除: convert_logo_to_ico.py")
            
            if os.path.exists("verify_icon_update.py"):
                print("✅ 即将删除: verify_icon_update.py")
                # 注意：这个脚本会删除自己，所以这是最后的输出
        except Exception as e:
            print(f"⚠️ 清理文件时出错: {e}")
    else:
        print("⏭️ 跳过清理，临时文件保留")

if __name__ == "__main__":
    main()
