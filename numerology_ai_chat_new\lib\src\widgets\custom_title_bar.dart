import 'package:flutter/material.dart';
import 'package:window_manager/window_manager.dart';

/// 自定义标题栏组件，集成窗口控制按钮
class CustomTitleBar extends StatelessWidget implements PreferredSizeWidget {
  final Widget? title;
  final List<Widget>? actions;
  final bool automaticallyImplyLeading;
  final Color? backgroundColor;
  final double elevation;

  const CustomTitleBar({
    super.key,
    this.title,
    this.actions,
    this.automaticallyImplyLeading = true,
    this.backgroundColor,
    this.elevation = 0,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      height: preferredSize.height,
      decoration: BoxDecoration(
        color: backgroundColor ?? theme.appBarTheme.backgroundColor ?? theme.colorScheme.surface,
        boxShadow: elevation > 0 ? [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            offset: const Offset(0, 1),
            blurRadius: elevation,
          ),
        ] : null,
      ),
      child: DragT<PERSON><PERSON><PERSON><PERSON><PERSON>(
        child: GestureDetector(
          onDoubleTap: () async {
            // 双击标题栏切换最大化状态
            if (await windowManager.isMaximized()) {
              windowManager.unmaximize();
            } else {
              windowManager.maximize();
            }
          },
          child: Row(
            children: [
              // 标题和操作区域
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    children: [
                      if (title != null) ...[
                        DefaultTextStyle(
                          style: theme.appBarTheme.titleTextStyle ??
                                 theme.textTheme.titleLarge!.copyWith(
                                   color: theme.appBarTheme.foregroundColor ??
                                          theme.colorScheme.onSurface,
                                 ),
                          child: title!,
                        ),
                      ],
                      const Spacer(),
                      if (actions != null) ...actions!,
                    ],
                  ),
                ),
              ),

              // 窗口控制按钮（不可拖拽）
              _buildWindowControls(context, isDark),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWindowControls(BuildContext context, bool isDark) {
    final theme = Theme.of(context);

    // 使用GestureDetector阻止拖拽事件传播到父级DragToMoveArea
    return GestureDetector(
      onPanStart: (_) {}, // 阻止拖拽开始
      onPanUpdate: (_) {}, // 阻止拖拽更新
      onPanEnd: (_) {}, // 阻止拖拽结束
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 最小化按钮
          _WindowControlButton(
            icon: Icons.minimize,
            onPressed: () => windowManager.minimize(),
            tooltip: '最小化',
            isDark: isDark,
          ),

          // 最大化/还原按钮
          FutureBuilder<bool>(
            future: windowManager.isMaximized(),
            builder: (context, snapshot) {
              final isMaximized = snapshot.data ?? false;
              return _WindowControlButton(
                icon: isMaximized ? Icons.fullscreen_exit : Icons.crop_square,
                onPressed: () async {
                  if (await windowManager.isMaximized()) {
                    windowManager.unmaximize();
                  } else {
                    windowManager.maximize();
                  }
                },
                tooltip: isMaximized ? '还原' : '最大化',
                isDark: isDark,
              );
            },
          ),

          // 关闭按钮
          _WindowControlButton(
            icon: Icons.close,
            onPressed: () => windowManager.close(),
            tooltip: '关闭',
            isDark: isDark,
            isCloseButton: true,
          ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// 窗口控制按钮
class _WindowControlButton extends StatefulWidget {
  final IconData icon;
  final VoidCallback onPressed;
  final String tooltip;
  final bool isDark;
  final bool isCloseButton;

  const _WindowControlButton({
    required this.icon,
    required this.onPressed,
    required this.tooltip,
    required this.isDark,
    this.isCloseButton = false,
  });

  @override
  State<_WindowControlButton> createState() => _WindowControlButtonState();
}

class _WindowControlButtonState extends State<_WindowControlButton> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    Color getBackgroundColor() {
      if (!_isHovered) return Colors.transparent;
      
      if (widget.isCloseButton) {
        return Colors.red.shade600;
      }
      
      return widget.isDark 
          ? Colors.white.withOpacity(0.1)
          : Colors.black.withOpacity(0.05);
    }
    
    Color getIconColor() {
      if (widget.isCloseButton && _isHovered) {
        return Colors.white;
      }
      
      return theme.appBarTheme.foregroundColor ?? 
             theme.colorScheme.onSurface;
    }

    return Tooltip(
      message: widget.tooltip,
      child: MouseRegion(
        onEnter: (_) => setState(() => _isHovered = true),
        onExit: (_) => setState(() => _isHovered = false),
        child: GestureDetector(
          onTap: widget.onPressed,
          child: Container(
            width: 46,
            height: kToolbarHeight,
            decoration: BoxDecoration(
              color: getBackgroundColor(),
            ),
            child: Icon(
              widget.icon,
              size: 16,
              color: getIconColor(),
            ),
          ),
        ),
      ),
    );
  }
}
