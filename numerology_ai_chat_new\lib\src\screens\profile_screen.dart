import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';

import '../providers/auth_provider.dart';
import '../providers/purchase_history_provider.dart';
import '../providers/usage_history_provider.dart';
import '../models/purchase_history_model.dart';
import '../models/usage_history_model.dart';

/// 个人中心页面
class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen> {
  @override
  void initState() {
    super.initState();
    // 延迟到下一帧执行初始化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 触发自动加载购买历史
      ref.read(autoLoadPurchaseHistoryProvider);
      // 加载消耗历史
      ref.read(usageHistoryProvider.notifier).loadUsageHistory();
      // 刷新用户信息
      _refreshUserInfo();
    });
  }

  /// 刷新用户信息
  Future<void> _refreshUserInfo() async {
    await ref.read(authProvider.notifier).refreshUser();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Card(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(
                    Icons.person_outline,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const Gap(8),
                  Text(
                    '个人中心',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                ],
              ),
            ),
            const Divider(height: 1),
            Expanded(
              child: _buildProfileContent(context, ref),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileContent(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authProvider);
    final user = authState.user;

    if (user == null) {
      return const Center(
        child: Text('用户信息加载失败'),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        // 同时刷新用户信息、购买历史和消耗历史
        await Future.wait([
          _refreshUserInfo(),
          ref.read(purchaseHistoryProvider.notifier).refresh(),
          ref.read(usageHistoryProvider.notifier).refresh(),
        ]);
      },
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // 基本信息
            _buildBasicInfo(context, user),
            const Gap(24),

            // 剩余次数信息
            _buildQuotaInfo(context, user),
            const Gap(24),

            // 历史记录 - 使用Expanded让它占据剩余空间
            Expanded(
              child: _buildHistoryTabs(context, ref),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建基本信息区域
  Widget _buildBasicInfo(BuildContext context, dynamic user) {
    return _buildInfoSection(
      context,
      '基本信息',
      [
        _buildInfoItem(context, '账号', user.username ?? '未知'),
        _buildInfoItem(context, '昵称', user.username ?? '未知'),
        _buildInfoItem(context, '邮箱', user.email ?? '未设置'),
        _buildInfoItem(context, '注册时间', _formatDate(user.createdAt)),
      ],
    );
  }

  /// 构建剩余算力信息区域
  Widget _buildQuotaInfo(BuildContext context, dynamic user) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '剩余算力',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const Spacer(),
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () async {
                await ref.read(authProvider.notifier).forceRefreshUser();
              },
              tooltip: '刷新算力',
              iconSize: 20,
            ),
          ],
        ),
        const Gap(8),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.account_balance_wallet_outlined,
                  size: 32,
                  color: theme.colorScheme.primary,
                ),
                const Gap(16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '可用算力',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                      const Gap(4),
                      Text(
                        '${user.availableCount ?? 0} 算力',
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ),
                if ((user.availableCount ?? 0) < 10)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.error,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      '算力不足',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onError,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建购买历史区域
  Widget _buildPurchaseHistory(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final purchaseHistoryState = ref.watch(purchaseHistoryProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Row(
          children: [
            Text(
              '购买历史',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const Spacer(),
            if (purchaseHistoryState.isLoading)
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            else
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: () {
                  ref.read(purchaseHistoryProvider.notifier).refresh();
                },
                tooltip: '刷新',
              ),
          ],
        ),
        const Gap(8),
        Expanded(
          child: Card(
            child: purchaseHistoryState.orders.isEmpty
                ? _buildEmptyPurchaseHistory(context)
                : _buildPurchaseHistoryList(context, purchaseHistoryState.orders),
          ),
        ),
        if (purchaseHistoryState.error != null)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              purchaseHistoryState.error!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.error,
              ),
            ),
          ),
      ],
    );
  }

  /// 构建空购买历史
  Widget _buildEmptyPurchaseHistory(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(minHeight: 200),
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 64,
            color: theme.colorScheme.onSurface.withOpacity(0.3),
          ),
          const Gap(24),
          Text(
            '暂无购买记录',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
              fontWeight: FontWeight.w500,
            ),
          ),
          const Gap(12),
          Text(
            '您还没有购买过任何套餐',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 构建购买历史列表
  Widget _buildPurchaseHistoryList(BuildContext context, List<PurchaseHistoryModel> orders) {
    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: orders.length,
      separatorBuilder: (context, index) => const Gap(12),
      itemBuilder: (context, index) {
        final order = orders[index];
        return _buildPurchaseHistoryItem(context, order);
      },
    );
  }

  /// 构建购买历史项
  Widget _buildPurchaseHistoryItem(BuildContext context, PurchaseHistoryModel order) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: order.isAdminRecharge
              ? theme.colorScheme.primary.withOpacity(0.3)
              : theme.colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          // 状态图标
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: order.isAdminRecharge
                  ? theme.colorScheme.primary
                  : _getStatusColor(theme, order.status),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              order.isAdminRecharge
                  ? Icons.admin_panel_settings
                  : Icons.shopping_bag_outlined,
              color: Colors.white,
              size: 24,
            ),
          ),
          const Gap(16),

          // 主要信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  order.packageName,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Gap(4),
                Text(
                  order.isAdminRecharge
                      ? '${order.packageQuota}算力 • 免费充值'
                      : '${order.packageQuota}算力 • ${order.formattedAmount}',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
                const Gap(2),
                Text(
                  order.formattedCreateTime,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.5),
                  ),
                ),
              ],
            ),
          ),

          // 状态标签
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: order.isAdminRecharge
                  ? theme.colorScheme.primary.withOpacity(0.1)
                  : _getStatusColor(theme, order.status).withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: order.isAdminRecharge
                    ? theme.colorScheme.primary.withOpacity(0.3)
                    : _getStatusColor(theme, order.status).withOpacity(0.3),
              ),
            ),
            child: Text(
              order.isAdminRecharge ? '管理员充值' : order.statusDisplayText,
              style: theme.textTheme.bodySmall?.copyWith(
                color: order.isAdminRecharge
                    ? theme.colorScheme.primary
                    : _getStatusColor(theme, order.status),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 获取状态颜色
  Color _getStatusColor(ThemeData theme, String status) {
    switch (status) {
      case 'PENDING':
        return Colors.orange;
      case 'PAID':
      case 'COMPLETED':
        return Colors.green;
      case 'CANCELLED':
      case 'REFUNDED':
        return theme.colorScheme.error;
      default:
        return theme.colorScheme.onSurface.withOpacity(0.5);
    }
  }

  Widget _buildInfoSection(BuildContext context, String title, List<Widget> children) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
        ),
        const Gap(8),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: children,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoItem(BuildContext context, String label, String value) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }



  String _formatDate(dynamic dateInput) {
    if (dateInput == null) return '未知';

    try {
      DateTime date;

      // 处理不同类型的输入
      if (dateInput is DateTime) {
        date = dateInput;
      } else if (dateInput is String) {
        date = DateTime.parse(dateInput);
      } else {
        return '未知';
      }

      return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    } catch (e) {
      return '未知';
    }
  }

  /// 构建历史记录标签页
  Widget _buildHistoryTabs(BuildContext context, WidgetRef ref) {
    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          TabBar(
            tabs: const [
              Tab(text: '购买历史'),
              Tab(text: '消耗历史'),
            ],
            labelColor: Theme.of(context).colorScheme.primary,
            unselectedLabelColor: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            indicatorColor: Theme.of(context).colorScheme.primary,
          ),
          Expanded(
            child: TabBarView(
              children: [
                _buildPurchaseHistory(context, ref),
                _buildUsageHistory(context, ref),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建消耗历史区域
  Widget _buildUsageHistory(BuildContext context, WidgetRef ref) {
    final usageHistoryState = ref.watch(usageHistoryProvider);
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const Gap(8),
        Row(
          children: [
            Text(
              '消耗历史',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const Spacer(),
            if (usageHistoryState.isLoading)
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            else
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: () {
                  ref.read(usageHistoryProvider.notifier).refresh();
                },
                tooltip: '刷新',
              ),
          ],
        ),
        const Gap(8),
        Expanded(
          child: Card(
            child: usageHistoryState.isEmpty
                ? _buildEmptyUsageHistory(context)
                : _buildUsageHistoryList(context, ref, usageHistoryState),
          ),
        ),
      ],
    );
  }

  /// 构建空的消耗历史状态
  Widget _buildEmptyUsageHistory(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(minHeight: 200),
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: theme.colorScheme.onSurface.withOpacity(0.3),
          ),
          const Gap(24),
          Text(
            '暂无消耗记录',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
              fontWeight: FontWeight.w500,
            ),
          ),
          const Gap(12),
          Text(
            '开始对话后将显示算力消耗记录',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 构建消耗历史列表
  Widget _buildUsageHistoryList(BuildContext context, WidgetRef ref, UsageHistoryState state) {
    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification scrollInfo) {
        // 检测滚动到底部，加载更多
        if (scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent &&
            state.hasMore &&
            !state.isLoadingMore) {
          ref.read(usageHistoryProvider.notifier).loadMoreUsageHistory();
        }
        return false;
      },
      child: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: state.history.length + (state.isLoadingMore ? 1 : 0),
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          if (index >= state.history.length) {
            // 加载更多指示器
            return const Padding(
              padding: EdgeInsets.all(16),
              child: Center(
                child: CircularProgressIndicator(),
              ),
            );
          }

          final historyItem = state.history[index];
          return _buildUsageHistoryItem(context, historyItem);
        },
      ),
    );
  }

  /// 构建单个消耗历史项
  Widget _buildUsageHistoryItem(BuildContext context, UsageHistoryModel item) {
    final theme = Theme.of(context);

    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      leading: CircleAvatar(
        backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
        child: Icon(
          Icons.psychology,
          color: theme.colorScheme.primary,
          size: 20,
        ),
      ),
      title: Text(
        item.agentName,
        style: theme.textTheme.bodyLarge?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Gap(4),
          Text(
            '${item.modelName} • ${item.shortDescription}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          const Gap(2),
          Text(
            item.formattedConsumeTime,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.5),
            ),
          ),
        ],
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            '-${item.powerCost}',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.error,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Gap(2),
          Text(
            '余额: ${item.balanceAfter}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
        ],
      ),
      onTap: () => _showUsageHistoryDetail(context, item),
    );
  }

  /// 显示消耗历史详情
  void _showUsageHistoryDetail(BuildContext context, UsageHistoryModel item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('消耗详情'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailItem('智能体', item.agentName),
            _buildDetailItem('模型', item.modelName),
            _buildDetailItem('档次', item.tierName),
            _buildDetailItem('等级', item.modelLevelDisplayName),
            _buildDetailItem('消耗算力', '${item.powerCost}'),
            _buildDetailItem('消耗前余额', '${item.balanceBefore}'),
            _buildDetailItem('消耗后余额', '${item.balanceAfter}'),
            _buildDetailItem('消耗时间', item.formattedConsumeTimeDetailed),
            if (item.description.isNotEmpty)
              _buildDetailItem('描述', item.description),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  /// 构建详情项
  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }


}
