<template>
  <div class="activation-codes">
    <div class="page-header">
      <h2>激活码管理</h2>
      <p>生成和管理激活码</p>
    </div>

    <!-- 生成激活码区域 -->
    <el-card class="generate-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>生成激活码</span>
        </div>
      </template>
      
      <el-form
        ref="generateFormRef"
        :model="generateForm"
        :rules="generateRules"
        inline
        class="generate-form"
      >
        <el-form-item label="数量" prop="quantity">
          <el-input-number
            v-model="generateForm.quantity"
            :min="1"
            :max="100"
            placeholder="请输入数量"
          />
        </el-form-item>
        
        <el-form-item label="算力数量" prop="quotaAmount">
          <el-input-number
            v-model="generateForm.quotaAmount"
            :min="1"
            :max="100000"
            placeholder="请输入算力数量"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            :loading="generating"
            @click="handleGenerate"
          >
            {{ generating ? '生成中...' : '生成激活码' }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 生成结果展示 -->
    <el-card v-if="generatedCodes.length > 0" class="result-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>生成结果</span>
          <div class="header-actions">
            <el-button size="small" @click="copyAllCodes">复制全部</el-button>
            <el-button size="small" @click="exportCodes">导出TXT</el-button>
          </div>
        </div>
      </template>
      
      <div class="codes-list">
        <div v-for="(item, index) in generatedCodes" :key="index" class="code-item">
          <div class="code-content">
            <span class="code-text">{{ item.code }}</span>
            <span class="code-info">{{ item.quotaAmount }}算力</span>
          </div>
          <el-button size="small" @click="copyCode(item.code)">复制</el-button>
        </div>
      </div>
    </el-card>

    <!-- 历史记录 -->
    <el-card class="history-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>核销历史</span>
          <el-button size="small" @click="loadHistory">刷新</el-button>
        </div>
      </template>

      <el-table
        v-loading="historyLoading"
        :data="historyData"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="activationCode" label="激活码" width="300">
          <template #default="{ row }">
            <span class="code-display">{{ row.activationCode.substring(0, 20) }}...</span>
          </template>
        </el-table-column>
        <el-table-column prop="quotaAmount" label="算力数量" width="120" />
        <el-table-column prop="usedBy" label="使用者" width="150" />
        <el-table-column prop="usedAt" label="使用时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.usedAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag type="success">{{ row.status === 'used' ? '已使用' : '未使用' }}</el-tag>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="historyPagination.page"
          v-model:page-size="historyPagination.pageSize"
          :total="historyPagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadHistory"
          @current-change="loadHistory"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import weakAdminService from '@/services/weakAdminService'

// 表单引用
const generateFormRef = ref()

// 生成表单数据
const generateForm = reactive({
  quantity: 1,
  quotaAmount: 1000
})

// 表单验证规则
const generateRules = {
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '数量必须在1-100之间', trigger: 'blur' }
  ],
  quotaAmount: [
    { required: true, message: '请输入算力数量', trigger: 'blur' },
    { type: 'number', min: 1, max: 100000, message: '算力数量必须在1-100000之间', trigger: 'blur' }
  ]
}

// 状态
const generating = ref(false)
const generatedCodes = ref([])
const historyLoading = ref(false)
const historyData = ref([])
const historyPagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 生成激活码
const handleGenerate = async () => {
  if (!generateFormRef.value) return

  try {
    const valid = await generateFormRef.value.validate()
    if (!valid) return

    generating.value = true
    const response = await weakAdminService.generateActivationCodes(
      generateForm.quantity,
      generateForm.quotaAmount
    )

    if (response.success) {
      generatedCodes.value = response.data.activationCodes
      ElMessage.success(response.message)
      loadHistory() // 刷新历史记录
    } else {
      ElMessage.error(response.error?.message || '生成失败')
    }
  } catch (error) {
    console.error('Generate error:', error)
    ElMessage.error('生成激活码失败')
  } finally {
    generating.value = false
  }
}

// 复制单个激活码
const copyCode = async (code) => {
  try {
    await navigator.clipboard.writeText(code)
    ElMessage.success('复制成功')
  } catch (error) {
    console.error('Copy error:', error)
    ElMessage.error('复制失败')
  }
}

// 复制全部激活码
const copyAllCodes = async () => {
  try {
    const codes = generatedCodes.value.map(item => item.code).join('\n')
    await navigator.clipboard.writeText(codes)
    ElMessage.success('全部复制成功')
  } catch (error) {
    console.error('Copy all error:', error)
    ElMessage.error('复制失败')
  }
}

// 导出激活码
const exportCodes = () => {
  const codes = generatedCodes.value.map(item =>
    `${item.code} (${item.quotaAmount}算力)`
  ).join('\n')

  const blob = new Blob([codes], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `激活码_${new Date().toISOString().slice(0, 10)}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  ElMessage.success('导出成功')
}

// 加载历史记录
const loadHistory = async () => {
  try {
    historyLoading.value = true
    const response = await weakAdminService.getActivationHistory(
      historyPagination.page,
      historyPagination.pageSize
    )

    if (response.success) {
      historyData.value = response.data.data
      historyPagination.total = response.data.total
    } else {
      ElMessage.error(response.error?.message || '加载历史记录失败')
    }
  } catch (error) {
    console.error('Load history error:', error)
    ElMessage.error('加载历史记录失败')
  } finally {
    historyLoading.value = false
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// 组件挂载时加载历史记录
onMounted(() => {
  loadHistory()
})
</script>

<style scoped>
.activation-codes {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 5px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.generate-card,
.result-card,
.history-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.generate-form {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.codes-list {
  max-height: 400px;
  overflow-y: auto;
}

.code-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 10px;
  background: #fafafa;
}

.code-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.code-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #303133;
  word-break: break-all;
}

.code-info {
  font-size: 12px;
  color: #909399;
}

.code-display {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
