// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'model_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Model _$ModelFromJson(Map<String, dynamic> json) => Model(
      modelId: json['modelId'] as String,
      modelName: json['modelName'] as String,
      modelDisplayName: json['modelDisplayName'] as String,
      modelApiUrl: json['modelApiUrl'] as String,
      maxTokens: (json['maxTokens'] as num).toInt(),
      temperature: (json['temperature'] as num).toDouble(),
      description: json['description'] as String,
      isActive: json['isActive'] as bool,
      sortOrder: (json['sortOrder'] as num).toInt(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      hasApiKey: json['hasApiKey'] as bool,
    );

Map<String, dynamic> _$ModelToJson(Model instance) => <String, dynamic>{
      'modelId': instance.modelId,
      'modelName': instance.modelName,
      'modelDisplayName': instance.modelDisplayName,
      'modelApiUrl': instance.modelApiUrl,
      'maxTokens': instance.maxTokens,
      'temperature': instance.temperature,
      'description': instance.description,
      'isActive': instance.isActive,
      'sortOrder': instance.sortOrder,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'hasApiKey': instance.hasApiKey,
    };
