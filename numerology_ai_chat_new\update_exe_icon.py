#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接更新现有exe文件的图标
"""

import os
import subprocess
import shutil

def check_rcedit():
    """
    检查是否有rcedit工具，如果没有则下载
    """
    rcedit_path = "rcedit.exe"
    
    if os.path.exists(rcedit_path):
        print(f"✅ 找到rcedit工具: {rcedit_path}")
        return rcedit_path
    
    print("📥 rcedit工具不存在，需要下载...")
    print("💡 rcedit是一个用于编辑Windows可执行文件资源的工具")
    print("🔗 下载地址: https://github.com/electron/rcedit/releases")
    
    # 提供手动下载指导
    print("\n📝 手动下载步骤:")
    print("1. 访问 https://github.com/electron/rcedit/releases")
    print("2. 下载最新版本的 rcedit-x64.exe")
    print("3. 将文件重命名为 rcedit.exe")
    print("4. 放置在当前目录中")
    
    return None

def update_exe_icon_with_rcedit(exe_path, ico_path):
    """
    使用rcedit更新exe文件图标
    """
    rcedit_path = check_rcedit()
    if not rcedit_path:
        return False
    
    try:
        # 备份原exe文件
        backup_path = exe_path + ".backup"
        shutil.copy2(exe_path, backup_path)
        print(f"📦 已备份exe文件到: {backup_path}")
        
        # 使用rcedit更新图标
        cmd = [rcedit_path, exe_path, "--set-icon", ico_path]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ 成功更新exe图标: {exe_path}")
            return True
        else:
            print(f"❌ 更新exe图标失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 更新exe图标时出错: {e}")
        return False

def manual_icon_update_guide():
    """
    提供手动更新图标的指导
    """
    print("\n🔧 手动更新图标指导:")
    print("\n方法1: 使用Resource Hacker")
    print("1. 下载Resource Hacker: http://www.angusj.com/resourcehacker/")
    print("2. 打开Resource Hacker")
    print("3. 打开你的exe文件")
    print("4. 在左侧树形结构中找到 'Icon' 节点")
    print("5. 右键点击图标资源，选择 'Replace Icon...'")
    print("6. 选择新的ico文件")
    print("7. 保存文件")
    
    print("\n方法2: 重新编译（推荐）")
    print("1. 确保没有程序在运行exe文件")
    print("2. 关闭所有相关进程")
    print("3. 重新运行: flutter build windows --release")
    
    print("\n方法3: 使用Visual Studio")
    print("1. 如果安装了Visual Studio")
    print("2. 可以直接编辑.rc资源文件")
    print("3. 重新编译项目")

def check_exe_files():
    """
    检查现有的exe文件
    """
    exe_files = [
        "build/windows/x64/runner/Debug/numerology_ai_chat.exe",
        "build/windows/x64/runner/Release/numerology_ai_chat.exe"
    ]
    
    existing_files = []
    for exe_file in exe_files:
        if os.path.exists(exe_file):
            size = os.path.getsize(exe_file)
            print(f"✅ 找到exe文件: {exe_file} (大小: {size} 字节)")
            existing_files.append(exe_file)
        else:
            print(f"❌ exe文件不存在: {exe_file}")
    
    return existing_files

def main():
    print("🔧 直接更新现有exe文件的图标...")
    
    # 检查图标文件
    ico_path = "windows/runner/resources/app_icon.ico"
    if not os.path.exists(ico_path):
        print(f"❌ 图标文件不存在: {ico_path}")
        return
    
    print(f"✅ 图标文件存在: {ico_path}")
    
    # 检查exe文件
    exe_files = check_exe_files()
    if not exe_files:
        print("❌ 没有找到可更新的exe文件")
        return
    
    # 尝试使用rcedit更新图标
    ico_path_abs = os.path.abspath(ico_path)
    success_count = 0
    
    for exe_file in exe_files:
        exe_path_abs = os.path.abspath(exe_file)
        print(f"\n🔄 尝试更新: {exe_file}")
        
        if update_exe_icon_with_rcedit(exe_path_abs, ico_path_abs):
            success_count += 1
        else:
            print(f"⚠️ 无法使用rcedit更新 {exe_file}")
    
    if success_count == 0:
        print("\n❌ 无法自动更新exe图标")
        manual_icon_update_guide()
    else:
        print(f"\n✅ 成功更新了 {success_count} 个exe文件的图标")
        print("\n📝 建议:")
        print("1. 重启Windows资源管理器以刷新图标缓存")
        print("2. 或者重启计算机")
        print("3. 检查任务栏和桌面图标是否已更新")

if __name__ == "__main__":
    main()
