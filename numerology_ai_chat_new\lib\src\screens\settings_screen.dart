import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';

import '../core/constants/app_constants.dart';
import '../providers/theme_provider.dart';
import '../providers/settings_provider.dart';
import '../providers/agent_provider.dart';
import '../providers/model_provider.dart';
import '../providers/chat_provider.dart';
import '../providers/bazi_history_provider.dart';
import '../models/agent_model.dart';
import '../models/ai_model.dart';
import '../services/bazi_storage_service.dart';

/// 设置页面
class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Card(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(
                      Icons.settings_outlined,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const Gap(8),
                    Text(
                      '应用设置',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                  ],
                ),
              ),
              const Divider(height: 1),
              Expanded(
                child: _buildSettingsList(context, ref),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsList(BuildContext context, WidgetRef ref) {
    final themeProvider = ref.watch(themeProviderNotifier);
    final settingsNotifier = ref.watch(settingsProvider);
    final settings = settingsNotifier.settings;

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSettingsSection(
          context,
          '外观设置',
          [
            _buildThemeSettingTile(context, ref, themeProvider),
          ],
        ),
        const Gap(24),
        _buildSettingsSection(
          context,
          'AI设置',
          [
            _buildDefaultAgentTile(context, ref, settings),
            _buildDefaultModelTile(context, ref, settings),
          ],
        ),
        const Gap(24),
        _buildSettingsSection(
          context,
          '数据设置',
          [
            _buildClearBaziDataTile(context),
            _buildClearChatDataTile(context),
          ],
        ),
        const Gap(24),
        _buildSettingsSection(
          context,
          '关于',
          [
            _buildVersionTile(context),
          ],
        ),
      ],
    );
  }

  Widget _buildSettingsSection(BuildContext context, String title, List<Widget> children) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
        ),
        const Gap(8),
        Card(
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildThemeSettingTile(BuildContext context, WidgetRef ref, ThemeProvider themeProvider) {
    return ListTile(
      leading: const Icon(Icons.palette_outlined),
      title: const Text('主题模式'),
      subtitle: Text(_getThemeModeDisplayName(themeProvider.themeMode)),
      trailing: const Icon(Icons.chevron_right),
      onTap: () => _showThemeDialog(context, ref),
    );
  }

  Widget _buildDefaultAgentTile(BuildContext context, WidgetRef ref, AppSettings settings) {
    final agentState = ref.watch(agentProvider);

    switch (agentState.loadingState) {
      case AgentLoadingState.loading:
        return const ListTile(
          leading: Icon(Icons.smart_toy_outlined),
          title: Text('默认智能体'),
          subtitle: Text('加载中...'),
          trailing: SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        );
      case AgentLoadingState.error:
        return ListTile(
          leading: const Icon(Icons.smart_toy_outlined),
          title: const Text('默认智能体'),
          subtitle: Text('加载失败: ${agentState.error}'),
          trailing: const Icon(Icons.error),
        );
      case AgentLoadingState.loaded:
      case AgentLoadingState.initial:
        final agents = agentState.agents;
        final defaultAgent = agents.isNotEmpty
            ? agents.firstWhere(
                (a) => a.id == settings.defaultAgentId,
                orElse: () => agents.first,
              )
            : null;

        return ListTile(
          leading: const Icon(Icons.smart_toy_outlined),
          title: const Text('默认智能体'),
          subtitle: Text(defaultAgent?.agentName ?? '未设置'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () => _showDefaultAgentDialog(context, ref, agents),
        );
    }
  }

  Widget _buildDefaultModelTile(BuildContext context, WidgetRef ref, AppSettings settings) {
    final modelState = ref.watch(modelProvider);

    switch (modelState.loadingState) {
      case ModelLoadingState.loading:
        return const ListTile(
          leading: Icon(Icons.psychology_outlined),
          title: Text('默认AI模型'),
          subtitle: Text('加载中...'),
          trailing: SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        );
      case ModelLoadingState.error:
        return ListTile(
          leading: const Icon(Icons.psychology_outlined),
          title: const Text('默认AI模型'),
          subtitle: Text('加载失败: ${modelState.error}'),
          trailing: const Icon(Icons.error),
        );
      case ModelLoadingState.loaded:
      case ModelLoadingState.initial:
        final models = modelState.models;
        final defaultModel = models.isNotEmpty
            ? models.firstWhere(
                (m) => m.id == settings.defaultModelName,
                orElse: () => models.first,
              )
            : null;

        return ListTile(
          leading: const Icon(Icons.psychology_outlined),
          title: const Text('默认AI模型'),
          subtitle: Text(defaultModel?.modelDisplayName ?? '未设置'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () => _showDefaultModelDialog(context, ref, models),
        );
    }
  }

  Widget _buildClearBaziDataTile(BuildContext context) {
    return ListTile(
      leading: Icon(Icons.auto_fix_high_outlined, color: Theme.of(context).colorScheme.error),
      title: Text('清除八字信息', style: TextStyle(color: Theme.of(context).colorScheme.error)),
      subtitle: const Text('删除所有本地八字记录'),
      trailing: const Icon(Icons.chevron_right),
      onTap: () => _showClearBaziDataDialog(context),
    );
  }

  Widget _buildClearChatDataTile(BuildContext context) {
    return ListTile(
      leading: Icon(Icons.chat_outlined, color: Theme.of(context).colorScheme.error),
      title: Text('清除聊天记录', style: TextStyle(color: Theme.of(context).colorScheme.error)),
      subtitle: const Text('删除所有本地聊天记录'),
      trailing: const Icon(Icons.chevron_right),
      onTap: () => _showClearChatDataDialog(context),
    );
  }

  Widget _buildVersionTile(BuildContext context) {
    return ListTile(
      leading: const Icon(Icons.info_outline),
      title: const Text('版本信息'),
      subtitle: Text('v${AppConstants.appVersion}'),
      trailing: const Icon(Icons.chevron_right),
      onTap: () => _showVersionDialog(context),
    );
  }

  String _getThemeModeDisplayName(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.system:
        return '跟随系统';
      case ThemeMode.light:
        return '明亮模式';
      case ThemeMode.dark:
        return '暗色模式';
    }
  }

  void _showThemeDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择主题'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: ThemeMode.values.map((mode) {
            return RadioListTile<ThemeMode>(
              title: Text(_getThemeModeDisplayName(mode)),
              value: mode,
              groupValue: ref.read(themeProviderNotifier).themeMode,
              onChanged: (value) {
                if (value != null) {
                  ref.read(themeProviderNotifier.notifier).setThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showDefaultAgentDialog(BuildContext context, WidgetRef ref, List<AgentModel> agents) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择默认智能体'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: agents.length + 1, // +1 for "未设置" option
            itemBuilder: (context, index) {
              if (index == 0) {
                return RadioListTile<String?>(
                  title: const Text('未设置'),
                  value: null,
                  groupValue: ref.read(settingsProvider).defaultAgentId,
                  onChanged: (value) {
                    ref.read(settingsProvider.notifier).setDefaultAgent(value);
                    Navigator.of(context).pop();
                  },
                );
              }

              final agent = agents[index - 1];
              return RadioListTile<String>(
                title: Text(agent.agentName),
                subtitle: Text(agent.agentDescription ?? ''),
                value: agent.id,
                groupValue: ref.read(settingsProvider).defaultAgentId,
                onChanged: (value) {
                  ref.read(settingsProvider.notifier).setDefaultAgent(value);
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
      ),
    );
  }

  void _showDefaultModelDialog(BuildContext context, WidgetRef ref, List<AIModel> models) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择默认AI模型'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: models.length + 1, // +1 for "未设置" option
            itemBuilder: (context, index) {
              if (index == 0) {
                return RadioListTile<String?>(
                  title: const Text('未设置'),
                  value: null,
                  groupValue: ref.read(settingsProvider).defaultModelName,
                  onChanged: (value) {
                    ref.read(settingsProvider.notifier).setDefaultModel(value);
                    Navigator.of(context).pop();
                  },
                );
              }

              final model = models[index - 1];
              return RadioListTile<String>(
                title: Text(model.modelDisplayName),
                subtitle: Text(model.description),
                value: model.id,
                groupValue: ref.read(settingsProvider).defaultModelName,
                onChanged: (value) {
                  ref.read(settingsProvider.notifier).setDefaultModel(value);
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
      ),
    );
  }

  void _showClearBaziDataDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清除八字信息'),
        content: const Text('确定要删除所有本地八字记录吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _clearBaziData(context);
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showClearChatDataDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清除聊天记录'),
        content: const Text('确定要删除所有本地聊天记录吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _clearChatData(context);
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showVersionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('版本信息'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('应用名称：${AppConstants.appName}'),
            const Gap(8),
            Text('版本号：v${AppConstants.appVersion}'),
            const Gap(8),
            Text('描述：${AppConstants.appDescription}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 清除八字数据
  Future<void> _clearBaziData(BuildContext context) async {
    try {
      // 显示加载指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在清除八字数据...'),
            ],
          ),
        ),
      );

      // 清除八字数据
      final baziStorage = BaziStorageService();
      await baziStorage.clearAllBaziRecords();

      // 通知八字历史记录刷新
      if (context.mounted) {
        final ref = ProviderScope.containerOf(context);
        ref.read(baziHistoryProvider.notifier).notifyHistoryChanged();
      }

      // 关闭加载对话框
      if (context.mounted) {
        Navigator.of(context).pop();

        // 显示成功消息
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('八字数据已清除'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      // 关闭加载对话框
      if (context.mounted) {
        Navigator.of(context).pop();

        // 显示错误消息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('清除八字数据失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 清除聊天数据
  Future<void> _clearChatData(BuildContext context) async {
    try {
      // 显示加载指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在清除聊天记录...'),
            ],
          ),
        ),
      );

      // 清除聊天数据
      if (context.mounted) {
        final ref = ProviderScope.containerOf(context);
        final chatNotifier = ref.read(chatProvider.notifier);
        await chatNotifier.clearAllConversations();
      }

      // 关闭加载对话框
      if (context.mounted) {
        Navigator.of(context).pop();

        // 显示成功消息
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('聊天记录已清除'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      // 关闭加载对话框
      if (context.mounted) {
        Navigator.of(context).pop();

        // 显示错误消息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('清除聊天记录失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
