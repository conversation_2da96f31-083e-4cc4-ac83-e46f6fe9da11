import 'package:json_annotation/json_annotation.dart';

part 'model_model.g.dart';

@JsonSerializable()
class Model {
  final String modelId;
  final String modelName;
  final String modelDisplayName;
  final String modelApiUrl;
  final int maxTokens;
  final double temperature;
  final String description;
  final bool isActive;
  final int sortOrder;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool hasApiKey;

  Model({
    required this.modelId,
    required this.modelName,
    required this.modelDisplayName,
    required this.modelApiUrl,
    required this.maxTokens,
    required this.temperature,
    required this.description,
    required this.isActive,
    required this.sortOrder,
    required this.createdAt,
    required this.updatedAt,
    required this.hasApiKey,
  });

  factory Model.fromJson(Map<String, dynamic> json) => _$ModelFromJson(json);
  Map<String, dynamic> toJson() => _$ModelToJson(this);
}
