
const bcrypt = require('bcryptjs')
const { getCollection, COLLECTIONS } = require('../utils/db')
const { createBusinessError, ERROR_CODES, formatSuccessResponse } = require('../utils/error_handler')
const logger = require('../utils/logger')

const saltRounds = 10; // 与注册逻辑保持一致

/**
 * 更新管理员密码
 * @param {object} params 云函数事件对象
 * @returns {object} 更新结果
 */
async function updateAdminPassword(params) {
  try {
    const { adminAccount, newPassword } = params

    if (!adminAccount || !newPassword) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '管理员账号和新密码不能为空', 400)
    }

    logger.info('准备更新管理员密码', { adminAccount })

    // 查找管理员
    const admin = await getCollection(COLLECTIONS.ADMINS).where({ adminAccount }).get().then(res => res.data[0])
    if (!admin) {
      logger.warn('更新管理员密码失败：账号不存在', { adminAccount })
      throw createBusinessError(ERROR_CODES.ADMIN_NOT_FOUND, '管理员账号不存在', 404)
    }

    // 加密新密码
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds)

    // 更新数据库
    await getCollection(COLLECTIONS.ADMINS).doc(admin._id).update({
      data: {
        adminPassword: hashedPassword,
        updatedAt: new Date()
      }
    })

    logger.info('管理员密码更新成功', { adminAccount })

    return formatSuccessResponse(null, '密码更新成功')

  } catch (error) {
    logger.error('更新管理员密码异常', {
      adminAccount: params?.adminAccount,
      error: error.message
    })
    throw error
  }
}

module.exports = {
  updateAdminPassword
}
