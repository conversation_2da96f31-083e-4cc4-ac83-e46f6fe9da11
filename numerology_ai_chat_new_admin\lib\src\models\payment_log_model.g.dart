// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_log_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PaymentLogModel _$PaymentLogModelFromJson(Map<String, dynamic> json) =>
    PaymentLogModel(
      id: json['_id'] as String,
      operationType: json['operationType'] as String,
      orderId: json['orderId'] as String,
      userId: json['userId'] as String,
      requestData: json['requestData'] as Map<String, dynamic>,
      responseData: json['responseData'] as Map<String, dynamic>,
      status: json['status'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$PaymentLogModelToJson(PaymentLogModel instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'operationType': instance.operationType,
      'orderId': instance.orderId,
      'userId': instance.userId,
      'requestData': instance.requestData,
      'responseData': instance.responseData,
      'status': instance.status,
      'timestamp': instance.timestamp.toIso8601String(),
    };
