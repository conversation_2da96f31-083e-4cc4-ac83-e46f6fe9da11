const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 分析统计处理器
 */
class AnalyticsHandler {
  /**
   * 获取综合统计数据
   */
  static async getOverallStats(event) {
    try {
      // 并行获取各种统计数据
      const [
        userStats,
        orderStats,
        paymentStats,
        usageStats,
        agentStats,
        modelStats
      ] = await Promise.all([
        this.getUserStats(),
        this.getOrderStats(),
        this.getPaymentStats(),
        this.getUsageStats(),
        this.getAgentStats(),
        this.getModelStats()
      ]);

      return {
        code: 0,
        message: '获取综合统计成功',
        data: {
          users: userStats,
          orders: orderStats,
          payments: paymentStats,
          usage: usageStats,
          agents: agentStats,
          models: modelStats,
          lastUpdated: new Date()
        }
      };
    } catch (error) {
      console.error('获取综合统计失败:', error);
      return {
        code: -1,
        message: '获取综合统计失败: ' + error.message
      };
    }
  }

  /**
   * 获取用户统计
   */
  static async getUserStats() {
    const totalUsers = await db.collection('exe_users').count();
    
    // 获取今日新增用户
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayUsers = await db.collection('exe_users')
      .where({
        createdAt: db.command.gte(today)
      })
      .count();

    // 获取活跃用户（最近7天有使用记录）
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const activeUsers = await db.collection('exe_usage_history')
      .where({
        consumeTime: db.command.gte(sevenDaysAgo)
      })
      .field({ userId: true })
      .get();

    const uniqueActiveUsers = [...new Set(activeUsers.data.map(item => item.userId))];

    return {
      total: totalUsers.total,
      todayNew: todayUsers.total,
      activeLastWeek: uniqueActiveUsers.length
    };
  }

  /**
   * 获取订单统计
   */
  static async getOrderStats() {
    const totalOrders = await db.collection('exe_purchase_orders').count();
    
    const completedOrders = await db.collection('exe_purchase_orders')
      .where({ status: 'COMPLETED' })
      .count();

    const pendingOrders = await db.collection('exe_purchase_orders')
      .where({ status: 'PENDING' })
      .count();

    // 获取今日订单
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayOrders = await db.collection('exe_purchase_orders')
      .where({
        createTime: db.command.gte(today)
      })
      .count();

    // 计算总收入
    const completedOrdersData = await db.collection('exe_purchase_orders')
      .where({ status: 'COMPLETED' })
      .get();

    const totalRevenue = completedOrdersData.data.reduce((sum, order) => {
      return sum + (order.orderAmount || 0);
    }, 0);

    return {
      total: totalOrders.total,
      completed: completedOrders.total,
      pending: pendingOrders.total,
      todayNew: todayOrders.total,
      totalRevenue: totalRevenue / 100 // 转换为元
    };
  }

  /**
   * 获取支付统计
   */
  static async getPaymentStats() {
    const totalLogs = await db.collection('exe_payment_logs').count();
    
    const successLogs = await db.collection('exe_payment_logs')
      .where({ status: 'SUCCESS' })
      .count();

    const failedLogs = await db.collection('exe_payment_logs')
      .where({ status: 'FAILED' })
      .count();

    // 获取今日支付日志
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayLogs = await db.collection('exe_payment_logs')
      .where({
        timestamp: db.command.gte(today)
      })
      .count();

    return {
      total: totalLogs.total,
      success: successLogs.total,
      failed: failedLogs.total,
      todayNew: todayLogs.total,
      successRate: totalLogs.total > 0 ? (successLogs.total / totalLogs.total * 100).toFixed(2) : 0
    };
  }

  /**
   * 获取使用统计
   */
  static async getUsageStats() {
    const totalUsage = await db.collection('exe_usage_history').count();
    
    // 获取今日使用
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayUsage = await db.collection('exe_usage_history')
      .where({
        consumeTime: db.command.gte(today)
      })
      .count();

    // 计算总算力消耗
    const allUsageData = await db.collection('exe_usage_history').get();
    const totalPowerConsumed = allUsageData.data.reduce((sum, record) => {
      return sum + (record.powerCost || 0);
    }, 0);

    // 计算今日算力消耗
    const todayUsageData = await db.collection('exe_usage_history')
      .where({
        consumeTime: db.command.gte(today)
      })
      .get();

    const todayPowerConsumed = todayUsageData.data.reduce((sum, record) => {
      return sum + (record.powerCost || 0);
    }, 0);

    return {
      total: totalUsage.total,
      todayNew: todayUsage.total,
      totalPowerConsumed,
      todayPowerConsumed
    };
  }

  /**
   * 获取智能体统计
   */
  static async getAgentStats() {
    const totalAgents = await db.collection('exe_agents').count();
    
    const enabledAgents = await db.collection('exe_agents')
      .where({ isEnabled: true })
      .count();

    // 获取最受欢迎的智能体
    const usageByAgent = await db.collection('exe_usage_history')
      .field({ agentId: true, agentName: true })
      .get();

    const agentUsageCount = {};
    usageByAgent.data.forEach(record => {
      const agentId = record.agentId;
      if (!agentUsageCount[agentId]) {
        agentUsageCount[agentId] = {
          name: record.agentName,
          count: 0
        };
      }
      agentUsageCount[agentId].count++;
    });

    const popularAgents = Object.entries(agentUsageCount)
      .sort(([,a], [,b]) => b.count - a.count)
      .slice(0, 5)
      .map(([id, data]) => ({ id, name: data.name, count: data.count }));

    return {
      total: totalAgents.total,
      enabled: enabledAgents.total,
      popular: popularAgents
    };
  }

  /**
   * 获取模型统计
   */
  static async getModelStats() {
    const totalModels = await db.collection('exe_models').count();
    
    const enabledModels = await db.collection('exe_models')
      .where({ isEnabled: true })
      .count();

    // 获取各等级模型使用统计
    const usageByLevel = await db.collection('exe_usage_history')
      .field({ modelLevel: true })
      .get();

    const levelStats = {};
    usageByLevel.data.forEach(record => {
      const level = record.modelLevel;
      levelStats[level] = (levelStats[level] || 0) + 1;
    });

    return {
      total: totalModels.total,
      enabled: enabledModels.total,
      usageByLevel: levelStats
    };
  }

  /**
   * 获取时间范围内的趋势数据
   */
  static async getTrendData(event) {
    try {
      const { days = 7 } = event;
      
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      
      // 获取每日数据
      const dailyData = [];
      for (let i = 0; i < days; i++) {
        const date = new Date(startDate);
        date.setDate(date.getDate() + i);
        const nextDate = new Date(date);
        nextDate.setDate(nextDate.getDate() + 1);
        
        const [orders, usage, users] = await Promise.all([
          db.collection('exe_purchase_orders')
            .where({
              createTime: db.command.gte(date).and(db.command.lt(nextDate))
            })
            .count(),
          db.collection('exe_usage_history')
            .where({
              consumeTime: db.command.gte(date).and(db.command.lt(nextDate))
            })
            .count(),
          db.collection('exe_users')
            .where({
              createdAt: db.command.gte(date).and(db.command.lt(nextDate))
            })
            .count()
        ]);

        dailyData.push({
          date: date.toISOString().split('T')[0],
          orders: orders.total,
          usage: usage.total,
          users: users.total
        });
      }

      return {
        code: 0,
        message: '获取趋势数据成功',
        data: dailyData
      };
    } catch (error) {
      console.error('获取趋势数据失败:', error);
      return {
        code: -1,
        message: '获取趋势数据失败: ' + error.message
      };
    }
  }
}

module.exports = AnalyticsHandler;
