
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:numerology_ai_chat_admin/src/models/agent_model.dart';
import 'package:numerology_ai_chat_admin/src/providers/agent_provider.dart';
import 'package:numerology_ai_chat_admin/src/widgets/edit_agent_dialog.dart';

class AgentManagementScreen extends ConsumerStatefulWidget {
  const AgentManagementScreen({super.key});

  @override
  ConsumerState<AgentManagementScreen> createState() => _AgentManagementScreenState();
}

class _AgentManagementScreenState extends ConsumerState<AgentManagementScreen> {
  bool? _selectedIsActive;

  @override
  void initState() {
    super.initState();
    // 初始加载智能体列表
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(agentProvider.notifier).fetchAgents();
    });
  }

  @override
  Widget build(BuildContext context) {
    final agentState = ref.watch(agentProvider);

    // 监听错误状态
    ref.listen<AgentListState>(agentProvider, (previous, next) {
      if (next.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.error!),
            backgroundColor: Colors.red,
          ),
        );
      }
    });

    return Scaffold(
      body: Column(
        children: [
          // 页面标题和操作栏
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Text(
                  '智能体管理',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () => _refreshAgents(),
                  tooltip: '刷新',
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: () => _showCreateAgentDialog(),
                  icon: const Icon(Icons.add),
                  label: const Text('新建智能体'),
                ),
              ],
            ),
          ),
          // 筛选条件
          _buildFilterBar(),
          const Divider(height: 1),
          // 智能体列表
          Expanded(
            child: agentState.isLoading
                ? const Center(child: CircularProgressIndicator())
                : agentState.agents == null || agentState.agents!.isEmpty
                    ? const Center(child: Text('暂无智能体数据'))
                    : _buildAgentList(agentState.agents!),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // 状态筛选
          SizedBox(
            width: 120,
            child: DropdownButtonFormField<bool>(
              value: _selectedIsActive,
              decoration: const InputDecoration(
                labelText: '状态',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: const [
                DropdownMenuItem(value: null, child: Text('全部状态')),
                DropdownMenuItem(value: true, child: Text('启用')),
                DropdownMenuItem(value: false, child: Text('禁用')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedIsActive = value;
                });
                _refreshAgents();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAgentList(List<Agent> agents) {
    // 根据筛选条件过滤智能体
    final filteredAgents = agents.where((agent) {
      if (_selectedIsActive != null && agent.isActive != _selectedIsActive) {
        return false;
      }
      return true;
    }).toList();

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('智能体名称')),
          DataColumn(label: Text('显示名称')),
          DataColumn(label: Text('类型')),
          DataColumn(label: Text('描述')),
          DataColumn(label: Text('状态')),
          DataColumn(label: Text('排序')),
          DataColumn(label: Text('更新时间')),
          DataColumn(label: Text('操作')),
        ],
        rows: filteredAgents.map((agent) => _buildAgentRow(agent)).toList(),
      ),
    );
  }

  DataRow _buildAgentRow(Agent agent) {
    return DataRow(
      cells: [
        DataCell(
          SizedBox(
            width: 120,
            child: Text(
              agent.agentName,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        DataCell(
          SizedBox(
            width: 120,
            child: Text(
              agent.agentDisplayName,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        DataCell(Text(agent.agentType)),
        DataCell(
          SizedBox(
            width: 200,
            child: Text(
              agent.description,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: agent.isActive ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              agent.isActive ? '启用' : '禁用',
              style: TextStyle(
                color: agent.isActive ? Colors.green : Colors.red,
                fontSize: 12,
              ),
            ),
          ),
        ),
        DataCell(Text(agent.sortOrder.toString())),
        DataCell(Text(_formatDateTime(agent.updatedAt))),
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: const Icon(Icons.edit, size: 18),
                onPressed: () => _showEditAgentDialog(agent),
                tooltip: '编辑',
              ),
              IconButton(
                icon: const Icon(Icons.delete, size: 18),
                onPressed: () => _showDeleteConfirmDialog(agent),
                tooltip: '删除',
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _refreshAgents() {
    ref.read(agentProvider.notifier).fetchAgents();
  }

  void _showCreateAgentDialog() {
    showDialog(
      context: context,
      builder: (context) => const EditAgentDialog(),
    );
  }

  void _showEditAgentDialog(Agent agent) {
    showDialog(
      context: context,
      builder: (context) => EditAgentDialog(agent: agent),
    );
  }

  void _showDeleteConfirmDialog(Agent agent) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除智能体"${agent.agentDisplayName}"吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final success = await ref.read(agentProvider.notifier).deleteAgent(agent.agentId);
              if (success) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('智能体删除成功')),
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }
}
