import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/constants/app_constants.dart';
import '../services/storage_service.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';
import '../services/token_manager.dart';
import '../services/http_client_service.dart';
import '../services/system_config_service.dart';
import 'service_providers.dart';

/// 认证状态枚举
enum AuthStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

/// 兼容旧代码的类型别名【AuthState认证状态】
typedef AuthState = AuthProvider;

/// 用户认证提供者
class AuthProvider extends ChangeNotifier {
  final AuthService _authService;
  final StorageService _storageService;
  final SystemConfigService _systemConfigService;
  late final TokenManager _tokenManager;

  AuthStatus _status = AuthStatus.initial;
  UserModel? _user;
  String? _token;
  String? _refreshToken;
  String? _errorMessage;
  DateTime? _lastRefreshTime;
  DateTime? _tokenExpiryTime;
  bool _isRefreshing = false;

  AuthProvider(this._authService, this._storageService, this._systemConfigService) {
    _tokenManager = TokenManager(
      authService: _authService,
      storageService: _storageService,
    );

    // 设置TokenManager回调
    _tokenManager.setCallbacks(
      onTokenRefreshed: _onTokenRefreshed,
      onRefreshFailed: _onRefreshFailed,
    );

    // 设置HttpClientService回调
    HttpClientService.instance.setCallbacks(
      onTokenExpired: _onRefreshFailed,
      getCurrentToken: () => _token,
      refreshToken: () => _tokenManager.forceRefreshToken(),
    );
  }

  // Getters
  AuthStatus get status => _status;
  UserModel? get user => _user;
  String? get token => _token;
  String? get refreshToken => _refreshToken;
  String? get errorMessage => _errorMessage;
  DateTime? get tokenExpiryTime => _tokenExpiryTime;
  bool get isAuthenticated => _status == AuthStatus.authenticated && _user != null;
  bool get isLoading => _status == AuthStatus.loading;
  bool get isTokenRefreshing => _tokenManager.isRefreshing;

  /// 初始化认证状态（会话级登录：每次启动都需要重新登录）
  Future<void> initAuth() async {
    _setStatus(AuthStatus.loading);

    try {
      // 清除任何可能残留的token数据（确保会话级登录）
      await _clearAuthData();

      // 始终设置为未认证状态，要求用户重新登录
      _setStatus(AuthStatus.unauthenticated);
    } catch (e) {
      _setError('初始化认证状态失败: $e');
    }
  }

  /// 确保存储服务初始化后再初始化认证状态
  Future<void> initAuthWithStorage() async {
    try {
      // 确保底层存储服务已初始化
      await _storageService.init();

      // 然后初始化认证状态
      await initAuth();
    } catch (e) {
      _setError('初始化认证状态失败: $e');
    }
  }

  /// 用户登录（会话级登录：token只存储在内存中）
  Future<bool> login(String username, String password, {bool rememberPassword = false}) async {
    _setStatus(AuthStatus.loading);
    _clearError();

    try {
      final result = await _authService.login(username, password);

      if (result['success'] == true) {
        // 保存token到内存和存储（续签需要）
        _token = result['token'] as String;
        _refreshToken = result['refresh_token'] as String?;
        _user = UserModel.fromJson(result['user'] as Map<String, dynamic>);

        // 保存token到存储，供TokenManager续签使用
        await _storageService.setString(AppConstants.authTokenKey, _token!);
        if (_refreshToken != null) {
          await _storageService.setString(AppConstants.refreshTokenKey, _refreshToken!);
          debugPrint('AuthProvider: 已保存refresh token到存储');
        }

        // 设置token过期时间（从服务器返回或默认1小时后）
        if (result['expires_at'] != null) {
          // 确保正确处理UTC时间
          _tokenExpiryTime = DateTime.parse(result['expires_at'] as String).toLocal();
          debugPrint('AuthProvider: 服务器返回过期时间(UTC): ${result['expires_at']}, 转换为本地时间: $_tokenExpiryTime');
        } else {
          _tokenExpiryTime = DateTime.now().add(const Duration(hours: 1));
          debugPrint('AuthProvider: 使用默认过期时间: $_tokenExpiryTime');
        }

        // 根据用户选择决定是否保存账号密码
        if (rememberPassword) {
          await _saveCredentials(username, password);
        } else {
          await _clearCredentials();
        }

        // 启动自动续签（仅在当前会话中有效）
        debugPrint('AuthProvider: 准备启动TokenManager自动续签，Token过期时间: $_tokenExpiryTime');
        _tokenManager.startAutoRefresh(_tokenExpiryTime!);

        // 登录成功后强制刷新API地址，确保使用最新配置
        try {
          debugPrint('AuthProvider: 登录成功，开始刷新API地址配置');
          await _systemConfigService.forceRefreshGoProxyApiUrl();
          debugPrint('AuthProvider: API地址配置刷新成功');
        } catch (e) {
          debugPrint('AuthProvider: API地址配置刷新失败: $e');
          // 不影响登录流程，只记录错误
        }

        debugPrint('AuthProvider: 登录成功，Token过期时间: $_tokenExpiryTime');
        _setStatus(AuthStatus.authenticated);
        return true;
      } else {
        _setError(result['message'] as String? ?? '登录失败');
        return false;
      }
    } catch (e) {
      _setError('登录失败: $e');
      return false;
    }
  }

  /// 用户注册（注册成功后不自动登录，需要手动登录）
  Future<bool> register(String username, String email, String password, [String? activationCode]) async {
    _setStatus(AuthStatus.loading);
    _clearError();

    try {
      final result = await _authService.register(username, email, password, activationCode);

      if (result['success'] == true) {
        // 注册成功后不自动登录，要求用户手动登录
        _setStatus(AuthStatus.unauthenticated);
        return true;
      } else {
        _setError(result['message'] as String? ?? '注册失败');
        return false;
      }
    } catch (e) {
      _setError('注册失败: $e');
      return false;
    }
  }

  /// 验证激活码
  Future<Map<String, dynamic>> validateActivationCode(String activationCode) async {
    try {
      final result = await _authService.validateActivationCode(activationCode);
      return result;
    } catch (e) {
      return {
        'valid': false,
        'error': '验证失败: $e',
      };
    }
  }

  /// 用户登出（清除所有认证数据，但保留密码记住功能的数据）
  Future<void> logout() async {
    _setStatus(AuthStatus.loading);

    try {
      // 调用服务端登出接口
      await _authService.logout();
    } catch (e) {
      // 即使服务端登出失败，也要清除本地数据
      debugPrint('服务端登出失败: $e');
    }

    // 停止自动续签
    _tokenManager.stopAutoRefresh();

    // 清除认证数据（但保留密码记住功能的数据）
    await _clearAuthData();

    _token = null;
    _refreshToken = null;
    _user = null;
    _tokenExpiryTime = null;
    _setStatus(AuthStatus.unauthenticated);
  }

  /// 刷新用户信息（带防抖机制）
  Future<void> refreshUser({bool force = false}) async {
    if (!isAuthenticated || _token == null) return;

    // 防抖机制：5秒内不重复刷新（除非强制刷新）
    final now = DateTime.now();
    if (!force && _lastRefreshTime != null &&
        now.difference(_lastRefreshTime!).inSeconds < 5) {
      debugPrint('刷新用户信息被防抖机制阻止');
      return;
    }

    // 避免并发刷新
    if (_isRefreshing) {
      debugPrint('用户信息正在刷新中，跳过重复请求');
      return;
    }

    _isRefreshing = true;
    try {
      debugPrint('开始刷新用户信息，token: ${_token!.substring(0, 10)}...');
      final result = await _authService.getUserInfo(_token!);

      debugPrint('getUserInfo返回结果: $result');

      if (result['success'] == true) {
        final userData = result['user'];
        debugPrint('用户数据: $userData');

        if (userData == null) {
          debugPrint('警告: 用户数据为null');
          return;
        }

        final newUser = UserModel.fromJson(userData as Map<String, dynamic>);

        // 只有在数据真正变化时才更新UI
        if (_user == null || _user!.availableCount != newUser.availableCount ||
            _user!.totalUsageCount != newUser.totalUsageCount) {
          _user = newUser;
          await _saveAuthData();
          _lastRefreshTime = now;
          notifyListeners();
          debugPrint('用户信息已更新: 算力=${newUser.availableCount}');
        } else {
          _lastRefreshTime = now;
          debugPrint('用户信息无变化，跳过UI更新');
        }
      } else {
        debugPrint('刷新用户信息失败: ${result['message']}');
      }
    } catch (e) {
      debugPrint('刷新用户信息异常: $e');
    } finally {
      _isRefreshing = false;
    }
  }

  /// 强制刷新用户信息（绕过防抖机制）
  Future<void> forceRefreshUser() async {
    await refreshUser(force: true);
  }

  /// 更新用户额度
  void updateUserCredits(int newCredits) {
    if (_user != null) {
      _user = _user!.copyWith(availableCount: newCredits);
      _saveAuthData();
      notifyListeners();
    }
  }

  /// 扣除用户额度
  bool deductCredits(int amount) {
    if (_user != null && _user!.availableCount >= amount) {
      _user = _user!.copyWith(availableCount: _user!.availableCount - amount);
      _saveAuthData();
      notifyListeners();
      return true;
    }
    return false;
  }

  /// 手动刷新Token
  Future<bool> forceRefreshToken() async {
    if (!isAuthenticated) return false;
    return await _tokenManager.forceRefreshToken();
  }

  /// 检查Token是否需要续签
  bool shouldRefreshToken() {
    return _tokenManager.shouldRefreshToken();
  }

  /// 应用恢复时检查Token状态
  Future<void> onAppResumed() async {
    if (!isAuthenticated) return;

    // 检查是否需要续签
    if (_tokenManager.shouldRefreshToken()) {
      await _tokenManager.refreshTokenIfNeeded();
    }
  }

  /// TokenManager回调：Token续签成功
  void _onTokenRefreshed(String newToken) {
    _token = newToken;
    // TokenManager已经更新了过期时间，这里同步获取
    _tokenExpiryTime = _tokenManager.tokenExpiryTime;
    _saveAuthData();
    notifyListeners();
    debugPrint('AuthProvider: Token续签成功，新过期时间: $_tokenExpiryTime');
  }

  /// TokenManager回调：Token续签失败
  void _onRefreshFailed() {
    debugPrint('AuthProvider: Token续签失败，需要重新登录');

    // 停止自动续签
    _tokenManager.stopAutoRefresh();

    // 清除认证数据
    _token = null;
    _refreshToken = null;
    _user = null;
    _tokenExpiryTime = null;

    // 设置为未认证状态
    _setStatus(AuthStatus.unauthenticated);

    // 设置错误信息
    _setError('登录已过期，请重新登录');

    // 强制跳转到登录页面
    _forceNavigateToLogin();
  }

  /// 强制跳转到登录页面
  void _forceNavigateToLogin() {
    // 使用延迟执行确保状态更新完成
    Future.microtask(() {
      try {
        debugPrint('AuthProvider: 认证状态已更新，需要跳转到登录页面');

        // 先设置标记，再通知监听者
        _shouldNavigateToLogin = true;
        debugPrint('AuthProvider: 已设置导航标记 shouldNavigateToLogin = true');

        // 强制通知所有监听者状态已变化
        notifyListeners();
        debugPrint('AuthProvider: 已通知所有监听者状态变化');
      } catch (e) {
        debugPrint('AuthProvider: 处理登录跳转失败: $e');
      }
    });
  }

  // 添加一个标记来指示是否需要跳转到登录页面
  bool _shouldNavigateToLogin = false;
  bool get shouldNavigateToLogin => _shouldNavigateToLogin;

  /// 清除导航标记
  void clearNavigationFlag() {
    debugPrint('AuthProvider: 清除导航标记 shouldNavigateToLogin = false');
    _shouldNavigateToLogin = false;
  }

  /// 设置认证状态
  void _setStatus(AuthStatus status) {
    _status = status;
    notifyListeners();
  }

  /// 设置错误信息
  void _setError(String message) {
    _errorMessage = message;
    _setStatus(AuthStatus.error);
  }

  /// 清除错误信息
  void _clearError() {
    _errorMessage = null;
  }

  /// 保存认证数据到本地（会话级登录：不保存token）
  Future<void> _saveAuthData() async {
    // 会话级登录：不保存token到持久存储
    // token只存储在内存中，应用退出后自动失效
  }

  /// 清除本地认证数据
  Future<void> _clearAuthData() async {
    try {
      // 确保存储服务已初始化
      await _storageService.init();

      // 清除可能残留的token数据
      await _storageService.remove(AppConstants.authTokenKey);
      await _storageService.remove(AppConstants.refreshTokenKey);
      await _storageService.remove(AppConstants.userDataKey);
    } catch (e) {
      print('清除认证数据失败: $e');
    }
  }

  /// 保存账号密码（记住密码功能）
  Future<void> _saveCredentials(String username, String password) async {
    try {
      // 确保存储服务已初始化
      await _storageService.init();

      await _storageService.setString(AppConstants.rememberedUsernameKey, username);
      await _storageService.setString(AppConstants.rememberedPasswordKey, password);
      await _storageService.setBool(AppConstants.rememberPasswordEnabledKey, true);
    } catch (e) {
      print('保存账号密码失败: $e');
    }
  }

  /// 清除保存的账号密码
  Future<void> _clearCredentials() async {
    try {
      // 确保存储服务已初始化
      await _storageService.init();

      await _storageService.remove(AppConstants.rememberedUsernameKey);
      await _storageService.remove(AppConstants.rememberedPasswordKey);
      await _storageService.setBool(AppConstants.rememberPasswordEnabledKey, false);
    } catch (e) {
      print('清除账号密码失败: $e');
    }
  }

  /// 获取保存的账号密码
  Future<Map<String, String?>> getSavedCredentials() async {
    try {
      // 确保存储服务已初始化
      await _storageService.init();

      final isEnabled = await _storageService.getBool(AppConstants.rememberPasswordEnabledKey) ?? false;
      if (!isEnabled) {
        return {'username': null, 'password': null};
      }

      final username = await _storageService.getString(AppConstants.rememberedUsernameKey);
      final password = await _storageService.getString(AppConstants.rememberedPasswordKey);

      return {
        'username': username,
        'password': password,
      };
    } catch (e) {
      print('获取保存的账号密码失败: $e');
      return {'username': null, 'password': null};
    }
  }

  /// 检查是否启用了记住密码功能
  Future<bool> isRememberPasswordEnabled() async {
    try {
      // 确保存储服务已初始化
      await _storageService.init();
      return await _storageService.getBool(AppConstants.rememberPasswordEnabledKey) ?? false;
    } catch (e) {
      print('检查记住密码功能失败: $e');
      return false;
    }
  }

  @override
  void dispose() {
    _tokenManager.dispose();
    HttpClientService.instance.dispose();
    super.dispose();
  }
}

/// 认证提供者
final authProvider = ChangeNotifierProvider<AuthProvider>((ref) {
  final authService = ref.read(authServiceProvider);
  final storageService = ref.read(authStorageServiceProvider);
  final systemConfigService = ref.read(systemConfigServiceProvider);
  final provider = AuthProvider(authService, storageService, systemConfigService);
  // 不在这里立即初始化，而是在需要时通过initAuthWithStorage方法初始化
  return provider;
});

/// 认证服务提供者
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService();
});

/// 认证存储服务提供者
final authStorageServiceProvider = Provider<StorageService>((ref) {
  return StorageService();
});
