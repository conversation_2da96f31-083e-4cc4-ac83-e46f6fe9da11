const jwt = require('jsonwebtoken')
const { userCollection } = require('../utils/db')
const { validateAuth } = require('../utils/validate')
const { createBusinessError, ERROR_CODES } = require('./error_handler')
const logger = require('../utils/logger')

// JWT密钥 - 生产环境应从环境变量获取
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production'
const JWT_EXPIRES_IN = '1h' // Access Token有效期 - 1小时
const REFRESH_TOKEN_EXPIRES_IN = '30d' // Refresh Token有效期

/**
 * 生成Access Token
 * @param {object} payload Token载荷
 * @returns {string} JWT Token
 */
function generateAccessToken(payload) {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN,
    issuer: 'numerology-ai-chat',
    audience: 'user'
  })
}

/**
 * 生成Refresh Token
 * @param {object} payload Token载荷
 * @returns {string} JWT Token
 */
function generateRefreshToken(payload) {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: REFRESH_TOKEN_EXPIRES_IN,
    issuer: 'numerology-ai-chat',
    audience: 'refresh'
  })
}

/**
 * 验证Token
 * @param {string} token JWT Token
 * @param {string} audience 预期的audience
 * @returns {object} 解码后的载荷
 */
function verifyToken(token, audience = 'user') {
  try {
    return jwt.verify(token, JWT_SECRET, {
      issuer: 'numerology-ai-chat',
      audience
    })
  } catch (error) {
    throw error // 让上层处理JWT相关错误
  }
}

/**
 * 检查用户会员状态
 * @param {object} user 用户对象
 * @returns {boolean} 是否为有效会员
 */
function checkMembershipStatus(user) {
  if (!user.membership || !user.membership.expiresAt) {
    return false
  }
  
  const now = new Date()
  const expiresAt = new Date(user.membership.expiresAt)
  
  return expiresAt > now
}

/**
 * 鉴权中间件
 * @param {object} params - 请求参数
 * @param {object} options - 鉴权选项
 * @param {boolean} options.skipQuotaCheck - 是否跳过算力检查
 * @returns {Promise<void>}
 */
async function authMiddleware(params, options = {}) {
  const token = params.token; // 从index.js注入
  if (!token) {
    throw createBusinessError(ERROR_CODES.TOKEN_MISSING, '请求未携带Token', 401);
  }

  try {
    const decoded = verifyToken(token, 'user');

    // 将解码后的userId附加到参数上，传递给后续处理器
    params.userId = decoded.userId;

    // 获取用户信息
    const user = await userCollection.findById(decoded.userId)
    if (!user) {
      throw createBusinessError(
        ERROR_CODES.USER_NOT_FOUND,
        '用户不存在',
        401
      )
    }

    // 检查用户状态
    if (user.status !== '激活') {
      throw createBusinessError(
        ERROR_CODES.USER_DISABLED,
        '用户账户已被禁用',
        401
      )
    }

    // 检查会员状态
    const isMember = checkMembershipStatus(user)
    if (!isMember) {
      logger.warn('User membership expired', { userId: user._id, username: user.username })
      // 注意：会员过期不阻止访问，但前端可以根据会员状态提示续费
    }

    // 检查可用次数（购买相关操作跳过此检查）
    if (!options.skipQuotaCheck && user.availableCount <= 0) {
      throw createBusinessError(
        ERROR_CODES.INSUFFICIENT_QUOTA,
        '可用次数不足，请充值',
        403
      )
    }

    // 更新最后登录时间
    await userCollection.update(user._id, {
      lastLoginAt: new Date()
    })

    logger.info('User authenticated successfully', {
      userId: user._id,
      username: user.username,
      isMember,
      availableCount: user.availableCount,
      skipQuotaCheck: options.skipQuotaCheck
    })

    // 返回用户信息（不包含敏感信息）
    return {
      userId: user._id,
      username: user.username,
      membership: user.membership,
      availableCount: user.availableCount,
      isMember
    }

  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw createBusinessError(ERROR_CODES.TOKEN_EXPIRED, 'Token已过期', 401);
    }
    if (error.name === 'JsonWebTokenError') {
      throw createBusinessError(ERROR_CODES.TOKEN_INVALID, 'Token无效', 401);
    }
    logger.error('Authentication failed', {
      error: error.message,
      params: { ...params, authorization: '[HIDDEN]' }
    })
    throw error
  }
}

/**
 * 生成Token对
 * @param {string} userId 用户ID
 * @param {string} username 用户名
 * @returns {object} Token对象
 */
function generateTokenPair(userId, username) {
  const payload = { userId, username }
  
  const accessToken = generateAccessToken(payload)
  const refreshToken = generateRefreshToken(payload)
  
  return {
    accessToken,
    refreshToken,
    expiresIn: JWT_EXPIRES_IN
  }
}

module.exports = {
  authMiddleware,
  generateAccessToken,
  generateRefreshToken,
  generateTokenPair,
  verifyToken,
  checkMembershipStatus,
  JWT_SECRET,
  JWT_EXPIRES_IN,
  REFRESH_TOKEN_EXPIRES_IN
}