#!/bin/bash

# Go Proxy 打包脚本
# 将部署所需的文件打包成 tar.gz

echo "=== Go Proxy 打包脚本 ==="

# 检查必要文件
if [ ! -f "go_proxy_linux" ]; then
    echo "错误: 找不到 go_proxy_linux 可执行文件"
    echo "请先运行编译命令"
    exit 1
fi

if [ ! -f ".env" ]; then
    echo "警告: 找不到 .env 配置文件"
    echo "将使用 .env.example 作为模板"
    cp .env.example .env
fi

# 创建部署包
PACKAGE_NAME="go_proxy_deploy_$(date +%Y%m%d_%H%M%S).tar.gz"

echo "创建部署包: $PACKAGE_NAME"

# 打包文件
tar -czf "$PACKAGE_NAME" \
    go_proxy_linux \
    .env \
    deploy.sh \
    start.sh \
    stop.sh \
    go_proxy.service \
    README_DEPLOY.md

echo "打包完成: $PACKAGE_NAME"
echo ""
echo "=== 部署步骤 ==="
echo "1. 上传 $PACKAGE_NAME 到服务器"
echo "2. 解压: tar -xzf $PACKAGE_NAME"
echo "3. 进入目录并运行: ./deploy.sh"
echo "4. 启动服务: ./start.sh"
echo ""
echo "文件列表:"
tar -tzf "$PACKAGE_NAME"
