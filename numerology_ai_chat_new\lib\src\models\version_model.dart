/// 版本信息模型
class VersionInfo {
  final String versionNumber;
  final String versionName;
  final bool isAvailable;
  final bool forceUpdate;
  final String downloadUrl;
  final String updateDescription;
  final String releaseNotes;
  final bool isActive;
  final DateTime? publishedAt;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? createdBy;
  final String? updatedBy;

  const VersionInfo({
    required this.versionNumber,
    required this.versionName,
    required this.isAvailable,
    required this.forceUpdate,
    required this.downloadUrl,
    required this.updateDescription,
    required this.releaseNotes,
    required this.isActive,
    this.publishedAt,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
  });

  factory VersionInfo.fromJson(Map<String, dynamic> json) {
    return VersionInfo(
      versionNumber: json['versionNumber'] as String,
      versionName: json['versionName'] as String,
      isAvailable: json['isAvailable'] as bool,
      forceUpdate: json['forceUpdate'] as bool,
      downloadUrl: json['downloadUrl'] as String,
      updateDescription: json['updateDescription'] as String,
      releaseNotes: json['releaseNotes'] as String,
      isActive: json['isActive'] as bool,
      publishedAt: json['publishedAt'] != null 
          ? DateTime.parse(json['publishedAt'] as String)
          : null,
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      createdBy: json['createdBy'] as String?,
      updatedBy: json['updatedBy'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'versionNumber': versionNumber,
      'versionName': versionName,
      'isAvailable': isAvailable,
      'forceUpdate': forceUpdate,
      'downloadUrl': downloadUrl,
      'updateDescription': updateDescription,
      'releaseNotes': releaseNotes,
      'isActive': isActive,
      if (publishedAt != null) 'publishedAt': publishedAt!.toIso8601String(),
      if (createdAt != null) 'createdAt': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updatedAt': updatedAt!.toIso8601String(),
      if (createdBy != null) 'createdBy': createdBy,
      if (updatedBy != null) 'updatedBy': updatedBy,
    };
  }

  @override
  String toString() {
    return 'VersionInfo(versionNumber: $versionNumber, versionName: $versionName, isAvailable: $isAvailable)';
  }
}

/// 版本检查响应数据
class VersionCheckResponse {
  final bool isAvailable;
  final bool hasUpdate;
  final bool needsForceUpdate;
  final String currentVersion;
  final String latestVersion;
  final String downloadUrl;
  final String updateDescription;
  final String releaseNotes;
  final String versionName;

  const VersionCheckResponse({
    required this.isAvailable,
    required this.hasUpdate,
    required this.needsForceUpdate,
    required this.currentVersion,
    required this.latestVersion,
    required this.downloadUrl,
    required this.updateDescription,
    required this.releaseNotes,
    required this.versionName,
  });

  factory VersionCheckResponse.fromJson(Map<String, dynamic> json) {
    return VersionCheckResponse(
      isAvailable: json['isAvailable'] as bool,
      hasUpdate: json['hasUpdate'] as bool,
      needsForceUpdate: json['needsForceUpdate'] as bool,
      currentVersion: json['currentVersion'] as String,
      latestVersion: json['latestVersion'] as String,
      downloadUrl: json['downloadUrl'] as String,
      updateDescription: json['updateDescription'] as String,
      releaseNotes: json['releaseNotes'] as String,
      versionName: json['versionName'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'isAvailable': isAvailable,
      'hasUpdate': hasUpdate,
      'needsForceUpdate': needsForceUpdate,
      'currentVersion': currentVersion,
      'latestVersion': latestVersion,
      'downloadUrl': downloadUrl,
      'updateDescription': updateDescription,
      'releaseNotes': releaseNotes,
      'versionName': versionName,
    };
  }

  @override
  String toString() {
    return 'VersionCheckResponse(currentVersion: $currentVersion, latestVersion: $latestVersion, hasUpdate: $hasUpdate, needsForceUpdate: $needsForceUpdate)';
  }
}

/// 更新信息详情
class UpdateInfo {
  final String version;
  final String versionName;
  final String description;
  final String releaseNotes;
  final String downloadUrl;
  final bool isForced;
  final DateTime? releaseDate;

  const UpdateInfo({
    required this.version,
    required this.versionName,
    required this.description,
    required this.releaseNotes,
    required this.downloadUrl,
    required this.isForced,
    this.releaseDate,
  });

  factory UpdateInfo.fromVersionCheckResponse(VersionCheckResponse response) {
    return UpdateInfo(
      version: response.latestVersion,
      versionName: response.versionName,
      description: response.updateDescription,
      releaseNotes: response.releaseNotes,
      downloadUrl: response.downloadUrl,
      isForced: response.needsForceUpdate,
    );
  }

  factory UpdateInfo.fromJson(Map<String, dynamic> json) {
    return UpdateInfo(
      version: json['version'] as String,
      versionName: json['versionName'] as String,
      description: json['description'] as String,
      releaseNotes: json['releaseNotes'] as String,
      downloadUrl: json['downloadUrl'] as String,
      isForced: json['isForced'] as bool,
      releaseDate: json['releaseDate'] != null 
          ? DateTime.parse(json['releaseDate'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'version': version,
      'versionName': versionName,
      'description': description,
      'releaseNotes': releaseNotes,
      'downloadUrl': downloadUrl,
      'isForced': isForced,
      if (releaseDate != null) 'releaseDate': releaseDate!.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'UpdateInfo(version: $version, versionName: $versionName, isForced: $isForced)';
  }
}

/// 版本检查状态枚举
enum VersionCheckStatus {
  /// 未检查
  notChecked,
  /// 检查中
  checking,
  /// 检查完成
  completed,
  /// 检查失败
  failed,
}

/// 版本检查结果
class VersionCheckResult {
  final VersionCheckStatus status;
  final VersionCheckResponse? response;
  final String? error;
  final DateTime? checkedAt;

  const VersionCheckResult({
    required this.status,
    this.response,
    this.error,
    this.checkedAt,
  });

  /// 创建检查中状态
  factory VersionCheckResult.checking() {
    return const VersionCheckResult(
      status: VersionCheckStatus.checking,
    );
  }

  /// 创建成功状态
  factory VersionCheckResult.success(VersionCheckResponse response) {
    return VersionCheckResult(
      status: VersionCheckStatus.completed,
      response: response,
      checkedAt: DateTime.now(),
    );
  }

  /// 创建失败状态
  factory VersionCheckResult.failure(String error) {
    return VersionCheckResult(
      status: VersionCheckStatus.failed,
      error: error,
      checkedAt: DateTime.now(),
    );
  }

  /// 是否检查成功
  bool get isSuccess => status == VersionCheckStatus.completed && response != null;

  /// 是否检查失败
  bool get isFailure => status == VersionCheckStatus.failed;

  /// 是否正在检查
  bool get isChecking => status == VersionCheckStatus.checking;

  /// 是否有更新
  bool get hasUpdate => response?.hasUpdate ?? false;

  /// 是否需要强制更新
  bool get needsForceUpdate => response?.needsForceUpdate ?? false;

  @override
  String toString() {
    return 'VersionCheckResult(status: $status, hasUpdate: $hasUpdate, error: $error)';
  }
}
