import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:collection/collection.dart';
import '../models/ai_model.dart';
import '../services/model_service.dart';
import 'auth_provider.dart';
import 'settings_provider.dart';

/// 模型状态
enum ModelLoadingState {
  initial,
  loading,
  loaded,
  error,
}

/// 模型状态数据
class ModelState {
  final ModelLoadingState loadingState;
  final List<AIModel> models;
  final String? error;
  final DateTime? lastUpdated;

  const ModelState({
    this.loadingState = ModelLoadingState.initial,
    this.models = const [],
    this.error,
    this.lastUpdated,
  });

  ModelState copyWith({
    ModelLoadingState? loadingState,
    List<AIModel>? models,
    String? error,
    DateTime? lastUpdated,
  }) {
    return ModelState(
      loadingState: loadingState ?? this.loadingState,
      models: models ?? this.models,
      error: error,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  bool get isLoading => loadingState == ModelLoadingState.loading;
  bool get isLoaded => loadingState == ModelLoadingState.loaded;
  bool get hasError => loadingState == ModelLoadingState.error;
  bool get isEmpty => models.isEmpty;
  bool get isNotEmpty => models.isNotEmpty;
}

/// 模型状态通知器
class ModelNotifier extends StateNotifier<ModelState> {
  ModelNotifier(this._modelService) : super(const ModelState());

  final ModelService _modelService;

  /// 加载模型列表
  Future<void> loadModels(String token, {bool forceRefresh = false}) async {
    if (state.isLoading) return;

    state = state.copyWith(
      loadingState: ModelLoadingState.loading,
      error: null,
    );

    try {
      final models = await _modelService.getModels(
        token: token,
        forceRefresh: forceRefresh,
      );

      state = state.copyWith(
        loadingState: ModelLoadingState.loaded,
        models: models,
        lastUpdated: DateTime.now(),
      );
    } catch (e) {
      state = state.copyWith(
        loadingState: ModelLoadingState.error,
        error: e.toString(),
      );
    }
  }

  /// 刷新模型列表
  Future<void> refreshModels(String token) async {
    await loadModels(token, forceRefresh: true);
  }

  /// 根据ID获取模型
  AIModel? getModelById(String modelId) {
    try {
      return state.models.firstWhere((model) => model.id == modelId);
    } catch (e) {
      return null;
    }
  }

  /// 根据模型名称获取模型
  AIModel? getModelByName(String modelName) {
    try {
      return state.models.firstWhere((model) => model.modelName == modelName);
    } catch (e) {
      return null;
    }
  }

  /// 搜索模型
  List<AIModel> searchModels(String query) {
    if (query.isEmpty) return state.models;

    final lowerQuery = query.toLowerCase();
    return state.models.where((model) {
      return model.modelName.toLowerCase().contains(lowerQuery) ||
             model.modelDisplayName.toLowerCase().contains(lowerQuery) ||
             model.description.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// 获取推荐模型
  List<AIModel> getRecommendedModels() {
    return state.models.take(3).toList();
  }

  /// 按提供商分组模型
  Map<String, List<AIModel>> getModelsByProvider() {
    final Map<String, List<AIModel>> groupedModels = {};
    
    for (final model in state.models) {
      String provider = 'Unknown';
      
      if (model.modelName.toLowerCase().contains('gpt')) {
        provider = 'OpenAI';
      } else if (model.modelName.toLowerCase().contains('claude')) {
        provider = 'Anthropic';
      } else if (model.modelName.toLowerCase().contains('gemini')) {
        provider = 'Google';
      } else if (model.modelName.toLowerCase().contains('deepseek')) {
        provider = 'DeepSeek';
      }
      
      if (!groupedModels.containsKey(provider)) {
        groupedModels[provider] = [];
      }
      groupedModels[provider]!.add(model);
    }
    
    return groupedModels;
  }

  /// 清除缓存
  void clearCache() {
    _modelService.clearCache();
    state = const ModelState();
  }

  /// 重置状态
  void reset() {
    state = const ModelState();
  }
}

/// 模型服务提供者
final modelServiceProvider = Provider<ModelService>((ref) {
  return ModelService();
});

/// 模型状态提供者
final modelProvider = StateNotifierProvider<ModelNotifier, ModelState>((ref) {
  final modelService = ref.read(modelServiceProvider);
  return ModelNotifier(modelService);
});

/// 模型列表提供者
final modelsProvider = Provider<List<AIModel>>((ref) {
  return ref.watch(modelProvider).models;
});

/// 模型加载状态提供者
final modelLoadingStateProvider = Provider<ModelLoadingState>((ref) {
  return ref.watch(modelProvider).loadingState;
});

/// 模型错误提供者
final modelErrorProvider = Provider<String?>((ref) {
  return ref.watch(modelProvider).error;
});

/// 按提供商分组的模型提供者
final modelsByProviderProvider = Provider<Map<String, List<AIModel>>>((ref) {
  final modelNotifier = ref.read(modelProvider.notifier);
  return modelNotifier.getModelsByProvider();
});

/// 推荐模型提供者
final recommendedModelsProvider = Provider<List<AIModel>>((ref) {
  final models = ref.watch(modelsProvider);
  return models.take(3).toList();
});

/// 模型搜索提供者
final modelSearchProvider = StateProvider<String>((ref) => '');

/// 搜索结果模型提供者
final searchedModelsProvider = Provider<List<AIModel>>((ref) {
  final query = ref.watch(modelSearchProvider);
  final modelNotifier = ref.read(modelProvider.notifier);
  return modelNotifier.searchModels(query);
});

/// 自动加载模型提供者
final autoLoadModelsProvider = Provider<void>((ref) {
  final authState = ref.watch(authProvider);
  final modelNotifier = ref.read(modelProvider.notifier);
  
  // 当用户登录时自动加载模型
  if (authState.isAuthenticated && authState.token != null) {
    Future.microtask(() {
      modelNotifier.loadModels(authState.token!);
    });
  }
});

/// 模型是否已加载提供者
final modelsLoadedProvider = Provider<bool>((ref) {
  final modelState = ref.watch(modelProvider);
  return modelState.isLoaded && modelState.isNotEmpty;
});

/// 模型加载中提供者
final modelsLoadingProvider = Provider<bool>((ref) {
  return ref.watch(modelProvider).isLoading;
});

/// 模型加载失败提供者
final modelsErrorProvider = Provider<bool>((ref) {
  return ref.watch(modelProvider).hasError;
});

/// 可用模型提供者（只返回激活的模型）
final availableModelsProvider = Provider<List<AIModel>>((ref) {
  final models = ref.watch(modelsProvider);
  return models.where((model) => model.isAvailable).toList();
});

/// 默认模型提供者（根据用户设置返回默认模型）
final defaultModelProvider = Provider<AIModel?>((ref) {
  final availableModels = ref.watch(availableModelsProvider);
  final settings = ref.watch(settingsProvider);

  // 只有在用户设置了默认模型时才返回
  if (settings.defaultModelName != null && availableModels.isNotEmpty) {
    // 查找用户设置的默认模型
    final defaultModel = availableModels.firstWhereOrNull((m) => m.modelName == settings.defaultModelName);
    return defaultModel;
  }

  // 如果用户没有设置默认模型，返回 null
  return null;
});
