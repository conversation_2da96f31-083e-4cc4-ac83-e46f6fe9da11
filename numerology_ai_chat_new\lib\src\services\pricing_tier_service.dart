import '../models/pricing_tier.dart';
import '../models/api_response.dart';
import '../core/constants/app_constants.dart';
import 'cloud_function_service.dart';
import 'storage_service.dart';

/// 档次配置服务
class PricingTierService {
  final CloudFunctionService _cloudFunctionService = CloudFunctionService();
  final StorageService _storageService = StorageService();

  // 缓存
  List<PricingTier>? _cachedPricingTiers;
  DateTime? _lastCacheTime;

  /// 获取档次配置列表
  Future<List<PricingTier>> getPricingTiers({
    required String token,
    bool forceRefresh = false,
  }) async {
    // 检查缓存
    if (!forceRefresh && _cachedPricingTiers != null && _lastCacheTime != null) {
      final cacheAge = DateTime.now().difference(_lastCacheTime!);
      if (cacheAge < AppConstants.cacheExpiry) {
        return _cachedPricingTiers!;
      }
    }

    try {
      final response = await _cloudFunctionService.getPricingTiers(token: token);
      
      if (response.isSuccess && response.data?.success == true) {
        final tiersData = response.data?.data?.pricingTiers ?? [];
        final tiers = tiersData
            .map((tierJson) => PricingTier.fromJson(tierJson as Map<String, dynamic>))
            .where((tier) => tier.isActive) // 只返回激活的档次
            .toList();

        // 按排序权重排序
        tiers.sort((a, b) => a.sortOrder.compareTo(b.sortOrder));

        // 更新缓存
        _cachedPricingTiers = tiers;
        _lastCacheTime = DateTime.now();

        // 保存到本地存储
        await _savePricingTiersToStorage(tiers);

        return tiers;
      } else {
        // 如果云函数调用失败，尝试从本地存储加载
        return await _loadPricingTiersFromStorage();
      }
    } catch (e) {
      // 网络错误时从本地存储加载，如果本地也没有则抛出异常
      final localTiers = await _loadPricingTiersFromStorage();
      if (localTiers.isEmpty) {
        throw Exception('无法加载档次配置列表，请检查网络连接');
      }
      return localTiers;
    }
  }

  /// 根据ID获取档次配置
  Future<PricingTier?> getPricingTierById(String tierId, String token) async {
    final tiers = await getPricingTiers(token: token);
    try {
      return tiers.firstWhere((tier) => tier.id == tierId);
    } catch (e) {
      return null;
    }
  }

  /// 清除缓存
  void clearCache() {
    _cachedPricingTiers = null;
    _lastCacheTime = null;
  }

  /// 保存档次配置到本地存储
  Future<void> _savePricingTiersToStorage(List<PricingTier> tiers) async {
    try {
      final tiersJson = tiers.map((tier) => tier.toJson()).toList();
      await _storageService.setJsonList('cached_pricing_tiers', tiersJson);
      await _storageService.setString('pricing_tiers_cache_time', DateTime.now().toIso8601String());
    } catch (e) {
      print('PricingTierService: 保存档次配置到本地存储失败: $e');
    }
  }

  /// 从本地存储加载档次配置
  Future<List<PricingTier>> _loadPricingTiersFromStorage() async {
    try {
      final tiersJson = await _storageService.getJsonList('cached_pricing_tiers');
      final cacheTimeStr = await _storageService.getString('pricing_tiers_cache_time');

      if (tiersJson != null && cacheTimeStr != null) {
        final cacheTime = DateTime.parse(cacheTimeStr);
        final cacheAge = DateTime.now().difference(cacheTime);

        // 如果缓存时间超过7天，则不使用本地缓存
        if (cacheAge.inDays > 7) {
          return [];
        }

        final tiersList = tiersJson
            .map((tierJson) => PricingTier.fromJson(tierJson))
            .toList();

        print('PricingTierService: 从本地存储加载档次配置成功，数量: ${tiersList.length}');
        return tiersList;
      }

      return [];
    } catch (e) {
      print('PricingTierService: 从本地存储加载档次配置失败: $e');
      return [];
    }
  }
}
