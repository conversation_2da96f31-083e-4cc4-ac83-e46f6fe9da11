package proxy

import (
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"go_proxy/internal/services/database"

	"github.com/gin-gonic/gin"
)

// ImageAttachment 图片附件结构
type ImageAttachment struct {
	ID         string `json:"id"`
	Base64Data string `json:"base64_data"`
	MimeType   string `json:"mime_type"`
	Width      int    `json:"width"`
	Height     int    `json:"height"`
	FileSize   int    `json:"file_size"`
}

// ChatMessage 聊天消息结构
type ChatMessage struct {
	Role        string            `json:"role"`
	Content     string            `json:"content"`
	BaziData    string            `json:"baziData,omitempty"`    // 八字数据
	Images      []ImageAttachment `json:"images,omitempty"`      // 图片数据
	MessageType string            `json:"message_type,omitempty"` // 消息类型
}

// ChatRequest 聊天请求结构
type ChatRequest struct {
	AgentID  string        `json:"agentId"`
	ModelID  string        `json:"modelId"`
	Messages []ChatMessage `json:"messages"`
	Stream   bool          `json:"stream,omitempty"`
}

// LLMRequest LLM API请求结构
type LLMRequest struct {
	Model       string        `json:"model"`
	Messages    []ChatMessage `json:"messages"`
	Stream      bool          `json:"stream"`
	MaxTokens   int           `json:"max_tokens,omitempty"`
	Temperature float64       `json:"temperature,omitempty"`
}

// LLMProxy LLM代理结构
type LLMProxy struct {
	dbClient   *database.Client
	httpClient *http.Client
}

// NewLLMProxy 创建新的LLM代理
func NewLLMProxy(dbClient *database.Client) *LLMProxy {
	return &LLMProxy{
		dbClient: dbClient,
		httpClient: &http.Client{
			Timeout: 300 * time.Second, // 增加到5分钟
		},
	}
}

// ProxyRequest 代理聊天请求
func (p *LLMProxy) ProxyRequest(c *gin.Context, chatReq *ChatRequest, token string) error {
	// 获取智能体信息
	agent, err := p.dbClient.GetAgent(chatReq.AgentID, token)
	if err != nil {
		return fmt.Errorf("failed to get agent: %w", err)
	}

	// 获取模型信息
	model, err := p.dbClient.GetModel(chatReq.ModelID, token)
	if err != nil {
		return fmt.Errorf("failed to get model: %w", err)
	}

	// 检查是否需要大白话版本
	if agent.NeedLaymanVersion && agent.LaymanPrompt != "" {
		return p.handleLaymanVersionRequest(c, agent, model, chatReq)
	}

	// 普通请求处理
	return p.handleNormalRequest(c, agent, model, chatReq)
}

// buildMessages 构造完整的消息列表
func (p *LLMProxy) buildMessages(agent *database.Agent, userMessages []ChatMessage) []ChatMessage {
	messages := make([]ChatMessage, 0, len(userMessages)+1)

	// 构建系统提示词
	systemPrompt := agent.AgentPrompt

	// 检查是否需要八字数据
	if agent.AgentType == "八字" {
		// 查找最新的用户消息中的八字数据
		var latestBaziData string
		for i := len(userMessages) - 1; i >= 0; i-- {
			if userMessages[i].BaziData != "" {
				latestBaziData = userMessages[i].BaziData
				break
			}
		}

		// 如果找到八字数据，添加到系统提示词中
		if latestBaziData != "" {
			systemPrompt += "\n\n用户的八字信息：\n" + latestBaziData
		}
	}

	// 添加系统提示词
	if systemPrompt != "" {
		messages = append(messages, ChatMessage{
			Role:    "system",
			Content: systemPrompt,
		})
	}

	// 添加用户消息（保留图片数据和消息类型）
	for _, msg := range userMessages {
		// 创建新的消息，保留所有字段
		newMsg := ChatMessage{
			Role:        msg.Role,
			Content:     msg.Content,
			Images:      msg.Images,      // 保留图片数据
			MessageType: msg.MessageType, // 保留消息类型
		}
		messages = append(messages, newMsg)
	}

	return messages
}

// isMultimodalModel 检查模型是否支持多模态（图片）
func (p *LLMProxy) isMultimodalModel(modelName string) bool {
	// 支持多模态的模型列表
	multimodalModels := []string{
		"gemini-2.5-pro",
		"gemini-2.5-pro-preview",
		"gemini-1.5-pro",
		"gemini-1.5-flash",
		"gpt-4-vision",
		"gpt-4o",
		"gpt-4o-mini",
		"claude-3-opus",
		"claude-3-sonnet",
		"claude-3-haiku",
	}

	for _, supportedModel := range multimodalModels {
		if strings.Contains(strings.ToLower(modelName), supportedModel) {
			return true
		}
	}
	return false
}

// convertToMultimodalFormat 将消息转换为多模态格式
func (p *LLMProxy) convertToMultimodalFormat(messages []ChatMessage, modelName string) []map[string]interface{} {
	result := make([]map[string]interface{}, 0, len(messages))

	for _, msg := range messages {
		if len(msg.Images) == 0 {
			// 纯文本消息
			result = append(result, map[string]interface{}{
				"role":    msg.Role,
				"content": msg.Content,
			})
		} else {
			// 包含图片的消息
			if strings.Contains(strings.ToLower(modelName), "gemini") {
				// Gemini格式：content是数组
				content := []map[string]interface{}{}

				// 添加文本部分
				if msg.Content != "" {
					content = append(content, map[string]interface{}{
						"type": "text",
						"text": msg.Content,
					})
				}

				// 添加图片部分
				for _, img := range msg.Images {
					content = append(content, map[string]interface{}{
						"type": "image_url",
						"image_url": map[string]interface{}{
							"url": img.Base64Data,
						},
					})
				}

				result = append(result, map[string]interface{}{
					"role":    msg.Role,
					"content": content,
				})
			} else {
				// OpenAI/Claude格式：content是数组
				content := []map[string]interface{}{}

				// 添加文本部分
				if msg.Content != "" {
					content = append(content, map[string]interface{}{
						"type": "text",
						"text": msg.Content,
					})
				}

				// 添加图片部分
				for _, img := range msg.Images {
					content = append(content, map[string]interface{}{
						"type": "image_url",
						"image_url": map[string]interface{}{
							"url": img.Base64Data,
						},
					})
				}

				result = append(result, map[string]interface{}{
					"role":    msg.Role,
					"content": content,
				})
			}
		}
	}

	return result
}

// forwardRequest 转发请求到LLM API
func (p *LLMProxy) forwardRequest(c *gin.Context, model *database.Model, llmReq *LLMRequest) error {
	// 检查是否有图片消息
	hasImages := false
	for _, msg := range llmReq.Messages {
		if len(msg.Images) > 0 {
			hasImages = true
			break
		}
	}

	var reqBody []byte
	var err error

	if hasImages && p.isMultimodalModel(model.ModelName) {
		// 多模态模型：转换消息格式
		multimodalMessages := p.convertToMultimodalFormat(llmReq.Messages, model.ModelName)

		// 构建多模态请求
		multimodalReq := map[string]interface{}{
			"model":       llmReq.Model,
			"messages":    multimodalMessages,
			"stream":      llmReq.Stream,
			"max_tokens":  llmReq.MaxTokens,
			"temperature": llmReq.Temperature,
		}

		reqBody, err = json.Marshal(multimodalReq)
		if err != nil {
			return fmt.Errorf("failed to marshal multimodal request: %w", err)
		}
	} else if hasImages && !p.isMultimodalModel(model.ModelName) {
		// 不支持多模态的模型：移除图片，只保留文本
		textOnlyMessages := make([]ChatMessage, 0, len(llmReq.Messages))
		for _, msg := range llmReq.Messages {
			if len(msg.Images) > 0 {
				// 如果有图片，添加提示信息
				content := msg.Content
				if content == "" {
					content = "[用户发送了图片，但当前模型不支持图片分析]"
				} else {
					content += "\n[注：用户还发送了图片，但当前模型不支持图片分析]"
				}
				textOnlyMessages = append(textOnlyMessages, ChatMessage{
					Role:    msg.Role,
					Content: content,
				})
			} else {
				textOnlyMessages = append(textOnlyMessages, msg)
			}
		}

		// 构建纯文本请求
		textOnlyReq := LLMRequest{
			Model:       llmReq.Model,
			Messages:    textOnlyMessages,
			Stream:      llmReq.Stream,
			MaxTokens:   llmReq.MaxTokens,
			Temperature: llmReq.Temperature,
		}

		reqBody, err = json.Marshal(textOnlyReq)
		if err != nil {
			return fmt.Errorf("failed to marshal text-only request: %w", err)
		}
	} else {
		// 纯文本消息：使用原始格式
		reqBody, err = json.Marshal(llmReq)
		if err != nil {
			return fmt.Errorf("failed to marshal request: %w", err)
		}
	}

	// 使用客户端的上下文创建HTTP请求
	req, err := http.NewRequestWithContext(c.Request.Context(), "POST", model.ModelApiUrl, bytes.NewBuffer(reqBody))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+model.ModelApiKey)
	req.Header.Set("Accept", "text/event-stream")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Connection", "keep-alive")

	// 发送请求
	resp, err := p.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("LLM API returned status %d: %s", resp.StatusCode, string(body))
	}

	// 设置响应头为流式传输
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Headers", "Cache-Control")
	c.Header("X-Accel-Buffering", "no") // 禁用nginx缓冲

	// 流式转发响应
	return p.streamResponse(c, resp)
}

// streamResponse 流式转发响应
func (p *LLMProxy) streamResponse(c *gin.Context, resp *http.Response) error {
	scanner := bufio.NewScanner(resp.Body)
	// 增加缓冲区大小以处理大的响应块
	scanner.Buffer(make([]byte, 0, 64*1024), 1024*1024)

	flusher, ok := c.Writer.(http.Flusher)
	if !ok {
		return fmt.Errorf("streaming not supported")
	}

	// 检查客户端连接状态
	clientGone := c.Request.Context().Done()

	for scanner.Scan() {
		// 检查客户端是否断开连接
		select {
		case <-clientGone:
			return fmt.Errorf("client disconnected")
		default:
		}

		line := scanner.Text()

		// 跳过空行
		if strings.TrimSpace(line) == "" {
			continue
		}

		// 写入响应
		if _, err := c.Writer.Write([]byte(line + "\n")); err != nil {
			return fmt.Errorf("failed to write response: %w", err)
		}

		// 立即刷新缓冲区
		flusher.Flush()

		// 检查是否为结束标记
		if strings.Contains(line, "[DONE]") {
			break
		}
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("error reading response: %w", err)
	}

	return nil
}

// handleNormalRequest 处理普通请求
func (p *LLMProxy) handleNormalRequest(c *gin.Context, agent *database.Agent, model *database.Model, chatReq *ChatRequest) error {
	// 构造完整的消息列表
	messages := p.buildMessages(agent, chatReq.Messages)

	// 构造LLM请求
	llmReq := LLMRequest{
		Model:       model.ModelName,
		Messages:    messages,
		Stream:      true, // 强制使用流式响应
		MaxTokens:   model.MaxTokens,
		Temperature: model.Temperature,
	}

	// 发起代理请求
	return p.forwardRequest(c, model, &llmReq)
}

// handleLaymanVersionRequest 处理大白话版本请求（流式）
func (p *LLMProxy) handleLaymanVersionRequest(c *gin.Context, agent *database.Agent, model *database.Model, chatReq *ChatRequest) error {
	// 设置流式响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Headers", "Content-Type,Authorization")

	// 获取响应写入器
	w := c.Writer
	flusher, ok := w.(http.Flusher)
	if !ok {
		return fmt.Errorf("streaming unsupported")
	}

	// 第一阶段：流式生成专业版本
	professionalResult, err := p.streamProfessionalResult(w, flusher, agent, model, chatReq)
	if err != nil {
		p.writeSSEError(w, flusher, fmt.Sprintf("专业版本生成失败: %v", err))
		return nil
	}

	// 发送专业版本完成标记，包含完整内容
	p.writeSSEStageComplete(w, flusher, "professional", professionalResult)

	// 第二阶段：流式生成大白话版本（移除多余的transition消息，streamLaymanResult内部会发送）
	laymanResult, err := p.streamLaymanResult(w, flusher, agent, model, professionalResult)
	if err != nil {
		p.writeSSEError(w, flusher, fmt.Sprintf("大白话版本生成失败: %v", err))
		return nil
	}

	// 发送大白话版本完成标记，包含完整内容
	p.writeSSEStageComplete(w, flusher, "layman", laymanResult)

	// 发送最终完成标记
	p.writeSSEComplete(w, flusher, professionalResult)
	return nil
}

// getProfessionalResult 获取专业版结果
func (p *LLMProxy) getProfessionalResult(agent *database.Agent, model *database.Model, chatReq *ChatRequest) (string, error) {
	// 构造完整的消息列表
	messages := p.buildMessages(agent, chatReq.Messages)

	// 构造LLM请求（非流式）
	llmReq := LLMRequest{
		Model:       model.ModelName,
		Messages:    messages,
		Stream:      false, // 非流式响应
		MaxTokens:   model.MaxTokens,
		Temperature: model.Temperature,
	}

	// 发起请求并获取完整响应
	return p.callLLMAPI(model, &llmReq)
}

// getLaymanResult 获取大白话版本结果
func (p *LLMProxy) getLaymanResult(agent *database.Agent, model *database.Model, professionalResult string) (string, error) {
	// 构造大白话润色消息
	laymanMessages := []ChatMessage{
		{
			Role:    "system",
			Content: agent.LaymanPrompt,
		},
		{
			Role:    "user",
			Content: professionalResult,
		},
	}

	// 使用大白话专用模型参数（如果配置了的话）
	var laymanModelName string
	var laymanMaxTokens int
	var laymanTemperature float64

	if agent.LaymanModelName != "" {
		laymanModelName = agent.LaymanModelName
	} else {
		laymanModelName = model.ModelName
	}

	if agent.LaymanMaxTokens > 0 {
		laymanMaxTokens = agent.LaymanMaxTokens
	} else {
		laymanMaxTokens = model.MaxTokens
	}

	if agent.LaymanTemperature > 0 {
		laymanTemperature = agent.LaymanTemperature
	} else {
		laymanTemperature = model.Temperature
	}

	// 构造LLM请求（非流式）
	llmReq := LLMRequest{
		Model:       laymanModelName,
		Messages:    laymanMessages,
		Stream:      false, // 非流式响应
		MaxTokens:   laymanMaxTokens,
		Temperature: laymanTemperature,
	}

	// 如果配置了大白话专用API，使用专用API调用
	if agent.LaymanApiKey != "" && agent.LaymanApiUrl != "" {
		return p.callLaymanLLMAPI(agent, &llmReq)
	}

	// 否则使用原有模型的API
	return p.callLLMAPI(model, &llmReq)
}

// callLLMAPI 调用LLM API并获取完整响应
func (p *LLMProxy) callLLMAPI(model *database.Model, llmReq *LLMRequest) (string, error) {
	// 检查是否有图片消息
	hasImages := false
	for _, msg := range llmReq.Messages {
		if len(msg.Images) > 0 {
			hasImages = true
			break
		}
	}

	var reqBody []byte
	var err error

	if hasImages && p.isMultimodalModel(model.ModelName) {
		// 多模态模型：转换消息格式
		multimodalMessages := p.convertToMultimodalFormat(llmReq.Messages, model.ModelName)

		// 构建多模态请求
		multimodalReq := map[string]interface{}{
			"model":       llmReq.Model,
			"messages":    multimodalMessages,
			"stream":      llmReq.Stream,
			"max_tokens":  llmReq.MaxTokens,
			"temperature": llmReq.Temperature,
		}

		reqBody, err = json.Marshal(multimodalReq)
		if err != nil {
			return "", fmt.Errorf("failed to marshal multimodal request: %w", err)
		}
	} else if hasImages && !p.isMultimodalModel(model.ModelName) {
		// 不支持多模态的模型：移除图片，只保留文本
		textOnlyMessages := make([]ChatMessage, 0, len(llmReq.Messages))
		for _, msg := range llmReq.Messages {
			if len(msg.Images) > 0 {
				// 如果有图片，添加提示信息
				content := msg.Content
				if content == "" {
					content = "[用户发送了图片，但当前模型不支持图片分析]"
				} else {
					content += "\n[注：用户还发送了图片，但当前模型不支持图片分析]"
				}
				textOnlyMessages = append(textOnlyMessages, ChatMessage{
					Role:    msg.Role,
					Content: content,
				})
			} else {
				textOnlyMessages = append(textOnlyMessages, msg)
			}
		}

		// 构建纯文本请求
		textOnlyReq := LLMRequest{
			Model:       llmReq.Model,
			Messages:    textOnlyMessages,
			Stream:      llmReq.Stream,
			MaxTokens:   llmReq.MaxTokens,
			Temperature: llmReq.Temperature,
		}

		reqBody, err = json.Marshal(textOnlyReq)
		if err != nil {
			return "", fmt.Errorf("failed to marshal text-only request: %w", err)
		}
	} else {
		// 纯文本消息：使用原始格式
		reqBody, err = json.Marshal(llmReq)
		if err != nil {
			return "", fmt.Errorf("failed to marshal request: %w", err)
		}
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", model.ModelApiUrl, bytes.NewBuffer(reqBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+model.ModelApiKey)

	// 发送请求
	resp, err := p.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("LLM API returned status %d: %s", resp.StatusCode, string(body))
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	// 解析响应
	var response map[string]interface{}
	if err := json.Unmarshal(body, &response); err != nil {
		return "", fmt.Errorf("failed to unmarshal response: %w", err)
	}

	// 提取内容
	choices, ok := response["choices"].([]interface{})
	if !ok || len(choices) == 0 {
		return "", fmt.Errorf("invalid response format: no choices")
	}

	choice, ok := choices[0].(map[string]interface{})
	if !ok {
		return "", fmt.Errorf("invalid choice format")
	}

	message, ok := choice["message"].(map[string]interface{})
	if !ok {
		return "", fmt.Errorf("invalid message format")
	}

	content, ok := message["content"].(string)
	if !ok {
		return "", fmt.Errorf("invalid content format")
	}

	return content, nil
}

// streamProfessionalResult 流式生成专业版本
func (p *LLMProxy) streamProfessionalResult(w http.ResponseWriter, flusher http.Flusher, agent *database.Agent, model *database.Model, chatReq *ChatRequest) (string, error) {
	// 构造完整的消息列表
	messages := p.buildMessages(agent, chatReq.Messages)

	// 构造LLM请求（流式）
	llmReq := LLMRequest{
		Model:       model.ModelName,
		Messages:    messages,
		Stream:      true, // 流式响应
		MaxTokens:   model.MaxTokens,
		Temperature: model.Temperature,
	}

	// 发送阶段标识 - 确保在开始流式调用前发送
	p.writeSSEStage(w, flusher, "professional")

	// 发送阶段开始消息
	p.writeSSETransition(w, flusher, "正在生成专业版本...")

	// 流式调用API并收集结果，使用回调函数处理内容
	return p.streamLLMAPIWithCallback(w, flusher, model, &llmReq, func(content string) {
		// 在专业版本阶段，直接转发内容
		p.writeSSEData(w, flusher, content)
	})
}

// streamLaymanResult 流式生成大白话版本
func (p *LLMProxy) streamLaymanResult(w http.ResponseWriter, flusher http.Flusher, agent *database.Agent, model *database.Model, professionalResult string) (string, error) {
	// 构造大白话润色消息
	laymanMessages := []ChatMessage{
		{
			Role:    "system",
			Content: agent.LaymanPrompt,
		},
		{
			Role:    "user",
			Content: professionalResult,
		},
	}

	// 使用大白话专用模型参数（如果配置了的话）
	var laymanModelName string
	var laymanMaxTokens int
	var laymanTemperature float64

	if agent.LaymanModelName != "" {
		laymanModelName = agent.LaymanModelName
	} else {
		laymanModelName = model.ModelName
	}

	if agent.LaymanMaxTokens > 0 {
		laymanMaxTokens = agent.LaymanMaxTokens
	} else {
		laymanMaxTokens = model.MaxTokens
	}

	if agent.LaymanTemperature > 0 {
		laymanTemperature = agent.LaymanTemperature
	} else {
		laymanTemperature = model.Temperature
	}

	// 构造LLM请求（流式）
	llmReq := LLMRequest{
		Model:       laymanModelName,
		Messages:    laymanMessages,
		Stream:      true, // 流式响应
		MaxTokens:   laymanMaxTokens,
		Temperature: laymanTemperature,
	}

	// 发送阶段标识 - 确保在开始流式调用前发送
	p.writeSSEStage(w, flusher, "layman")

	// 发送阶段开始消息
	p.writeSSETransition(w, flusher, "正在生成大白话版本...")

	// 如果配置了大白话专用API，使用专用API调用
	if agent.LaymanApiKey != "" && agent.LaymanApiUrl != "" {
		return p.streamLaymanLLMAPIWithCallback(w, flusher, agent, &llmReq, func(content string) {
			// 在大白话阶段，直接转发内容
			p.writeSSEData(w, flusher, content)
		})
	}

	// 否则使用原有模型的API
	laymanResult, err := p.streamLLMAPIWithCallback(w, flusher, model, &llmReq, func(content string) {
		// 在大白话阶段，直接转发内容
		p.writeSSEData(w, flusher, content)
	})
	return laymanResult, err
}

// streamLLMAPIWithCallback 流式调用LLM API（带回调函数）
func (p *LLMProxy) streamLLMAPIWithCallback(w http.ResponseWriter, flusher http.Flusher, model *database.Model, llmReq *LLMRequest, contentCallback func(string)) (string, error) {
	// 检查是否有图片消息
	hasImages := false
	for _, msg := range llmReq.Messages {
		if len(msg.Images) > 0 {
			hasImages = true
			break
		}
	}

	var reqBody []byte
	var err error

	if hasImages && p.isMultimodalModel(model.ModelName) {
		// 多模态模型：转换消息格式
		multimodalMessages := p.convertToMultimodalFormat(llmReq.Messages, model.ModelName)

		// 构建多模态请求
		multimodalReq := map[string]interface{}{
			"model":       llmReq.Model,
			"messages":    multimodalMessages,
			"stream":      llmReq.Stream,
			"max_tokens":  llmReq.MaxTokens,
			"temperature": llmReq.Temperature,
		}

		reqBody, err = json.Marshal(multimodalReq)
		if err != nil {
			return "", fmt.Errorf("failed to marshal multimodal request: %w", err)
		}
	} else if hasImages && !p.isMultimodalModel(model.ModelName) {
		// 不支持多模态的模型：移除图片，只保留文本
		textOnlyMessages := make([]ChatMessage, 0, len(llmReq.Messages))
		for _, msg := range llmReq.Messages {
			if len(msg.Images) > 0 {
				// 如果有图片，添加提示信息
				content := msg.Content
				if content == "" {
					content = "[用户发送了图片，但当前模型不支持图片分析]"
				} else {
					content += "\n[注：用户还发送了图片，但当前模型不支持图片分析]"
				}
				textOnlyMessages = append(textOnlyMessages, ChatMessage{
					Role:    msg.Role,
					Content: content,
				})
			} else {
				textOnlyMessages = append(textOnlyMessages, msg)
			}
		}

		// 构建纯文本请求
		textOnlyReq := LLMRequest{
			Model:       llmReq.Model,
			Messages:    textOnlyMessages,
			Stream:      llmReq.Stream,
			MaxTokens:   llmReq.MaxTokens,
			Temperature: llmReq.Temperature,
		}

		reqBody, err = json.Marshal(textOnlyReq)
		if err != nil {
			return "", fmt.Errorf("failed to marshal text-only request: %w", err)
		}
	} else {
		// 纯文本消息：使用原始格式
		reqBody, err = json.Marshal(llmReq)
		if err != nil {
			return "", fmt.Errorf("failed to marshal request: %w", err)
		}
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", model.ModelApiUrl, bytes.NewBuffer(reqBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+model.ModelApiKey)

	// 发送请求
	resp, err := p.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("LLM API returned status %d: %s", resp.StatusCode, string(body))
	}

	// 处理流式响应
	return p.processStreamResponseWithCallback(resp, contentCallback)
}

// streamLLMAPI 流式调用LLM API
func (p *LLMProxy) streamLLMAPI(w http.ResponseWriter, flusher http.Flusher, model *database.Model, llmReq *LLMRequest) (string, error) {
	// 检查是否有图片消息
	hasImages := false
	for _, msg := range llmReq.Messages {
		if len(msg.Images) > 0 {
			hasImages = true
			break
		}
	}

	var reqBody []byte
	var err error

	if hasImages && p.isMultimodalModel(model.ModelName) {
		// 多模态模型：转换消息格式
		multimodalMessages := p.convertToMultimodalFormat(llmReq.Messages, model.ModelName)

		// 构建多模态请求
		multimodalReq := map[string]interface{}{
			"model":       llmReq.Model,
			"messages":    multimodalMessages,
			"stream":      llmReq.Stream,
			"max_tokens":  llmReq.MaxTokens,
			"temperature": llmReq.Temperature,
		}

		reqBody, err = json.Marshal(multimodalReq)
		if err != nil {
			return "", fmt.Errorf("failed to marshal multimodal request: %w", err)
		}
	} else if hasImages && !p.isMultimodalModel(model.ModelName) {
		// 不支持多模态的模型：移除图片，只保留文本
		textOnlyMessages := make([]ChatMessage, 0, len(llmReq.Messages))
		for _, msg := range llmReq.Messages {
			if len(msg.Images) > 0 {
				// 如果有图片，添加提示信息
				content := msg.Content
				if content == "" {
					content = "[用户发送了图片，但当前模型不支持图片分析]"
				} else {
					content += "\n[注：用户还发送了图片，但当前模型不支持图片分析]"
				}
				textOnlyMessages = append(textOnlyMessages, ChatMessage{
					Role:    msg.Role,
					Content: content,
				})
			} else {
				textOnlyMessages = append(textOnlyMessages, msg)
			}
		}

		// 构建纯文本请求
		textOnlyReq := LLMRequest{
			Model:       llmReq.Model,
			Messages:    textOnlyMessages,
			Stream:      llmReq.Stream,
			MaxTokens:   llmReq.MaxTokens,
			Temperature: llmReq.Temperature,
		}

		reqBody, err = json.Marshal(textOnlyReq)
		if err != nil {
			return "", fmt.Errorf("failed to marshal text-only request: %w", err)
		}
	} else {
		// 纯文本消息：使用原始格式
		reqBody, err = json.Marshal(llmReq)
		if err != nil {
			return "", fmt.Errorf("failed to marshal request: %w", err)
		}
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", model.ModelApiUrl, bytes.NewBuffer(reqBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+model.ModelApiKey)

	// 发送请求
	resp, err := p.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("LLM API returned status %d: %s", resp.StatusCode, string(body))
	}

	// 处理流式响应
	return p.processStreamResponse(w, flusher, resp)
}

// processStreamResponse 处理流式响应
func (p *LLMProxy) processStreamResponse(w http.ResponseWriter, flusher http.Flusher, resp *http.Response) (string, error) {
	scanner := bufio.NewScanner(resp.Body)
	var fullContent strings.Builder

	for scanner.Scan() {
		line := scanner.Text()
		if line == "" {
			continue
		}

		// 处理Server-Sent Events格式
		if strings.HasPrefix(line, "data: ") {
			data := line[6:] // 移除"data: "前缀

			// 检查是否为结束标记
			if data == "[DONE]" {
				break
			}

			// 解析JSON数据
			var jsonData map[string]interface{}
			if err := json.Unmarshal([]byte(data), &jsonData); err != nil {
				continue // 忽略解析错误，继续处理下一行
			}

			// 提取内容
			choices, ok := jsonData["choices"].([]interface{})
			if !ok || len(choices) == 0 {
				continue
			}

			choice, ok := choices[0].(map[string]interface{})
			if !ok {
				continue
			}

			delta, ok := choice["delta"].(map[string]interface{})
			if !ok {
				continue
			}

			content, ok := delta["content"].(string)
			if !ok {
				continue
			}

			// 累积完整内容
			fullContent.WriteString(content)

			// 转发给客户端
			p.writeSSEData(w, flusher, content)
		}
	}

	if err := scanner.Err(); err != nil {
		return "", fmt.Errorf("error reading stream: %w", err)
	}

	return fullContent.String(), nil
}

// processStreamResponseWithCallback 处理流式响应（带回调函数）
func (p *LLMProxy) processStreamResponseWithCallback(resp *http.Response, contentCallback func(string)) (string, error) {
	scanner := bufio.NewScanner(resp.Body)
	var fullContent strings.Builder

	for scanner.Scan() {
		line := scanner.Text()
		if line == "" {
			continue
		}

		// 处理Server-Sent Events格式
		if strings.HasPrefix(line, "data: ") {
			data := line[6:] // 移除"data: "前缀

			// 检查是否为结束标记
			if data == "[DONE]" {
				break
			}

			// 解析JSON数据
			var jsonData map[string]interface{}
			if err := json.Unmarshal([]byte(data), &jsonData); err != nil {
				continue // 忽略解析错误，继续处理下一行
			}

			// 提取内容
			choices, ok := jsonData["choices"].([]interface{})
			if !ok || len(choices) == 0 {
				continue
			}

			choice, ok := choices[0].(map[string]interface{})
			if !ok {
				continue
			}

			delta, ok := choice["delta"].(map[string]interface{})
			if !ok {
				continue
			}

			content, ok := delta["content"].(string)
			if !ok {
				continue
			}

			// 累积完整内容
			fullContent.WriteString(content)

			// 调用回调函数处理内容
			if contentCallback != nil {
				contentCallback(content)
			}
		}
	}

	if err := scanner.Err(); err != nil {
		return "", fmt.Errorf("error reading stream: %w", err)
	}

	return fullContent.String(), nil
}

// SSE辅助方法
func (p *LLMProxy) writeSSEData(w http.ResponseWriter, flusher http.Flusher, content string) {
	// 构造标准的SSE数据格式
	data := map[string]interface{}{
		"choices": []map[string]interface{}{
			{
				"delta": map[string]interface{}{
					"content": content,
				},
			},
		},
	}

	jsonData, _ := json.Marshal(data)
	fmt.Fprintf(w, "data: %s\n\n", string(jsonData))
	flusher.Flush()
}

func (p *LLMProxy) writeSSEStage(w http.ResponseWriter, flusher http.Flusher, stage string) {
	// 发送阶段标识
	stageData := map[string]interface{}{
		"stage": stage,
	}
	jsonData, _ := json.Marshal(stageData)
	fmt.Printf("发送阶段标识: %s\n", string(jsonData))
	fmt.Fprintf(w, "data: %s\n\n", string(jsonData))
	flusher.Flush()
}

func (p *LLMProxy) writeSSETransition(w http.ResponseWriter, flusher http.Flusher, message string) {
	// 发送阶段转换消息
	transitionData := map[string]interface{}{
		"transition": message,
	}
	jsonData, _ := json.Marshal(transitionData)
	fmt.Fprintf(w, "data: %s\n\n", string(jsonData))
	flusher.Flush()
}

func (p *LLMProxy) writeSSEStageTransition(w http.ResponseWriter, flusher http.Flusher, message string) {
	// 发送阶段转换消息（兼容旧方法名）
	p.writeSSETransition(w, flusher, message)
}

func (p *LLMProxy) writeSSEStageComplete(w http.ResponseWriter, flusher http.Flusher, stage string, content string) {
	// 发送阶段完成标记，包含该阶段的完整内容
	stageCompleteData := map[string]interface{}{
		"stage_complete": stage,
		"content":        content,
	}
	jsonData, _ := json.Marshal(stageCompleteData)
	fmt.Printf("发送阶段完成: %s, 内容长度: %d\n", stage, len(content))
	fmt.Fprintf(w, "data: %s\n\n", string(jsonData))
	flusher.Flush()
}

func (p *LLMProxy) writeSSEComplete(w http.ResponseWriter, flusher http.Flusher, professionalContent string) {
	// 发送完成标记，包含专业版本内容用于前端组合显示
	completeData := map[string]interface{}{
		"complete":             true,
		"professional_version": professionalContent,
	}
	jsonData, _ := json.Marshal(completeData)
	fmt.Fprintf(w, "data: %s\n\n", string(jsonData))
	fmt.Fprintf(w, "data: [DONE]\n\n")
	flusher.Flush()
}

func (p *LLMProxy) writeSSEError(w http.ResponseWriter, flusher http.Flusher, errorMsg string) {
	// 发送错误消息
	errorData := map[string]interface{}{
		"error": errorMsg,
	}
	jsonData, _ := json.Marshal(errorData)
	fmt.Fprintf(w, "data: %s\n\n", string(jsonData))
	fmt.Fprintf(w, "data: [DONE]\n\n")
	flusher.Flush()
}

// callLaymanLLMAPI 调用大白话专用LLM API并获取完整响应
func (p *LLMProxy) callLaymanLLMAPI(agent *database.Agent, llmReq *LLMRequest) (string, error) {
	// 检查是否有图片消息
	hasImages := false
	for _, msg := range llmReq.Messages {
		if len(msg.Images) > 0 {
			hasImages = true
			break
		}
	}

	var reqBody []byte
	var err error

	if hasImages {
		// 多模态模型：转换消息格式
		multimodalMessages := p.convertToMultimodalFormat(llmReq.Messages, agent.LaymanModelName)

		// 构建多模态请求
		multimodalReq := map[string]interface{}{
			"model":       llmReq.Model,
			"messages":    multimodalMessages,
			"stream":      llmReq.Stream,
			"max_tokens":  llmReq.MaxTokens,
			"temperature": llmReq.Temperature,
		}

		reqBody, err = json.Marshal(multimodalReq)
		if err != nil {
			return "", fmt.Errorf("failed to marshal multimodal request: %w", err)
		}
	} else {
		// 普通文本请求
		reqBody, err = json.Marshal(llmReq)
		if err != nil {
			return "", fmt.Errorf("failed to marshal request: %w", err)
		}
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", agent.LaymanApiUrl, bytes.NewBuffer(reqBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+agent.LaymanApiKey)

	// 发送请求
	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var response map[string]interface{}
	if err := json.Unmarshal(body, &response); err != nil {
		return "", fmt.Errorf("failed to unmarshal response: %w", err)
	}

	// 提取内容
	choices, ok := response["choices"].([]interface{})
	if !ok || len(choices) == 0 {
		return "", fmt.Errorf("no choices in response")
	}

	choice, ok := choices[0].(map[string]interface{})
	if !ok {
		return "", fmt.Errorf("invalid choice format")
	}

	message, ok := choice["message"].(map[string]interface{})
	if !ok {
		return "", fmt.Errorf("invalid message format")
	}

	content, ok := message["content"].(string)
	if !ok {
		return "", fmt.Errorf("invalid content format")
	}

	return content, nil
}

// streamLaymanLLMAPIWithCallback 流式调用大白话专用LLM API（带回调函数）
func (p *LLMProxy) streamLaymanLLMAPIWithCallback(w http.ResponseWriter, flusher http.Flusher, agent *database.Agent, llmReq *LLMRequest, contentCallback func(string)) (string, error) {
	// 检查是否有图片消息
	hasImages := false
	for _, msg := range llmReq.Messages {
		if len(msg.Images) > 0 {
			hasImages = true
			break
		}
	}

	var reqBody []byte
	var err error

	if hasImages {
		// 多模态模型：转换消息格式
		multimodalMessages := p.convertToMultimodalFormat(llmReq.Messages, agent.LaymanModelName)

		// 构建多模态请求
		multimodalReq := map[string]interface{}{
			"model":       llmReq.Model,
			"messages":    multimodalMessages,
			"stream":      llmReq.Stream,
			"max_tokens":  llmReq.MaxTokens,
			"temperature": llmReq.Temperature,
		}

		reqBody, err = json.Marshal(multimodalReq)
		if err != nil {
			return "", fmt.Errorf("failed to marshal multimodal request: %w", err)
		}
	} else {
		// 普通文本请求
		reqBody, err = json.Marshal(llmReq)
		if err != nil {
			return "", fmt.Errorf("failed to marshal request: %w", err)
		}
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", agent.LaymanApiUrl, bytes.NewBuffer(reqBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+agent.LaymanApiKey)

	// 发送请求
	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// 处理流式响应
	scanner := bufio.NewScanner(resp.Body)
	var fullContent strings.Builder

	for scanner.Scan() {
		line := scanner.Text()
		if strings.HasPrefix(line, "data: ") {
			data := strings.TrimPrefix(line, "data: ")
			if data == "[DONE]" {
				break
			}

			var chunk map[string]interface{}
			if err := json.Unmarshal([]byte(data), &chunk); err != nil {
				continue
			}

			choices, ok := chunk["choices"].([]interface{})
			if !ok || len(choices) == 0 {
				continue
			}

			choice, ok := choices[0].(map[string]interface{})
			if !ok {
				continue
			}

			delta, ok := choice["delta"].(map[string]interface{})
			if !ok {
				continue
			}

			content, ok := delta["content"].(string)
			if !ok {
				continue
			}

			// 调用回调函数处理内容
			contentCallback(content)
			fullContent.WriteString(content)
		}
	}

	if err := scanner.Err(); err != nil {
		return "", fmt.Errorf("error reading stream: %w", err)
	}

	return fullContent.String(), nil
}