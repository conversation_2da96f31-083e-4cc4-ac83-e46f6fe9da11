
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:numerology_ai_chat_admin/src/providers/stats_provider.dart';
import 'package:numerology_ai_chat_admin/src/models/overall_stats_model.dart';

class DashboardScreen extends ConsumerStatefulWidget {
  const DashboardScreen({super.key});

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends ConsumerState<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    // 初始加载统计数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(statsProvider.notifier).fetchStats();
    });
  }

  @override
  Widget build(BuildContext context) {
    final statsState = ref.watch(statsProvider);

    // 监听错误状态
    ref.listen<StatsState>(statsProvider, (previous, next) {
      if (next.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.error!),
            backgroundColor: Colors.red,
          ),
        );
        ref.read(statsProvider.notifier).clearError();
      }
    });

    return Scaffold(
      body: Column(
        children: [
          // 页面标题和操作栏
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Text(
                  '仪表盘',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const Spacer(),
                if (statsState.lastUpdated != null)
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      '最后更新: ${_formatTime(statsState.lastUpdated!)}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () => ref.read(statsProvider.notifier).refreshStats(),
                  tooltip: '刷新数据',
                ),
              ],
            ),
          ),
          // 主要内容
          Expanded(
            child: statsState.isLoading && statsState.stats == null
                ? const Center(child: CircularProgressIndicator())
                : statsState.stats == null
                    ? const Center(child: Text('暂无统计数据'))
                    : _buildDashboardContent(statsState.stats!),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardContent(OverallStatsModel stats) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 欢迎信息
          _buildWelcomeCard(),
          const SizedBox(height: 16),
          // 统计卡片网格
          _buildStatsGrid(stats),
          const SizedBox(height: 16),
          // 详细统计
          _buildDetailedStats(stats),
        ],
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            const Icon(
              LucideIcons.layoutDashboard,
              size: 32,
              color: Colors.blue,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '欢迎来到管理后台',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '这里是系统运行状态的总览',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsGrid(OverallStatsModel stats) {
    return GridView.count(
      crossAxisCount: 4,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        _buildStatCard(
          title: '用户总数',
          value: stats.users.total.toString(),
          icon: LucideIcons.users,
          color: Colors.blue,
          subtitle: '今日新增: ${stats.users.todayNew}',
        ),
        _buildStatCard(
          title: '订单总数',
          value: stats.orders.total.toString(),
          icon: LucideIcons.shoppingCart,
          color: Colors.green,
          subtitle: '已完成: ${stats.orders.completed}',
        ),
        _buildStatCard(
          title: '总收入',
          value: '¥${stats.orders.totalRevenue.toStringAsFixed(2)}',
          icon: LucideIcons.dollarSign,
          color: Colors.orange,
          subtitle: '今日订单: ${stats.orders.todayNew}',
        ),
        _buildStatCard(
          title: '算力消耗',
          value: stats.usage.totalPowerConsumed.toString(),
          icon: LucideIcons.zap,
          color: Colors.purple,
          subtitle: '今日消耗: ${stats.usage.todayPowerConsumed}',
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    String? subtitle,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const Spacer(),
                Text(
                  value,
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        color: color,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
            ),
            if (subtitle != null) ...[
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedStats(OverallStatsModel stats) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 用户详细统计
        Expanded(
          child: _buildDetailCard(
            title: '用户统计详情',
            icon: LucideIcons.users,
            color: Colors.blue,
            children: [
              _buildDetailRow('总用户数', stats.users.total.toString()),
              _buildDetailRow('今日新增', stats.users.todayNew.toString()),
              _buildDetailRow('活跃用户(7天)', stats.users.activeLastWeek.toString()),
            ],
          ),
        ),
        const SizedBox(width: 16),
        // 支付统计
        Expanded(
          child: _buildDetailCard(
            title: '支付统计',
            icon: LucideIcons.creditCard,
            color: Colors.green,
            children: [
              _buildDetailRow('总支付记录', stats.payments.total.toString()),
              _buildDetailRow('成功支付', stats.payments.success.toString()),
              _buildDetailRow('失败支付', stats.payments.failed.toString()),
              _buildDetailRow('成功率', '${stats.payments.successRate}%'),
            ],
          ),
        ),
        const SizedBox(width: 16),
        // 模型使用分布
        Expanded(
          child: _buildDetailCard(
            title: '模型等级使用分布',
            icon: LucideIcons.cpu,
            color: Colors.purple,
            children: stats.models.usageByLevel.entries
                .map((entry) => _buildDetailRow(entry.key, entry.value.toString()))
                .toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildDetailCard({
    required String title,
    required IconData icon,
    required Color color,
    required List<Widget> children,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}';
  }
}
