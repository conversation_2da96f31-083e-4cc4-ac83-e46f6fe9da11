import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import '../core/constants/app_constants.dart';
import 'auth_service.dart';
import 'storage_service.dart';

/// Token管理器
/// 负责token的自动续签、过期检查和生命周期管理
class TokenManager {
  final AuthService _authService;
  final StorageService _storageService;
  
  // 定时器
  Timer? _refreshTimer;
  
  // Token过期时间
  DateTime? _tokenExpiryTime;
  
  // 续签状态
  bool _isRefreshing = false;
  Completer<bool>? _refreshCompleter;
  
  // 续签回调
  Function(String newToken)? _onTokenRefreshed;
  Function()? _onRefreshFailed;
  
  TokenManager({
    required AuthService authService,
    required StorageService storageService,
  }) : _authService = authService,
       _storageService = storageService;

  /// 设置回调函数
  void setCallbacks({
    Function(String newToken)? onTokenRefreshed,
    Function()? onRefreshFailed,
  }) {
    _onTokenRefreshed = onTokenRefreshed;
    _onRefreshFailed = onRefreshFailed;
  }

  /// 启动自动续签
  /// [tokenExpiryTime] Token过期时间
  void startAutoRefresh(DateTime tokenExpiryTime) {
    _tokenExpiryTime = tokenExpiryTime;

    // 停止现有定时器
    stopAutoRefresh();

    debugPrint('TokenManager: 启动自动续签，Token过期时间: $tokenExpiryTime');

    // 设置定期检查定时器（每5分钟检查一次）
    _refreshTimer = Timer.periodic(
      const Duration(minutes: 5),
      (_) => _periodicTokenCheck(),
    );

    // 立即检查一次
    Future.microtask(() => _periodicTokenCheck());
  }

  /// 停止自动续签
  void stopAutoRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
    debugPrint('TokenManager: 停止自动续签');
  }

  /// 检查token是否需要续签
  bool shouldRefreshToken() {
    if (_tokenExpiryTime == null) return false;

    final now = DateTime.now();
    final timeUntilExpiry = _tokenExpiryTime!.difference(now);

    // 如果已经过期，需要续签
    if (timeUntilExpiry.inSeconds <= 0) {
      return true;
    }

    // 动态续签时机：
    // - 如果剩余时间超过20分钟，在剩余10分钟时续签
    // - 如果剩余时间较短，在剩余时间的一半时续签
    if (timeUntilExpiry.inMinutes >= 20) {
      // 长期token：剩余10分钟时续签
      debugPrint('TokenManager: 长期token，剩余${timeUntilExpiry.inMinutes}分钟，续签阈值：10分钟');
      return timeUntilExpiry.inMinutes < 10;
    } else {
      // 短期token：剩余时间少于一半时续签
      final halfTime = timeUntilExpiry.inSeconds ~/ 2;
      debugPrint('TokenManager: 短期token，剩余${timeUntilExpiry.inSeconds}秒，续签阈值：${halfTime}秒');
      return timeUntilExpiry.inSeconds < halfTime;
    }
  }

  /// 检查token是否已过期
  bool isTokenExpired() {
    if (_tokenExpiryTime == null) return true;

    final now = DateTime.now();
    return now.isAfter(_tokenExpiryTime!);
  }

  /// 定期token检查
  Future<void> _periodicTokenCheck() async {
    final now = DateTime.now();

    debugPrint('TokenManager: 定期检查 - 当前时间: $now, Token过期时间: $_tokenExpiryTime');

    if (_tokenExpiryTime == null) {
      debugPrint('TokenManager: Token过期时间未设置');
      return;
    }

    // 检查token是否已过期
    if (isTokenExpired()) {
      debugPrint('TokenManager: Token已过期，尝试续签');
      // Token已过期，尝试续签
      final refreshSuccess = await _performRefresh();
      if (!refreshSuccess) {
        debugPrint('TokenManager: Token过期后续签失败，触发失败回调');
        _onRefreshFailed?.call();
      }
      return;
    }

    // 检查是否需要续签
    if (shouldRefreshToken()) {
      debugPrint('TokenManager: Token即将过期，开始续签');
      final refreshSuccess = await refreshTokenIfNeeded();
      if (!refreshSuccess) {
        debugPrint('TokenManager: Token续签失败');
        // 续签失败，但token还没过期，继续监控
      }
    } else {
      final timeUntilExpiry = _tokenExpiryTime!.difference(now);
      debugPrint('TokenManager: Token仍有效，剩余时间: ${timeUntilExpiry.inMinutes}分钟');
    }
  }

  /// 执行token续签（如果需要）
  Future<bool> refreshTokenIfNeeded() async {
    // 如果正在续签，等待完成
    if (_isRefreshing) {
      debugPrint('TokenManager: 续签正在进行中，等待完成');
      return await _refreshCompleter!.future;
    }

    // 检查是否需要续签
    if (!shouldRefreshToken()) {
      final now = DateTime.now();
      final timeUntilExpiry = _tokenExpiryTime?.difference(now);
      debugPrint('TokenManager: Token尚未到期，无需续签。剩余时间: ${timeUntilExpiry?.inMinutes}分钟');
      return true;
    }

    debugPrint('TokenManager: Token需要续签，开始执行');
    return await _performRefresh();
  }

  /// 强制执行token续签
  Future<bool> forceRefreshToken() async {
    // 如果正在续签，等待完成
    if (_isRefreshing) {
      return await _refreshCompleter!.future;
    }
    
    return await _performRefresh();
  }

  /// 执行实际的续签操作
  Future<bool> _performRefresh() async {
    _isRefreshing = true;
    _refreshCompleter = Completer<bool>();
    
    debugPrint('TokenManager: 开始执行token续签');
    
    try {
      // 获取refresh token
      final refreshToken = await _storageService.getString(AppConstants.refreshTokenKey);
      if (refreshToken == null) {
        debugPrint('TokenManager: Refresh token不存在');
        _refreshCompleter!.complete(false);
        _onRefreshFailed?.call();
        return false;
      }
      
      // 调用续签接口
      debugPrint('TokenManager: 正在调用续签接口...');
      final result = await _authService.refreshToken(refreshToken);
      debugPrint('TokenManager: 续签接口返回结果: $result');

      if (result['success'] == true) {
        final newToken = result['token'] as String;
        final newRefreshToken = result['refresh_token'] as String?;
        final expiresAt = result['expires_at'] as String?;

        // 保存新token
        await _storageService.setString(AppConstants.authTokenKey, newToken);
        if (newRefreshToken != null) {
          await _storageService.setString(AppConstants.refreshTokenKey, newRefreshToken);
        }

        // 更新过期时间（从服务器返回或默认1小时后）
        if (expiresAt != null) {
          // 确保正确处理UTC时间
          _tokenExpiryTime = DateTime.parse(expiresAt).toLocal();
          debugPrint('TokenManager: 服务器返回过期时间(UTC): $expiresAt, 转换为本地时间: $_tokenExpiryTime');
        } else {
          _tokenExpiryTime = DateTime.now().add(const Duration(hours: 1));
          debugPrint('TokenManager: 使用默认过期时间: $_tokenExpiryTime');
        }

        debugPrint('TokenManager: Token续签成功，新Token过期时间: $_tokenExpiryTime');

        // 通知回调
        _onTokenRefreshed?.call(newToken);

        _refreshCompleter!.complete(true);

        // 续签成功后，继续监控新token
        debugPrint('TokenManager: 续签成功，继续监控新token');
        return true;
      } else {
        debugPrint('TokenManager: Token续签失败: ${result['message']}');
        _refreshCompleter!.complete(false);
        _onRefreshFailed?.call();
        return false;
      }
    } catch (e) {
      debugPrint('TokenManager: Token续签异常: $e');
      _refreshCompleter!.complete(false);
      _onRefreshFailed?.call();
      return false;
    } finally {
      _isRefreshing = false;
      _refreshCompleter = null;
    }
  }

  /// 计算到下次续签的时间
  Duration _calculateTimeUntilRefresh(DateTime expiryTime, DateTime now) {
    // 在过期前10分钟续签
    final refreshTime = expiryTime.subtract(const Duration(minutes: 10));
    final timeUntilRefresh = refreshTime.difference(now);

    // 如果已经过了续签时间，立即执行
    if (timeUntilRefresh.inSeconds <= 0) {
      return Duration.zero;
    }

    return timeUntilRefresh;
  }

  /// 更新token过期时间
  void updateTokenExpiryTime(DateTime expiryTime) {
    _tokenExpiryTime = expiryTime;
    debugPrint('TokenManager: 更新Token过期时间: $expiryTime');
  }

  /// 获取token过期时间
  DateTime? get tokenExpiryTime => _tokenExpiryTime;

  /// 获取续签状态
  bool get isRefreshing => _isRefreshing;

  /// 清理资源
  void dispose() {
    stopAutoRefresh();
    _onTokenRefreshed = null;
    _onRefreshFailed = null;
    debugPrint('TokenManager: 资源已清理');
  }
}
