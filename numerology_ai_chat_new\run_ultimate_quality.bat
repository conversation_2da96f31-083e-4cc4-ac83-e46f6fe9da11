@echo off
chcp 65001 >nul
echo 🚀 终极质量图标开发运行脚本
echo ========================================
echo 📝 本脚本将生成高质量图标并运行应用
echo 🎨 开发模式，支持热重载
echo ========================================
echo.

echo 🔄 步骤1: 运行预构建钩子（生成高质量图标）...
python scripts\pre_build_hook.py
if %errorlevel% neq 0 (
    echo ❌ 预构建钩子执行失败！
    echo 💡 提示: 请确保Python和PIL库已正确安装
    echo 💡 安装命令: pip install Pillow
    pause
    exit /b 1
)

echo.
echo 🔄 步骤2: 获取依赖...
flutter pub get

echo.
echo 🔄 步骤3: 验证图标质量...
python -c "
import os
from pathlib import Path
icon_path = Path('windows/runner/resources/app_icon.ico')
if icon_path.exists():
    size = icon_path.stat().st_size
    print(f'✅ 图标文件大小: {size:,} 字节')
    if size > 10000:
        print('✅ 图标质量检查通过')
    else:
        print('⚠️ 图标质量可能较低')
else:
    print('❌ 图标文件不存在')
"

echo.
echo 🔄 步骤4: 启动应用（开发模式）...
echo 💡 应用将在新窗口中启动，支持热重载
echo 💡 按 Ctrl+C 可停止应用
echo.

flutter run -d windows

echo.
echo 👋 应用已退出
pause
