const { validate, agentSchema } = require('../utils/validate')
const { createBusinessError, ERROR_CODES, formatSuccessResponse } = require('../utils/error_handler')
const { agentCollection } = require('../utils/db')
const { checkPermission } = require('../middleware/auth')
const logger = require('../utils/logger')

/**
 * 创建智能体
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 创建结果
 */
async function createAgentByAdmin(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminPermissions, 'agent_create')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限创建智能体', 403)
    }
    
    // 参数校验
    const validatedData = validate(agentSchema, event.data)
    const { agentName, agentPrompt, agentType, description, isActive, sortOrder } = validatedData
    
    logger.info('管理员创建智能体', {
      adminId: adminAuth.adminId,
      agentName,
      agentType
    })
    
    // 检查智能体名称是否已存在
    const existingAgents = await agentCollection.getList()
    const existingAgent = existingAgents.find(agent => agent.agentName === agentName)
    if (existingAgent) {
      throw createBusinessError(ERROR_CODES.AGENT_ALREADY_EXISTS, '智能体名称已存在', 400)
    }

    // 创建智能体数据
    const agentData = {
      agentName,
      agentDisplayName: event.data.agentDisplayName || agentName,
      systemPrompt: event.data.systemPrompt || agentPrompt,
      agentType,
      description: description || '',
      isActive: isActive !== undefined ? isActive : true,
      sortOrder: sortOrder || 0,
      createdBy: adminAuth.adminId,
      updatedBy: adminAuth.adminId
    }

    // 创建智能体
    const newAgentId = await agentCollection.create(agentData)
    const newAgent = await agentCollection.findById(newAgentId)
    
    logger.info('管理员创建智能体成功', {
      adminId: adminAuth.adminId,
      agentId: newAgent._id,
      agentName
    })
    
    const responseData = {
      agentId: newAgent._id,
      agentName: newAgent.agentName,
      agentDisplayName: newAgent.agentDisplayName,
      agentType: newAgent.agentType,
      description: newAgent.description,
      isActive: newAgent.isActive,
      sortOrder: newAgent.sortOrder,
      createdAt: newAgent.createdAt
    }
    
    return formatSuccessResponse(responseData, '智能体创建成功')
    
  } catch (error) {
    logger.error('管理员创建智能体异常', {
      adminId: adminAuth.adminId,
      agentName: event.data?.agentName,
      error: error.message
    })
    throw error
  }
}

/**
 * 获取智能体列表（分页）
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 智能体列表
 */
async function getAgentList(event, adminAuth) {
  try {
    // 暂时跳过权限检查
    const { page = 1, pageSize = 20 } = event || {}

    logger.info('管理员获取智能体列表', {
      adminId: adminAuth.adminId,
      page,
      pageSize
    })

    // 获取智能体列表
    const result = await agentCollection.getList()

    const responseData = {
      agents: result,
      pagination: {
        page: 1,
        pageSize: result.length,
        total: result.length,
        totalPages: 1
      }
    }

    return formatSuccessResponse(responseData, '获取智能体列表成功')

  } catch (error) {
    logger.error('管理员获取智能体列表异常', {
      adminId: adminAuth.adminId,
      error: error.message
    })
    throw error
  }
}

/**
 * 获取智能体详情
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 智能体详情
 */
async function getAgentDetail(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminPermissions, 'agent_read')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限查看智能体详情', 403)
    }
    
    const { agentId } = event.data
    
    if (!agentId) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '智能体ID不能为空', 400)
    }
    
    logger.info('管理员获取智能体详情', {
      adminId: adminAuth.adminId,
      agentId
    })
    
    // 查找智能体
    const agent = await agentCollection.findById(agentId)
    if (!agent) {
      throw createBusinessError(ERROR_CODES.AGENT_NOT_FOUND, '智能体不存在', 404)
    }
    
    return formatSuccessResponse(agent, '获取智能体详情成功')
    
  } catch (error) {
    logger.error('管理员获取智能体详情异常', {
      adminId: adminAuth.adminId,
      agentId: event.data?.agentId,
      error: error.message
    })
    throw error
  }
}

/**
 * 更新智能体
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 更新结果
 */
async function updateAgentByAdmin(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminPermissions, 'agent_update')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限更新智能体', 403)
    }
    
    const { agentId, ...updateData } = event.data
    
    if (!agentId) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '智能体ID不能为空', 400)
    }
    
    // 参数校验（排除agentId）
    const validatedData = validate(agentSchema, updateData)
    
    logger.info('管理员更新智能体', {
      adminId: adminAuth.adminId,
      agentId,
      agentName: validatedData.agentName
    })
    
    // 检查智能体是否存在
    const existingAgent = await agentCollection.findById(agentId)
    if (!existingAgent) {
      throw createBusinessError(ERROR_CODES.AGENT_NOT_FOUND, '智能体不存在', 404)
    }

    // 如果更新名称，检查是否与其他智能体重名
    if (updateData.agentName && updateData.agentName !== existingAgent.agentName) {
      const allAgents = await agentCollection.getList()
      const duplicateAgent = allAgents.find(agent =>
        agent.agentName === updateData.agentName && agent._id !== agentId
      )
      if (duplicateAgent) {
        throw createBusinessError(ERROR_CODES.AGENT_ALREADY_EXISTS, '智能体名称已存在', 400)
      }
    }

    // 准备更新数据 - 只更新提供的字段
    const updateFields = {
      ...updateData,
      updatedBy: adminAuth.adminId
    }

    // 更新智能体
    await agentCollection.update(agentId, updateFields)
    
    logger.info('管理员更新智能体成功', {
      adminId: adminAuth.adminId,
      agentId,
      agentName: validatedData.agentName
    })
    
    return formatSuccessResponse(null, '智能体更新成功')
    
  } catch (error) {
    logger.error('管理员更新智能体异常', {
      adminId: adminAuth.adminId,
      agentId: event.data?.agentId,
      error: error.message
    })
    throw error
  }
}

/**
 * 删除智能体
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 删除结果
 */
async function deleteAgentByAdmin(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminPermissions, 'agent_delete')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限删除智能体', 403)
    }
    
    const { agentId } = event.data
    
    if (!agentId) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '智能体ID不能为空', 400)
    }
    
    logger.info('管理员删除智能体', {
      adminId: adminAuth.adminId,
      agentId
    })
    
    // 检查智能体是否存在
    const existingAgent = await agentCollection.findById(agentId)
    if (!existingAgent) {
      throw createBusinessError(ERROR_CODES.AGENT_NOT_FOUND, '智能体不存在', 404)
    }

    // 删除智能体
    await agentCollection.delete(agentId)
    
    logger.info('管理员删除智能体成功', {
      adminId: adminAuth.adminId,
      agentId,
      agentName: existingAgent.agentName
    })
    
    return formatSuccessResponse(null, '智能体删除成功')
    
  } catch (error) {
    logger.error('管理员删除智能体异常', {
      adminId: adminAuth.adminId,
      agentId: event.data?.agentId,
      error: error.message
    })
    throw error
  }
}

/**
 * 批量更新智能体状态
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 更新结果
 */
async function batchUpdateAgentStatus(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminPermissions, 'agent_update')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限批量更新智能体状态', 403)
    }
    
    const { agentIds, isActive } = event.data
    
    if (!agentIds || !Array.isArray(agentIds) || agentIds.length === 0) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '智能体ID列表不能为空', 400)
    }
    
    if (typeof isActive !== 'boolean') {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '状态值必须是布尔类型', 400)
    }
    
    logger.info('管理员批量更新智能体状态', {
      adminId: adminAuth.adminId,
      agentIds,
      isActive
    })
    
    // 批量更新
    const updateFields = {
      isActive,
      updatedTime: new Date().toISOString(),
      updatedBy: adminAuth.adminId
    }
    
    let successCount = 0
    for (const agentId of agentIds) {
      try {
        await agentCollection.update(agentId, updateFields)
        successCount++
      } catch (error) {
        logger.warn('批量更新智能体状态失败', {
          agentId,
          error: error.message
        })
      }
    }
    
    logger.info('管理员批量更新智能体状态完成', {
      adminId: adminAuth.adminId,
      total: agentIds.length,
      success: successCount
    })
    
    const responseData = {
      total: agentIds.length,
      success: successCount,
      failed: agentIds.length - successCount
    }
    
    return formatSuccessResponse(responseData, `批量更新完成，成功${successCount}个`)
    
  } catch (error) {
    logger.error('管理员批量更新智能体状态异常', {
      adminId: adminAuth.adminId,
      error: error.message
    })
    throw error
  }
}

/**
 * 获取智能体统计信息
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 统计信息
 */
async function getAgentStatistics(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminPermissions, 'agent_read')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限查看智能体统计', 403)
    }
    
    logger.info('管理员获取智能体统计', { adminId: adminAuth.adminId })

    // 获取智能体统计信息
    const agents = await agentCollection.getList()
    const stats = {
      total: agents.length,
      active: agents.filter(agent => agent.isActive).length,
      inactive: agents.filter(agent => !agent.isActive).length,
      byType: agents.reduce((acc, agent) => {
        acc[agent.agentType] = (acc[agent.agentType] || 0) + 1
        return acc
      }, {})
    }

    return formatSuccessResponse(stats, '获取智能体统计成功')
    
  } catch (error) {
    logger.error('管理员获取智能体统计异常', {
      adminId: adminAuth.adminId,
      error: error.message
    })
    throw error
  }
}

module.exports = {
  createAgentByAdmin,
  getAgentList,
  getAgentDetail,
  updateAgentByAdmin,
  deleteAgentByAdmin,
  batchUpdateAgentStatus,
  getAgentStatistics
}