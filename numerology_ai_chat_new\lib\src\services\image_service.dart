import 'dart:io';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image/image.dart' as img;
import 'package:mime/mime.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

import '../models/chat_message.dart';

/// 图片服务
class ImageService {
  static final ImageService _instance = ImageService._internal();
  factory ImageService() => _instance;
  ImageService._internal();

  final ImagePicker _picker = ImagePicker();

  // 图片配置常量
  static const int maxImageSize = 2 * 1024 * 1024; // 2MB
  static const int maxImageWidth = 1920;
  static const int maxImageHeight = 1920;
  static const int compressionQuality = 85;
  static const List<String> supportedMimeTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
  ];

  /// 从相册选择图片
  Future<List<ImageAttachment>?> pickImagesFromGallery({
    bool multiple = true,
    int? maxImages,
  }) async {
    try {
      List<XFile> files;
      
      if (multiple) {
        files = await _picker.pickMultiImage(
          maxWidth: maxImageWidth.toDouble(),
          maxHeight: maxImageHeight.toDouble(),
          imageQuality: compressionQuality,
        );
        
        // 限制选择数量
        if (maxImages != null && files.length > maxImages) {
          files = files.take(maxImages).toList();
        }
      } else {
        final file = await _picker.pickImage(
          source: ImageSource.gallery,
          maxWidth: maxImageWidth.toDouble(),
          maxHeight: maxImageHeight.toDouble(),
          imageQuality: compressionQuality,
        );
        files = file != null ? [file] : [];
      }

      if (files.isEmpty) return null;

      final attachments = <ImageAttachment>[];
      for (final file in files) {
        final attachment = await _processImageFile(file);
        if (attachment != null) {
          attachments.add(attachment);
        }
      }

      return attachments.isNotEmpty ? attachments : null;
    } catch (e) {
      debugPrint('选择图片失败: $e');
      return null;
    }
  }

  /// 从相机拍照
  Future<ImageAttachment?> takePhoto() async {
    try {
      final file = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: maxImageWidth.toDouble(),
        maxHeight: maxImageHeight.toDouble(),
        imageQuality: compressionQuality,
      );

      if (file == null) return null;

      return await _processImageFile(file);
    } catch (e) {
      debugPrint('拍照失败: $e');
      return null;
    }
  }

  /// 处理图片文件
  Future<ImageAttachment?> _processImageFile(XFile file) async {
    try {
      // 检查文件大小
      final fileSize = await file.length();
      if (fileSize > maxImageSize) {
        debugPrint('图片文件过大: ${fileSize}字节');
        return null;
      }

      // 检查MIME类型
      final mimeType = lookupMimeType(file.path);
      if (mimeType == null || !supportedMimeTypes.contains(mimeType)) {
        debugPrint('不支持的图片格式: $mimeType');
        return null;
      }

      // 读取图片数据
      final bytes = await file.readAsBytes();
      
      // 解码图片获取尺寸信息
      final image = img.decodeImage(bytes);
      if (image == null) {
        debugPrint('无法解码图片');
        return null;
      }

      // 压缩图片（如果需要）
      final processedBytes = await _compressImageIfNeeded(bytes, image);
      
      // 转换为base64
      final base64Data = base64Encode(processedBytes);

      // 保存到本地缓存目录
      final localPath = await _saveImageToCache(processedBytes, file.name);

      return ImageAttachment(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        localPath: localPath,
        base64Data: base64Data,
        mimeType: mimeType,
        fileSize: processedBytes.length,
        width: image.width,
        height: image.height,
      );
    } catch (e) {
      debugPrint('处理图片文件失败: $e');
      return null;
    }
  }

  /// 压缩图片（如果需要）
  Future<Uint8List> _compressImageIfNeeded(Uint8List bytes, img.Image image) async {
    // 如果图片尺寸或文件大小超过限制，进行压缩
    if (image.width > maxImageWidth || 
        image.height > maxImageHeight || 
        bytes.length > maxImageSize) {
      
      // 计算新的尺寸
      double scale = 1.0;
      if (image.width > maxImageWidth) {
        scale = maxImageWidth / image.width;
      }
      if (image.height > maxImageHeight) {
        final heightScale = maxImageHeight / image.height;
        if (heightScale < scale) {
          scale = heightScale;
        }
      }

      final newWidth = (image.width * scale).round();
      final newHeight = (image.height * scale).round();

      // 调整图片大小
      final resized = img.copyResize(image, width: newWidth, height: newHeight);
      
      // 重新编码为JPEG格式以减小文件大小
      return Uint8List.fromList(img.encodeJpg(resized, quality: compressionQuality));
    }

    return bytes;
  }

  /// 保存图片到缓存目录
  Future<String> _saveImageToCache(Uint8List bytes, String originalName) async {
    final cacheDir = await getTemporaryDirectory();
    final imagesDir = Directory(path.join(cacheDir.path, 'chat_images'));
    
    if (!await imagesDir.exists()) {
      await imagesDir.create(recursive: true);
    }

    final fileName = '${DateTime.now().millisecondsSinceEpoch}_$originalName';
    final file = File(path.join(imagesDir.path, fileName));
    
    await file.writeAsBytes(bytes);
    return file.path;
  }

  /// 清理缓存图片
  Future<void> clearImageCache() async {
    try {
      final cacheDir = await getTemporaryDirectory();
      final imagesDir = Directory(path.join(cacheDir.path, 'chat_images'));
      
      if (await imagesDir.exists()) {
        await imagesDir.delete(recursive: true);
      }
    } catch (e) {
      debugPrint('清理图片缓存失败: $e');
    }
  }

  /// 获取图片文件
  File? getImageFile(String localPath) {
    try {
      final file = File(localPath);
      return file.existsSync() ? file : null;
    } catch (e) {
      debugPrint('获取图片文件失败: $e');
      return null;
    }
  }

  /// 验证图片是否有效
  bool isValidImage(ImageAttachment attachment) {
    return attachment.hasValidData && 
           (attachment.mimeType == null || supportedMimeTypes.contains(attachment.mimeType));
  }

  /// 获取图片显示名称
  String getImageDisplayName(ImageAttachment attachment) {
    if (attachment.localPath != null) {
      return path.basename(attachment.localPath!);
    }
    return '图片_${attachment.id}';
  }
}
