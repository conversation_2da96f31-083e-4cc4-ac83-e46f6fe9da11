# 大白话功能部署验证指南

## 🎯 本次更新内容

### 修改的功能
- **大白话版本分离显示**: 专业版本和大白话版本现在显示在不同的气泡中
- **气泡颜色区分**: 大白话版本使用特殊的颜色和标识
- **历史对话优化**: 大白话版本不会作为上下文发送给LLM，但会保存在本地

### 修改的文件
1. **Go代理服务** (`go_proxy/internal/proxy/llm_proxy.go`)
   - 新增 `writeSSEStageComplete` 方法
   - 修改 `handleLaymanVersionRequest` 函数
   - 调整 `streamLaymanResult` 返回值

2. **Flutter前端**
   - `ChatMessage` 模型扩展
   - `ChatBubble` 组件样式更新
   - `ChatProvider` 流式响应处理重构
   - `GoProxyService` SSE事件解析优化

## 📦 部署包信息

- **文件名**: `go_proxy_deploy_20250715_010708.tar.gz`
- **大小**: 6.5MB
- **Linux可执行文件**: `go_proxy_linux` (12.3MB)
- **编译时间**: 2025-07-15 01:06

## 🚀 部署步骤

### 1. 上传到服务器
```bash
# 上传部署包到服务器
scp go_proxy_deploy_20250715_010708.tar.gz user@your-server:/opt/

# 或使用其他方式上传
```

### 2. 服务器端部署
```bash
# 进入部署目录
cd /opt

# 备份当前版本（如果存在）
if [ -f "go_proxy_linux" ]; then
    cp go_proxy_linux go_proxy_linux.backup.$(date +%Y%m%d_%H%M%S)
fi

# 解压新版本
tar -xzf go_proxy_deploy_20250715_010708.tar.gz

# 设置权限
chmod +x go_proxy_linux *.sh

# 检查配置文件
cat .env

# 停止旧服务（如果运行中）
./stop.sh

# 启动新服务
./start.sh
```

### 3. 验证部署
```bash
# 检查服务状态
curl http://localhost:8080/healthz

# 查看日志
tail -f go_proxy.log

# 检查进程
ps aux | grep go_proxy_linux
```

## 🧪 功能测试

### 测试大白话功能
1. **前端测试**:
   - 选择支持大白话的智能体
   - 发送测试消息
   - 观察是否出现两个独立的气泡
   - 检查大白话气泡是否有特殊标识

2. **API测试**:
   - 使用提供的 `test_layman_version.dart` 脚本
   - 观察SSE响应中的 `stage_complete` 事件
   - 验证专业版本和大白话版本都有完整内容

### 预期行为
- ✅ 专业版本先显示（正常样式）
- ✅ 大白话版本后显示（特殊样式+标识）
- ✅ 两个版本在不同的气泡中
- ✅ 历史对话只包含专业版本
- ✅ 本地存储包含所有消息

## 🔍 故障排除

### 常见问题
1. **服务启动失败**
   ```bash
   # 检查端口占用
   sudo netstat -tuln | grep 8080
   
   # 检查权限
   ls -la go_proxy_linux
   ```

2. **大白话功能不工作**
   ```bash
   # 检查日志中的SSE事件
   tail -f go_proxy.log | grep "stage_complete"
   
   # 验证智能体配置
   curl -X POST http://localhost:8080/api/chat \
     -H "Content-Type: application/json" \
     -d '{"agentId":"test","modelId":"test","messages":[{"role":"user","content":"test"}]}'
   ```

3. **前端显示异常**
   - 清除应用缓存
   - 重新启动Flutter应用
   - 检查网络连接

## 📊 性能监控

### 关键指标
- **响应时间**: 大白话功能会增加处理时间
- **内存使用**: 监控缓冲区使用情况
- **并发处理**: 确保多用户同时使用正常

### 监控命令
```bash
# CPU和内存使用
top -p $(pgrep go_proxy_linux)

# 网络连接数
ss -tuln | grep 8080

# 日志大小
du -h go_proxy.log
```

## 🔄 回滚方案

如果新版本出现问题，可以快速回滚：

```bash
# 停止当前服务
./stop.sh

# 恢复备份版本
cp go_proxy_linux.backup.* go_proxy_linux

# 重新启动
./start.sh
```

## 📞 技术支持

### 日志关键字
- `stage_complete`: 阶段完成事件
- `LaymanVersionChunk`: 前端处理
- `writeSSEStageComplete`: 后端发送

### 调试模式
在 `.env` 文件中添加：
```bash
DEBUG=true
LOG_LEVEL=debug
```

---

**部署完成后，大白话功能将按照新的分离显示方式工作！** 🎉
