// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'page_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PageModel _$PageModelFromJson(Map<String, dynamic> json) => PageModel(
      id: json['_id'] as String,
      pageTitle: json['pageTitle'] as String,
      pageContent: json['pageContent'] as String,
      pageType: json['pageType'] as String,
      isActive: json['isActive'] as bool,
      sortOrder: (json['sortOrder'] as num).toInt(),
      slug: json['slug'] as String,
      metaDescription: json['metaDescription'] as String?,
      keywords: (json['keywords'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      createdBy: json['createdBy'] as String?,
      updatedBy: json['updatedBy'] as String?,
    );

Map<String, dynamic> _$PageModelToJson(PageModel instance) => <String, dynamic>{
      '_id': instance.id,
      'pageTitle': instance.pageTitle,
      'pageContent': instance.pageContent,
      'pageType': instance.pageType,
      'isActive': instance.isActive,
      'sortOrder': instance.sortOrder,
      'slug': instance.slug,
      'metaDescription': instance.metaDescription,
      'keywords': instance.keywords,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'createdBy': instance.createdBy,
      'updatedBy': instance.updatedBy,
    };

CreatePageRequest _$CreatePageRequestFromJson(Map<String, dynamic> json) =>
    CreatePageRequest(
      pageTitle: json['pageTitle'] as String,
      pageContent: json['pageContent'] as String,
      pageType: json['pageType'] as String,
      isActive: json['isActive'] as bool? ?? true,
      sortOrder: (json['sortOrder'] as num?)?.toInt() ?? 0,
      slug: json['slug'] as String,
      metaDescription: json['metaDescription'] as String?,
      keywords: (json['keywords'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$CreatePageRequestToJson(CreatePageRequest instance) =>
    <String, dynamic>{
      'pageTitle': instance.pageTitle,
      'pageContent': instance.pageContent,
      'pageType': instance.pageType,
      'isActive': instance.isActive,
      'sortOrder': instance.sortOrder,
      'slug': instance.slug,
      'metaDescription': instance.metaDescription,
      'keywords': instance.keywords,
    };

UpdatePageRequest _$UpdatePageRequestFromJson(Map<String, dynamic> json) =>
    UpdatePageRequest(
      pageTitle: json['pageTitle'] as String?,
      pageContent: json['pageContent'] as String?,
      pageType: json['pageType'] as String?,
      isActive: json['isActive'] as bool?,
      sortOrder: (json['sortOrder'] as num?)?.toInt(),
      slug: json['slug'] as String?,
      metaDescription: json['metaDescription'] as String?,
      keywords: (json['keywords'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$UpdatePageRequestToJson(UpdatePageRequest instance) =>
    <String, dynamic>{
      'pageTitle': instance.pageTitle,
      'pageContent': instance.pageContent,
      'pageType': instance.pageType,
      'isActive': instance.isActive,
      'sortOrder': instance.sortOrder,
      'slug': instance.slug,
      'metaDescription': instance.metaDescription,
      'keywords': instance.keywords,
    };
