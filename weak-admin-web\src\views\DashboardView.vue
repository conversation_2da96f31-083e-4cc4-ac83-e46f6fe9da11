<template>
  <div class="dashboard">
    <div class="dashboard-content">
      <div class="welcome-section">
        <h2>欢迎，{{ authStore.user?.username }}</h2>
        <p>您可以使用以下功能管理激活码和用户算力</p>
      </div>

      <!-- 功能卡片 -->
      <div class="feature-cards">
        <el-card class="feature-card" shadow="hover" @click="$router.push('/activation-codes')">
          <div class="card-content">
            <el-icon class="card-icon" color="#409eff"><Ticket /></el-icon>
            <h3>激活码管理</h3>
            <p>生成激活码，查看核销历史</p>
          </div>
        </el-card>

        <el-card class="feature-card" shadow="hover" @click="$router.push('/user-quota')">
          <div class="card-content">
            <el-icon class="card-icon" color="#67c23a"><User /></el-icon>
            <h3>用户算力管理</h3>
            <p>查询用户信息，修改算力余额</p>
          </div>
        </el-card>

        <el-card class="feature-card" shadow="hover" @click="$router.push('/history')">
          <div class="card-content">
            <el-icon class="card-icon" color="#e6a23c"><Document /></el-icon>
            <h3>操作历史</h3>
            <p>查看算力操作和激活码核销记录</p>
          </div>
        </el-card>
      </div>

      <!-- 快速统计 -->
      <div class="stats-section">
        <h3>快速统计</h3>
        <div class="stats-cards">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalActivationCodes }}</div>
              <div class="stat-label">已生成激活码</div>
            </div>
          </el-card>

          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalQuotaOperations }}</div>
              <div class="stat-label">算力操作次数</div>
            </div>
          </el-card>

          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalQuotaAmount }}</div>
              <div class="stat-label">累计分配算力</div>
            </div>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { House, Ticket, User, Document } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import weakAdminService from '@/services/weakAdminService'

const router = useRouter()
const authStore = useAuthStore()

// 统计数据
const stats = reactive({
  totalActivationCodes: 0,
  totalQuotaOperations: 0,
  totalQuotaAmount: 0
})

// 加载统计数据
const loadStats = async () => {
  try {
    // 加载激活码历史统计
    const activationResponse = await weakAdminService.getActivationHistory(1, 1)
    if (activationResponse.success) {
      stats.totalActivationCodes = activationResponse.data.total
    }

    // 加载算力操作历史统计
    const quotaResponse = await weakAdminService.getQuotaOperations(1, 100)
    if (quotaResponse.success) {
      stats.totalQuotaOperations = quotaResponse.data.total
      // 计算累计分配算力
      stats.totalQuotaAmount = quotaResponse.data.data.reduce((total, item) => {
        return total + (item.operationType === 'increase' ? item.quotaAmount : 0)
      }, 0)
    }
  } catch (error) {
    console.error('Load stats error:', error)
  }
}

// 组件挂载时加载统计数据
onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

/* 仪表板内容样式 */
.dashboard-content {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  margin-bottom: 30px;
  text-align: center;
}

.welcome-section h2 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.welcome-section p {
  margin: 0;
  color: #909399;
  font-size: 16px;
}

.feature-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.feature-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-content {
  text-align: center;
  padding: 20px;
}

.card-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.card-content h3 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.card-content p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-section {
  margin-top: 40px;
}

.stats-section h3 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
  text-align: center;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 20px;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #409eff;
  margin-bottom: 10px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}
</style>
