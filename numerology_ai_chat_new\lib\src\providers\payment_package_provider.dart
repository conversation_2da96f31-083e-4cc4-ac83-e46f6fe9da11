import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/payment_package_model.dart';
import '../services/payment_package_service.dart';

/// 套餐状态
class PaymentPackageState {
  final List<PaymentPackageModel> packages;
  final bool isLoading;
  final String? error;

  const PaymentPackageState({
    this.packages = const [],
    this.isLoading = false,
    this.error,
  });

  PaymentPackageState copyWith({
    List<PaymentPackageModel>? packages,
    bool? isLoading,
    String? error,
    bool clearError = false,
  }) {
    return PaymentPackageState(
      packages: packages ?? this.packages,
      isLoading: isLoading ?? this.isLoading,
      error: clearError ? null : (error ?? this.error),
    );
  }
}

/// 套餐状态管理
class PaymentPackageNotifier extends StateNotifier<PaymentPackageState> {
  final PaymentPackageService _service;

  PaymentPackageNotifier(this._service) : super(const PaymentPackageState());

  /// 加载套餐列表
  Future<void> loadPackages({bool refresh = false}) async {
    if (state.isLoading && !refresh) return;

    state = state.copyWith(isLoading: true, clearError: true);

    try {
      final packages = await _service.getActivePackages();
      
      state = state.copyWith(
        packages: packages,
        isLoading: false,
        clearError: true,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 刷新套餐列表
  Future<void> refresh() async {
    await loadPackages(refresh: true);
  }

  /// 根据ID获取套餐
  PaymentPackageModel? getPackageById(String packageId) {
    try {
      return state.packages.firstWhere((pkg) => pkg.id == packageId);
    } catch (e) {
      return null;
    }
  }

  /// 获取推荐套餐
  List<PaymentPackageModel> getRecommendedPackages() {
    return state.packages.where((pkg) => pkg.isRecommended).toList();
  }

  /// 获取按价格排序的套餐
  List<PaymentPackageModel> getPackagesSortedByPrice() {
    final packages = List<PaymentPackageModel>.from(state.packages);
    packages.sort((a, b) => a.price.compareTo(b.price));
    return packages;
  }

  /// 获取按性价比排序的套餐
  List<PaymentPackageModel> getPackagesSortedByValue() {
    final packages = List<PaymentPackageModel>.from(state.packages);
    packages.sort((a, b) => a.pricePerQuota.compareTo(b.pricePerQuota));
    return packages;
  }

  /// 清除错误
  void clearError() {
    state = state.copyWith(clearError: true);
  }
}

/// 套餐服务提供者
final paymentPackageServiceProvider = Provider<PaymentPackageService>((ref) {
  return PaymentPackageService();
});

/// 套餐状态提供者
final paymentPackageProvider = StateNotifierProvider<PaymentPackageNotifier, PaymentPackageState>((ref) {
  final service = ref.read(paymentPackageServiceProvider);
  return PaymentPackageNotifier(service);
});

/// 自动加载套餐提供者
final autoLoadPaymentPackagesProvider = Provider<void>((ref) {
  final packageNotifier = ref.read(paymentPackageProvider.notifier);
  
  // 延迟执行以避免在build过程中调用
  Future.microtask(() {
    packageNotifier.loadPackages();
  });
});

/// 推荐套餐提供者
final recommendedPackagesProvider = Provider<List<PaymentPackageModel>>((ref) {
  final packageState = ref.watch(paymentPackageProvider);
  return packageState.packages.where((pkg) => pkg.isRecommended).toList();
});

/// 按价格排序的套餐提供者
final packagesSortedByPriceProvider = Provider<List<PaymentPackageModel>>((ref) {
  final packageState = ref.watch(paymentPackageProvider);
  final packages = List<PaymentPackageModel>.from(packageState.packages);
  packages.sort((a, b) => a.price.compareTo(b.price));
  return packages;
});

/// 按性价比排序的套餐提供者
final packagesSortedByValueProvider = Provider<List<PaymentPackageModel>>((ref) {
  final packageState = ref.watch(paymentPackageProvider);
  final packages = List<PaymentPackageModel>.from(packageState.packages);
  packages.sort((a, b) => a.pricePerQuota.compareTo(b.pricePerQuota));
  return packages;
});
