import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import '../models/bazi_model.dart';
import '../services/bazi_service.dart';
import '../services/bazi_storage_service.dart';
import '../providers/bazi_history_provider.dart';

/// 八字历史生成列表组件
class BaziHistoryList extends ConsumerStatefulWidget {
  const BaziHistoryList({super.key});

  @override
  ConsumerState<BaziHistoryList> createState() => _BaziHistoryListState();
}

class _BaziHistoryListState extends ConsumerState<BaziHistoryList> {
  final BaziService _baziService = BaziService();
  List<BaziRecordSummary> _baziRecords = [];
  bool _isLoading = false;
  int _lastRefreshCounter = 0;

  @override
  void initState() {
    super.initState();
    _loadBaziRecords();
  }

  /// 加载八字记录
  Future<void> _loadBaziRecords() async {
    setState(() => _isLoading = true);

    try {
      final records = await _baziService.getAllBaziRecords();
      setState(() {
        _baziRecords = records;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('加载历史记录失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // 监听历史记录变化
    final historyNotifier = ref.watch(baziHistoryProvider);

    // 检查是否需要刷新
    if (historyNotifier.refreshCounter != _lastRefreshCounter) {
      _lastRefreshCounter = historyNotifier.refreshCounter;
      // 使用Future.microtask来避免在build过程中调用setState
      Future.microtask(() {
        if (mounted) {
          _loadBaziRecords();
        }
      });
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 标题栏
            Row(
              children: [
                Icon(
                  Icons.history,
                  color: theme.colorScheme.primary,
                ),
                const Gap(8),
                Text(
                  '历史生成',
                  style: theme.textTheme.titleLarge,
                ),
                const Spacer(),
                // 刷新按钮
                IconButton(
                  onPressed: _loadBaziRecords,
                  icon: Icon(
                    Icons.refresh,
                    color: theme.colorScheme.primary,
                  ),
                  tooltip: '刷新列表',
                ),
              ],
            ),
            const Gap(16),
            
            // 历史记录列表
            Expanded(
              child: _buildHistoryList(theme),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建历史记录列表
  Widget _buildHistoryList(ThemeData theme) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_baziRecords.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.folder_open_outlined,
              size: 64,
              color: theme.colorScheme.onSurface.withOpacity(0.4),
            ),
            const Gap(16),
            Text(
              '暂无历史记录',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
            const Gap(8),
            Text(
              '完成排盘后记录将显示在这里',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.5),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _baziRecords.length,
      itemBuilder: (context, index) {
        final record = _baziRecords[index];
        return _buildHistoryItem(record, theme);
      },
    );
  }

  /// 构建历史记录项
  Widget _buildHistoryItem(BaziRecordSummary record, ThemeData theme) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.3),
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        leading: CircleAvatar(
          radius: 20,
          backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
          child: Text(
            record.name.isNotEmpty ? record.name[0] : '?',
            style: TextStyle(
              color: theme.colorScheme.primary,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          record.name,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Gap(4),
            Text(
              '${record.gender} • ${record.birthDate} ${record.birthTime}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const Gap(2),
            Text(
              record.calendarType,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.primary,
                fontSize: 11,
              ),
            ),
            if (record.fourPillars.isNotEmpty) ...[
              const Gap(4),
              Text(
                record.fourPillars,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.secondary,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
        trailing: PopupMenuButton<String>(
          icon: Icon(
            Icons.more_vert,
            size: 18,
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
          onSelected: (value) {
            switch (value) {
              case 'open_file':
                _openFileLocation(record);
                break;
              case 'view_content':
                _viewFileContent(record);
                break;
              case 'delete':
                _deleteRecord(record);
                break;
            }
          },
          itemBuilder: (context) => [
            if (record.filePath != null)
              const PopupMenuItem(
                value: 'open_file',
                child: Row(
                  children: [
                    Icon(Icons.folder_open, size: 16),
                    Gap(8),
                    Text('打开文件位置'),
                  ],
                ),
              ),
            if (record.filePath != null)
              const PopupMenuItem(
                value: 'view_content',
                child: Row(
                  children: [
                    Icon(Icons.visibility, size: 16),
                    Gap(8),
                    Text('查看内容'),
                  ],
                ),
              ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete_outline, size: 16, color: Colors.red),
                  Gap(8),
                  Text('删除记录', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
        onTap: () => _viewFileContent(record),
      ),
    );
  }

  /// 打开文件位置
  Future<void> _openFileLocation(BaziRecordSummary record) async {
    if (record.filePath == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('文件路径不存在'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    try {
      await _baziService.openFileLocation(record.filePath!);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('打开文件位置失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 查看文件内容
  Future<void> _viewFileContent(BaziRecordSummary record) async {
    if (record.filePath == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('文件路径不存在'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    try {
      final content = await _baziService.readBaziFile(record.filePath!);
      
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text('${record.name} 的八字分析'),
            content: SizedBox(
              width: 500,
              height: 400,
              child: SingleChildScrollView(
                child: SelectableText(
                  content,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('关闭'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('读取文件内容失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 删除记录
  Future<void> _deleteRecord(BaziRecordSummary record) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除 ${record.name} 的八字记录吗？\n这将同时删除本地文件。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _baziService.deleteBaziRecord(record.id);
        
        // 重新加载列表
        await _loadBaziRecords();
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('已删除 ${record.name} 的八字记录'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('删除失败: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
