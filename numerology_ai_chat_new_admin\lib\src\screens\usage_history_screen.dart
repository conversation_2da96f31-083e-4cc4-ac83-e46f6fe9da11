import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../providers/usage_history_provider.dart';
import '../models/usage_history_model.dart';
import '../widgets/usage_history_detail_dialog.dart';

class UsageHistoryScreen extends ConsumerStatefulWidget {
  const UsageHistoryScreen({super.key});

  @override
  ConsumerState<UsageHistoryScreen> createState() => _UsageHistoryScreenState();
}

class _UsageHistoryScreenState extends ConsumerState<UsageHistoryScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedAgent = '';
  String _selectedModelLevel = '';
  String _selectedTier = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(usageHistoryProvider.notifier).loadUsageHistories();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final historyState = ref.watch(usageHistoryProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('使用历史管理'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Column(
        children: [
          // 搜索和筛选区域
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: TextField(
                        controller: _searchController,
                        decoration: const InputDecoration(
                          labelText: '搜索用户ID或智能体名称',
                          prefixIcon: Icon(Icons.search),
                          border: OutlineInputBorder(),
                        ),
                        onSubmitted: (value) {
                          ref.read(usageHistoryProvider.notifier).searchHistories(value);
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedModelLevel.isEmpty ? null : _selectedModelLevel,
                        decoration: const InputDecoration(
                          labelText: '模型等级',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: '', child: Text('全部等级')),
                          DropdownMenuItem(value: '初级', child: Text('初级')),
                          DropdownMenuItem(value: '中级', child: Text('中级')),
                          DropdownMenuItem(value: '高级', child: Text('高级')),
                          DropdownMenuItem(value: '顶级', child: Text('顶级')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedModelLevel = value ?? '';
                          });
                          ref.read(usageHistoryProvider.notifier).filterByModelLevel(_selectedModelLevel);
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedTier.isEmpty ? null : _selectedTier,
                        decoration: const InputDecoration(
                          labelText: '档次',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: '', child: Text('全部档次')),
                          DropdownMenuItem(value: '基础档次', child: Text('基础档次')),
                          DropdownMenuItem(value: '标准档次', child: Text('标准档次')),
                          DropdownMenuItem(value: '高级档次', child: Text('高级档次')),
                          DropdownMenuItem(value: '专业档次', child: Text('专业档次')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedTier = value ?? '';
                          });
                          ref.read(usageHistoryProvider.notifier).filterByTier(_selectedTier);
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    ElevatedButton.icon(
                      onPressed: () {
                        ref.read(usageHistoryProvider.notifier).refreshHistories();
                      },
                      icon: const Icon(Icons.refresh),
                      label: const Text('刷新'),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const Divider(),
          // 统计信息
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                Text(
                  '总记录数: ${historyState.totalCount}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(width: 24),
                if (historyState.histories.isNotEmpty)
                  Text(
                    '总消耗算力: ${historyState.histories.fold<int>(0, (sum, item) => sum + item.powerCost)}',
                    style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.blue),
                  ),
              ],
            ),
          ),
          const Divider(),
          // 使用历史列表
          Expanded(
            child: historyState.isLoading
                ? const Center(child: CircularProgressIndicator())
                : historyState.error != null
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              '错误: ${historyState.error}',
                              style: const TextStyle(color: Colors.red),
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: () {
                                ref.read(usageHistoryProvider.notifier).refreshHistories();
                              },
                              child: const Text('重试'),
                            ),
                          ],
                        ),
                      )
                    : historyState.histories.isEmpty
                        ? const Center(child: Text('暂无使用历史数据'))
                        : Column(
                            children: [
                              Expanded(
                                child: SingleChildScrollView(
                                  scrollDirection: Axis.horizontal,
                                  child: DataTable(
                                    columns: const [
                                      DataColumn(label: Text('用户ID')),
                                      DataColumn(label: Text('智能体')),
                                      DataColumn(label: Text('模型')),
                                      DataColumn(label: Text('档次')),
                                      DataColumn(label: Text('消耗算力')),
                                      DataColumn(label: Text('余额变化')),
                                      DataColumn(label: Text('使用时间')),
                                      DataColumn(label: Text('操作')),
                                    ],
                                    rows: historyState.histories.map((history) {
                                      return DataRow(
                                        cells: [
                                          DataCell(
                                            Text(
                                              history.userId.length > 8 
                                                  ? '${history.userId.substring(0, 8)}...'
                                                  : history.userId,
                                              style: const TextStyle(fontFamily: 'monospace'),
                                            ),
                                          ),
                                          DataCell(
                                            Text(
                                              history.agentName.length > 10 
                                                  ? '${history.agentName.substring(0, 10)}...'
                                                  : history.agentName,
                                            ),
                                          ),
                                          DataCell(
                                            Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              mainAxisAlignment: MainAxisAlignment.center,
                                              children: [
                                                Text(
                                                  history.modelName.length > 12 
                                                      ? '${history.modelName.substring(0, 12)}...'
                                                      : history.modelName,
                                                  style: const TextStyle(fontSize: 12),
                                                ),
                                                Container(
                                                  padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                                                  decoration: BoxDecoration(
                                                    color: _getModelLevelColor(history.modelLevel),
                                                    borderRadius: BorderRadius.circular(4),
                                                  ),
                                                  child: Text(
                                                    history.modelLevel,
                                                    style: const TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 10,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          DataCell(Text(history.tierName)),
                                          DataCell(
                                            Text(
                                              '${history.powerCost}',
                                              style: const TextStyle(
                                                fontWeight: FontWeight.bold,
                                                color: Colors.red,
                                              ),
                                            ),
                                          ),
                                          DataCell(
                                            Text(
                                              '${history.balanceBefore} → ${history.balanceAfter}',
                                              style: const TextStyle(fontSize: 12),
                                            ),
                                          ),
                                          DataCell(
                                            Text(DateFormat('MM-dd HH:mm').format(history.consumeTime)),
                                          ),
                                          DataCell(
                                            IconButton(
                                              icon: const Icon(Icons.visibility),
                                              onPressed: () => _showHistoryDetail(history),
                                              tooltip: '查看详情',
                                            ),
                                          ),
                                        ],
                                      );
                                    }).toList(),
                                  ),
                                ),
                              ),
                              // 分页控件
                              if (historyState.totalPages > 1)
                                Container(
                                  padding: const EdgeInsets.all(16),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      IconButton(
                                        onPressed: historyState.currentPage > 1
                                            ? () => ref.read(usageHistoryProvider.notifier).loadPage(historyState.currentPage - 1)
                                            : null,
                                        icon: const Icon(Icons.chevron_left),
                                      ),
                                      Text('${historyState.currentPage} / ${historyState.totalPages}'),
                                      IconButton(
                                        onPressed: historyState.currentPage < historyState.totalPages
                                            ? () => ref.read(usageHistoryProvider.notifier).loadPage(historyState.currentPage + 1)
                                            : null,
                                        icon: const Icon(Icons.chevron_right),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
          ),
        ],
      ),
    );
  }

  Color _getModelLevelColor(String level) {
    switch (level) {
      case '初级':
        return Colors.green;
      case '中级':
        return Colors.blue;
      case '高级':
        return Colors.orange;
      case '顶级':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  void _showHistoryDetail(UsageHistoryModel history) {
    showDialog(
      context: context,
      builder: (context) => UsageHistoryDetailDialog(history: history),
    );
  }
}
