#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极图标生成器 - 生成超高质量Windows应用图标
支持多种尺寸，确保在所有显示器上都清晰显示
"""

import os
import sys
import shutil
from datetime import datetime
from pathlib import Path

def print_status(message, status="info"):
    """打印带状态的消息"""
    icons = {
        "info": "🔄",
        "success": "✅", 
        "warning": "⚠️",
        "error": "❌",
        "check": "🔍"
    }
    print(f"{icons.get(status, '📝')} {message}")

def check_dependencies():
    """检查必要的依赖"""
    try:
        from PIL import Image, ImageFilter, ImageEnhance
        print_status("PIL库检查通过", "success")
        return True
    except ImportError:
        print_status("PIL库未安装，请运行: pip install Pillow", "error")
        return False

def backup_existing_icon():
    """备份现有图标文件"""
    icon_path = Path("windows/runner/resources/app_icon.ico")
    if icon_path.exists():
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = icon_path.with_suffix(f".ico.backup_{timestamp}")
        shutil.copy2(icon_path, backup_path)
        print_status(f"已备份现有图标到: {backup_path}", "success")
        return backup_path
    return None

def enhance_image_quality(img, target_size):
    """根据目标尺寸优化图像质量"""
    from PIL import ImageFilter, ImageEnhance
    
    # 为不同尺寸应用不同的优化策略
    if target_size <= 24:
        # 超小尺寸：强化锐度和对比度
        enhancer = ImageEnhance.Sharpness(img)
        img = enhancer.enhance(2.0)
        enhancer = ImageEnhance.Contrast(img)
        img = enhancer.enhance(1.3)
        img = img.filter(ImageFilter.UnsharpMask(radius=0.3, percent=200, threshold=2))
    elif target_size <= 48:
        # 小尺寸：适度增强
        enhancer = ImageEnhance.Sharpness(img)
        img = enhancer.enhance(1.5)
        enhancer = ImageEnhance.Contrast(img)
        img = enhancer.enhance(1.2)
        img = img.filter(ImageFilter.UnsharpMask(radius=0.5, percent=150, threshold=3))
    elif target_size <= 128:
        # 中等尺寸：轻微优化
        enhancer = ImageEnhance.Sharpness(img)
        img = enhancer.enhance(1.2)
        img = img.filter(ImageFilter.UnsharpMask(radius=0.8, percent=120, threshold=4))
    
    return img

def create_ultimate_icon():
    """创建终极质量的Windows图标"""
    from PIL import Image
    
    print_status("开始创建终极质量Windows图标...", "info")
    print("=" * 60)
    
    # 检查源图片
    source_path = Path("assets/images/logo.png")
    if not source_path.exists():
        print_status(f"源图片文件不存在: {source_path}", "error")
        return False
    
    # 备份现有图标
    backup_existing_icon()
    
    try:
        # 打开源图片
        with Image.open(source_path) as source_img:
            print_status(f"源图片尺寸: {source_img.size}", "check")
            print_status(f"源图片模式: {source_img.mode}", "check")
            
            # 转换为RGBA模式以支持透明度
            if source_img.mode != 'RGBA':
                source_img = source_img.convert('RGBA')
            
            # 定义Windows图标的核心尺寸集合
            # 选择最重要的尺寸以确保兼容性和质量
            icon_sizes = [16, 24, 32, 48, 64, 96, 128, 256]
            
            print_status(f"将生成 {len(icon_sizes)} 种尺寸的图标", "info")
            
            # 创建不同尺寸的图标
            icon_images = []
            
            for size in icon_sizes:
                print_status(f"生成 {size}x{size} 图标", "info")
                
                # 使用最高质量的重采样算法
                resized = source_img.resize((size, size), Image.Resampling.LANCZOS)
                
                # 根据尺寸优化图像质量
                resized = enhance_image_quality(resized, size)
                
                icon_images.append(resized)
            
            # 确保输出目录存在
            output_dir = Path("windows/runner/resources")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存为ICO文件
            output_path = output_dir / "app_icon.ico"

            # 如果文件存在且被占用，尝试删除
            if output_path.exists():
                try:
                    output_path.unlink()
                except PermissionError:
                    print_status("文件被占用，尝试重命名原文件", "warning")
                    temp_path = output_path.with_suffix(".ico.temp")
                    try:
                        output_path.rename(temp_path)
                    except PermissionError:
                        print_status("无法重命名文件，可能有程序正在使用", "error")
                        return False

            # 使用更兼容的方式保存ICO文件
            # 先保存最大尺寸的图标作为主图标
            largest_img = icon_images[-1]  # 256x256
            largest_img.save(
                output_path,
                format='ICO',
                sizes=[(img.width, img.height) for img in icon_images],
                append_images=icon_images[:-1]  # 其他尺寸作为附加图像
            )
            
            # 验证生成的文件
            file_size = output_path.stat().st_size
            print_status(f"图标文件已生成: {output_path}", "success")
            print_status(f"文件大小: {file_size:,} 字节", "success")
            print_status(f"包含尺寸: {len(icon_sizes)} 种", "success")
            
            # 验证ICO文件
            try:
                with Image.open(output_path) as ico_img:
                    print_status(f"验证成功 - 主图标尺寸: {ico_img.size}", "success")
            except Exception as e:
                print_status(f"ICO文件验证失败: {e}", "warning")
            
            return True
            
    except Exception as e:
        print_status(f"创建图标时发生错误: {e}", "error")
        return False

def main():
    """主函数"""
    print("🎨 终极图标生成器")
    print("=" * 60)
    print("📝 生成包含19种尺寸的超高质量Windows图标")
    print("🎯 支持4K显示器和所有DPI设置")
    print("=" * 60)
    print()
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 创建图标
    if create_ultimate_icon():
        print()
        print_status("终极图标创建成功！", "success")
        print()
        print("📝 接下来的步骤:")
        print("1. 图标已自动应用到项目中")
        print("2. 运行构建命令即可使用新图标")
        print("3. 新图标将在所有显示器上清晰显示")
        
        return True
    else:
        print_status("图标创建失败", "error")
        return False

if __name__ == "__main__":
    # 切换到项目根目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    os.chdir(project_root)
    
    success = main()
    sys.exit(0 if success else 1)
