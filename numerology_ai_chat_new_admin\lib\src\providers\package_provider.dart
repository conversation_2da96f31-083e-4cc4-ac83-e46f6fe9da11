import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:numerology_ai_chat_admin/src/models/package_model.dart';
import 'package:numerology_ai_chat_admin/src/services/admin_api_service.dart';
import 'package:numerology_ai_chat_admin/src/providers/auth_provider.dart';

class PackageState {
  final List<PaymentPackage>? packages;
  final bool isLoading;
  final String? error;
  final int total;
  final int currentPage;
  final int pageSize;

  PackageState({
    this.packages,
    this.isLoading = false,
    this.error,
    this.total = 0,
    this.currentPage = 1,
    this.pageSize = 20,
  });

  PackageState copyWith({
    List<PaymentPackage>? packages,
    bool? isLoading,
    String? error,
    int? total,
    int? currentPage,
    int? pageSize,
  }) {
    return PackageState(
      packages: packages ?? this.packages,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      total: total ?? this.total,
      currentPage: currentPage ?? this.currentPage,
      pageSize: pageSize ?? this.pageSize,
    );
  }
}

class PackageNotifier extends StateNotifier<PackageState> {
  final AdminApiService _apiService;
  final Ref _ref;

  PackageNotifier(this._apiService, this._ref) : super(PackageState());

  Future<void> fetchPackages({
    bool? isActive,
    int page = 1,
    int pageSize = 20,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final authState = _ref.read(authProvider);
      if (authState.token == null) {
        throw Exception('未登录');
      }

      final response = await _apiService.getPackageList(
        authState.token!,
        isActive: isActive,
        page: page,
        pageSize: pageSize,
      );

      if (response['code'] == 0) {
        final data = response['data']['data'];
        final List<dynamic> packageList = data['packages'] ?? [];
        // 过滤掉不完整的数据记录
        final validPackages = packageList.where((json) {
          return json is Map<String, dynamic> &&
                 json.containsKey('packageName') &&
                 json.containsKey('quotaCount');
        }).toList();
        final packages = validPackages.map((json) => PaymentPackage.fromJson(json)).toList();

        state = state.copyWith(
          packages: packages,
          isLoading: false,
          total: data['pagination']['total'] ?? 0,
          currentPage: page,
          pageSize: pageSize,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response['message'] ?? '获取套餐列表失败',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<bool> createPackage(CreatePackageRequest request) async {
    try {
      final authState = _ref.read(authProvider);
      if (authState.token == null) {
        throw Exception('未登录');
      }

      final response = await _apiService.createPackage(
        authState.token!,
        request.toJson(),
      );

      if (response['code'] == 0) {
        // 刷新套餐列表
        await fetchPackages(
          page: state.currentPage,
          pageSize: state.pageSize,
        );
        return true;
      } else {
        state = state.copyWith(error: response['message'] ?? '创建套餐失败');
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  Future<bool> updatePackage(String packageId, UpdatePackageRequest request) async {
    try {
      final authState = _ref.read(authProvider);
      if (authState.token == null) {
        throw Exception('未登录');
      }

      final response = await _apiService.updatePackage(
        authState.token!,
        packageId,
        request.toJson(),
      );

      if (response['code'] == 0) {
        // 刷新套餐列表
        await fetchPackages(
          page: state.currentPage,
          pageSize: state.pageSize,
        );
        return true;
      } else {
        state = state.copyWith(error: response['message'] ?? '更新套餐失败');
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  Future<bool> deletePackage(String packageId) async {
    try {
      final authState = _ref.read(authProvider);
      if (authState.token == null) {
        throw Exception('未登录');
      }

      final response = await _apiService.deletePackage(authState.token!, packageId);

      if (response['code'] == 0) {
        // 刷新套餐列表
        await fetchPackages(
          page: state.currentPage,
          pageSize: state.pageSize,
        );
        return true;
      } else {
        state = state.copyWith(error: response['message'] ?? '删除套餐失败');
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

final packageProvider = StateNotifierProvider<PackageNotifier, PackageState>((ref) {
  return PackageNotifier(AdminApiService(), ref);
});
