const cloud = require('wx-server-sdk')
const { paymentPackageCollection, purchaseOrderCollection, userCollection } = require('../utils/db')
const { successResponse, createBusinessError, ERROR_CODES } = require('../middleware/error_handler')
const logger = require('../utils/logger')
const fuiouPayment = require('../utils/fuiou_payment')

/**
 * 获取启用的套餐列表（用户端）
 * @param {object} params 请求参数
 * @returns {object} 套餐列表
 */
async function getActivePackages(params) {
  try {
    logger.info('Getting active payment packages')

    const packages = await paymentPackageCollection.getActiveList()

    // 过滤敏感信息，只返回用户需要的字段
    const userPackages = packages.map(pkg => ({
      id: pkg._id,
      packageName: pkg.packageName,
      packageDescription: pkg.packageDescription,
      quotaCount: pkg.quotaCount,
      price: pkg.price,
      originalPrice: pkg.originalPrice,
      discountRate: pkg.discountRate,
      isRecommended: pkg.isRecommended,
      tags: pkg.tags || [],
      validDays: pkg.validDays,
      minPurchaseCount: pkg.minPurchaseCount,
      maxPurchaseCount: pkg.maxPurchaseCount,
      sortOrder: pkg.sortOrder
    }))

    logger.info('Active payment packages retrieved successfully', {
      packagesCount: userPackages.length
    })

    return successResponse(userPackages, '获取套餐列表成功')

  } catch (error) {
    logger.error('Failed to get active payment packages', { 
      error: error.message
    })
    throw error
  }
}

/**
 * 根据ID获取套餐详情
 * @param {object} params 请求参数
 * @param {string} params.packageId 套餐ID
 * @returns {object} 套餐详情
 */
async function getPackageDetail(params) {
  try {
    const { packageId } = params

    if (!packageId) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAM, '套餐ID不能为空', 400)
    }

    logger.info('Getting payment package detail', { packageId })

    const packageData = await paymentPackageCollection.findById(packageId)

    if (!packageData) {
      throw createBusinessError(ERROR_CODES.PACKAGE_NOT_FOUND, '套餐不存在', 404)
    }

    if (!packageData.isActive) {
      throw createBusinessError(ERROR_CODES.PACKAGE_NOT_AVAILABLE, '套餐已下架', 400)
    }

    // 过滤敏感信息，只返回用户需要的字段
    const userPackage = {
      id: packageData._id,
      packageName: packageData.packageName,
      packageDescription: packageData.packageDescription,
      quotaCount: packageData.quotaCount,
      price: packageData.price,
      originalPrice: packageData.originalPrice,
      discountRate: packageData.discountRate,
      isRecommended: packageData.isRecommended,
      tags: packageData.tags || [],
      validDays: packageData.validDays,
      minPurchaseCount: packageData.minPurchaseCount,
      maxPurchaseCount: packageData.maxPurchaseCount,
      sortOrder: packageData.sortOrder
    }

    logger.info('Payment package detail retrieved successfully', {
      packageId,
      packageName: packageData.packageName
    })

    return successResponse(userPackage, '获取套餐详情成功')

  } catch (error) {
    logger.error('Failed to get payment package detail', { 
      error: error.message,
      packageId: params.packageId
    })
    throw error
  }
}

/**
 * 创建购买订单
 * @param {object} params 请求参数（包含鉴权后的用户信息）
 * @param {string} params.userId 用户ID（由鉴权中间件设置）
 * @param {string} params.packageId 套餐ID
 * @param {number} [params.quantity=1] 购买数量
 * @param {string} [params.paymentMethod=WECHAT] 支付方式（WECHAT/ALIPAY）
 * @returns {object} 订单信息
 */
async function createPurchaseOrder(params) {
  try {
    const { userId, packageId, quantity = 1, paymentMethod = 'WECHAT' } = params

    if (!userId) {
      throw createBusinessError(ERROR_CODES.UNAUTHORIZED, '无效的Token或用户认证失败', 401)
    }

    if (!packageId) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAM, '套餐ID不能为空', 400)
    }

    if (quantity < 1) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAM, '购买数量必须大于0', 400)
    }

    // 验证支付方式
    const validPaymentMethods = ['WECHAT', 'ALIPAY']
    if (!validPaymentMethods.includes(paymentMethod)) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAM, `不支持的支付方式: ${paymentMethod}，支持的支付方式: ${validPaymentMethods.join(', ')}`, 400)
    }

    logger.info('Creating purchase order', { userId, packageId, quantity, paymentMethod })

    // 获取套餐信息
    const packageData = await paymentPackageCollection.findById(packageId)

    if (!packageData) {
      throw createBusinessError(ERROR_CODES.PACKAGE_NOT_FOUND, '套餐不存在', 404)
    }

    if (!packageData.isActive) {
      throw createBusinessError(ERROR_CODES.PACKAGE_NOT_AVAILABLE, '套餐已下架', 400)
    }

    // 检查购买数量限制
    if (quantity < packageData.minPurchaseCount) {
      throw createBusinessError(ERROR_CODES.INVALID_QUANTITY, `最少购买${packageData.minPurchaseCount}个`, 400)
    }

    if (packageData.maxPurchaseCount > 0 && quantity > packageData.maxPurchaseCount) {
      throw createBusinessError(ERROR_CODES.INVALID_QUANTITY, `最多购买${packageData.maxPurchaseCount}个`, 400)
    }

    // 生成订单号（使用富友支付测试环境前缀）
    const fuiouPayment = require('../utils/fuiou_payment')
    const orderPrefix = fuiouPayment.FUIOU_CONFIG.ORDER_PREFIX || 'ORD'
    const orderNo = `${orderPrefix}${new Date().toISOString().slice(0, 10).replace(/-/g, '')}${Date.now().toString().slice(-6)}`
    
    // 计算订单金额和算力
    const totalAmount = packageData.price * quantity
    const totalQuota = packageData.quotaCount * quantity

    // 创建订单数据
    const orderData = {
      userId: userId,
      orderNo: orderNo,
      packageName: packageData.packageName,
      packageQuota: totalQuota,
      orderAmount: totalAmount,
      status: 'PENDING',
      createTime: new Date(),
      payTime: null,
      transactionId: null,
      updatedAt: new Date(),
      packageId: packageId,
      quantity: quantity,
      unitPrice: packageData.price,
      unitQuota: packageData.quotaCount,
      paymentMethod: paymentMethod // 保存支付方式
    }

    // 将订单保存到数据库
    const orderId = await purchaseOrderCollection.create(orderData)

    // 调用富友支付统一下单接口
    let paymentInfo
    try {
      const fuiouOrderInfo = {
        orderNo: orderNo,
        amount: totalAmount, // totalAmount已经是以分为单位，无需再转换
        description: `购买${packageData.packageName}`,
        goodsDescription: `${packageData.packageName} - ${totalQuota}算力`,
        paymentMethod: paymentMethod // 传递支付方式
      }

      const fuiouResult = await fuiouPayment.createUnifiedOrder(fuiouOrderInfo)

      if (fuiouResult.success) {
        // 更新订单记录，添加富友支付信息
        await purchaseOrderCollection.updateOrderPaymentInfo(orderId, {
          fuiouOrderId: fuiouResult.fuiouOrderId,
          paymentUrl: fuiouResult.paymentUrl,
          qrCodeData: fuiouResult.qrCodeData,
          expireTime: fuiouResult.expireTime,
          paymentMethod: fuiouResult.paymentMethod // 保存支付方式
        })

        paymentInfo = {
          orderId: orderId,
          orderNo: orderNo,
          orderAmount: totalAmount,
          paymentUrl: fuiouResult.paymentUrl,
          qrCodeData: fuiouResult.qrCodeData,
          expireTime: fuiouResult.expireTime,
          fuiouOrderId: fuiouResult.fuiouOrderId,
          paymentMethod: paymentMethod // 返回支付方式
        }

        logger.info('富友支付统一下单成功', {
          userId,
          orderNo,
          fuiouOrderId: fuiouResult.fuiouOrderId,
          paymentUrl: fuiouResult.paymentUrl
        })
      } else {
        throw new Error('富友支付统一下单失败')
      }
    } catch (error) {
      logger.error('富友支付统一下单失败，使用模拟支付', {
        error: error.message,
        orderNo
      })

      // 富友支付失败时，回退到模拟支付
      paymentInfo = {
        orderId: orderId,
        orderNo: orderNo,
        orderAmount: totalAmount,
        paymentUrl: `https://pay.example.com/order/${orderNo}`,
        qrCodeData: `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`,
        expireTime: new Date(Date.now() + 30 * 60 * 1000), // 30分钟后过期
        isSimulated: true // 标记为模拟支付
      }
    }

    logger.info('Purchase order created successfully', {
      userId,
      orderNo,
      packageName: packageData.packageName,
      totalAmount,
      totalQuota
    })

    return successResponse({
      order: { ...orderData, _id: orderId },
      payment: paymentInfo
    }, '创建购买订单成功')

  } catch (error) {
    logger.error('Failed to create purchase order', { 
      error: error.message,
      userId: params.userId,
      packageId: params.packageId,
      quantity: params.quantity
    })
    throw error
  }
}

/**
 * 模拟支付处理
 * @param {object} params 请求参数（包含鉴权后的用户信息）
 * @param {string} params.userId 用户ID（由鉴权中间件设置）
 * @param {string} params.orderId 订单ID
 * @param {boolean} params.success 模拟支付是否成功
 * @returns {object} 支付结果
 */
async function simulatePayment(params) {
  try {
    const { userId, orderId, success = true } = params

    if (!userId) {
      throw createBusinessError(ERROR_CODES.UNAUTHORIZED, '无效的Token或用户认证失败', 401)
    }

    if (!orderId) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAM, '订单ID不能为空', 400)
    }

    logger.info('Processing simulated payment', { userId, orderId, success })

    // 获取订单详情
    const order = await purchaseOrderCollection.getOrderDetail(orderId, userId)

    if (!order) {
      throw createBusinessError(ERROR_CODES.ORDER_NOT_FOUND, '订单不存在或无权访问', 404)
    }

    // 检查订单状态
    if (order.status !== 'PENDING') {
      throw createBusinessError(ERROR_CODES.INVALID_ORDER_STATUS, '订单状态不允许支付', 400)
    }

    if (success) {
      // 模拟支付成功
      const transactionId = `TXN${Date.now()}`
      const payTime = new Date()

      // 更新订单状态为已完成
      await purchaseOrderCollection.updateOrderStatus(orderId, 'COMPLETED', {
        payTime,
        transactionId
      })

      // 给用户充值算力
      await userCollection.addQuota(userId, order.packageQuota)

      logger.info('Simulated payment successful', {
        userId,
        orderId,
        transactionId,
        quotaAdded: order.packageQuota
      })

      return successResponse({
        success: true,
        orderId,
        transactionId,
        quotaAdded: order.packageQuota,
        message: '支付成功，算力已充值到您的账户'
      }, '模拟支付成功')

    } else {
      // 模拟支付失败，订单状态保持PENDING
      logger.info('Simulated payment failed', { userId, orderId })

      return successResponse({
        success: false,
        orderId,
        message: '支付失败，请重试或联系客服'
      }, '模拟支付失败')
    }

  } catch (error) {
    logger.error('Failed to process simulated payment', {
      error: error.message,
      userId: params.userId,
      orderId: params.orderId,
      success: params.success
    })
    throw error
  }
}

/**
 * 查询支付状态
 * @param {object} params 请求参数（包含鉴权后的用户信息）
 * @param {string} params.userId 用户ID（由鉴权中间件设置）
 * @param {string} params.orderId 订单ID
 * @returns {object} 支付状态信息
 */
async function queryPaymentStatus(params) {
  try {
    const { userId, orderId } = params

    if (!userId) {
      throw createBusinessError(ERROR_CODES.UNAUTHORIZED, '无效的Token或用户认证失败', 401)
    }

    if (!orderId) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAM, '订单ID不能为空', 400)
    }

    logger.info('查询支付状态', { userId, orderId })

    // 获取订单详情
    const order = await purchaseOrderCollection.getOrderDetail(orderId, userId)

    if (!order) {
      throw createBusinessError(ERROR_CODES.ORDER_NOT_FOUND, '订单不存在或无权访问', 404)
    }

    // 如果订单已经完成，直接返回状态
    if (order.status === 'COMPLETED') {
      logger.info('订单已完成', { userId, orderId })
      return successResponse({
        orderStatus: 'COMPLETED',
        paymentStatus: 'SUCCESS',
        message: '支付已完成'
      }, '查询支付状态成功')
    }

    // 如果有富友支付相关信息，查询富友支付状态
    if (order.fuiouOrderId || order.paymentUrl) {
      try {
        // 从订单中获取支付方式，如果没有则默认为微信支付（向后兼容）
        const orderPaymentMethod = order.paymentMethod || 'WECHAT'
        const fuiouResult = await fuiouPayment.queryOrderStatus(order.orderNo, orderPaymentMethod)

        if (fuiouResult.success) {
          // 如果富友支付显示支付成功，更新本地订单状态
          if (fuiouResult.orderStatus === '02' && fuiouResult.payStatus === '02') {
            // 支付成功，更新订单状态并充值算力
            const transactionId = `FUIOU_${order.orderNo}_${Date.now()}`
            const payTime = new Date()

            await purchaseOrderCollection.updateOrderStatus(orderId, 'COMPLETED', {
              payTime,
              transactionId,
              fuiouPayTime: fuiouResult.payTime,
              fuiouPayType: fuiouResult.payType
            })

            // 给用户充值算力
            await userCollection.addQuota(userId, order.packageQuota)

            logger.info('富友支付成功，订单状态已更新', {
              userId,
              orderId,
              transactionId,
              quotaAdded: order.packageQuota
            })

            return successResponse({
              orderStatus: 'COMPLETED',
              paymentStatus: 'SUCCESS',
              transactionId,
              quotaAdded: order.packageQuota,
              message: '支付成功，算力已充值到您的账户'
            }, '查询支付状态成功')
          } else {
            // 支付未完成
            return successResponse({
              orderStatus: 'PENDING',
              paymentStatus: 'PENDING',
              message: '支付未完成，请继续支付'
            }, '查询支付状态成功')
          }
        }
      } catch (error) {
        logger.error('查询富友支付状态失败', {
          error: error.message,
          orderId,
          fuiouOrderId: order.fuiouOrderId
        })
        // 富友支付查询失败，返回本地订单状态
      }
    }

    // 返回本地订单状态
    return successResponse({
      orderStatus: order.status,
      paymentStatus: order.status === 'COMPLETED' ? 'SUCCESS' : 'PENDING',
      message: order.status === 'COMPLETED' ? '支付已完成' : '支付未完成'
    }, '查询支付状态成功')

  } catch (error) {
    logger.error('查询支付状态失败', {
      error: error.message,
      userId: params.userId,
      orderId: params.orderId
    })
    throw error
  }
}

/**
 * 处理支付成功（内部接口，供回调云函数调用）
 * @param {object} params 请求参数
 * @param {string} params.orderNo 订单号
 * @param {string} params.orderAmount 支付金额
 * @param {string} params.payTime 支付时间
 * @param {string} params.payType 支付方式
 * @param {string} params.internalSecret 内部调用密钥
 * @returns {object} 处理结果
 */
async function processPaymentSuccess(params) {
  try {
    const { orderNo, orderAmount, payTime, payType, internalSecret } = params

    // 验证内部调用密钥
    const expectedSecret = 'numerology_ai_chat_internal_2024'
    if (internalSecret !== expectedSecret) {
      throw createBusinessError(ERROR_CODES.UNAUTHORIZED, '无效的内部调用密钥', 401)
    }

    if (!orderNo) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAM, '订单号不能为空', 400)
    }

    logger.info('处理支付成功回调', { orderNo, orderAmount, payTime, payType })

    // 根据订单号查找订单
    const db = cloud.database()
    const orderQuery = await db.collection('exe_purchase_orders')
      .where({ orderNo })
      .get()

    if (orderQuery.data.length === 0) {
      throw createBusinessError(ERROR_CODES.ORDER_NOT_FOUND, '订单不存在', 404)
    }

    const order = orderQuery.data[0]
    const orderId = order._id

    // 检查订单状态，避免重复处理
    if (order.status === 'COMPLETED') {
      logger.info('订单已处理，跳过重复处理', { orderNo, orderId })
      return successResponse({
        success: true,
        message: '订单已处理'
      }, '支付成功处理完成')
    }

    // 生成交易ID
    const transactionId = `FUIOU_${orderNo}_${Date.now()}`

    // 解析富友支付时间格式 (YYYYMMDDHHMMSS)
    let parsedPayTime
    try {
      if (payTime && payTime.length === 14) {
        const year = payTime.substring(0, 4)
        const month = payTime.substring(4, 6)
        const day = payTime.substring(6, 8)
        const hour = payTime.substring(8, 10)
        const minute = payTime.substring(10, 12)
        const second = payTime.substring(12, 14)
        parsedPayTime = new Date(`${year}-${month}-${day}T${hour}:${minute}:${second}Z`)
      } else {
        parsedPayTime = new Date()
      }
    } catch (error) {
      logger.warn('解析支付时间失败，使用当前时间', { payTime, error: error.message })
      parsedPayTime = new Date()
    }

    // 更新订单状态为已完成
    await purchaseOrderCollection.updateOrderStatus(orderId, 'COMPLETED', {
      payTime: parsedPayTime,
      transactionId,
      fuiouPayTime: payTime,
      fuiouPayType: payType,
      fuiouPayAmount: orderAmount
    })

    // 给用户充值算力
    await userCollection.addQuota(order.userId, order.packageQuota)

    logger.info('支付成功处理完成', {
      orderNo,
      orderId,
      userId: order.userId,
      transactionId,
      quotaAdded: order.packageQuota
    })

    return successResponse({
      success: true,
      orderId,
      transactionId,
      quotaAdded: order.packageQuota,
      message: '支付成功处理完成'
    }, '支付成功处理完成')

  } catch (error) {
    logger.error('处理支付成功失败', {
      error: error.message,
      orderNo: params.orderNo,
      orderAmount: params.orderAmount
    })
    throw error
  }
}

module.exports = {
  getActivePackages,
  getPackageDetail,
  createPurchaseOrder,
  simulatePayment,
  queryPaymentStatus,
  processPaymentSuccess
}
