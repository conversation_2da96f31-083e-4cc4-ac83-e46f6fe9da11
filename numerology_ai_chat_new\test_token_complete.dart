import 'dart:convert';
import 'package:http/http.dart' as http;

/// 完整测试Token自动续签功能
void main() async {
  print('开始完整测试Token自动续签功能...');
  
  final testUsername = 'tokentest${DateTime.now().millisecondsSinceEpoch}';
  const testPassword = 'testpass123';
  
  // 1. 测试注册
  final registerSuccess = await testRegister(testUsername, testPassword);
  if (!registerSuccess) {
    print('❌ 注册失败，无法继续测试');
    return;
  }
  
  // 2. 测试登录并验证过期时间格式
  final loginTokens = await testLogin(testUsername, testPassword);
  if (loginTokens == null) {
    print('❌ 登录失败，无法继续测试');
    return;
  }
  
  // 3. 测试Token刷新
  await testTokenRefresh(loginTokens['refreshToken']);
  
  print('\n✅ Token自动续签功能测试完成');
}

/// 测试注册功能
Future<bool> testRegister(String username, String password) async {
  print('\n=== 测试注册功能 ===');
  
  try {
    final response = await http.post(
      Uri.parse('https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'action': 'register',
        'username': username,
        'password': password,
      }),
    );

    print('注册响应状态码: ${response.statusCode}');
    print('注册响应内容: ${response.body}');

    if (response.statusCode == 200) {
      final responseData = jsonDecode(response.body);
      
      if (responseData['code'] == 0) {
        print('✅ 注册成功');
        return true;
      } else {
        print('❌ 注册失败: ${responseData['message']}');
        return false;
      }
    } else {
      print('❌ 注册请求失败: ${response.statusCode}');
      return false;
    }
  } catch (e) {
    print('❌ 注册测试异常: $e');
    return false;
  }
}

/// 测试登录功能并返回tokens
Future<Map<String, dynamic>?> testLogin(String username, String password) async {
  print('\n=== 测试登录功能 ===');
  
  try {
    final response = await http.post(
      Uri.parse('https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'action': 'login',
        'username': username,
        'password': password,
      }),
    );

    print('登录响应状态码: ${response.statusCode}');
    print('登录响应内容: ${response.body}');

    if (response.statusCode == 200) {
      final responseData = jsonDecode(response.body);
      
      if (responseData['code'] == 0) {
        final data = responseData['data'];
        final innerData = data['data'];
        final tokens = innerData['tokens'];
        
        print('✅ 登录成功');
        print('Access Token: ${tokens['accessToken']?.substring(0, 20)}...');
        print('Refresh Token: ${tokens['refreshToken']?.substring(0, 20)}...');
        print('过期时间: ${tokens['expiresAt']}');
        
        // 验证过期时间格式
        if (tokens['expiresAt'] != null) {
          try {
            final expiryTime = DateTime.parse(tokens['expiresAt']);
            final now = DateTime.now();
            final duration = expiryTime.difference(now);
            print('Token有效期剩余: ${duration.inMinutes}分钟');
            
            if (duration.inHours >= 1 && duration.inHours <= 3) {
              print('✅ Token过期时间设置正确');
            } else {
              print('❌ Token过期时间异常: ${duration.inHours}小时');
            }
          } catch (e) {
            print('❌ 过期时间格式错误: $e');
          }
        } else {
          print('❌ 未返回过期时间');
        }
        
        return tokens;
      } else {
        print('❌ 登录失败: ${responseData['message']}');
        return null;
      }
    } else {
      print('❌ 登录请求失败: ${response.statusCode}');
      return null;
    }
  } catch (e) {
    print('❌ 登录测试异常: $e');
    return null;
  }
}

/// 测试Token刷新功能
Future<void> testTokenRefresh(String refreshToken) async {
  print('\n=== 测试Token刷新功能 ===');
  
  try {
    print('使用Refresh Token: ${refreshToken.substring(0, 20)}...');
    
    // 测试刷新Token
    final refreshResponse = await http.post(
      Uri.parse('https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'action': 'refreshToken',
        'refreshToken': refreshToken,
      }),
    );

    print('刷新Token响应状态码: ${refreshResponse.statusCode}');
    print('刷新Token响应内容: ${refreshResponse.body}');

    if (refreshResponse.statusCode == 200) {
      final refreshData = jsonDecode(refreshResponse.body);
      
      if (refreshData['code'] == 0) {
        final refreshResult = refreshData['data'];
        final newTokens = refreshResult['data']['tokens'];
        
        print('✅ Token刷新成功');
        print('新Access Token: ${newTokens['accessToken']?.substring(0, 20)}...');
        print('新Refresh Token: ${newTokens['refreshToken']?.substring(0, 20)}...');
        print('新过期时间: ${newTokens['expiresAt']}');
        
        // 验证新过期时间
        if (newTokens['expiresAt'] != null) {
          try {
            final newExpiryTime = DateTime.parse(newTokens['expiresAt']);
            final now = DateTime.now();
            final duration = newExpiryTime.difference(now);
            print('新Token有效期剩余: ${duration.inMinutes}分钟');
            
            if (duration.inHours >= 1 && duration.inHours <= 3) {
              print('✅ 新Token过期时间设置正确');
            } else {
              print('❌ 新Token过期时间异常: ${duration.inHours}小时');
            }
          } catch (e) {
            print('❌ 新过期时间格式错误: $e');
          }
        } else {
          print('❌ 未返回新过期时间');
        }
      } else {
        print('❌ Token刷新失败: ${refreshData['message']}');
      }
    } else {
      print('❌ Token刷新请求失败: ${refreshResponse.statusCode}');
    }
  } catch (e) {
    print('❌ Token刷新测试异常: $e');
  }
}
