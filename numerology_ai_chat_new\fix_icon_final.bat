@echo off
chcp 65001 >nul
echo ════════════════════════════════════════════════════════════
echo    🎨 完美图标修复工具 - 最终解决方案
echo ════════════════════════════════════════════════════════════
echo.
echo 📋 本工具将彻底解决Windows应用图标显示问题:
echo    ✅ 生成包含11种尺寸的高质量ICO文件
echo    ✅ 确保文件管理器正确显示自定义图标
echo    ✅ 支持高DPI显示和4K分辨率
echo    ✅ 自动验证和修复配置问题
echo.

echo 🔍 步骤1: 检查环境和文件...
if not exist "assets\images\logo.png" (
    echo ❌ 源图片文件不存在: assets\images\logo.png
    echo 💡 请确保将您的logo图片放在正确位置
    pause
    exit /b 1
)
echo ✅ 源图片文件存在

echo.
echo 🔍 步骤2: 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ Python未安装或不在PATH中
    echo 💡 将使用Flutter原生方式生成图标
    goto :flutter_method
)

python -c "from PIL import Image; print('PIL available')" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ PIL库未安装
    echo 💡 将使用Flutter原生方式生成图标
    goto :flutter_method
)

echo ✅ Python和PIL环境检查通过
goto :python_method

:python_method
echo.
echo 🎨 步骤3: 使用Python生成完美图标...
python create_perfect_icon.py
if %errorlevel% neq 0 (
    echo ❌ Python图标生成失败，尝试Flutter方式
    goto :flutter_method
)
echo ✅ Python图标生成完成
goto :build_app

:flutter_method
echo.
echo 🎨 步骤3: 使用Flutter生成图标...
echo 💡 运行flutter_launcher_icons插件
dart run flutter_launcher_icons
if %errorlevel% neq 0 (
    echo ❌ Flutter图标生成失败！
    echo 💡 请检查pubspec.yaml配置
    pause
    exit /b 1
)
echo ✅ Flutter图标生成完成

:build_app
echo.
echo 🔄 步骤4: 清理构建缓存...
flutter clean
if %errorlevel% neq 0 (
    echo ⚠️ 清理缓存失败，继续构建...
)
echo ✅ 缓存清理完成

echo.
echo 🔄 步骤5: 获取依赖包...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ 获取依赖包失败！
    pause
    exit /b 1
)
echo ✅ 依赖包获取完成

echo.
echo 🔄 步骤6: 构建Windows应用...
flutter build windows --release
if %errorlevel% neq 0 (
    echo ❌ 构建失败！
    echo 💡 可能的原因:
    echo    - 有其他程序正在使用exe文件
    echo    - 权限不足
    echo    - 构建环境问题
    echo.
    echo 📝 解决方案:
    echo    1. 关闭正在运行的应用程序
    echo    2. 以管理员身份运行此脚本
    echo    3. 检查flutter doctor输出
    pause
    exit /b 1
)

echo.
echo 🔍 步骤7: 验证构建结果...
set "exe_path=build\windows\x64\runner\Release\numerology_ai_chat.exe"
if exist "%exe_path%" (
    echo ✅ 应用程序构建成功
    for %%A in ("%exe_path%") do echo 📏 文件大小: %%~zA 字节
) else (
    echo ❌ 应用程序文件未找到
    pause
    exit /b 1
)

echo.
echo 🔍 步骤8: 检查图标文件...
set "icon_path=windows\runner\resources\app_icon.ico"
if exist "%icon_path%" (
    echo ✅ 图标文件存在
    for %%A in ("%icon_path%") do echo 📏 图标文件大小: %%~zA 字节
    
    REM 检查图标文件大小，判断质量
    for %%A in ("%icon_path%") do set icon_size=%%~zA
    if %icon_size% LSS 1000 (
        echo ⚠️ 图标文件较小，可能质量不足
        echo 💡 建议运行Python版本获得更好效果
    ) else (
        echo ✅ 图标文件大小正常
    )
) else (
    echo ❌ 图标文件不存在
)

echo.
echo 🎉 图标修复完成！
echo ════════════════════════════════════════════════════════════
echo 📁 可执行文件位置: %exe_path%
echo 🎨 图标文件位置: %icon_path%
echo.
echo 💡 如果图标仍然显示为默认图标，请尝试:
echo    1. 重启Windows资源管理器:
echo       - 按 Ctrl+Shift+Esc 打开任务管理器
echo       - 找到"Windows资源管理器"进程
echo       - 右键选择"重新启动"
echo.
echo    2. 清理Windows图标缓存:
echo       - 按 Win+R，输入: %%localappdata%%\Microsoft\Windows\Explorer
echo       - 删除所有 iconcache*.db 文件
echo       - 重启计算机
echo.
echo    3. 检查显示设置:
echo       - 确保Windows显示缩放设置合适
echo       - 检查是否启用了高DPI支持
echo.
echo 📝 技术说明:
echo    - 图标包含多种尺寸: 16x16 到 256x256
echo    - 支持高DPI显示和4K分辨率
echo    - 自动应用到编译后的exe文件
echo.
pause
