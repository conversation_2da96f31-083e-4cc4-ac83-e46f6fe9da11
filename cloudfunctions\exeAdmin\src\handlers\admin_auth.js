const bcrypt = require('bcryptjs')
const { validate, adminLoginSchema } = require('../utils/validate')
const { createBusinessError, ERROR_CODES, formatSuccessResponse } = require('../utils/error_handler')
const { adminCollection } = require('../utils/db')
const { generateTokenPair, verifyRefreshToken } = require('../middleware/auth')
const logger = require('../utils/logger')

/**
 * 管理员登录
 * @param {object} params 云函数事件对象
 * @returns {object} 登录结果
 */
async function adminLogin(params) {
  try {
    // 参数校验
    const validatedData = validate(adminLoginSchema, params)
    const { adminAccount, adminPassword } = validatedData
    
    logger.info('管理员登录尝试', { adminAccount })
    
    // 查找管理员
    const admin = await adminCollection.findByAccount(adminAccount)
    if (!admin) {
      logger.warn('管理员登录失败：账号不存在', { adminAccount })
      throw createBusinessError(ERROR_CODES.ADMIN_NOT_FOUND, '管理员账号或密码错误', 401)
    }
    
    // 检查管理员状态
    if (!admin.isActive) {
      logger.warn('管理员登录失败：账号已禁用', {
        adminAccount,
        isActive: admin.isActive
      })
      throw createBusinessError(ERROR_CODES.UNAUTHORIZED, '管理员账号已被禁用', 401)
    }
    
    // 验证密码
    const isPasswordValid = await bcrypt.compare(adminPassword, admin.adminPassword)
    if (!isPasswordValid) {
      logger.warn('管理员登录失败：密码错误', { adminAccount })
      throw createBusinessError(ERROR_CODES.INVALID_PASSWORD, '管理员账号或密码错误', 401)
    }
    
    // 生成Token
    const tokens = generateTokenPair(admin)
    
    // 更新最后登录时间
    await adminCollection.updateLoginInfo(admin._id)
    
    logger.info('管理员登录成功', {
      adminId: admin._id,
      adminAccount: admin.adminAccount,
      adminRole: admin.adminRole
    })
    
    // 返回登录结果（不包含敏感信息）
    const responseData = {
      adminInfo: {
        adminId: admin._id,
        adminAccount: admin.adminAccount,
        adminRole: admin.adminRole,
        adminPermissions: admin.adminPermissions || [],
        lastLoginTime: new Date().toISOString()
      },
      tokens
    }
    
    return formatSuccessResponse(responseData, '登录成功')
    
  } catch (error) {
    logger.error('管理员登录异常', {
      adminAccount: params?.adminAccount,
      error: error.message
    })
    throw error
  }
}

/**
 * 刷新管理员Token
 * @param {object} params 云函数事件对象
 * @returns {object} 刷新结果
 */
async function refreshAdminToken(params) {
  try {
    const { refreshToken } = params
    
    if (!refreshToken) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, 'Refresh Token不能为空', 400)
    }
    
    logger.info('管理员Token刷新尝试')
    
    // 验证refresh token
    const decoded = verifyRefreshToken(refreshToken)
    
    // 查找管理员
    const admin = await adminCollection.findById(decoded.adminId)
    if (!admin) {
      logger.warn('Token刷新失败：管理员不存在', { adminId: decoded.adminId })
      throw createBusinessError(ERROR_CODES.ADMIN_NOT_FOUND, '管理员不存在', 401)
    }
    
    // 检查管理员状态
    if (!admin.isActive) {
      logger.warn('Token刷新失败：管理员账号已禁用', {
        adminId: admin._id,
        isActive: admin.isActive
      })
      throw createBusinessError(ERROR_CODES.UNAUTHORIZED, '管理员账号已被禁用', 401)
    }
    
    // 生成新的Token对
    const tokens = generateTokenPair(admin)
    
    logger.info('管理员Token刷新成功', {
      adminId: admin._id,
      adminAccount: admin.adminAccount
    })
    
    const responseData = {
      adminInfo: {
        adminId: admin._id,
        adminAccount: admin.adminAccount,
        adminRole: admin.adminRole,
        adminPermissions: admin.adminPermissions || []
      },
      tokens
    }
    
    return formatSuccessResponse(responseData, 'Token刷新成功')
    
  } catch (error) {
    logger.error('管理员Token刷新异常', {
      error: error.message
    })
    throw error
  }
}

/**
 * 获取管理员信息
 * @param {object} params 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 管理员信息
 */
async function getAdminInfo(params, adminAuth) {
  try {
    logger.info('获取管理员信息', { adminId: adminAuth.adminId })
    
    // 查找管理员完整信息
    const admin = await adminCollection.findById(adminAuth.adminId)
    if (!admin) {
      throw createBusinessError(ERROR_CODES.ADMIN_NOT_FOUND, '管理员不存在', 404)
    }
    
    const responseData = {
      adminId: admin._id,
      adminAccount: admin.adminAccount,
      adminRole: admin.adminRole,
      adminPermissions: admin.adminPermissions || [],
      adminStatus: admin.adminStatus,
      createdTime: admin.createdTime,
      lastLoginTime: admin.lastLoginTime,
      description: admin.description || ''
    }
    
    return formatSuccessResponse(responseData, '获取管理员信息成功')
    
  } catch (error) {
    logger.error('获取管理员信息异常', {
      adminId: adminAuth.adminId,
      error: error.message
    })
    throw error
  }
}

const adminAuthHandler = {
  login: adminLogin,
  refresh: refreshAdminToken,
  getInfo: getAdminInfo,
};

module.exports = adminAuthHandler;