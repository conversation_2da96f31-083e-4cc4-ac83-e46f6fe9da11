// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stats_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SystemStats _$SystemStatsFromJson(Map<String, dynamic> json) => SystemStats(
      overview:
          OverviewStats.fromJson(json['overview'] as Map<String, dynamic>),
      trends: json['trends'] == null
          ? null
          : TrendsData.fromJson(json['trends'] as Map<String, dynamic>),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$SystemStatsToJson(SystemStats instance) =>
    <String, dynamic>{
      'overview': instance.overview,
      'trends': instance.trends,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };

OverviewStats _$OverviewStatsFromJson(Map<String, dynamic> json) =>
    OverviewStats(
      users: UserStats.fromJson(json['users'] as Map<String, dynamic>),
      models: ModelStats.fromJson(json['models'] as Map<String, dynamic>),
      agents: AgentStats.fromJson(json['agents'] as Map<String, dynamic>),
      pages: PageStats.fromJson(json['pages'] as Map<String, dynamic>),
      usage: json['usage'] == null
          ? null
          : UsageStats.fromJson(json['usage'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$OverviewStatsToJson(OverviewStats instance) =>
    <String, dynamic>{
      'users': instance.users,
      'models': instance.models,
      'agents': instance.agents,
      'pages': instance.pages,
      'usage': instance.usage,
    };

UserStats _$UserStatsFromJson(Map<String, dynamic> json) => UserStats(
      total: (json['total'] as num).toInt(),
      active: (json['active'] as num).toInt(),
      inactive: (json['inactive'] as num).toInt(),
      withMembership: (json['withMembership'] as num).toInt(),
      totalQuota: (json['totalQuota'] as num).toInt(),
      totalUsage: (json['totalUsage'] as num).toInt(),
    );

Map<String, dynamic> _$UserStatsToJson(UserStats instance) => <String, dynamic>{
      'total': instance.total,
      'active': instance.active,
      'inactive': instance.inactive,
      'withMembership': instance.withMembership,
      'totalQuota': instance.totalQuota,
      'totalUsage': instance.totalUsage,
    };

ModelStats _$ModelStatsFromJson(Map<String, dynamic> json) => ModelStats(
      total: (json['total'] as num).toInt(),
      active: (json['active'] as num).toInt(),
      inactive: (json['inactive'] as num).toInt(),
      withApiKey: (json['withApiKey'] as num).toInt(),
    );

Map<String, dynamic> _$ModelStatsToJson(ModelStats instance) =>
    <String, dynamic>{
      'total': instance.total,
      'active': instance.active,
      'inactive': instance.inactive,
      'withApiKey': instance.withApiKey,
    };

AgentStats _$AgentStatsFromJson(Map<String, dynamic> json) => AgentStats(
      total: (json['total'] as num).toInt(),
      active: (json['active'] as num).toInt(),
      inactive: (json['inactive'] as num).toInt(),
      typeDistribution: Map<String, int>.from(json['byType'] as Map),
    );

Map<String, dynamic> _$AgentStatsToJson(AgentStats instance) =>
    <String, dynamic>{
      'total': instance.total,
      'active': instance.active,
      'inactive': instance.inactive,
      'byType': instance.typeDistribution,
    };

PageStats _$PageStatsFromJson(Map<String, dynamic> json) => PageStats(
      total: (json['total'] as num).toInt(),
      active: (json['active'] as num).toInt(),
      inactive: (json['inactive'] as num).toInt(),
      typeDistribution: Map<String, int>.from(json['byType'] as Map),
    );

Map<String, dynamic> _$PageStatsToJson(PageStats instance) => <String, dynamic>{
      'total': instance.total,
      'active': instance.active,
      'inactive': instance.inactive,
      'byType': instance.typeDistribution,
    };

UsageStats _$UsageStatsFromJson(Map<String, dynamic> json) => UsageStats(
      todayRequests: (json['todayRequests'] as num).toInt(),
      todayUsers: (json['todayUsers'] as num).toInt(),
      avgResponseTime: (json['avgResponseTime'] as num).toInt(),
      errorRate: (json['errorRate'] as num).toDouble(),
      peakHour: json['peakHour'] as String,
      popularModels: (json['popularModels'] as List<dynamic>)
          .map((e) => PopularItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      popularAgents: (json['popularAgents'] as List<dynamic>)
          .map((e) => PopularItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$UsageStatsToJson(UsageStats instance) =>
    <String, dynamic>{
      'todayRequests': instance.todayRequests,
      'todayUsers': instance.todayUsers,
      'avgResponseTime': instance.avgResponseTime,
      'errorRate': instance.errorRate,
      'peakHour': instance.peakHour,
      'popularModels': instance.popularModels,
      'popularAgents': instance.popularAgents,
    };

PopularItem _$PopularItemFromJson(Map<String, dynamic> json) => PopularItem(
      name: json['name'] as String,
      usage: (json['usage'] as num).toInt(),
    );

Map<String, dynamic> _$PopularItemToJson(PopularItem instance) =>
    <String, dynamic>{
      'name': instance.name,
      'usage': instance.usage,
    };

TrendsData _$TrendsDataFromJson(Map<String, dynamic> json) => TrendsData(
      dailyUsers: (json['dailyUsers'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
      dailyRequests: (json['dailyRequests'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
      dailyErrors: (json['dailyErrors'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
      dates: (json['dates'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$TrendsDataToJson(TrendsData instance) =>
    <String, dynamic>{
      'dailyUsers': instance.dailyUsers,
      'dailyRequests': instance.dailyRequests,
      'dailyErrors': instance.dailyErrors,
      'dates': instance.dates,
    };
