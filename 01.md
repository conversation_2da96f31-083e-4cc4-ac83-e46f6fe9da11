# Project Overview
- numerology_ai_chat_new项目架构：敏感数据不加载到本地，大语言模型调用通过go_proxy目录下的go程序中转，云函数已部署并绑定域名可直接调用
- Go proxy service is deployed at http://go.ziyuanit.com/ and only handles API streaming, authentication, and computing power deduction
- 项目仅支持Windows平台，不需要考虑多平台支持，数据库设计和功能实现应简化为Windows单平台
- 用户要求严格遵循提供的文档进行项目开发，持续维护exe_项目开发日志.md记录开发过程
- 云环境2部署信息：云函数链接为https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction、exeAdmin、exePaymentCallback，go中转链接为http://go1.ziyuan.com

# Development Principles
- 避免硬编码配置，从数据库动态获取智能体、模型和档次配置信息，不依赖本地配置
- 敏感数据通过云函数处理，前端只处理UI交互
- 用户偏好调用已部署云函数的HTTP端点而非在Go代理服务中直接使用腾讯云SDK
- 用户偏好简洁的文档风格，不喜欢冗长臃肿的文档
- 用户要求使用MCP工具直接调用测试而非创建Python测试脚本进行功能验证
- User prefers version management systems that support proper version ordering rather than just marking one version as 'latest' with a boolean flag.
- 腾讯云开发中，要更新云函数代码应该使用updateFunctionCode_cloudbase-mcp工具，而不是uploadFiles_cloudbase-mcp工具。uploadFiles只能上传到静态网站托管。
- 强制更新功能应该在用户点击更新按钮后阻止继续使用应用，而不是允许用户正常使用，否则就失去了强制更新的意义。
- 强制更新功能应该在用户点击下载后自动关闭当前程序，而不是保持程序运行显示更新状态。
- 用户偏好非强制更新时也要提示用户是否更新，如果同意更新则关闭程序并打开下载链接，如果不同意则继续使用
- 用户偏好可选更新时也应该提示用户并询问是否更新，而不是直接跳过更新提示进入应用
- 用户偏好在程序运行期间定时续签token以保持有效期，避免因token失效导致智能体、模型加载和对话功能失败
- 用户偏好通过临时缩短token过期时间来快速测试续签功能，测试通过后再恢复正常时长的测试方法
- 用户偏好实现记住密码功能：勾选时下次启动自动填入上次成功登录的账号密码，未勾选时不保存账号密码信息
- 用户偏好将硬编码的API端点存储在数据库配置中而非前端代码中，以便后期维护和修改，建议使用exe_system_config集合存储系统配置信息
- 用户偏好删除整个数据库集合而不仅仅是清空集合中的数据
- 用户偏好云函数使用Nodejs16.13运行时而不是Nodejs18.15
- 管理员密码的加解密逻辑位于云函数中，而不是本地代码中

# Features
- 实现登录和注册功能，注册完成后自动跳转到登录页，保持设计风格一致
- 添加个人中心页面，显示账号、昵称、邮箱、剩余次数、购买历史、额度消耗历史
- 实现算力计费系统：充值1000元得1000算力/充值10000元得20000算力，根据智能体类型和模型等级扣除不同算力
- 聊天界面支持回车发送消息、Shift+回车换行的键盘快捷键交互
- 聊天记录持久本地存储功能，支持图片存储
- 对话列表初始加载时显示真实对话标题而非'新对话'占位符
- 八字排盘功能使用省市区三级下拉选择框进行地址选择，支持真太阳时计算和夏令时处理
- 八字排盘功能添加子时规则选择（子时是否算作第二天）
- 实现token自动续签功能，用户偏好在程序运行期间定时续签token以保持有效期，避免因token失效导致智能体、模型加载和对话功能失败
- 新用户注册后不应获得任何默认算力，需要移除注册时自动分配10算力的功能
- 账号输入规则：纯数字或数字加大小写英文字母组成，不得有其他内容，长度不得小于8位；密码规则：可使用常规符号但不可使用汉字，长度不得小于8位
- 实现经销商渠道系统：邀请码注册制、渠道ID绑定、算力划转功能、条件性充值权限（经销商渠道用户默认无官方充值权限，但经销商余额不足8000时自动开放），以及服务到期时间显示等功能。邀请码系统使用简化设计：仅存储已核销的邀请码记录，邀请码通过云函数秘钥实时生成而非预存储，移除邀请码携带算力功能以降低复杂度，改为注册后由渠道转账算力的方式
- 经销商渠道系统需要三个独立程序：前端numerology_ai_chat_new、管理端admin、渠道端程序，渠道端需要独立的账号密码登录系统并具备算力转账功能
- 经销商渠道系统设计要求：官方渠道通过admin端创建邀请码而非渠道端，官方渠道无算力余额且不能转账，但有给所有账号赠送/修改/增减/扣除额度的权限，移除渠道类型字段和官方充值权限字段
- 用户偏好官方渠道不单独存储在渠道表中，而是在核销表和用户表的渠道ID字段中直接使用'官方'字符串标识

# UI/UX Requirements
- 新建对话时默认不选中八字信息、智能体和模型，只有用户在设置中配置了默认选项后才会预选
- 对话开始前必须先选中智能体和模型，满足所有必需条件后才能发送消息
- 对话开始后显示提示信息告知用户无法更改设置
- UI提示组件保持一致的设计风格，状态提示与主标题同级显示在一行内
- 流式输出时自动滚动到底部，用户滚动时停止自动滚动，滑动到底部后重新激活
- 输入字段保持自动跳转功能，同时保留正常的删除、全选、复制粘贴操作
- 整个页面范围内响应回车键事件，登录页面支持回车键触发登录按钮点击

# Payment Integration
- 富友支付系统集成已完成，支持用户选择微信或支付宝付款方式
- 富友支付生产环境：商户ID 0004910F9046468，终端ID 33945363
- 富友支付API端点：生产环境使用https://aipay-cloud.fuioupay.com/aggregatePay/preCreate
- 富友支付回调域名已绑定：https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exePaymentCallback
- 富友支付官方API文档：https://fundwx.fuiou.com/doc/#/aggregatePay/api

# Database
- 项目数据库架构包含13个核心集合：exe_admins, exe_agents, exe_app_versions, exe_models, exe_orders, exe_pages, exe_payment_logs, exe_payment_packages, exe_pricing_tiers, exe_purchase_orders, exe_system_config, exe_usage_history, exe_users
- 数据库已包含正确的deepseek和gemini模型配置，应予以保留，仅删除其他未使用的模型
- 创建档次数据库存储扣费标准并关联智能体和模型
- 用户认为数据库版本表中每个版本记录都有minSupportedVersion字段是不合理设计，偏好更简单的版本管理方案

# External Libraries
- lunar-javascript库官方API文档：https://6tail.cn/calendar/api.html，用于验证真太阳时计算功能

# Additional Requirements
- 用户要求在个人主页新增额度消耗历史功能，需要制定详细解决方案、创建执行计划文档、更新数据库结构文档，并要求充分了解前端、云函数、go代理和数据库的完整项目运作流程后再制定计划
- 每次更新完成之后，都需要由AI助手来运行并测试应用功能，确保更新生效
`