#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预构建钩子脚本 - 高质量图标自动生成
在Flutter构建前执行必要的准备工作，包括生成高质量图标
"""

import os
import sys
import subprocess
from pathlib import Path

def print_status(message, status="info"):
    """打印带状态的消息"""
    icons = {
        "info": "🔄",
        "success": "✅",
        "warning": "⚠️",
        "error": "❌"
    }
    print(f"{icons.get(status, '📝')} {message}")

def check_pillow_available():
    """检查PIL库是否可用"""
    try:
        import PIL
        return True
    except ImportError:
        return False

def run_ultimate_icon_generator():
    """运行终极图标生成器"""
    print_status("正在生成终极质量图标...", "info")

    if not check_pillow_available():
        print_status("PIL库不可用，跳过高级图标生成", "warning")
        return True

    try:
        script_path = Path("scripts/generate_ultimate_icon.py")
        if not script_path.exists():
            print_status("终极图标生成器不存在，跳过", "warning")
            return True

        result = subprocess.run(
            [sys.executable, str(script_path)],
            capture_output=True,
            text=True,
            check=True
        )

        print_status("终极图标生成成功", "success")
        return True

    except subprocess.CalledProcessError as e:
        print_status(f"终极图标生成失败: {e}", "warning")
        print_status("将使用Flutter默认图标生成", "info")
        return True  # 不阻止构建继续

def run_flutter_launcher_icons():
    """运行Flutter Launcher Icons生成图标"""
    print_status("正在生成Flutter图标...", "info")

    try:
        # 运行flutter pub get确保依赖已安装
        result = subprocess.run(
            ["flutter", "pub", "get"],
            capture_output=True,
            text=True,
            check=True
        )

        # 运行flutter_launcher_icons
        result = subprocess.run(
            ["flutter", "pub", "run", "flutter_launcher_icons"],
            capture_output=True,
            text=True,
            check=True
        )

        print_status("Flutter图标生成成功", "success")
        return True

    except subprocess.CalledProcessError as e:
        print_status(f"Flutter图标生成失败: {e}", "error")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误: {e.stderr}")
        return False
    except FileNotFoundError:
        print_status("Flutter命令未找到，请确保Flutter已正确安装", "error")
        return False

def verify_icon_quality():
    """验证生成的图标质量"""
    icon_path = Path("windows/runner/resources/app_icon.ico")

    if not icon_path.exists():
        print_status("图标文件不存在", "warning")
        return False

    file_size = icon_path.stat().st_size
    print_status(f"图标文件大小: {file_size:,} 字节", "info")

    # 检查文件大小是否合理（高质量图标应该大于10KB）
    if file_size < 10000:
        print_status("图标文件可能质量较低", "warning")
        return False
    else:
        print_status("图标文件质量检查通过", "success")
        return True

def main():
    """主函数"""
    print("🔧 预构建钩子 - 高质量图标生成")
    print("=" * 50)

    # 切换到项目根目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    os.chdir(project_root)

    print_status(f"工作目录: {os.getcwd()}", "info")

    # 步骤1: 尝试生成终极质量图标
    run_ultimate_icon_generator()

    # 步骤2: 运行Flutter Launcher Icons（作为备用）
    if not run_flutter_launcher_icons():
        print_status("预构建准备失败", "error")
        return False

    # 步骤3: 验证图标质量
    verify_icon_quality()

    print_status("预构建准备完成", "success")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
