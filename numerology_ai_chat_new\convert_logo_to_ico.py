#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将logo.png转换为Windows应用图标格式(.ico)
"""

from PIL import Image
import os

def convert_jpg_to_ico(jpg_path, ico_path):
    """
    将JPG图片转换为ICO格式
    
    Args:
        jpg_path: 源JPG文件路径
        ico_path: 目标ICO文件路径
    """
    try:
        # 打开JPG图片
        with Image.open(jpg_path) as img:
            # 转换为RGBA模式（支持透明度）
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            # 创建多个尺寸的图标（Windows标准尺寸）
            sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
            
            # 调整图片为正方形（取较小的边作为基准）
            min_size = min(img.size)
            # 从中心裁剪为正方形
            left = (img.size[0] - min_size) // 2
            top = (img.size[1] - min_size) // 2
            right = left + min_size
            bottom = top + min_size
            img_square = img.crop((left, top, right, bottom))
            
            # 创建不同尺寸的图标
            icon_images = []
            for size in sizes:
                resized = img_square.resize(size, Image.Resampling.LANCZOS)
                icon_images.append(resized)
            
            # 保存为ICO文件
            icon_images[0].save(
                ico_path,
                format='ICO',
                sizes=sizes,
                append_images=icon_images[1:]
            )
            
            print(f"✅ 成功将 {jpg_path} 转换为 {ico_path}")
            print(f"📏 生成的图标包含以下尺寸: {', '.join([f'{s[0]}x{s[1]}' for s in sizes])}")
            
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return False
    
    return True

def main():
    # 定义文件路径
    jpg_path = "assets/images/logo.png"
    ico_path = "windows/runner/resources/app_icon.ico"
    
    # 检查源文件是否存在
    if not os.path.exists(jpg_path):
        print(f"❌ 源文件不存在: {jpg_path}")
        return
    
    # 确保目标目录存在
    os.makedirs(os.path.dirname(ico_path), exist_ok=True)
    
    # 备份原有的ico文件
    if os.path.exists(ico_path):
        backup_path = ico_path + ".backup"
        try:
            os.rename(ico_path, backup_path)
            print(f"📦 已备份原图标文件到: {backup_path}")
        except Exception as e:
            print(f"⚠️ 备份原图标文件失败: {e}")
    
    # 执行转换
    print(f"🔄 开始转换 {jpg_path} 到 {ico_path}...")
    success = convert_jpg_to_ico(jpg_path, ico_path)
    
    if success:
        print("🎉 图标转换完成！")
        print("📝 接下来需要重新编译Flutter应用以应用新图标。")
        print("💡 运行命令: flutter build windows --release")
    else:
        print("💥 图标转换失败，请检查错误信息。")

if __name__ == "__main__":
    main()
