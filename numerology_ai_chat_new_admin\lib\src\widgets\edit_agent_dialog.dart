
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:numerology_ai_chat_admin/src/models/agent_model.dart';
import 'package:numerology_ai_chat_admin/src/providers/agent_provider.dart';

class EditAgentDialog extends ConsumerStatefulWidget {
  final Agent? agent;
  const EditAgentDialog({super.key, this.agent});

  @override
  ConsumerState<EditAgentDialog> createState() => _EditAgentDialogState();
}

class _EditAgentDialogState extends ConsumerState<EditAgentDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _displayNameController;
  late TextEditingController _promptController;
  late TextEditingController _descriptionController;
  late String _agentType;
  late bool _isActive;

  @override
  void initState() {
    super.initState();
    final agent = widget.agent;
    _nameController = TextEditingController(text: agent?.agentName ?? '');
    _displayNameController = TextEditingController(text: agent?.agentDisplayName ?? '');
    _promptController = TextEditingController(text: '');
    _descriptionController = TextEditingController(text: agent?.description ?? '');
    _agentType = agent?.agentType ?? '八字';
    _isActive = agent?.isActive ?? true;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.agent == null ? '创建智能体' : '编辑智能体'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(controller: _nameController, decoration: const InputDecoration(labelText: '智能体名称 (agentName)')),
              TextFormField(controller: _displayNameController, decoration: const InputDecoration(labelText: '显示名称 (agentDisplayName)')),
              TextFormField(controller: _promptController, decoration: const InputDecoration(labelText: '系统提示词 (systemPrompt)'), maxLines: 5),
              TextFormField(controller: _descriptionController, decoration: const InputDecoration(labelText: '描述 (description)')),
              DropdownButtonFormField<String>(
                value: _agentType,
                items: ['八字', '通用'].map((e) => DropdownMenuItem(value: e, child: Text(e))).toList(),
                onChanged: (value) => setState(() => _agentType = value!),
                decoration: const InputDecoration(labelText: '智能体类型 (agentType)'),
              ),
              SwitchListTile(
                title: const Text('是否激活'),
                value: _isActive,
                onChanged: (value) => setState(() => _isActive = value),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('取消')),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              final agentData = {
                'agentName': _nameController.text,
                'agentDisplayName': _displayNameController.text,
                'systemPrompt': _promptController.text,
                'description': _descriptionController.text,
                'agentType': _agentType,
                'isActive': _isActive,
                'sortOrder': widget.agent?.sortOrder ?? 0,
              };

              if (widget.agent == null) {
                ref.read(agentProvider.notifier).createAgent(agentData);
              } else {
                ref.read(agentProvider.notifier).updateAgent(widget.agent!.agentId, agentData);
              }
              Navigator.of(context).pop();
            }
          },
          child: const Text('保存'),
        ),
      ],
    );
  }
}
