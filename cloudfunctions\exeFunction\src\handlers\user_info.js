const { userCollection, usageHistoryCollection, agentCollection, modelCollection, db, _, COLLECTIONS } = require('../utils/db')
const { successResponse, createBusinessError, ERROR_CODES } = require('../middleware/error_handler')
const { checkMembershipStatus } = require('../middleware/auth')
const logger = require('../utils/logger')
const cloud = require('wx-server-sdk')
const { calculatePowerCost, getPowerCostDetails } = require('../services/power_calculation')

/**
 * 获取用户信息
 * @param {object} params 请求参数（包含鉴权后的用户信息）
 * @returns {object} 用户详细信息
 */
async function get(params) {
  try {
    const { userId } = params

    logger.info('Getting user info - params received', { params, userId })

    if (!userId) {
      throw createBusinessError(ERROR_CODES.UNAUTHORIZED, '无效的Token或用户认证失败', 401)
    }

    logger.info('Getting user info', { userId })
    
    const user = await userCollection.findById(userId)
    if (!user) {
      throw createBusinessError(ERROR_CODES.USER_NOT_FOUND, '用户不存在', 404)
    }
    
    const isMember = checkMembershipStatus(user)
    
    logger.info('User info retrieved successfully', {
      userId: user._id,
      username: user.username,
      isMember
    })
    
    return successResponse({
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        phone: user.phone,
        membership: {
          type: user.membership.type,
          expiresAt: user.membership.expiresAt,
          isValid: isMember
        },
        availableCount: user.availableCount,
        totalUsageCount: user.totalUsageCount,
        status: user.status,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt,
        recentPurchaseHistory: user.purchaseHistory ? user.purchaseHistory.slice(-10).reverse() : []
      }
    }, '获取用户信息成功')
    
  } catch (error) {
    logger.error('Failed to get user info', { error: error.message })
    throw error
  }
}

/**
 * 更新用户使用次数（扣减可用次数，增加总使用次数）
 * 使用数据库事务确保扣费和消耗记录创建的原子性
 * @param {object} params 请求参数（包含鉴权后的用户信息）
 * @param {string} params.userId 用户ID（由鉴权中间件设置）
 * @param {string} [params.agentId] 智能体ID（用于计算算力消耗）
 * @param {string} [params.modelId] 模型ID（用于计算算力消耗）
 * @returns {object} 更新结果
 */
async function updateUsage(params) {
  try {
    const { userId, agentId, modelId } = params

    if (!userId) {
      throw createBusinessError(ERROR_CODES.UNAUTHORIZED, '无效的Token或用户认证失败', 401)
    }

    logger.info('Updating user usage', { userId, agentId, modelId })

    const user = await userCollection.findById(userId)
    if (!user) {
      throw createBusinessError(ERROR_CODES.USER_NOT_FOUND, '用户不存在', 404)
    }

    if (user.status !== '激活') {
      throw createBusinessError(ERROR_CODES.USER_DISABLED, '用户账户已被禁用', 403)
    }

    // 计算需要扣除的算力
    let powerCost = 1 // 默认扣除1个算力（向后兼容）
    let powerDetails = null
    let agentInfo = null
    let modelInfo = null

    if (agentId && modelId) {
      // 如果提供了智能体和模型ID，则计算实际算力消耗
      powerCost = await calculatePowerCost(agentId, modelId)
      powerDetails = await getPowerCostDetails(agentId, modelId)

      // 获取智能体和模型的详细信息用于记录
      agentInfo = await agentCollection.findById(agentId)
      modelInfo = await modelCollection.findById(modelId)

      logger.info('Calculated power cost', {
        userId,
        agentId,
        modelId,
        powerCost,
        powerDetails,
        agentName: agentInfo?.agentName,
        modelName: modelInfo?.modelDisplayName
      })
    } else {
      logger.info('Using default power cost (backward compatibility)', {
        userId,
        powerCost
      })
    }

    if (user.availableCount < powerCost) {
      throw createBusinessError(ERROR_CODES.INSUFFICIENT_QUOTA, `可用算力不足，需要${powerCost}算力，当前仅有${user.availableCount}算力`, 403)
    }

    const balanceBefore = user.availableCount
    const balanceAfter = balanceBefore - powerCost
    const consumeTime = new Date()

    // 使用数据库事务确保扣费和消耗记录创建的原子性
    await db.runTransaction(async (transaction) => {
      // 在事务中执行用户扣费
      await transaction.collection(COLLECTIONS.USERS)
        .doc(userId)
        .update({
          data: {
            availableCount: _.inc(-powerCost),
            totalUsageCount: _.inc(powerCost),
            updatedAt: new Date()
          }
        })

      // 在事务中创建消耗记录（仅当有智能体和模型信息时）
      if (agentId && modelId && agentInfo && modelInfo && powerDetails) {
        const usageHistoryData = {
          userId,
          agentId,
          agentName: agentInfo.agentName,
          modelId,
          modelName: modelInfo.modelDisplayName,
          pricingTierId: powerDetails.pricingTierId || '',
          tierName: powerDetails.tierName || '未知档次',
          modelLevel: modelInfo.modelLevel || '初级',
          powerCost,
          balanceBefore,
          balanceAfter,
          consumeTime,
          description: powerDetails.description || `消耗${powerCost}算力`,
          createdAt: new Date()
        }

        await transaction.collection(COLLECTIONS.USAGE_HISTORY)
          .add({
            data: usageHistoryData
          })

        logger.info('Usage history record created in transaction', {
          userId,
          agentId,
          modelId,
          powerCost,
          balanceBefore,
          balanceAfter
        })
      }
    })

    const newAvailableCount = balanceAfter
    const newTotalUsageCount = user.totalUsageCount + powerCost

    logger.info('User usage updated successfully with transaction', {
      userId: userId,
      previousAvailableCount: balanceBefore,
      newAvailableCount,
      newTotalUsageCount,
      powerCost,
      powerDetails,
      usageHistoryCreated: !!(agentId && modelId && agentInfo && modelInfo)
    })

    return successResponse({
      userId: userId,
      availableCount: newAvailableCount,
      totalUsageCount: newTotalUsageCount,
      powerCost,
      powerDetails
    }, '使用次数更新成功')

  } catch (error) {
    logger.error('Failed to update user usage', {
      error: error.message,
      userId: params.userId,
      agentId: params.agentId,
      modelId: params.modelId
    })
    throw error
  }
}

/**
 * 获取用户消耗历史
 * @param {object} params 请求参数（包含鉴权后的用户信息）
 * @param {string} params.userId 用户ID（由鉴权中间件设置）
 * @param {number} [params.page=1] 页码
 * @param {number} [params.limit=20] 每页数量
 * @param {string} [params.startDate] 开始日期（ISO字符串）
 * @param {string} [params.endDate] 结束日期（ISO字符串）
 * @returns {object} 消耗历史列表
 */
async function getUserUsageHistory(params) {
  try {
    const { userId, page = 1, limit = 20, startDate, endDate } = params

    if (!userId) {
      throw createBusinessError(ERROR_CODES.UNAUTHORIZED, '无效的Token或用户认证失败', 401)
    }

    logger.info('Getting user usage history', {
      userId,
      page,
      limit,
      startDate,
      endDate
    })

    // 验证用户存在
    const user = await userCollection.findById(userId)
    if (!user) {
      throw createBusinessError(ERROR_CODES.USER_NOT_FOUND, '用户不存在', 404)
    }

    let result
    if (startDate && endDate) {
      // 按日期范围查询
      const start = new Date(startDate)
      const end = new Date(endDate)
      result = await usageHistoryCollection.getUserHistoryByDateRange(
        userId,
        start,
        end,
        page,
        limit
      )
    } else {
      // 查询全部历史
      result = await usageHistoryCollection.getUserHistory(userId, page, limit)
    }

    logger.info('User usage history retrieved successfully', {
      userId,
      page,
      limit,
      total: result.total,
      dataCount: result.data.length
    })

    return successResponse({
      history: result.data,
      pagination: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages
      }
    }, '获取消耗历史成功')

  } catch (error) {
    logger.error('Failed to get user usage history', {
      error: error.message,
      userId: params.userId,
      page: params.page,
      limit: params.limit
    })
    throw error
  }
}

module.exports = {
  get,
  updateUsage,
  getUserUsageHistory
}