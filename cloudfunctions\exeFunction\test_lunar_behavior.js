/**
 * 测试lunar-javascript库在关键时刻的行为
 * 用于验证年柱、月柱、日柱计算的准确性
 */

const { Lunar, Solar } = require('lunar-javascript');

console.log('=== 测试lunar-javascript库行为 ===\n');

// 测试样例1：年柱测试 - 2024年2月4日16点26分（立春前1分钟）
console.log('样例1：年柱测试 - 2024年2月4日16点26分');
console.log('立春时间：2024年2月4日16点27分');
console.log('预期：年柱应为癸卯（兔年），因为尚未交立春\n');

const solar1 = Solar.fromYmdHms(2024, 2, 4, 16, 26, 0);
const lunar1 = solar1.getLunar();

console.log('Solar对象信息：', solar1.toYmdHms());
console.log('Lunar对象信息：', lunar1.toFullString());

console.log('\n年柱计算方法对比：');
console.log('getYearInGanZhi():', lunar1.getYearInGanZhi());
console.log('getYearInGanZhiByLiChun():', lunar1.getYearInGanZhiByLiChun());
console.log('getYearInGanZhiExact():', lunar1.getYearInGanZhiExact());

console.log('\n月柱计算方法对比：');
console.log('getMonthInGanZhi():', lunar1.getMonthInGanZhi());
console.log('getMonthInGanZhiExact():', lunar1.getMonthInGanZhiExact());

console.log('\n日柱计算方法对比：');
console.log('getDayInGanZhi():', lunar1.getDayInGanZhi());
console.log('getDayInGanZhiExact():', lunar1.getDayInGanZhiExact());
console.log('getDayInGanZhiExact2():', lunar1.getDayInGanZhiExact2());

console.log('\n时柱计算：');
console.log('getTimeInGanZhi():', lunar1.getTimeInGanZhi());

console.log('\n=== 分割线 ===\n');

// 测试样例2：子时测试 - 1990年12月15日23点30分
console.log('样例2：子时测试 - 1990年12月15日23点30分');
console.log('争议点：子时是否算作第二天\n');

const solar2 = Solar.fromYmdHms(1990, 12, 15, 23, 30, 0);
const lunar2 = solar2.getLunar();

console.log('Solar对象信息：', solar2.toYmdHms());
console.log('Lunar对象信息：', lunar2.toFullString());

console.log('\n年柱计算方法对比：');
console.log('getYearInGanZhi():', lunar2.getYearInGanZhi());
console.log('getYearInGanZhiByLiChun():', lunar2.getYearInGanZhiByLiChun());
console.log('getYearInGanZhiExact():', lunar2.getYearInGanZhiExact());

console.log('\n月柱计算方法对比：');
console.log('getMonthInGanZhi():', lunar2.getMonthInGanZhi());
console.log('getMonthInGanZhiExact():', lunar2.getMonthInGanZhiExact());

console.log('\n日柱计算方法对比（关键）：');
console.log('getDayInGanZhi():', lunar2.getDayInGanZhi());
console.log('getDayInGanZhiExact():', lunar2.getDayInGanZhiExact());
console.log('getDayInGanZhiExact2():', lunar2.getDayInGanZhiExact2());

console.log('\n时柱计算：');
console.log('getTimeInGanZhi():', lunar2.getTimeInGanZhi());

console.log('\n=== 分割线 ===\n');

// 测试样例3：月柱节气交接测试 - 1990年11月8日23点30分
console.log('样例3：月柱节气交接测试 - 1990年11月8日23点30分');
console.log('立冬时间：1990年11月8日0点23分');
console.log('预期：月柱应该根据立冬时间判断\n');

const solar3 = Solar.fromYmdHms(1990, 11, 8, 23, 30, 0);
const lunar3 = solar3.getLunar();

console.log('Solar对象信息：', solar3.toYmdHms());
console.log('Lunar对象信息：', lunar3.toFullString());

console.log('\n年柱计算方法对比：');
console.log('getYearInGanZhi():', lunar3.getYearInGanZhi());
console.log('getYearInGanZhiByLiChun():', lunar3.getYearInGanZhiByLiChun());
console.log('getYearInGanZhiExact():', lunar3.getYearInGanZhiExact());

console.log('\n月柱计算方法对比（关键）：');
console.log('getMonthInGanZhi():', lunar3.getMonthInGanZhi());
console.log('getMonthInGanZhiExact():', lunar3.getMonthInGanZhiExact());

console.log('\n日柱计算方法对比：');
console.log('getDayInGanZhi():', lunar3.getDayInGanZhi());
console.log('getDayInGanZhiExact():', lunar3.getDayInGanZhiExact());
console.log('getDayInGanZhiExact2():', lunar3.getDayInGanZhiExact2());

console.log('\n时柱计算：');
console.log('getTimeInGanZhi():', lunar3.getTimeInGanZhi());

console.log('\n=== 节气信息查询 ===\n');

// 查询2024年立春的精确时间
console.log('2024年立春精确时间查询：');
try {
  const lichun2024 = lunar1.getJieQi('立春');
  if (lichun2024) {
    console.log('立春时间：', lichun2024.toYmdHms());
  } else {
    console.log('无法获取立春时间');
  }
} catch (e) {
  console.log('查询立春时间出错：', e.message);
}

// 查询1990年立冬的精确时间
console.log('\n1990年立冬精确时间查询：');
try {
  const lidong1990 = lunar3.getJieQi('立冬');
  if (lidong1990) {
    console.log('立冬时间：', lidong1990.toYmdHms());
  } else {
    console.log('无法获取立冬时间');
  }
} catch (e) {
  console.log('查询立冬时间出错：', e.message);
}

console.log('\n=== 测试完成 ===');
