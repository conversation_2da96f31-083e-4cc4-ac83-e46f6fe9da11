/// 档次配置模型（匹配云函数数据结构）
class PricingTier {
  final String id;
  final String tierName;
  final String tierDescription;
  final int basicModelCost;
  final int advancedModelCost;
  final bool isActive;
  final int sortOrder;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? createdBy;
  final String? updatedBy;

  const PricingTier({
    required this.id,
    required this.tierName,
    required this.tierDescription,
    required this.basicModelCost,
    required this.advancedModelCost,
    required this.isActive,
    required this.sortOrder,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
  });

  /// 从JSON创建档次配置模型（匹配云函数返回格式）
  factory PricingTier.fromJson(Map<String, dynamic> json) {
    return PricingTier(
      id: json['_id'] as String? ?? json['id'] as String,
      tierName: json['tierName'] as String,
      tierDescription: json['tierDescription'] as String,
      basicModelCost: json['basicModelCost'] as int,
      advancedModelCost: json['advancedModelCost'] as int,
      isActive: json['isActive'] as bool? ?? true,
      sortOrder: json['sortOrder'] as int? ?? 0,
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt'] as String) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt'] as String) : null,
      createdBy: json['createdBy'] as String?,
      updatedBy: json['updatedBy'] as String?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'tierName': tierName,
      'tierDescription': tierDescription,
      'basicModelCost': basicModelCost,
      'advancedModelCost': advancedModelCost,
      'isActive': isActive,
      'sortOrder': sortOrder,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'createdBy': createdBy,
      'updatedBy': updatedBy,
    };
  }

  /// 复制并修改档次配置模型
  PricingTier copyWith({
    String? id,
    String? tierName,
    String? tierDescription,
    int? basicModelCost,
    int? advancedModelCost,
    bool? isActive,
    int? sortOrder,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
  }) {
    return PricingTier(
      id: id ?? this.id,
      tierName: tierName ?? this.tierName,
      tierDescription: tierDescription ?? this.tierDescription,
      basicModelCost: basicModelCost ?? this.basicModelCost,
      advancedModelCost: advancedModelCost ?? this.advancedModelCost,
      isActive: isActive ?? this.isActive,
      sortOrder: sortOrder ?? this.sortOrder,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
    );
  }

  /// 是否可用
  bool get isAvailable => isActive;

  /// 根据模型等级获取算力消耗
  int getCostForModelLevel(String? modelLevel) {
    if (modelLevel == '高级') {
      return advancedModelCost;
    } else {
      return basicModelCost; // 默认为初级模型
    }
  }

  @override
  String toString() {
    return 'PricingTier(id: $id, tierName: $tierName, '
        'basicModelCost: $basicModelCost, advancedModelCost: $advancedModelCost, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PricingTier && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// 档次配置列表响应模型
class PricingTierListResponse {
  final List<Map<String, dynamic>> pricingTiers;

  const PricingTierListResponse({
    required this.pricingTiers,
  });

  factory PricingTierListResponse.fromJson(Map<String, dynamic> json) {
    return PricingTierListResponse(
      pricingTiers: (json['pricingTiers'] as List<dynamic>)
          .map((item) => item as Map<String, dynamic>)
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pricingTiers': pricingTiers,
    };
  }
}
