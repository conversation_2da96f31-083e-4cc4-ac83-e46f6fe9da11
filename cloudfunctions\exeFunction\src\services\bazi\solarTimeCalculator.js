/**
 * 真太阳时计算模块
 * 包含夏令时处理和真太阳时计算功能
 */

const { adjustForDaylightSaving, getDaylightSavingInfo } = require('./daylightSavingData');

/**
 * 计算真太阳时
 * @param {Date} beijingTime - 北京时间
 * @param {number} longitude - 经度（东经为正）
 * @param {Date} birthDate - 出生日期（用于夏令时判断）
 * @param {string} region - 地区（大陆、台湾、香港、澳门）
 * @param {boolean} considerDaylightSaving - 是否考虑夏令时（默认true）
 * @returns {Date} 真太阳时
 */
function calculateSolarTime(beijingTime, longitude, birthDate, region = '大陆', considerDaylightSaving = true) {
  try {
    // 验证输入参数
    if (!beijingTime || isNaN(beijingTime.getTime())) {
      throw new Error('无效的北京时间参数');
    }
    if (!birthDate || isNaN(birthDate.getTime())) {
      throw new Error('无效的出生日期参数');
    }
    if (typeof longitude !== 'number' || isNaN(longitude)) {
      throw new Error('无效的经度参数');
    }

    // 步骤1：夏令时调整（根据用户选择）
    let adjustedTime = beijingTime;
    if (considerDaylightSaving) {
      adjustedTime = adjustForDaylightSaving(beijingTime, birthDate, region);

      // 验证夏令时调整后的时间是否有效
      if (!adjustedTime || isNaN(adjustedTime.getTime())) {
        console.warn('夏令时调整产生无效时间，使用原始时间');
        adjustedTime = beijingTime;
      }
    }

    // 步骤2：真太阳时计算
    // 公式：真太阳时 = 调整后北京时间 + (当地经度 - 120°) × 4分钟/度
    const longitudeDiff = longitude - 120; // 与北京时间基准经度的差值
    const timeOffsetMinutes = longitudeDiff * 4; // 每度经度差4分钟
    const timeOffsetMs = timeOffsetMinutes * 60 * 1000; // 转换为毫秒

    const solarTime = new Date(adjustedTime.getTime() + timeOffsetMs);

    // 验证计算结果是否有效
    if (isNaN(solarTime.getTime())) {
      throw new Error('真太阳时计算结果无效');
    }

    // 获取夏令时信息
    const dstInfo = getDaylightSavingInfo(birthDate, region);

    console.log(`真太阳时计算: 北京时间=${beijingTime.toISOString()}, 经度=${longitude}, 调整=${timeOffsetMinutes.toFixed(2)}分钟, 考虑夏令时=${considerDaylightSaving}, 夏令时=${dstInfo.isDaylightSaving}, 真太阳时=${solarTime.toISOString()}`);

    return solarTime;
  } catch (error) {
    console.error('真太阳时计算失败:', error);
    return beijingTime; // 出错时返回原始时间
  }
}

/**
 * 验证经纬度参数
 * @param {number} latitude - 纬度
 * @param {number} longitude - 经度
 * @returns {boolean} 是否有效
 */
function validateCoordinates(latitude, longitude) {
  // 中国境内经纬度范围
  const MIN_LAT = 18.0;  // 最南端（海南）
  const MAX_LAT = 54.0;  // 最北端（黑龙江）
  const MIN_LNG = 73.0;  // 最西端（新疆）
  const MAX_LNG = 135.0; // 最东端（黑龙江）
  
  return (
    typeof latitude === 'number' &&
    typeof longitude === 'number' &&
    latitude >= MIN_LAT &&
    latitude <= MAX_LAT &&
    longitude >= MIN_LNG &&
    longitude <= MAX_LNG &&
    !isNaN(latitude) &&
    !isNaN(longitude)
  );
}

/**
 * 格式化时间差信息
 * @param {Date} originalTime - 原始时间
 * @param {Date} solarTime - 真太阳时
 * @param {number} longitude - 经度
 * @param {string} region - 地区
 * @param {boolean} considerDaylightSaving - 是否考虑了夏令时
 * @returns {Object} 时间差信息
 */
function getTimeDifferenceInfo(originalTime, solarTime, longitude, region = '大陆', considerDaylightSaving = true) {
  const totalDiffMs = solarTime.getTime() - originalTime.getTime();
  const totalDiffMinutes = Math.round(totalDiffMs / (60 * 1000));
  const longitudeDiff = longitude - 120;

  // 计算仅经度调整的时间差异（不考虑夏令时）
  const longitudeOnlyDiffMinutes = Math.round(longitudeDiff * 4);

  // 获取夏令时信息
  const dstInfo = getDaylightSavingInfo(originalTime, region);

  // 计算夏令时调整量
  let daylightSavingAdjustmentMinutes = 0;
  if (considerDaylightSaving && dstInfo.isDaylightSaving) {
    daylightSavingAdjustmentMinutes = -60; // 夏令时减去1小时
  }

  // 生成详细的时间差异描述
  let detailedTimeDiffText = '';
  if (considerDaylightSaving && dstInfo.isDaylightSaving) {
    // 考虑夏令时的情况
    if (longitudeOnlyDiffMinutes === 0 && daylightSavingAdjustmentMinutes !== 0) {
      // 只有夏令时调整
      detailedTimeDiffText = `慢${Math.abs(daylightSavingAdjustmentMinutes)}分钟（夏令时调整）`;
    } else if (longitudeOnlyDiffMinutes !== 0 && daylightSavingAdjustmentMinutes !== 0) {
      // 既有经度调整又有夏令时调整
      const longitudeText = longitudeOnlyDiffMinutes > 0 ? `快${longitudeOnlyDiffMinutes}分钟` : `慢${Math.abs(longitudeOnlyDiffMinutes)}分钟`;
      detailedTimeDiffText = `${totalDiffMinutes > 0 ? '快' : '慢'}${Math.abs(totalDiffMinutes)}分钟（经度调整${longitudeText}，夏令时调整慢60分钟）`;
    } else {
      // 只有经度调整（理论上不会到这里，因为已经检查了夏令时）
      detailedTimeDiffText = longitudeOnlyDiffMinutes > 0 ? `快${longitudeOnlyDiffMinutes}分钟` : `慢${Math.abs(longitudeOnlyDiffMinutes)}分钟`;
    }
  } else {
    // 不考虑夏令时的情况，只有经度调整
    detailedTimeDiffText = longitudeOnlyDiffMinutes > 0 ? `快${longitudeOnlyDiffMinutes}分钟` : `慢${Math.abs(longitudeOnlyDiffMinutes)}分钟`;
  }

  return {
    originalTime: originalTime.toISOString(),
    solarTime: solarTime.toISOString(),
    longitude,
    longitudeDiff: parseFloat(longitudeDiff.toFixed(4)),
    // 总时间差异
    timeDiffMinutes: totalDiffMinutes,
    timeDiffText: totalDiffMinutes > 0 ? `快${totalDiffMinutes}分钟` : `慢${Math.abs(totalDiffMinutes)}分钟`,
    // 详细的时间差异描述
    detailedTimeDiffText,
    // 分解的时间差异信息
    longitudeOnlyDiffMinutes,
    daylightSavingAdjustmentMinutes,
    // 夏令时信息
    daylightSaving: dstInfo,
    // 是否考虑了夏令时
    considerDaylightSaving
  };
}

/**
 * 批量计算真太阳时（用于测试）
 * @param {Array} testCases - 测试用例数组
 * @returns {Array} 计算结果数组
 */
function batchCalculateSolarTime(testCases) {
  return testCases.map(testCase => {
    const { beijingTime, longitude, birthDate, region, description } = testCase;
    
    try {
      const solarTime = calculateSolarTime(beijingTime, longitude, birthDate, region);
      const timeDiff = getTimeDifferenceInfo(beijingTime, solarTime, longitude, region, true);

      return {
        description,
        success: true,
        ...timeDiff
      };
    } catch (error) {
      return {
        description,
        success: false,
        error: error.message
      };
    }
  });
}

/**
 * 获取地区列表
 * @returns {Array} 支持的地区列表
 */
function getSupportedRegions() {
  return ['大陆', '台湾', '香港', '澳门'];
}

/**
 * 检查是否需要真太阳时计算
 * @param {number} longitude - 经度
 * @returns {boolean} 是否需要计算
 */
function needsSolarTimeCalculation(longitude) {
  if (!longitude || typeof longitude !== 'number') {
    return false;
  }
  
  // 如果经度与120度相差超过0.1度，则需要真太阳时计算
  const diff = Math.abs(longitude - 120);
  return diff > 0.1;
}

/**
 * 获取真太阳时计算说明
 * @param {number} longitude - 经度
 * @param {string} region - 地区
 * @returns {string} 计算说明
 */
function getSolarTimeExplanation(longitude, region = '大陆') {
  const longitudeDiff = longitude - 120;
  const timeOffsetMinutes = Math.round(longitudeDiff * 4);
  
  let explanation = `根据出生地经度${longitude.toFixed(4)}°，与北京时间基准经度120°相差${Math.abs(longitudeDiff).toFixed(4)}°，`;
  
  if (timeOffsetMinutes > 0) {
    explanation += `真太阳时比北京时间快${timeOffsetMinutes}分钟。`;
  } else if (timeOffsetMinutes < 0) {
    explanation += `真太阳时比北京时间慢${Math.abs(timeOffsetMinutes)}分钟。`;
  } else {
    explanation += `真太阳时与北京时间相同。`;
  }
  
  if (region !== '大陆') {
    explanation += `\n注意：${region}地区可能有特殊的夏令时规定。`;
  }
  
  return explanation;
}

module.exports = {
  calculateSolarTime,
  validateCoordinates,
  getTimeDifferenceInfo,
  batchCalculateSolarTime,
  getSupportedRegions,
  needsSolarTimeCalculation,
  getSolarTimeExplanation
};
