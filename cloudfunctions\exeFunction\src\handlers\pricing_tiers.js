const { pricingTierCollection } = require('../utils/db')
const { successResponse } = require('../middleware/error_handler')
const logger = require('../utils/logger')

/**
 * 获取档次配置列表
 * @param {object} params 请求参数
 * @returns {object} 档次配置列表
 */
async function getList(params) {
  try {
    logger.info('Getting pricing tiers list')

    // 获取启用的档次配置列表
    const tiers = await pricingTierCollection.getActiveList()

    logger.info('Pricing tiers list retrieved successfully', {
      count: tiers.length
    })

    // 返回档次配置列表
    return successResponse({
      pricingTiers: tiers.map(tier => ({
        id: tier._id,
        tierName: tier.tierName,
        tierDescription: tier.tierDescription,
        basicModelCost: tier.basicModelCost,
        advancedModelCost: tier.advancedModelCost,
        isActive: tier.isActive,
        sortOrder: tier.sortOrder,
        createdAt: tier.createdAt || new Date().toISOString(),
        updatedAt: tier.updatedAt || new Date().toISOString()
      }))
    }, '获取档次配置列表成功')

  } catch (error) {
    logger.error('Failed to get pricing tiers list', {
      error: error.message
    })
    throw error
  }
}

module.exports = {
  getList
}
