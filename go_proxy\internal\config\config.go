package config

import (
	"os"

	"github.com/joho/godotenv"
)

// Config 应用配置结构
type Config struct {
	ServerPort       string
	CloudFuncBaseURL string
}

// Load 加载配置
func Load() (*Config, error) {
	// 尝试加载.env文件（如果存在）
	_ = godotenv.Load()

	cfg := &Config{
		ServerPort:       getEnv("SERVER_PORT", "8081"),
		CloudFuncBaseURL: getEnv("CLOUDFUNC_BASE_URL", "https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction"),
	}

	return cfg, nil
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}