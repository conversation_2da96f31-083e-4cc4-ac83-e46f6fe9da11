import{r as _,b as h,D as T,o as E,c as r,y as j,d as z,e as K,f as n,g as t,w as o,E as m,k as M,m as u,C as B,z as H,t as c}from"./index-0bf154dd.js";import{w as L}from"./weakAdminService-c6c1aace.js";import{_ as Q}from"./_plugin-vue_export-helper-c27b6911.js";const F={class:"history"},I={class:"card-header"},O={class:"header-actions"},G={class:"pagination"},J={class:"card-header"},R={class:"code-display"},W={class:"pagination"},X={__name:"HistoryView",setup(Y){const v=_("quota"),f=_(!1),C=_([]),i=h({page:1,pageSize:20,total:0}),y=h({targetUsername:""}),b=_(!1),S=_([]),p=h({page:1,pageSize:20,total:0}),d=async()=>{var s;try{f.value=!0;const e=await L.getQuotaOperations(i.page,i.pageSize,y.targetUsername);e.success?(C.value=e.data.data,i.total=e.data.total):m.error(((s=e.error)==null?void 0:s.message)||"加载算力操作历史失败")}catch(e){console.error("Load quota history error:",e),m.error("加载算力操作历史失败")}finally{f.value=!1}},g=async()=>{var s;try{b.value=!0;const e=await L.getActivationHistory(p.page,p.pageSize);e.success?(S.value=e.data.data,p.total=e.data.total):m.error(((s=e.error)==null?void 0:s.message)||"加载激活码历史失败")}catch(e){console.error("Load activation history error:",e),m.error("加载激活码历史失败")}finally{b.value=!1}},q=s=>s?new Date(s).toLocaleString("zh-CN"):"-";return T(v,s=>{s==="quota"?d():s==="activation"&&g()}),E(()=>{d()}),(s,e)=>{const N=r("el-input"),w=r("el-button"),l=r("el-table-column"),V=r("el-tag"),U=r("el-table"),x=r("el-pagination"),A=r("el-card"),k=r("el-tab-pane"),P=r("el-tabs"),D=j("loading");return z(),K("div",F,[e[11]||(e[11]=n("div",{class:"page-header"},[n("h2",null,"操作历史"),n("p",null,"查看算力操作和激活码核销历史")],-1)),t(P,{modelValue:v.value,"onUpdate:modelValue":e[5]||(e[5]=a=>v.value=a),class:"history-tabs"},{default:o(()=>[t(k,{label:"算力操作历史",name:"quota"},{default:o(()=>[t(A,{shadow:"hover"},{header:o(()=>[n("div",I,[e[8]||(e[8]=n("span",null,"算力操作记录",-1)),n("div",O,[t(N,{modelValue:y.targetUsername,"onUpdate:modelValue":e[0]||(e[0]=a=>y.targetUsername=a),placeholder:"搜索用户名",clearable:"",style:{width:"200px","margin-right":"10px"},onClear:d,onKeyup:M(d,["enter"])},null,8,["modelValue"]),t(w,{onClick:d},{default:o(()=>e[6]||(e[6]=[u("搜索")])),_:1,__:[6]}),t(w,{onClick:d},{default:o(()=>e[7]||(e[7]=[u("刷新")])),_:1,__:[7]})])])]),default:o(()=>[B((z(),H(U,{data:C.value,stripe:"",style:{width:"100%"}},{default:o(()=>[t(l,{prop:"targetUsername",label:"目标用户",width:"150"}),t(l,{prop:"operationType",label:"操作类型",width:"120"},{default:o(({row:a})=>[t(V,{type:a.operationType==="increase"?"success":"warning"},{default:o(()=>[u(c(a.operationType==="increase"?"增加":"减少"),1)]),_:2},1032,["type"])]),_:1}),t(l,{prop:"quotaAmount",label:"算力数量",width:"120"}),t(l,{prop:"quotaBefore",label:"操作前",width:"120"}),t(l,{prop:"quotaAfter",label:"操作后",width:"120"}),t(l,{prop:"reason",label:"操作原因","min-width":"200"},{default:o(({row:a})=>[n("span",null,c(a.reason||"-"),1)]),_:1}),t(l,{prop:"createdAt",label:"操作时间",width:"180"},{default:o(({row:a})=>[u(c(q(a.createdAt)),1)]),_:1})]),_:1},8,["data"])),[[D,f.value]]),n("div",G,[t(x,{"current-page":i.page,"onUpdate:currentPage":e[1]||(e[1]=a=>i.page=a),"page-size":i.pageSize,"onUpdate:pageSize":e[2]||(e[2]=a=>i.pageSize=a),total:i.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:d,onCurrentChange:d},null,8,["current-page","page-size","total"])])]),_:1})]),_:1}),t(k,{label:"激活码核销历史",name:"activation"},{default:o(()=>[t(A,{shadow:"hover"},{header:o(()=>[n("div",J,[e[10]||(e[10]=n("span",null,"激活码核销记录",-1)),t(w,{onClick:g},{default:o(()=>e[9]||(e[9]=[u("刷新")])),_:1,__:[9]})])]),default:o(()=>[B((z(),H(U,{data:S.value,stripe:"",style:{width:"100%"}},{default:o(()=>[t(l,{prop:"activationCode",label:"激活码",width:"300"},{default:o(({row:a})=>[n("span",R,c(a.activationCode.substring(0,20))+"...",1)]),_:1}),t(l,{prop:"quotaAmount",label:"算力数量",width:"120"}),t(l,{prop:"usedBy",label:"使用者",width:"150"}),t(l,{prop:"usedAt",label:"使用时间",width:"180"},{default:o(({row:a})=>[u(c(q(a.usedAt)),1)]),_:1}),t(l,{prop:"status",label:"状态",width:"100"},{default:o(({row:a})=>[t(V,{type:"success"},{default:o(()=>[u(c(a.status==="used"?"已使用":"未使用"),1)]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[D,b.value]]),n("div",W,[t(x,{"current-page":p.page,"onUpdate:currentPage":e[3]||(e[3]=a=>p.page=a),"page-size":p.pageSize,"onUpdate:pageSize":e[4]||(e[4]=a=>p.pageSize=a),total:p.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:g,onCurrentChange:g},null,8,["current-page","page-size","total"])])]),_:1})]),_:1})]),_:1},8,["modelValue"])])}}},te=Q(X,[["__scopeId","data-v-b126b745"]]);export{te as default};
