const cloud = require('wx-server-sdk')
const { successResponse } = require('../middleware/error_handler')
const logger = require('../utils/logger')

// 初始化数据库
const db = cloud.database()
const versionCollection = db.collection('exe_app_versions')

/**
 * 版本号比较函数
 * 使用简单的语义化版本比较
 * @param {string} version1 版本号1
 * @param {string} version2 版本号2
 * @returns {number} 1: version1 > version2, 0: 相等, -1: version1 < version2
 */
function compareVersions(version1, version2) {
  const v1Parts = version1.split('.').map(Number)
  const v2Parts = version2.split('.').map(Number)
  
  const maxLength = Math.max(v1Parts.length, v2Parts.length)
  
  for (let i = 0; i < maxLength; i++) {
    const v1Part = v1Parts[i] || 0
    const v2Part = v2Parts[i] || 0
    
    if (v1Part > v2Part) return 1
    if (v1Part < v2Part) return -1
  }
  
  return 0
}

/**
 * 检查版本更新
 * @param {object} params 请求参数
 * @param {string} params.currentVersion 当前版本号
 * @returns {object} 版本检查结果
 */
async function checkVersion(params) {
  try {
    const { currentVersion } = params
    
    if (!currentVersion) {
      throw new Error('缺少当前版本号参数')
    }
    
    logger.info('Checking version update', {
      currentVersion
    })

    // 获取当前版本信息
    const currentVersionQuery = await versionCollection
      .where({
        versionNumber: currentVersion
      })
      .get()

    // 获取所有可用版本，按发布时间排序
    const allVersionsQuery = await versionCollection
      .where({
        isActive: true
      })
      .orderBy('publishedAt', 'desc')
      .get()

    const allVersions = allVersionsQuery.data || []
    
    // 找到最新版本（版本号最大的可用版本）
    const availableVersions = allVersions.filter(v => v.isAvailable)
    let latestVersion = null
    
    if (availableVersions.length > 0) {
      latestVersion = availableVersions.reduce((latest, current) => {
        if (!latest) return current
        return compareVersions(current.versionNumber, latest.versionNumber) > 0 ? current : latest
      })
    }

    // 当前版本信息
    const currentVersionRecord = currentVersionQuery.data?.[0]
    
    // 判断是否有更新
    const hasUpdate = latestVersion && compareVersions(latestVersion.versionNumber, currentVersion) > 0
    
    // 判断当前版本是否可用
    const isCurrentVersionAvailable = currentVersionRecord?.isAvailable !== false
    
    // 判断是否需要强制更新
    const needsForceUpdate = !isCurrentVersionAvailable || (currentVersionRecord?.forceUpdate === true)

    const result = {
      isAvailable: isCurrentVersionAvailable,
      hasUpdate: hasUpdate,
      needsForceUpdate: needsForceUpdate,
      currentVersion: currentVersion,
      latestVersion: latestVersion?.versionNumber || currentVersion,
      downloadUrl: latestVersion?.downloadUrl || '',
      updateDescription: latestVersion?.updateDescription || '',
      releaseNotes: latestVersion?.releaseNotes || '',
      versionName: latestVersion?.versionName || ''
    }

    logger.info('Version check completed', {
      currentVersion,
      result
    })

    return successResponse(result, '版本检查成功')

  } catch (error) {
    logger.error('Failed to check version', {
      error: error.message,
      currentVersion: params.currentVersion
    })
    throw error
  }
}

module.exports = {
  checkVersion
}
