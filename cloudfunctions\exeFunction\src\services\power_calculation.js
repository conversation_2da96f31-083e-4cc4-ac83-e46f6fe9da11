/**
 * 算力计算服务
 * 基于数据库动态获取配置，移除硬编码
 */

const { agentCollection, modelCollection, pricingTierCollection } = require('../utils/db')

// 缓存配置
let cachedPricingTiers = null
let cachedAgentPricingTiers = null
let cachedModelLevels = null
let cacheExpireTime = null

/**
 * 从数据库加载配置
 */
async function loadPricingConfiguration() {
  try {
    // 检查缓存是否有效（5分钟）
    if (cacheExpireTime &&
        Date.now() < cacheExpireTime &&
        cachedPricingTiers &&
        cachedAgentPricingTiers &&
        cachedModelLevels) {
      return // 缓存仍然有效
    }

    console.log('PowerCalculationService: 从数据库加载档次配置...')

    // 获取档次配置
    const pricingTiers = await pricingTierCollection.getActiveList()
    const tiersMap = {}
    pricingTiers.forEach(tier => {
      tiersMap[tier._id] = {
        name: tier.tierName,
        description: tier.tierDescription,
        basicModelCost: tier.basicModelCost,
        advancedModelCost: tier.advancedModelCost,
      }
    })

    // 获取智能体档次映射
    const agents = await agentCollection.getActiveList()
    const agentPricingTiers = {}
    agents.forEach(agent => {
      if (agent.pricingTierId) {
        agentPricingTiers[agent._id] = agent.pricingTierId
      }
    })

    // 获取模型等级映射
    const models = await modelCollection.getActiveList()
    const modelLevels = {}
    models.forEach(model => {
      if (model.modelLevel) {
        const level = model.modelLevel === '高级' ? 'advanced' : 'basic'
        modelLevels[model._id] = level
      }
    })

    // 更新缓存
    cachedPricingTiers = tiersMap
    cachedAgentPricingTiers = agentPricingTiers
    cachedModelLevels = modelLevels
    cacheExpireTime = Date.now() + (5 * 60 * 1000) // 5分钟

    console.log('PowerCalculationService: 档次配置加载完成')
    console.log('档次数量:', Object.keys(tiersMap).length)
    console.log('智能体数量:', Object.keys(agentPricingTiers).length)
    console.log('模型数量:', Object.keys(modelLevels).length)

  } catch (e) {
    console.error('PowerCalculationService: 加载档次配置失败:', e)
    // 如果加载失败，使用默认配置
    useDefaultConfiguration()
  }
}

/**
 * 使用默认配置（当数据库不可用时的后备方案）
 */
function useDefaultConfiguration() {
  cachedPricingTiers = {
    '2ed3518f684fc84402ecb3472b0e960d': { // 基础档次
      name: '基础档次',
      description: '适用于简单对话类智能体',
      basicModelCost: 5,
      advancedModelCost: 10,
    },
    '2ed3518f684fc84402ecb348303ee4b9': { // 标准档次
      name: '标准档次',
      description: '适用于一般专业智能体',
      basicModelCost: 10,
      advancedModelCost: 20,
    },
    '2ed3518f684fc84402ecb34925b02dec': { // 高级档次
      name: '高级档次',
      description: '适用于复杂分析类智能体',
      basicModelCost: 15,
      advancedModelCost: 30,
    },
  }

  cachedAgentPricingTiers = {
    'd77d384f684d773c02cee7a00b964919': '2ed3518f684fc84402ecb348303ee4b9', // 八字命理大师
    'd77d384f684d773c02cee7a149887ea2': '2ed3518f684fc84402ecb348303ee4b9', // 紫微斗数大师
    'd77d384f684d773c02cee7a2638103ef': '2ed3518f684fc84402ecb3472b0e960d', // 通用AI助手
  }

  cachedModelLevels = {
    '684e58d3dc6caee3a2101c6b': 'basic',    // DeepSeek Chat
    '684e58dedc6caee3a2102b76': 'advanced', // Gemini 2.5 Pro
  }

  cacheExpireTime = Date.now() + (1 * 60 * 1000) // 短期缓存，尽快重试
  console.log('PowerCalculationService: 使用默认配置')
}

/**
 * 计算对话消耗的算力
 * @param {string} agentId 智能体ID
 * @param {string} modelId 模型ID
 * @returns {Promise<number>} 消耗的算力数量，如果无法计算则返回默认值5
 */
async function calculatePowerCost(agentId, modelId) {
  try {
    // 确保配置已加载
    await loadPricingConfiguration()

    // 获取智能体的档次ID
    const pricingTierId = cachedAgentPricingTiers[agentId]
    if (!pricingTierId) {
      console.warn(`警告: 未找到智能体 ${agentId} 的档次配置，使用默认值`)
      return 5 // 默认基础档次的初级模型消耗
    }

    // 获取模型等级
    const modelLevel = cachedModelLevels[modelId]
    if (!modelLevel) {
      console.warn(`警告: 未找到模型 ${modelId} 的等级配置，使用默认值`)
      return 5 // 默认基础档次的初级模型消耗
    }

    // 获取档次配置
    const tierConfig = cachedPricingTiers[pricingTierId]
    if (!tierConfig) {
      console.warn(`警告: 未找到档次 ${pricingTierId} 的配置，使用默认值`)
      return 5 // 默认基础档次的初级模型消耗
    }

    // 根据模型等级获取对应的算力消耗
    const costKey = modelLevel === 'basic' ? 'basicModelCost' : 'advancedModelCost'
    const cost = tierConfig[costKey]

    if (cost == null) {
      console.warn(`警告: 档次配置中未找到 ${costKey}，使用默认值`)
      return 5 // 默认基础档次的初级模型消耗
    }

    console.log(`PowerCalculationService: 计算算力消耗 - 智能体:${agentId}, 模型:${modelId}, 档次:${tierConfig.name}, 等级:${modelLevel}, 消耗:${cost}`)
    return cost
  } catch (e) {
    console.error(`错误: 计算算力消耗时发生异常: ${e}，使用默认值`)
    return 5 // 默认基础档次的初级模型消耗
  }
}

/**
 * 获取算力消耗的详细说明
 * @param {string} agentId 智能体ID
 * @param {string} modelId 模型ID
 * @returns {Promise<object>} 包含档次、等级和消耗的详细信息
 */
async function getPowerCostDetails(agentId, modelId) {
  // 确保配置已加载
  await loadPricingConfiguration()

  const pricingTierId = cachedAgentPricingTiers[agentId]
  const modelLevel = cachedModelLevels[modelId]
  const cost = await calculatePowerCost(agentId, modelId)

  // 获取档次名称
  const tierConfig = cachedPricingTiers[pricingTierId]
  const tierName = tierConfig ? tierConfig.name : '未知档次'

  // 获取等级名称
  let levelName = '未知等级'
  if (modelLevel === 'basic') {
    levelName = '初级模型'
  } else if (modelLevel === 'advanced') {
    levelName = '高级模型'
  }

  return {
    agentId,
    modelId,
    pricingTierId,
    tierName,
    modelLevel,
    levelName,
    cost,
    description: `${tierName} + ${levelName} = ${cost}算力`,
  }
}

/**
 * 检查用户算力是否足够
 * @param {number} userAvailablePower 用户当前可用算力
 * @param {string} agentId 智能体ID
 * @param {string} modelId 模型ID
 * @returns {Promise<boolean>} 是否有足够算力
 */
async function hasEnoughPower(userAvailablePower, agentId, modelId) {
  const requiredPower = await calculatePowerCost(agentId, modelId)
  return userAvailablePower >= requiredPower
}

/**
 * 清除缓存（用于强制重新加载配置）
 */
function clearCache() {
  cachedPricingTiers = null
  cachedAgentPricingTiers = null
  cachedModelLevels = null
  cacheExpireTime = null
  console.log('PowerCalculationService: 缓存已清除')
}

module.exports = {
  calculatePowerCost,
  getPowerCostDetails,
  hasEnoughPower,
  loadPricingConfiguration,
  clearCache
}
