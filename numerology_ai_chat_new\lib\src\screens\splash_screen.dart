import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';

import '../core/constants/app_constants.dart';
import '../core/storage/storage_service.dart';
import '../providers/auth_provider.dart';
import '../providers/version_provider.dart';
import '../widgets/version_update_dialog.dart';
import '../models/version_model.dart';
import '../services/system_config_service.dart';

/// 启动页面
class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  String _statusMessage = '正在初始化...';
  bool _forceUpdateRequired = false;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      // 1. 存储服务初始化
      _updateStatus('正在初始化存储服务...');
      await ref.read(storageInitProvider.future);

      // 2. 系统配置预加载
      _updateStatus('正在加载系统配置...');
      try {
        final storageService = ref.read(storageServiceProvider);
        final systemConfigService = SystemConfigService.getInstance(storageService);
        await systemConfigService.preloadGoProxyApiUrl();
        print('SplashScreen: 系统配置预加载完成');
      } catch (e) {
        print('SplashScreen: 系统配置预加载失败: $e');
        // 不阻止启动流程，使用默认配置
      }

      // 3. 版本检查
      _updateStatus('正在检查版本更新...');
      final versionResult = await ref.read(versionProvider.notifier).checkVersion();

      print('SplashScreen: 版本检查结果: $versionResult');

      // 处理版本检查结果
      if (versionResult.isSuccess && mounted) {
        print('SplashScreen: 开始处理版本检查结果');
        await _handleVersionCheck(versionResult);
        print('SplashScreen: 版本检查处理完成');
      } else {
        print('SplashScreen: 版本检查失败或组件已销毁');
      }

      // 如果需要强制更新，停止启动流程
      if (_forceUpdateRequired) {
        print('SplashScreen: 需要强制更新，停止启动流程');
        _updateStatus('需要强制更新，请完成更新后重启应用');
        return;
      }

      // 4. 认证状态初始化（会话级登录：始终设置为未认证状态）
      _updateStatus('正在初始化认证状态...');
      await ref.read(authProvider.notifier).initAuthWithStorage();

      // 5. 延迟一下显示启动动画
      await Future.delayed(const Duration(milliseconds: 1000));

      // 6. 跳转到登录页面（会话级登录：每次启动都需要重新登录）
      if (mounted) {
        print('SplashScreen: 准备跳转到登录页面');
        try {
          // 使用延迟确保当前帧完成
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              context.go(AppRoutes.login);
              print('SplashScreen: 已调用跳转到登录页面');
            }
          });
        } catch (e) {
          print('SplashScreen: 跳转到登录页面失败: $e');
        }
      } else {
        print('SplashScreen: 组件已销毁，无法跳转');
      }
    } catch (e) {
      print('SplashScreen: 初始化失败: $e');
      // 初始化失败，跳转到登录页面
      if (mounted) {
        print('SplashScreen: 异常情况下准备跳转到登录页面');
        try {
          // 使用延迟确保当前帧完成
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              context.go(AppRoutes.login);
              print('SplashScreen: 异常情况下已调用跳转到登录页面');
            }
          });
        } catch (navError) {
          print('SplashScreen: 异常情况下跳转到登录页面失败: $navError');
        }
      } else {
        print('SplashScreen: 异常情况下组件已销毁，无法跳转');
      }
    }
  }

  /// 更新状态消息
  void _updateStatus(String message) {
    if (mounted) {
      setState(() {
        _statusMessage = message;
      });
    }
  }

  /// 处理版本检查结果
  Future<void> _handleVersionCheck(VersionCheckResult result) async {
    final response = result.response;
    if (response == null) {
      print('SplashScreen: 版本检查响应为空');
      return;
    }

    print('SplashScreen: 版本检查响应: hasUpdate=${response.hasUpdate}, needsForceUpdate=${response.needsForceUpdate}');

    // 如果需要强制更新，显示强制更新对话框并阻止继续
    if (response.needsForceUpdate) {
      print('SplashScreen: 显示强制更新对话框');
      _forceUpdateRequired = true;
      final updateInfo = UpdateInfo.fromVersionCheckResponse(response);
      await VersionUpdateDialogHelper.show(
        context,
        updateInfo,
        isForced: true,
      );
      // 强制更新时不继续启动流程
      print('SplashScreen: 强制更新对话框已显示，设置强制更新标记');
      return;
    }

    // 如果有可选更新，显示更新提示并等待用户选择
    if (response.hasUpdate) {
      print('SplashScreen: 显示可选更新对话框');
      final updateInfo = UpdateInfo.fromVersionCheckResponse(response);

      // 显示可选更新对话框并等待用户选择
      await VersionUpdateDialogHelper.show(
        context,
        updateInfo,
        isForced: false,
      );

      print('SplashScreen: 可选更新对话框已关闭，继续启动流程');
    } else {
      print('SplashScreen: 无需更新');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 应用图标
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: theme.colorScheme.primary.withOpacity(0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(24),
                child: Image.asset(
                  'assets/images/logo.png',
                  width: 120,
                  height: 120,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    // 如果图片加载失败，显示默认图标
                    return Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            theme.colorScheme.primary,
                            theme.colorScheme.secondary,
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(24),
                      ),
                      child: Icon(
                        Icons.auto_awesome,
                        size: 60,
                        color: theme.colorScheme.onPrimary,
                      ),
                    );
                  },
                ),
              ),
            )
                .animate()
                .scale(
                  duration: 800.ms,
                  curve: Curves.elasticOut,
                )
                .fadeIn(duration: 600.ms),
            
            const SizedBox(height: 32),
            
            // 应用名称
            Text(
              AppConstants.appName,
              style: theme.textTheme.headlineLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onBackground,
              ),
            )
                .animate(delay: 300.ms)
                .fadeIn(duration: 600.ms)
                .slideY(begin: 0.3, end: 0),
            
            const SizedBox(height: 8),
            
            // 应用描述
            Text(
              AppConstants.appDescription,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onBackground.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            )
                .animate(delay: 500.ms)
                .fadeIn(duration: 600.ms)
                .slideY(begin: 0.3, end: 0),
            
            const SizedBox(height: 48),
            
            // 加载指示器
            SizedBox(
              width: 32,
              height: 32,
              child: CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(
                  theme.colorScheme.primary,
                ),
              ),
            )
                .animate(delay: 700.ms)
                .fadeIn(duration: 600.ms)
                .scale(begin: const Offset(0.5, 0.5)),
            
            const SizedBox(height: 16),
            
            // 加载文本
            Text(
              _statusMessage,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onBackground.withOpacity(0.6),
              ),
            )
                .animate(delay: 900.ms)
                .fadeIn(duration: 600.ms),
          ],
        ),
      ),
    );
  }
}

/// 启动页面加载状态
class SplashLoadingWidget extends StatelessWidget {
  final String message;
  final double? progress;

  const SplashLoadingWidget({
    super.key,
    required this.message,
    this.progress,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (progress != null)
          LinearProgressIndicator(
            value: progress,
            backgroundColor: theme.colorScheme.outline.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(
              theme.colorScheme.primary,
            ),
          )
        else
          LinearProgressIndicator(
            backgroundColor: theme.colorScheme.outline.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(
              theme.colorScheme.primary,
            ),
          ),
        
        const SizedBox(height: 16),
        
        Text(
          message,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onBackground.withOpacity(0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}

/// 启动错误页面
class SplashErrorWidget extends StatelessWidget {
  final String error;
  final VoidCallback? onRetry;

  const SplashErrorWidget({
    super.key,
    required this.error,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.error_outline,
          size: 48,
          color: theme.colorScheme.error,
        ),
        
        const SizedBox(height: 16),
        
        Text(
          '初始化失败',
          style: theme.textTheme.titleLarge?.copyWith(
            color: theme.colorScheme.error,
          ),
        ),
        
        const SizedBox(height: 8),
        
        Text(
          error,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onBackground.withOpacity(0.7),
          ),
          textAlign: TextAlign.center,
        ),
        
        if (onRetry != null) ...[
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: onRetry,
            child: const Text('重试'),
          ),
        ],
      ],
    );
  }
}
