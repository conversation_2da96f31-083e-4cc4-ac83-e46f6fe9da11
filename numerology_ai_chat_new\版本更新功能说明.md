# 版本更新功能说明

## 功能概述

版本更新功能为鲸准大师应用提供了自动版本检查和更新提示机制，确保用户始终使用最新版本的应用。

## 主要特性

### 1. 自动版本检查
- 应用启动时自动检查版本更新
- 支持24小时检查间隔配置
- 网络异常时的降级处理

### 2. 多种更新模式
- **可选更新**：提示用户有新版本，用户可选择更新或稍后提醒
- **强制更新**：阻止用户继续使用应用，必须更新到最新版本
- **版本不可用**：当前版本被标记为不可用时，强制用户更新

### 3. 智能版本比较
- 支持语义化版本号（如 1.0.0）
- 自动识别更新类型：主要更新、功能更新、补丁更新
- 版本兼容性检查

### 4. 用户友好的UI
- Material3设计风格的更新对话框
- 支持Markdown格式的版本说明
- 更新类型标签显示（主要更新、功能更新等）
- 明暗主题适配

## 技术架构

### 数据库设计
版本信息存储在 `exe_app_versions` 集合中，包含以下字段：

```json
{
  "versionNumber": "1.1.0",        // 版本号
  "versionName": "增强版",          // 版本名称
  "isAvailable": true,             // 版本是否可用
  "forceUpdate": false,            // 是否强制更新
  "downloadUrl": "https://...",    // 下载链接
  "updateDescription": "...",      // 更新说明
  "releaseNotes": "# 版本说明...", // 发布说明（Markdown）
  "isActive": true,                // 是否启用
  "publishedAt": "2024-12-19T...", // 发布时间
  "createdAt": "2024-12-19T...",   // 创建时间
  "updatedAt": "2024-12-19T..."    // 更新时间
}
```

### 云函数接口
- **接口地址**：`/exeFunction`
- **Action**：`checkVersion`
- **参数**：`currentVersion` - 当前版本号
- **返回**：版本检查结果，包含是否有更新、是否强制更新等信息

### 前端组件
1. **VersionService**：版本检查服务层
2. **VersionProvider**：Riverpod状态管理
3. **VersionUpdateDialog**：版本更新对话框
4. **SplashScreen**：集成版本检查的启动页面

## 使用流程

### 1. 启动时检查
```
应用启动 → 存储服务初始化 → 版本检查 → 处理检查结果 → 认证初始化 → 跳转主页面
```

### 2. 版本检查逻辑
```
获取当前版本 → 调用云函数 → 解析响应 → 判断更新类型 → 显示相应UI
```

### 3. 用户交互
- **无更新**：正常启动，无提示
- **可选更新**：显示更新对话框，用户可选择"稍后提醒"或"立即更新"
- **强制更新**：显示强制更新对话框，只有"立即更新"按钮

## 配置说明

### 应用常量配置
```dart
// 版本检查配置
static const Duration versionCheckInterval = Duration(hours: 24);
static const Duration versionCheckTimeout = Duration(seconds: 10);
```

### 数据库索引
- `versionNumber`: 唯一索引
- `isAvailable`: 普通索引
- `publishedAt`: 降序索引

## 管理操作

### 1. 发布新版本
1. 在数据库中插入新版本记录
2. 设置 `isAvailable: true`
3. 配置下载链接和更新说明

### 2. 强制更新旧版本
1. 更新旧版本记录：`forceUpdate: true`
2. 或设置：`isAvailable: false`

### 3. 版本回滚
1. 设置问题版本：`isAvailable: false`
2. 确保有可用的稳定版本

## 错误处理

### 网络异常
- 版本检查失败时不阻止应用启动
- 记录错误日志
- 下次启动时重新检查

### 数据异常
- 版本号格式验证
- 响应数据完整性检查
- 默认降级处理

### 用户体验
- 检查超时控制（10秒）
- 启动流程不被阻塞
- 友好的错误提示

## 测试验证

### 单元测试
- 版本号比较逻辑
- 更新类型识别
- 版本格式验证

### 集成测试
- 完整的版本检查流程
- UI交互测试
- 网络异常处理

### 手动测试场景
1. 当前版本为最新版本
2. 有可选更新
3. 需要强制更新
4. 版本不可用
5. 网络异常情况

## 最佳实践

### 版本发布
1. 遵循语义化版本号规范
2. 提供详细的更新说明
3. 测试版本检查逻辑
4. 逐步发布，监控反馈

### 强制更新使用
1. 仅在安全问题或重大bug时使用
2. 提供清晰的更新原因说明
3. 确保下载链接可用
4. 预留足够的更新时间

### 用户体验
1. 更新说明简洁明了
2. 下载链接稳定可靠
3. 避免频繁的强制更新
4. 提供版本回滚机制

## 监控和维护

### 关键指标
- 版本检查成功率
- 用户更新转化率
- 强制更新使用频率
- 网络异常比例

### 日志记录
- 版本检查请求和响应
- 用户更新行为
- 错误和异常情况
- 性能指标

### 定期维护
- 清理过期版本记录
- 更新下载链接
- 优化检查逻辑
- 收集用户反馈

通过以上设计和实现，版本更新功能为应用提供了完善的版本管理能力，确保用户体验的同时保证应用的安全性和稳定性。
