import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/constants/app_constants.dart';
import '../core/storage/storage_service.dart';

/// 应用设置模型
class AppSettings {
  final String? defaultAgentId;
  final String? defaultModelName;
  final ThemeMode? themeMode;

  const AppSettings({
    this.defaultAgentId,
    this.defaultModelName,
    this.themeMode,
  });

  AppSettings copyWith({
    String? defaultAgentId,
    String? defaultModelName,
    ThemeMode? themeMode,
  }) {
    return AppSettings(
      defaultAgentId: defaultAgentId ?? this.defaultAgentId,
      defaultModelName: defaultModelName ?? this.defaultModelName,
      themeMode: themeMode ?? this.themeMode,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'defaultAgentId': defaultAgentId,
      'defaultModelName': defaultModelName,
      'themeMode': themeMode?.index,
    };
  }

  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      defaultAgentId: json['defaultAgentId'] as String?,
      defaultModelName: json['defaultModelName'] as String?,
      themeMode: json['themeMode'] != null && json['themeMode'] is int &&
              (json['themeMode'] as int) < ThemeMode.values.length
          ? ThemeMode.values[json['themeMode'] as int]
          : null,
    );
  }
}

/// 设置管理提供者
class SettingsProvider extends ChangeNotifier {
  final StorageService _storageService;

  AppSettings _settings = const AppSettings();

  SettingsProvider(this._storageService);

  AppSettings get settings => _settings;

  /// 获取默认智能体ID
  String? get defaultAgentId => _settings.defaultAgentId;

  /// 获取默认模型名称
  String? get defaultModelName => _settings.defaultModelName;

  /// 获取主题模式
  ThemeMode get themeMode => _settings.themeMode ?? ThemeMode.system;

  /// 初始化设置
  Future<void> initSettings() async {
    final result = await _storageService.get<Map<String, dynamic>>(AppConstants.settingsKey);
    result.when(
      success: (data) {
        if (data != null) {
          _settings = AppSettings.fromJson(data);
          notifyListeners();
        }
      },
      failure: (error) {
        // 使用默认设置
        _settings = const AppSettings();
      },
    );
  }

  /// 设置默认智能体
  Future<void> setDefaultAgent(String? agentId) async {
    if (_settings.defaultAgentId == agentId) return;

    _settings = _settings.copyWith(defaultAgentId: agentId);
    notifyListeners();

    await _saveSettings();
  }

  /// 设置默认模型
  Future<void> setDefaultModel(String? modelName) async {
    if (_settings.defaultModelName == modelName) return;

    _settings = _settings.copyWith(defaultModelName: modelName);
    notifyListeners();

    await _saveSettings();
  }

  /// 设置主题模式
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_settings.themeMode == mode) return;

    _settings = _settings.copyWith(themeMode: mode);
    notifyListeners();

    await _saveSettings();
  }

  /// 保存设置到本地存储
  Future<void> _saveSettings() async {
    await _storageService.set(AppConstants.settingsKey, _settings.toJson());
  }

  /// 重置设置
  Future<void> resetSettings() async {
    _settings = const AppSettings();
    notifyListeners();
    await _saveSettings();
  }
}

/// 设置提供者
final settingsProvider = ChangeNotifierProvider<SettingsProvider>((ref) {
  final storageService = ref.read(storageServiceProvider);
  final provider = SettingsProvider(storageService);
  // 异步初始化设置
  provider.initSettings();
  return provider;
});
