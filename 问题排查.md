现在的numerology_ai_chat_new项目
有一些问题
我认为是token的原因
老账号登录上去之后，可以正常的发起对话
但新注册的账号登录上去发起对话显示以下报错信息（这个报错信息应该是前端传递token给go，go又传递给云函数，云函数返回给go，然后go又返回给了前端）。


flutter: Go代理服务请求失败:
flutter: 状态码: 401
flutter: 请求体: {"agentId":"c611b9466852f652032f664d580b8164","modelId":"684e58d3dc6caee3a2101c6b","messages":[{"role":"user","content":"你好"}],"stream":true}
flutter: 错误响应: {"error":"Invalid or expired token"}

我认为是加解密的环节出现了问题，
因为之前一直都是硬编码在代码中的加密解密的秘钥
但现在我在exe_system_config集合中新增了encryption_key字段，值为：8AE2D773F14379F832ACC2327087AF52
结果就是现在新注册的账号，如 账号：1334490264 密码：1334490264  ，账号：690440488 密码：690440488 这些账号都可以正常登录，但无法发送消息给ai。都会提示Invalid or expired token
但之前的账号都可以正常登录以及正常发送消息给ai，如：账号：13283736906 密码：fuck147258369

你首先需要排查当前程序的所有硬编码的地方，包括云函数以及numerology_ai_chat_new和weak-admin-web
也需要使用mcp查看一下数据库里的所有相关数据。




请你深度排查所有相关的代码，切记需要查看尽可能多的代码，也需要使用mcp工具去查询对应的数据库，然后找到问题后，将问题原因写入到当前文档中，请你不要吝啬上下文，尽可能多的查看尽可能多的代码以及查询数据库。


排查出来的问题以及解决方案（需要考虑到修复后所有账号都需要可以正常登录）：

## 问题根源分析

经过深入排查，发现问题的真正原因是：**SystemConfigService缺少兜底机制**，而不是token加解密问题。

### 详细分析过程

1. **JWT Token验证正常**：
   - 测试发现新账号和老账号的JWT token都使用相同的密钥（`your-super-secret-jwt-key-change-in-production`）
   - 新账号token在云函数中可以正常验证：`getUserInfo`接口返回成功
   - 新账号token在Go代理服务中也可以正常工作：直接curl测试成功

2. **AES加密密钥一致性**：
   - 系统中确实存在两套密钥：硬编码密钥`your-aes-secret-key-32-chars-long`和数据库动态密钥`8AE2D773F14379F832ACC2327087AF52`
   - 但这主要影响激活码验证和弱管理员系统，不影响普通用户的token验证
   - 新账号没有使用激活码注册，所以不涉及动态密钥问题

3. **真正的问题所在**：
   - 前端`SystemConfigService.getGoProxyApiUrl()`方法在获取配置失败时会抛出异常
   - 当前代码第73-74行：`throw Exception('无法获取Go代理API地址: $e')`
   - 缺少兜底机制，没有使用默认值`http://go.ziyuanit.com/api`

### 问题链条

1. 前端尝试发送AI消息
2. `GoProxyService`调用`SystemConfigService.getGoProxyApiUrl()`获取API地址
3. `SystemConfigService`从云函数获取配置时出现异常（网络问题、超时等）
4. 由于没有兜底机制，直接抛出异常
5. 前端无法获取API地址，导致请求失败，返回401错误

### 为什么看起来是新账号问题

- 老账号可能有本地缓存的Go代理API地址（30分钟缓存）
- 新账号没有缓存，每次都需要从云函数获取配置
- 当网络不稳定或云函数响应慢时，新账号更容易触发这个问题

## 修复方案

### 方案1：恢复兜底机制（推荐）

修改`SystemConfigService.getGoProxyApiUrl()`方法，在获取配置失败时使用默认值：

```dart
// 在 numerology_ai_chat_new/lib/src/services/system_config_service.dart 中修改
} catch (e) {
  print('获取Go代理API地址失败: $e');
  // 清除缓存
  _cachedGoProxyApiUrl = null;
  _cacheTime = null;
  await _storageService.delete(AppConstants.goProxyApiUrlKey);

  // 使用默认值作为兜底机制
  const defaultApiUrl = 'http://go.ziyuanit.com/api';
  print('使用默认Go代理API地址: $defaultApiUrl');

  // 缓存默认值
  _cachedGoProxyApiUrl = defaultApiUrl;
  _cacheTime = DateTime.now();
  await _storageService.set(AppConstants.goProxyApiUrlKey, defaultApiUrl);

  return defaultApiUrl;
}
```

### 方案2：增强错误处理

在`GoProxyService`中增加错误处理，当获取API地址失败时使用硬编码地址：

```dart
// 在 numerology_ai_chat_new/lib/src/services/go_proxy_service.dart 中修改
try {
  final baseUrl = await _systemConfigService.getGoProxyApiUrl();
  // 使用动态获取的地址
} catch (e) {
  print('获取动态API地址失败，使用默认地址: $e');
  const baseUrl = 'http://go.ziyuanit.com/api';
  // 使用默认地址
}
```

### 推荐实施方案1的原因

1. **根本解决问题**：在配置服务层面解决，避免在每个调用处都要处理异常
2. **保持架构一致性**：维持动态配置的设计理念
3. **向后兼容**：恢复之前验证报告中提到的兜底机制
4. **用户体验**：确保在任何情况下系统都能正常工作

## 立即修复步骤

### 步骤1：修复SystemConfigService

修改文件：`numerology_ai_chat_new/lib/src/services/system_config_service.dart`

将第67-75行的异常处理代码替换为：

```dart
} catch (e) {
  print('获取Go代理API地址失败: $e');
  // 清除缓存
  _cachedGoProxyApiUrl = null;
  _cacheTime = null;
  await _storageService.delete(AppConstants.goProxyApiUrlKey);

  // 使用默认值作为兜底机制
  const defaultApiUrl = 'http://go.ziyuanit.com/api';
  print('使用默认Go代理API地址: $defaultApiUrl');

  // 缓存默认值
  _cachedGoProxyApiUrl = defaultApiUrl;
  _cacheTime = DateTime.now();
  await _storageService.set(AppConstants.goProxyApiUrlKey, defaultApiUrl);

  return defaultApiUrl;
}
```

### 步骤2：添加默认值常量（可选）

在`numerology_ai_chat_new/lib/src/core/constants/app_constants.dart`中添加：

```dart
// Go代理服务配置（兜底机制）
@Deprecated('请使用SystemConfigService动态获取，此常量仅作兜底使用')
static const String defaultGoProxyApiUrl = 'http://go.ziyuanit.com/api';
```

### 步骤3：测试验证

1. **清除应用缓存**：删除应用的本地存储数据
2. **测试新账号登录**：使用账号`690440488`/`690440488`登录
3. **测试AI对话**：发送消息"你好"验证功能正常
4. **测试老账号**：使用账号`13283736906`/`fuck147258369`验证不受影响

### 步骤4：长期优化（可选）

1. **增强日志记录**：添加更详细的错误日志，便于排查问题
2. **监控配置获取成功率**：统计配置获取的成功率和失败原因
3. **优化缓存策略**：考虑增加缓存时间或实现更智能的缓存策略

## 预期效果

修复后，所有账号（包括新注册账号和老账号）都应该能够：

1. ✅ 正常登录
2. ✅ 正常发送AI消息
3. ✅ 正常获取智能体和模型列表
4. ✅ 正常查看用户信息和算力余额

系统将在以下情况下自动使用兜底机制：

- 云函数响应超时
- 网络连接问题
- 数据库配置缺失
- 任何其他配置获取异常

这确保了系统的高可用性和用户体验的一致性。
