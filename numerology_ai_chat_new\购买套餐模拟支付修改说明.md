# 购买套餐模拟支付功能修改说明

## 修改概述

本次修改主要解决了以下问题：
1. 导航标题从"购买次数"改为"购买套餐"
2. 实现真实的模拟支付流程，支付成功/失败后在数据库中创建真实订单记录
3. 支付成功后自动充值算力到用户账户
4. 支付完成后在个人中心购买历史中显示订单记录

## 修改内容

### 1. 前端修改

#### 1.1 导航标题修改
- **文件**: `lib/src/screens/purchase_screen.dart`
- **修改**: AppBar 标题从"购买次数"改为"购买套餐"
- **文件**: `lib/src/screens/home_screen.dart`  
- **修改**: 底部导航标签从"购买次数"改为"购买套餐"

#### 1.2 支付流程重构
- **文件**: `lib/src/screens/purchase_screen.dart`
- **主要修改**:
  - PaymentDialog 改为 ConsumerStatefulWidget 以支持 Riverpod
  - 添加订单创建流程，点击购买时先创建订单
  - 重构模拟支付逻辑，调用真实的云函数API
  - 添加错误处理和状态管理
  - 支付成功后自动刷新用户信息和购买历史

#### 1.3 服务层扩展
- **文件**: `lib/src/services/payment_package_service.dart`
- **修改**: 添加 `simulatePayment` 方法，用于调用云函数模拟支付接口

### 2. 后端修改

#### 2.1 云函数接口扩展
- **文件**: `cloudfunctions/exeFunction/src/handlers/payment_packages.js`
- **修改**:
  - 修改 `createPurchaseOrder` 函数，确保订单真实保存到数据库
  - 添加 `simulatePayment` 函数，处理模拟支付逻辑
  - 支付成功时更新订单状态为 COMPLETED 并充值算力

#### 2.2 数据库操作优化
- **文件**: `cloudfunctions/exeFunction/src/utils/db.js`
- **修改**: 
  - 优化 `updateOrderStatus` 方法，支持传入额外的更新字段
  - 确保 `userCollection.addQuota` 方法正常工作

#### 2.3 云函数入口注册
- **文件**: `cloudfunctions/exeFunction/index.js`
- **修改**: 注册 `simulatePayment` 接口

## 支付流程说明

### 新的支付流程
1. 用户点击"立即购买" → 显示确认对话框
2. 用户点击"确认购买" → 显示支付对话框
3. 支付对话框自动创建订单（调用 `createPurchaseOrder` API）
4. 订单创建成功后显示支付界面（二维码等）
5. 用户点击"模拟支付" → 调用 `simulatePayment` API
6. 根据随机结果（80%成功率）处理支付结果：
   - **支付成功**: 更新订单状态为 COMPLETED，充值算力到用户账户
   - **支付失败**: 订单状态保持 PENDING
7. 显示支付结果，支付成功时自动刷新用户信息和购买历史

### 数据库记录
- 无论支付成功还是失败，都会在 `exe_purchase_orders` 表中创建真实的订单记录
- 支付成功的订单会显示在个人中心的购买历史中
- 支付失败的订单状态为 PENDING，用户可以重新支付

## 测试建议

### 1. 功能测试
- [ ] 验证导航标题显示为"购买套餐"
- [ ] 测试购买流程：选择套餐 → 确认购买 → 创建订单 → 模拟支付
- [ ] 验证支付成功时算力正确充值
- [ ] 验证支付失败时订单状态为 PENDING
- [ ] 检查个人中心购买历史是否正确显示订单

### 2. 边界测试
- [ ] 测试网络异常情况下的错误处理
- [ ] 测试用户未登录时的处理
- [ ] 测试订单创建失败的处理
- [ ] 测试支付API调用失败的处理

### 3. 数据一致性测试
- [ ] 验证订单数据在数据库中正确保存
- [ ] 验证用户算力更新的原子性
- [ ] 验证购买历史数据的准确性

## 注意事项

1. **向后兼容**: 所有修改都保持了向后兼容性，不会影响现有功能
2. **错误处理**: 完善了各种异常情况的处理，提升用户体验
3. **状态同步**: 支付成功后自动刷新相关状态，确保数据一致性
4. **模拟支付**: 当前仍为模拟支付，未来可以轻松替换为真实支付接口

## 问题修复记录

### 问题：创建订单失败
**现象**: 点击购买后显示"创建订单失败"，模拟支付按钮不可点击

**原因分析**:
1. 云函数返回的数据结构是双层嵌套：
   ```json
   {
     "code": 0,
     "data": {
       "success": true,
       "data": {
         "order": {...},
         "payment": {...}
       }
     }
   }
   ```
2. 前端解析时没有正确处理嵌套结构

**解决方案**:
1. 修复前端 `PaymentPackageService.createPurchaseOrder` 方法的数据解析逻辑
2. 正确处理云函数的双层响应结构
3. 更新云函数代码到腾讯云

**修复文件**:
- `lib/src/services/payment_package_service.dart` - 修复数据解析逻辑
- `cloudfunctions/exeFunction/src/handlers/payment_packages.js` - 确保返回正确的订单ID

## 后续优化建议

1. 添加支付超时处理机制
2. 实现支付状态轮询查询
3. 添加支付失败重试机制
4. 优化支付界面的用户体验
5. 添加支付成功的动画效果
