#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的真太阳时和夏令时功能
"""

import requests
import json
from datetime import datetime

# API配置
API_URL = "https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction"

def test_optimized_bazi_features():
    """测试优化后的地址选择和夏令时功能"""

    print("🧪 测试优化后的地址选择和夏令时功能")
    print("=" * 60)
    
    # 测试用例1：北京地区，启用真太阳时和夏令时
    test_case_1 = {
        "action": "baziAnalyze",
        "name": "张三",
        "gender": "男",
        "calendarType": "公历",
        "birthDate": "1988-06-15",
        "birthTime": "14:30",
        "isLeapMonth": False,
        "birthPlace": "北京市朝阳区",
        "latitude": 39.9042,
        "longitude": 116.4074,
        "region": "大陆",
        "considerDaylightSaving": True,
        "enableSolarTimeCalculation": True
    }
    
    print("📍 测试用例1：北京地区，1988年夏令时期间")
    print(f"   输入时间：{test_case_1['birthDate']} {test_case_1['birthTime']}")
    print(f"   经纬度：{test_case_1['latitude']}, {test_case_1['longitude']}")
    print(f"   启用真太阳时：{test_case_1['enableSolarTimeCalculation']}")
    print(f"   考虑夏令时：{test_case_1['considerDaylightSaving']}")
    
    try:
        response = requests.post(API_URL, json=test_case_1, timeout=30)
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 0:
                result = data.get('data', {})
                solar_time_info = result.get('solarTimeInfo')
                
                print("✅ 请求成功")
                print(f"   姓名：{result.get('name')}")
                print(f"   八字：{result.get('baziStr')}")
                
                if solar_time_info:
                    print("🕐 真太阳时信息：")
                    print(f"   原始时间：{solar_time_info.get('originalTime')}")
                    print(f"   真太阳时：{solar_time_info.get('solarTime')}")
                    print(f"   时间差异：{solar_time_info.get('timeDiffText')}")
                    print(f"   经度差异：{solar_time_info.get('longitudeDiff')}°")
                    
                    dst_info = solar_time_info.get('daylightSaving', {})
                    if dst_info.get('isDaylightSaving'):
                        print(f"   夏令时：是（{dst_info.get('region')}地区）")
                    else:
                        print(f"   夏令时：否")
                else:
                    print("❌ 未返回真太阳时信息")
            else:
                print(f"❌ API返回错误：{data.get('message')}")
        else:
            print(f"❌ HTTP错误：{response.status_code}")
            print(f"   响应：{response.text}")
    except Exception as e:
        print(f"❌ 请求异常：{e}")
    
    print()
    
    # 测试用例2：新疆地区，关闭夏令时
    test_case_2 = {
        "action": "baziAnalyze",
        "name": "李四",
        "gender": "女",
        "calendarType": "公历",
        "birthDate": "1990-03-20",
        "birthTime": "10:15",
        "isLeapMonth": False,
        "birthPlace": "新疆乌鲁木齐市",
        "latitude": 43.8256,
        "longitude": 87.6168,
        "region": "大陆",
        "considerDaylightSaving": False,
        "enableSolarTimeCalculation": True
    }
    
    print("📍 测试用例2：新疆地区，关闭夏令时")
    print(f"   输入时间：{test_case_2['birthDate']} {test_case_2['birthTime']}")
    print(f"   经纬度：{test_case_2['latitude']}, {test_case_2['longitude']}")
    print(f"   启用真太阳时：{test_case_2['enableSolarTimeCalculation']}")
    print(f"   考虑夏令时：{test_case_2['considerDaylightSaving']}")
    
    try:
        response = requests.post(API_URL, json=test_case_2, timeout=30)
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 0:
                result = data.get('data', {})
                solar_time_info = result.get('solarTimeInfo')
                
                print("✅ 请求成功")
                print(f"   姓名：{result.get('name')}")
                print(f"   八字：{result.get('baziStr')}")
                
                if solar_time_info:
                    print("🕐 真太阳时信息：")
                    print(f"   原始时间：{solar_time_info.get('originalTime')}")
                    print(f"   真太阳时：{solar_time_info.get('solarTime')}")
                    print(f"   时间差异：{solar_time_info.get('timeDiffText')}")
                    print(f"   经度差异：{solar_time_info.get('longitudeDiff')}°")
                    
                    dst_info = solar_time_info.get('daylightSaving', {})
                    print(f"   夏令时：{'是' if dst_info.get('isDaylightSaving') else '否'}")
                else:
                    print("❌ 未返回真太阳时信息")
            else:
                print(f"❌ API返回错误：{data.get('message')}")
        else:
            print(f"❌ HTTP错误：{response.status_code}")
    except Exception as e:
        print(f"❌ 请求异常：{e}")
    
    print()

    # 测试用例3：不使用地址信息
    test_case_3 = {
        "action": "baziAnalyze",
        "name": "王五",
        "gender": "男",
        "calendarType": "公历",
        "birthDate": "1995-12-10",
        "birthTime": "16:45",
        "isLeapMonth": False,
        "birthPlace": "未指定",  # 不使用地址
        # 不传递经纬度参数
        "region": "大陆",
        "considerDaylightSaving": False,
        "enableSolarTimeCalculation": False
    }
    
    print("📍 测试用例3：不使用地址信息")
    print(f"   输入时间：{test_case_3['birthDate']} {test_case_3['birthTime']}")
    print(f"   出生地：{test_case_3['birthPlace']}")
    print(f"   启用真太阳时：{test_case_3['enableSolarTimeCalculation']}")
    print(f"   考虑夏令时：{test_case_3['considerDaylightSaving']}")
    
    try:
        response = requests.post(API_URL, json=test_case_3, timeout=30)
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 0:
                result = data.get('data', {})
                solar_time_info = result.get('solarTimeInfo')
                
                print("✅ 请求成功")
                print(f"   姓名：{result.get('name')}")
                print(f"   八字：{result.get('baziStr')}")
                
                if solar_time_info:
                    print("❌ 意外返回了真太阳时信息（应该为空）")
                else:
                    print("✅ 正确：未返回真太阳时信息")
            else:
                print(f"❌ API返回错误：{data.get('message')}")
        else:
            print(f"❌ HTTP错误：{response.status_code}")
    except Exception as e:
        print(f"❌ 请求异常：{e}")

    print()

    # 测试用例4：台湾地区夏令时期间
    test_case_4 = {
        "action": "baziAnalyze",
        "name": "陈小明",
        "gender": "男",
        "calendarType": "公历",
        "birthDate": "1975-05-15",  # 台湾夏令时期间
        "birthTime": "14:30",
        "isLeapMonth": False,
        "birthPlace": "台湾省台北市",
        "latitude": 25.0330,
        "longitude": 121.5654,
        "region": "台湾",
        "considerDaylightSaving": True,
        "enableSolarTimeCalculation": True
    }

    print("📍 测试用例4：台湾地区夏令时期间")
    print(f"   输入时间：{test_case_4['birthDate']} {test_case_4['birthTime']}")
    print(f"   地区：{test_case_4['region']}")
    print(f"   经纬度：{test_case_4['latitude']}, {test_case_4['longitude']}")
    print(f"   启用真太阳时：{test_case_4['enableSolarTimeCalculation']}")
    print(f"   考虑夏令时：{test_case_4['considerDaylightSaving']}")

    try:
        response = requests.post(API_URL, json=test_case_4, timeout=30)
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 0:
                result = data.get('data', {})
                solar_time_info = result.get('solarTimeInfo')

                print("✅ 请求成功")
                print(f"   姓名：{result.get('name')}")
                print(f"   八字：{result.get('baziStr')}")

                if solar_time_info:
                    print("🕐 真太阳时信息：")
                    print(f"   原始时间：{solar_time_info.get('originalTime')}")
                    print(f"   真太阳时：{solar_time_info.get('solarTime')}")
                    print(f"   时间差异：{solar_time_info.get('timeDiffText')}")

                    dst_info = solar_time_info.get('daylightSaving', {})
                    print(f"   夏令时：{'是' if dst_info.get('isDaylightSaving') else '否'}")
                    print(f"   用户选择考虑夏令时：{solar_time_info.get('considerDaylightSaving', False)}")
                else:
                    print("❌ 未返回真太阳时信息")
            else:
                print(f"❌ API返回错误：{data.get('message')}")
        else:
            print(f"❌ HTTP错误：{response.status_code}")
    except Exception as e:
        print(f"❌ 请求异常：{e}")

    print()

    # 测试用例5：夏令时期间但用户选择不考虑夏令时
    test_case_5 = {
        "action": "baziAnalyze",
        "name": "王小华",
        "gender": "女",
        "calendarType": "公历",
        "birthDate": "1988-06-15",  # 夏令时期间
        "birthTime": "14:30",
        "isLeapMonth": False,
        "birthPlace": "北京市",
        "latitude": 39.9042,
        "longitude": 116.4074,
        "region": "大陆",
        "considerDaylightSaving": False,  # 用户选择不考虑夏令时
        "enableSolarTimeCalculation": True
    }

    print("📍 测试用例5：夏令时期间但用户选择不考虑夏令时")
    print(f"   输入时间：{test_case_5['birthDate']} {test_case_5['birthTime']}")
    print(f"   地区：{test_case_5['region']}")
    print(f"   启用真太阳时：{test_case_5['enableSolarTimeCalculation']}")
    print(f"   考虑夏令时：{test_case_5['considerDaylightSaving']} (用户主动关闭)")

    try:
        response = requests.post(API_URL, json=test_case_5, timeout=30)
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 0:
                result = data.get('data', {})
                solar_time_info = result.get('solarTimeInfo')
                text_result = result.get('textResult', '')

                print("✅ 请求成功")
                print(f"   姓名：{result.get('name')}")
                print(f"   八字：{result.get('baziStr')}")

                # 检查文本结果中是否包含夏令时提示
                has_dst_text = '夏令时' in text_result and '已自动调整' in text_result
                print(f"   文本中包含夏令时提示：{'是' if has_dst_text else '否'} (应该为否)")

                if solar_time_info:
                    print("🕐 真太阳时信息：")
                    print(f"   原始时间：{solar_time_info.get('originalTime')}")
                    print(f"   真太阳时：{solar_time_info.get('solarTime')}")
                    print(f"   时间差异：{solar_time_info.get('timeDiffText')}")

                    dst_info = solar_time_info.get('daylightSaving', {})
                    print(f"   夏令时期间：{'是' if dst_info.get('isDaylightSaving') else '否'}")
                    print(f"   用户选择考虑夏令时：{solar_time_info.get('considerDaylightSaving', False)}")
                else:
                    print("❌ 未返回真太阳时信息")
            else:
                print(f"❌ API返回错误：{data.get('message')}")
        else:
            print(f"❌ HTTP错误：{response.status_code}")
    except Exception as e:
        print(f"❌ 请求异常：{e}")

if __name__ == "__main__":
    test_optimized_bazi_features()
