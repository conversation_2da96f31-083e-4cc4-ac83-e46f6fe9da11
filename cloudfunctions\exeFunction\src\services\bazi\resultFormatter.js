/**
 * 八字分析结果格式化模块
 * 用于生成自然语言格式的八字分析结果
 */

const utils = require('./utils');

/**
 * 生成自然语言格式的八字分析结果
 * @param {Object} baziInfo - 八字信息
 * @returns {string} - 自然语言格式的八字分析结果
 */
function generateTextResult(baziInfo) {
  // 构建完整的自然语言格式分析结果
  let result = '';
  
  // 基本信息部分
  result += `【基本信息】\n`;
  result += `姓名：${baziInfo.name}\n`;
  result += `性别：${baziInfo.gender}\n`;
  result += `公历：${baziInfo.solarDate} ${baziInfo.solarTime}\n`;
  result += `农历：${baziInfo.lunarDate}\n`;
  result += `出生地：${baziInfo.birthPlace || '未知'}\n`;

  // 如果有真太阳时信息，显示时间对比
  if (baziInfo.solarTimeInfo) {
    const solarTimeInfo = baziInfo.solarTimeInfo;
    result += `\n【时间信息】\n`;
    result += `输入时间：${baziInfo.originalDate || baziInfo.solarDate} ${baziInfo.originalTime || baziInfo.solarTime}\n`;
    if (solarTimeInfo.timeDiffMinutes !== 0) {
      result += `真太阳时：${baziInfo.solarDate} ${new Date(solarTimeInfo.solarTime).toTimeString().substring(0, 8)}\n`;

      // 使用详细的时间差异描述
      if (solarTimeInfo.detailedTimeDiffText) {
        result += `时间差异：真太阳时比北京时间${solarTimeInfo.detailedTimeDiffText}\n`;
      } else {
        result += `时间差异：真太阳时比北京时间${solarTimeInfo.timeDiffText}\n`;
      }

      result += `经度信息：${solarTimeInfo.longitude.toFixed(4)}°（与北京时间基准经度120°相差${Math.abs(solarTimeInfo.longitudeDiff).toFixed(4)}°）\n`;

      // 只有在用户选择考虑夏令时且实际在夏令时期间时才显示
      if (solarTimeInfo.daylightSaving &&
          solarTimeInfo.daylightSaving.isDaylightSaving &&
          solarTimeInfo.considerDaylightSaving) {
        result += `夏令时：该时期实行夏令时，已自动调整\n`;
      }
    } else {
      result += `真太阳时：与北京时间相同\n`;
    }
  }

  result += `\n`;
  
  // 八字信息部分
  result += `【八字信息】\n`;
  result += `八字：${baziInfo.baziStr}\n`;
  result += `主星：${baziInfo.yuanShen || (baziInfo.gender === '男' ? '元男' : '元女')}\n\n`;
  
  // 四柱部分
  result += `【四柱】\n`;
  result += `年柱：${baziInfo.year.ganZhi}　${baziInfo.yearGanZhiZhuXing}\n`;
  result += `月柱：${baziInfo.month.ganZhi}　${baziInfo.monthGanZhiZhuXing}\n`;
  result += `日柱：${baziInfo.day.ganZhi}　${baziInfo.dayGanZhiZhuXing}\n`;
  result += `时柱：${baziInfo.time.ganZhi}　${baziInfo.timeGanZhiZhuXing}\n\n`;
  
  // 天干与五行
  result += `【天干五行】\n`;
  result += baziInfo.ganWuXing.map(item => `${item.gan}(${item.wuXing})`).join('　') + '\n\n';
  
  // 地支与五行
  result += `【地支五行】\n`;
  result += baziInfo.zhiWuXing.map(item => `${item.zhi}(${item.wuXing})`).join('　') + '\n\n';
  
  // 地支藏干
  result += `【藏干】\n`;
  result += `年柱：${baziInfo.year.zhi} 藏 ${baziInfo.cangGan[0].cangGan.join('、')}\n`;
  result += `月柱：${baziInfo.month.zhi} 藏 ${baziInfo.cangGan[1].cangGan.join('、')}\n`;
  result += `日柱：${baziInfo.day.zhi} 藏 ${baziInfo.cangGan[2].cangGan.join('、')}\n`;
  result += `时柱：${baziInfo.time.zhi} 藏 ${baziInfo.cangGan[3].cangGan.join('、')}\n\n`;
  
  // 十神
  result += `【十神】\n`;
  result += `年柱：${baziInfo.shiShen[0].shiShen}（${baziInfo.shiShen[0].shiShenRelation || ''}）\n`;
  result += `月柱：${baziInfo.shiShen[1].shiShen}（${baziInfo.shiShen[1].shiShenRelation || ''}）\n`;
  result += `时柱：${baziInfo.shiShen[2].shiShen}（${baziInfo.shiShen[2].shiShenRelation || ''}）\n\n`;
  
  // 副星
  result += `【副星】\n`;
  if (baziInfo.fuXing && baziInfo.fuXing.length > 0) {
    baziInfo.fuXing.forEach(item => {
      result += `${item.pillar}：${item.zhi} 藏`;
      item.fuXingList.forEach(fuXing => {
        result += ` ${fuXing.gan}(${fuXing.fuXing})`;
      });
      result += '\n';
    });
    result += '\n';
  }
  
  // 星运（长生十二神）
  result += `【星运（长生十二神）】\n`;
  if (baziInfo.starDestiny && baziInfo.starDestiny.length >= 4) {
    result += `年柱：${baziInfo.year.zhi} - ${baziInfo.starDestiny[0].starDestiny || '无'}\n`;
    result += `月柱：${baziInfo.month.zhi} - ${baziInfo.starDestiny[1].starDestiny || '无'}\n`;
    result += `日柱：${baziInfo.day.zhi} - ${baziInfo.starDestiny[2].starDestiny || '无'}\n`;
    result += `时柱：${baziInfo.time.zhi} - ${baziInfo.starDestiny[3].starDestiny || '无'}\n\n`;
  }
  
  // 空亡
  result += `【空亡】\n`;
  result += `年柱空亡：${baziInfo.kongWang[0]}\n`;
  result += `月柱空亡：${baziInfo.kongWang[1]}\n`;
  result += `日柱空亡：${baziInfo.kongWang[2]}\n`;
  result += `时柱空亡：${baziInfo.kongWang[3]}\n\n`;
  
  // 纳音
  result += `【纳音五行】\n`;
  result += `年柱：${baziInfo.naYin[0].ganZhi} - ${baziInfo.naYin[0].naYin}\n`;
  result += `月柱：${baziInfo.naYin[1].ganZhi} - ${baziInfo.naYin[1].naYin}\n`;
  result += `日柱：${baziInfo.naYin[2].ganZhi} - ${baziInfo.naYin[2].naYin}\n`;
  result += `时柱：${baziInfo.naYin[3].ganZhi} - ${baziInfo.naYin[3].naYin}\n\n`;
  
  // 五行统计及喜用神
  result += `【五行统计】\n`;
  result += `木：${baziInfo.wuXingCount['木']} 个\n`;
  result += `火：${baziInfo.wuXingCount['火']} 个\n`;
  result += `土：${baziInfo.wuXingCount['土']} 个\n`;
  result += `金：${baziInfo.wuXingCount['金']} 个\n`;
  result += `水：${baziInfo.wuXingCount['水']} 个\n\n`;
  
  // 日元强弱与喜用神
  result += `【日元】\n`;
  result += `日元：${baziInfo.day.gan}(${utils.getWuXingByGan(baziInfo.day.gan)})\n`;
  result += `日元强弱：${baziInfo.dayElementStrength}\n`;
  result += `喜用神：${baziInfo.favorableElements ? baziInfo.favorableElements.join('、') : ''}\n\n`;
  
  // 起运年龄与大运
  result += `【起运】\n`;
  const destiny = baziInfo.destiny;
  if (destiny) {
    result += `起运年龄：${destiny.startAge}岁${destiny.startAgeMonth}个月${destiny.startAgeDay}天\n`;
    result += `起运年份：${destiny.startYear}年\n\n`;
    
    // 大运信息
    result += `【大运】\n`;
    destiny.destinyList.forEach((item, index) => {
      result += `${index + 1}运：${item.ganZhi} (${item.startAge}-${item.endAge}岁) ${item.startYear}-${item.endYear}年`;
      if (item.shiShen) {
        result += ` 十神：${item.shiShen}`;
      }
      result += '\n';
    });
    result += '\n';
  }
  
  // 流年信息
  result += `【流年】\n`;
  if (baziInfo.yearlyDestiny && baziInfo.yearlyDestiny.length > 0) {
    // 显示所有计算出来的流年
    baziInfo.yearlyDestiny.forEach(item => {
      result += `${item.year}年 ${item.ganZhi} (${item.age}岁)\n`;
    });
    result += '\n';
  }
  
  // 八字简评
  result += `【八字简评】\n`;
  result += `此命${baziInfo.gender}性，生于${baziInfo.solarDate}，${baziInfo.dayElementStrength}，`;
  result += `八字天干为 ${baziInfo.ganWuXing.map(item => item.gan).join('')}，地支为 ${baziInfo.zhiWuXing.map(item => item.zhi).join('')}。\n`;
  result += `五行中${utils.getBriefWuXingAnalysis(baziInfo.wuXingCount)}。\n`;
  result += `命主日元为${baziInfo.day.gan}(${utils.getWuXingByGan(baziInfo.day.gan)})，日元${baziInfo.dayElementStrength}，`;
  result += `宜用${baziInfo.favorableElements ? baziInfo.favorableElements.join('、') : ''}来${baziInfo.dayElementStrength === '身强' ? '制约' : '扶助'}。\n\n`;
  
  return result;
}

/**
 * 生成简要的八字分析结果
 * @param {Object} baziInfo - 八字信息
 * @returns {string} - 简要的八字分析结果
 */
function generateBriefResult(baziInfo) {
  let result = '';
  
  // 基本信息
  result += `姓名：${baziInfo.name}，性别：${baziInfo.gender}\n`;
  result += `八字：${baziInfo.baziStr}\n`;
  
  // 简要分析
  result += `日元：${baziInfo.day.gan}(${utils.getWuXingByGan(baziInfo.day.gan)})，${baziInfo.dayElementStrength}\n`;
  result += `五行：${Object.entries(baziInfo.wuXingCount).map(([k, v]) => `${k}${v}`).join(' ')}\n`;
  result += `喜用神：${baziInfo.favorableElements ? baziInfo.favorableElements.join('、') : ''}\n`;
  
  // 大运信息
  if (baziInfo.destiny) {
    result += `起运：${baziInfo.destiny.startAge}岁，${baziInfo.destiny.startYear}年\n`;
    result += `大运：${baziInfo.destiny.destinyList.slice(0, 3).map(item => item.ganZhi).join('，')}\n`;
  }
  
  return result;
}

// 导出模块
module.exports = {
  generateTextResult,
  generateBriefResult
}; 