const { generateActivationCode } = require('../utils/crypto')
const { activationCodeCollection } = require('../utils/db')
const { validateGenerateActivationCodes, validatePagination } = require('../utils/validate')
const { weakAdminAuthMiddleware } = require('../middleware/auth')
const { successResponse } = require('../middleware/error_handler')
const logger = require('../utils/logger')

/**
 * 批量生成激活码
 * @param {object} params 生成参数
 * @returns {object} 生成结果
 */
async function generateActivationCodes(params) {
  try {
    // 参数校验
    const { quantity, quotaAmount, token } = validateGenerateActivationCodes(params)
    
    // 权限验证
    const userInfo = await weakAdminAuthMiddleware(token)
    
    logger.info('开始生成激活码', {
      operator: userInfo.username,
      quantity,
      quotaAmount
    })
    
    // 批量生成激活码
    const activationCodes = []
    for (let i = 0; i < quantity; i++) {
      const code = await generateActivationCode(quotaAmount, userInfo.username)
      activationCodes.push({
        code,
        quotaAmount,
        createdBy: userInfo.username,
        createdAt: new Date().toISOString()
      })
    }
    
    logger.info('激活码生成完成', {
      operator: userInfo.username,
      quantity: activationCodes.length,
      quotaAmount
    })
    
    return successResponse({
      activationCodes,
      summary: {
        quantity: activationCodes.length,
        quotaAmount,
        totalQuota: activationCodes.length * quotaAmount,
        createdBy: userInfo.username
      }
    }, `成功生成${activationCodes.length}个激活码`)
    
  } catch (error) {
    logger.error('激活码生成失败', {
      error: error.message,
      params: { ...params, token: '[HIDDEN]' }
    })
    throw error
  }
}

/**
 * 获取激活码核销历史
 * @param {object} params 查询参数
 * @returns {object} 历史记录
 */
async function getActivationHistory(params) {
  try {
    // 参数校验
    const { page, pageSize, token } = validatePagination(params)
    
    // 权限验证
    const userInfo = await weakAdminAuthMiddleware(token)
    
    logger.info('查询激活码核销历史', {
      operator: userInfo.username,
      page,
      pageSize
    })
    
    // 查询历史记录（只显示当前管理员创建的激活码）
    const result = await activationCodeCollection.getHistory(page, pageSize, userInfo.username)
    
    return successResponse(result, '查询成功')
    
  } catch (error) {
    logger.error('查询激活码历史失败', {
      error: error.message,
      params: { ...params, token: '[HIDDEN]' }
    })
    throw error
  }
}

module.exports = {
  generateActivationCodes,
  getActivationHistory
}
