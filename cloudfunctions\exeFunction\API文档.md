# exeFunction 云函数接口文档

## 概述
exeFunction是面向用户端的云函数，提供用户认证、数据获取和使用次数管理等功能。

## 接口列表

### 1. 用户注册
- **Action**: `register`
- **参数**: 
  - `username` (string): 用户名
  - `password` (string): 密码
- **返回**: 用户信息和token
- **示例**:
```json
{
  "action": "register",
  "username": "testuser",
  "password": "123456"
}
```

### 2. 用户登录
- **Action**: `login`
- **参数**: 
  - `username` (string): 用户名
  - `password` (string): 密码
- **返回**: 用户信息、accessToken和refreshToken
- **示例**:
```json
{
  "action": "login",
  "username": "testuser",
  "password": "123456"
}
```

### 3. 刷新Token
- **Action**: `refreshToken`
- **参数**: 
  - `refreshToken` (string): 刷新令牌
- **返回**: 新的accessToken和refreshToken
- **示例**:
```json
{
  "action": "refreshToken",
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}
```

### 4. 获取智能体列表
- **Action**: `getAgents`
- **鉴权**: 需要token
- **参数**: 
  - `token` (string): 访问令牌
- **返回**: 启用的智能体列表（不含提示词）
- **示例**:
```json
{
  "action": "getAgents",
  "token": "eyJhbGciOiJIUzI1NiIs..."
}
```

### 5. 获取模型列表
- **Action**: `getModels`
- **鉴权**: 需要token
- **参数**: 
  - `token` (string): 访问令牌
- **返回**: 启用的模型列表（不含API密钥）
- **示例**:
```json
{
  "action": "getModels",
  "token": "eyJhbGciOiJIUzI1NiIs..."
}
```

### 6. 获取用户信息
- **Action**: `getUserInfo`
- **鉴权**: 需要token
- **参数**: 
  - `token` (string): 访问令牌
- **返回**: 用户详细信息（包含会员状态、可用次数等）
- **示例**:
```json
{
  "action": "getUserInfo",
  "token": "eyJhbGciOiJIUzI1NiIs..."
}
```

### 7. 更新使用次数
- **Action**: `updateUsage`
- **鉴权**: 需要token
- **参数**: 
  - `token` (string): 访问令牌
- **返回**: 更新后的使用次数信息
- **示例**:
```json
{
  "action": "updateUsage",
  "token": "eyJhbGciOiJIUzI1NiIs..."
}
```

## 响应格式
所有接口统一返回格式：
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "success": true,
    "message": "操作成功",
    "data": {},
    "timestamp": "2025-06-14T14:37:53.458Z"
  }
}
```

## 错误处理
- 400: 参数错误
- 401: 认证失败
- 403: 权限不足
- 500: 服务器内部错误

## 注意事项
1. 所有需要鉴权的接口都需要在参数中传入有效的token
2. Token有效期为2小时，过期后需要使用refreshToken刷新
3. 新用户注册自动赠送10次使用额度
4. 使用次数不足时无法调用需要消耗次数的功能
