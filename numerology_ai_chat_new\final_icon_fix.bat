@echo off
chcp 65001 >nul
echo ════════════════════════════════════════════
echo    最终图标修复方案
echo ════════════════════════════════════════════
echo.

echo 📋 这是最后的解决方案，将强制修复图标问题
echo.

echo 🔍 步骤1: 创建高质量ICO文件...
python final_icon_fix.py
if %errorlevel% neq 0 (
    echo ❌ Python脚本失败，请确保安装了PIL库
    echo 💡 安装命令: pip install Pillow
    pause
    exit /b 1
)

echo.
echo 🧹 步骤2: 完全清理构建缓存...
flutter clean
rmdir /s /q build 2>nul

echo.
echo 📦 步骤3: 重新获取依赖...
flutter pub get

echo.
echo 🔨 步骤4: 重新构建应用...
flutter build windows --release

echo.
echo 🎉 构建完成！
echo ════════════════════════════════════════════
echo 📁 新的exe文件: build\windows\x64\runner\Release\numerology_ai_chat.exe
echo.

echo 🔍 步骤5: 验证图标文件...
if exist "windows\runner\resources\app_icon.ico" (
    echo ✅ ICO文件存在
    for %%A in ("windows\runner\resources\app_icon.ico") do echo 📦 文件大小: %%~zA 字节
) else (
    echo ❌ ICO文件不存在
)

echo.
echo 📝 重要说明:
echo ════════════════════════════════════════════
echo 1. 新生成的exe文件应该包含正确的图标
echo 2. 如果图标仍然显示为默认，请重启Windows资源管理器:
echo    - 按 Ctrl+Shift+Esc 打开任务管理器
echo    - 找到 "Windows资源管理器" 进程
echo    - 右键选择 "重新启动"
echo.
echo 3. 或者重启计算机以清除所有图标缓存
echo.
echo 💡 提示: Windows图标缓存有时需要时间更新
echo    如果立即看不到变化，请稍等片刻或重启资源管理器
echo.
pause
