/**
 * 日志工具模块
 * 提供统一的日志记录功能
 */

const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3
}

const currentLevel = LOG_LEVELS.INFO // 可通过环境变量配置

/**
 * 格式化日志消息
 * @param {string} level 日志级别
 * @param {string} message 日志消息
 * @param {object} meta 附加信息
 * @returns {string} 格式化后的日志
 */
function formatLog(level, message, meta = {}) {
  const timestamp = new Date().toISOString()
  const logData = {
    timestamp,
    level,
    message,
    ...meta
  }
  return JSON.stringify(logData)
}

/**
 * 记录错误日志
 * @param {string} message 错误消息
 * @param {object} meta 附加信息
 */
function error(message, meta = {}) {
  if (currentLevel >= LOG_LEVELS.ERROR) {
    console.error(formatLog('ERROR', message, meta))
  }
}

/**
 * 记录警告日志
 * @param {string} message 警告消息
 * @param {object} meta 附加信息
 */
function warn(message, meta = {}) {
  if (currentLevel >= LOG_LEVELS.WARN) {
    console.warn(formatLog('WARN', message, meta))
  }
}

/**
 * 记录信息日志
 * @param {string} message 信息消息
 * @param {object} meta 附加信息
 */
function info(message, meta = {}) {
  if (currentLevel >= LOG_LEVELS.INFO) {
    console.log(formatLog('INFO', message, meta))
  }
}

/**
 * 记录调试日志
 * @param {string} message 调试消息
 * @param {object} meta 附加信息
 */
function debug(message, meta = {}) {
  if (currentLevel >= LOG_LEVELS.DEBUG) {
    console.log(formatLog('DEBUG', message, meta))
  }
}

module.exports = {
  error,
  warn,
  info,
  debug,
  LOG_LEVELS
}