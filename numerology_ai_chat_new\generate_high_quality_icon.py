#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成超高质量应用图标
支持多种尺寸和高DPI显示，确保在所有场景下都清晰显示
"""

from PIL import Image, ImageFilter, ImageEnhance
import os
import shutil

def enhance_image_quality(img):
    """
    增强图像质量
    
    Args:
        img: PIL Image对象
    
    Returns:
        增强后的PIL Image对象
    """
    # 增强对比度
    enhancer = ImageEnhance.Contrast(img)
    img = enhancer.enhance(1.1)
    
    # 增强锐度
    enhancer = ImageEnhance.Sharpness(img)
    img = enhancer.enhance(1.2)
    
    # 增强色彩饱和度
    enhancer = ImageEnhance.Color(img)
    img = enhancer.enhance(1.05)
    
    return img

def create_ultra_high_quality_ico(jpg_path, ico_path):
    """
    创建超高质量的ICO文件，支持4K显示和高DPI
    
    Args:
        jpg_path: 源JPG文件路径
        ico_path: 目标ICO文件路径
    """
    try:
        print(f"🔄 正在处理源图片: {jpg_path}")
        
        # 打开JPG图片
        with Image.open(jpg_path) as img:
            print(f"📏 原始图片尺寸: {img.size[0]}x{img.size[1]} 像素")
            print(f"🎨 原始图片模式: {img.mode}")
            
            # 转换为RGBA模式（支持透明度）
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            # 调整图片为正方形（取较小的边作为基准）
            min_size = min(img.size)
            # 从中心裁剪为正方形
            left = (img.size[0] - min_size) // 2
            top = (img.size[1] - min_size) // 2
            right = left + min_size
            bottom = top + min_size
            img_square = img.crop((left, top, right, bottom))
            
            print(f"✂️ 裁剪后尺寸: {img_square.size[0]}x{img_square.size[1]} 像素")
            
            # 如果原图尺寸小于512，先放大到512
            if img_square.size[0] < 512:
                print("🔍 原图尺寸较小，先放大到512x512以提高质量")
                img_square = img_square.resize((512, 512), Image.Resampling.LANCZOS)
            
            # 增强图像质量
            img_square = enhance_image_quality(img_square)
            
            # Windows图标标准尺寸（包含4K和高DPI支持）
            sizes = [
                (16, 16),   # 小图标
                (20, 20),   # 小图标 125% DPI
                (24, 24),   # 小图标 150% DPI
                (30, 30),   # 小图标 200% DPI
                (32, 32),   # 中等图标
                (40, 40),   # 中等图标 125% DPI
                (48, 48),   # 中等图标 150% DPI
                (60, 60),   # 中等图标 200% DPI
                (64, 64),   # 大图标
                (72, 72),   # 大图标 150% DPI
                (80, 80),   # 大图标 200% DPI
                (96, 96),   # 大图标 200% DPI
                (128, 128), # 超大图标
                (256, 256), # 超大图标 200% DPI
                (512, 512)  # 4K显示支持
            ]
            
            # 创建不同尺寸的超高质量图标
            icon_images = []
            for size in sizes:
                print(f"🔄 生成 {size[0]}x{size[1]} 图标...")
                
                # 使用LANCZOS重采样算法获得最佳质量
                resized = img_square.resize(size, Image.Resampling.LANCZOS)
                
                # 对不同尺寸应用不同的优化策略
                if size[0] <= 32:
                    # 小尺寸图标：增强锐度和对比度
                    resized = resized.filter(ImageFilter.SHARPEN)
                    enhancer = ImageEnhance.Contrast(resized)
                    resized = enhancer.enhance(1.15)
                elif size[0] <= 64:
                    # 中等尺寸图标：轻微锐化
                    resized = resized.filter(ImageFilter.UnsharpMask(radius=0.5, percent=120, threshold=2))
                else:
                    # 大尺寸图标：保持原始质量，轻微增强
                    enhancer = ImageEnhance.Sharpness(resized)
                    resized = enhancer.enhance(1.05)
                
                icon_images.append(resized)
                print(f"✅ 完成 {size[0]}x{size[1]} 图标")
            
            # 保存为ICO文件
            print(f"💾 保存ICO文件到: {ico_path}")
            icon_images[0].save(
                ico_path,
                format='ICO',
                sizes=sizes,
                append_images=icon_images[1:]
            )
            
            print(f"✅ 成功创建超高质量图标!")
            print(f"📏 包含 {len(sizes)} 种尺寸")
            
            # 获取文件大小
            size_bytes = os.path.getsize(ico_path)
            size_kb = size_bytes / 1024
            print(f"📦 文件大小: {size_kb:.1f} KB ({size_bytes} 字节)")
            
    except Exception as e:
        print(f"❌ 创建图标失败: {e}")
        return False
    
    return True

def backup_existing_icon(ico_path):
    """备份现有图标文件"""
    if os.path.exists(ico_path):
        backup_path = ico_path + ".backup"
        try:
            shutil.copy2(ico_path, backup_path)
            print(f"📦 已备份当前图标到: {backup_path}")
            return True
        except Exception as e:
            print(f"⚠️ 备份图标文件失败: {e}")
            return False
    return True

def verify_icon_quality(ico_path):
    """验证生成的图标质量"""
    try:
        with Image.open(ico_path) as img:
            print(f"🔍 验证图标文件: {ico_path}")
            print(f"📏 主图标尺寸: {img.size[0]}x{img.size[1]} 像素")
            print(f"🎨 图标模式: {img.mode}")
            
            # 检查文件大小
            size_bytes = os.path.getsize(ico_path)
            if size_bytes > 100 * 1024:  # 大于100KB
                print("✅ 图标文件大小合适，包含多种尺寸")
            else:
                print("⚠️ 图标文件较小，可能缺少高分辨率版本")
            
            return True
    except Exception as e:
        print(f"❌ 验证图标失败: {e}")
        return False

def main():
    print("🚀 生成超高质量应用图标")
    print("=" * 50)
    
    # 定义文件路径
    jpg_path = "assets/images/logo.png"
    ico_path = "windows/runner/resources/app_icon.ico"
    
    # 检查源文件是否存在
    if not os.path.exists(jpg_path):
        print(f"❌ 源文件不存在: {jpg_path}")
        print("请确保 assets/images/logo.png 文件存在")
        return False
    
    # 确保目标目录存在
    os.makedirs(os.path.dirname(ico_path), exist_ok=True)
    
    # 备份现有图标
    backup_existing_icon(ico_path)
    
    # 创建超高质量图标
    print(f"🔄 开始创建超高质量图标...")
    success = create_ultra_high_quality_ico(jpg_path, ico_path)
    
    if success:
        # 验证图标质量
        verify_icon_quality(ico_path)
        
        print("\n🎉 超高质量图标生成完成!")
        print("=" * 50)
        print("📝 特性说明:")
        print("✅ 支持15种不同尺寸 (16x16 到 512x512)")
        print("✅ 支持高DPI显示 (125%, 150%, 200%)")
        print("✅ 支持4K显示")
        print("✅ 针对不同尺寸优化锐度和对比度")
        print("✅ 增强色彩饱和度和清晰度")
        
        print("\n📝 接下来的步骤:")
        print("1. 运行: dart run flutter_launcher_icons")
        print("2. 清理缓存: flutter clean")
        print("3. 重新构建: flutter build windows --release")
        
        return True
    else:
        print("💥 图标生成失败，请检查错误信息。")
        return False

if __name__ == "__main__":
    main()
