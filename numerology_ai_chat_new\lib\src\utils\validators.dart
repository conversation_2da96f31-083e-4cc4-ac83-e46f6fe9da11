/// 表单验证器
class Validators {
  // 私有构造函数
  Validators._();

  /// 验证用户名
  static String? validateUsername(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入用户名';
    }

    if (value.length < 8) {
      return '用户名至少需要8个字符';
    }

    if (value.length > 30) {
      return '用户名不能超过30个字符';
    }

    // 只允许数字和大小写英文字母
    if (!RegExp(r'^[a-zA-Z0-9]+$').hasMatch(value)) {
      return '用户名只能包含数字和大小写英文字母';
    }

    return null;
  }

  /// 验证密码
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入密码';
    }

    if (value.length < 8) {
      return '密码至少需要8个字符';
    }

    if (value.length > 50) {
      return '密码不能超过50个字符';
    }

    // 检查是否包含中文字符
    if (RegExp(r'[\u4e00-\u9fa5]').hasMatch(value)) {
      return '密码不能包含中文字符';
    }

    // 只允许字母、数字和常规符号
    if (!RegExp(r'^[a-zA-Z0-9!@#$%^&*()_+\-=\[\]{};:"\\|,.<>\/?`~]+$').hasMatch(value)) {
      return '密码只能包含字母、数字和常规符号';
    }

    return null;
  }

  /// 验证确认密码
  static String? validateConfirmPassword(String? value, String? password) {
    if (value == null || value.isEmpty) {
      return '请确认密码';
    }
    
    if (value != password) {
      return '两次输入的密码不一致';
    }
    
    return null;
  }

  /// 验证邮箱
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入邮箱地址';
    }
    
    const emailPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
    if (!RegExp(emailPattern).hasMatch(value)) {
      return '请输入有效的邮箱地址';
    }
    
    return null;
  }

  /// 验证手机号
  static String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入手机号';
    }
    
    const phonePattern = r'^1[3-9]\d{9}$';
    if (!RegExp(phonePattern).hasMatch(value)) {
      return '请输入有效的手机号';
    }
    
    return null;
  }

  /// 验证必填字段
  static String? validateRequired(String? value, [String? fieldName]) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? '此字段'}不能为空';
    }
    return null;
  }

  /// 验证长度范围
  static String? validateLength(
    String? value, {
    int? minLength,
    int? maxLength,
    String? fieldName,
  }) {
    if (value == null) return null;
    
    if (minLength != null && value.length < minLength) {
      return '${fieldName ?? '此字段'}至少需要$minLength个字符';
    }
    
    if (maxLength != null && value.length > maxLength) {
      return '${fieldName ?? '此字段'}不能超过$maxLength个字符';
    }
    
    return null;
  }

  /// 验证数字
  static String? validateNumber(String? value, [String? fieldName]) {
    if (value == null || value.isEmpty) {
      return '${fieldName ?? '此字段'}不能为空';
    }
    
    if (double.tryParse(value) == null) {
      return '${fieldName ?? '此字段'}必须是有效数字';
    }
    
    return null;
  }

  /// 验证整数
  static String? validateInteger(String? value, [String? fieldName]) {
    if (value == null || value.isEmpty) {
      return '${fieldName ?? '此字段'}不能为空';
    }
    
    if (int.tryParse(value) == null) {
      return '${fieldName ?? '此字段'}必须是有效整数';
    }
    
    return null;
  }

  /// 验证数字范围
  static String? validateNumberRange(
    String? value, {
    double? min,
    double? max,
    String? fieldName,
  }) {
    if (value == null || value.isEmpty) return null;
    
    final number = double.tryParse(value);
    if (number == null) {
      return '${fieldName ?? '此字段'}必须是有效数字';
    }
    
    if (min != null && number < min) {
      return '${fieldName ?? '此字段'}不能小于$min';
    }
    
    if (max != null && number > max) {
      return '${fieldName ?? '此字段'}不能大于$max';
    }
    
    return null;
  }

  /// 验证URL
  static String? validateUrl(String? value, [String? fieldName]) {
    if (value == null || value.isEmpty) {
      return '${fieldName ?? 'URL'}不能为空';
    }
    
    try {
      final uri = Uri.parse(value);
      if (!uri.hasScheme || (!uri.scheme.startsWith('http'))) {
        return '${fieldName ?? 'URL'}格式不正确';
      }
    } catch (e) {
      return '${fieldName ?? 'URL'}格式不正确';
    }
    
    return null;
  }

  /// 组合验证器
  static String? Function(String?) combine(
    List<String? Function(String?)> validators,
  ) {
    return (String? value) {
      for (final validator in validators) {
        final result = validator(value);
        if (result != null) return result;
      }
      return null;
    };
  }
}
