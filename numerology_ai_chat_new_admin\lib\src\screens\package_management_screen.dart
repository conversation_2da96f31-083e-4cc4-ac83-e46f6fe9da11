import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:numerology_ai_chat_admin/src/models/package_model.dart';
import 'package:numerology_ai_chat_admin/src/providers/package_provider.dart';
import 'package:numerology_ai_chat_admin/src/widgets/package_edit_dialog.dart';

class PackageManagementScreen extends ConsumerStatefulWidget {
  const PackageManagementScreen({super.key});

  @override
  ConsumerState<PackageManagementScreen> createState() => _PackageManagementScreenState();
}

class _PackageManagementScreenState extends ConsumerState<PackageManagementScreen> {
  bool? _selectedIsActive;

  @override
  void initState() {
    super.initState();
    // 初始加载套餐列表
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(packageProvider.notifier).fetchPackages();
    });
  }

  @override
  Widget build(BuildContext context) {
    final packageState = ref.watch(packageProvider);

    // 监听错误状态
    ref.listen<PackageState>(packageProvider, (previous, next) {
      if (next.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.error!),
            backgroundColor: Colors.red,
          ),
        );
        ref.read(packageProvider.notifier).clearError();
      }
    });

    return Scaffold(
      body: Column(
        children: [
          // 页面标题和操作栏
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Text(
                  '算力套餐管理',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () => _refreshPackages(),
                  tooltip: '刷新',
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: () => _showCreatePackageDialog(),
                  icon: const Icon(Icons.add),
                  label: const Text('新建套餐'),
                ),
              ],
            ),
          ),
          // 筛选条件
          _buildFilterBar(),
          const Divider(height: 1),
          // 套餐列表
          Expanded(
            child: packageState.isLoading
                ? const Center(child: CircularProgressIndicator())
                : packageState.packages == null || packageState.packages!.isEmpty
                    ? const Center(child: Text('暂无套餐数据'))
                    : _buildPackageList(packageState.packages!),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // 状态筛选
          SizedBox(
            width: 120,
            child: DropdownButtonFormField<bool>(
              value: _selectedIsActive,
              decoration: const InputDecoration(
                labelText: '状态',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: const [
                DropdownMenuItem(value: null, child: Text('全部状态')),
                DropdownMenuItem(value: true, child: Text('启用')),
                DropdownMenuItem(value: false, child: Text('禁用')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedIsActive = value;
                });
                _refreshPackages();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPackageList(List<PaymentPackage> packages) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('套餐名称')),
          DataColumn(label: Text('套餐描述')),
          DataColumn(label: Text('原价')),
          DataColumn(label: Text('现价')),
          DataColumn(label: Text('算力数量')),
          DataColumn(label: Text('状态')),
          DataColumn(label: Text('排序')),
          DataColumn(label: Text('更新时间')),
          DataColumn(label: Text('操作')),
        ],
        rows: packages.map((package) => _buildPackageRow(package)).toList(),
      ),
    );
  }

  DataRow _buildPackageRow(PaymentPackage package) {
    return DataRow(
      cells: [
        DataCell(
          SizedBox(
            width: 150,
            child: Text(
              package.packageName,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        DataCell(
          SizedBox(
            width: 200,
            child: Text(
              package.packageDescription,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ),
        DataCell(Text('¥${(package.originalPrice / 100).toStringAsFixed(2)}')),
        DataCell(
          Text(
            '¥${(package.price / 100).toStringAsFixed(2)}',
            style: TextStyle(
              color: package.price < package.originalPrice ? Colors.red : null,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        DataCell(Text('${package.quotaAmount}算力')),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: package.isActive ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              package.isActive ? '启用' : '禁用',
              style: TextStyle(
                color: package.isActive ? Colors.green : Colors.red,
                fontSize: 12,
              ),
            ),
          ),
        ),
        DataCell(Text(package.sortOrder.toString())),
        DataCell(Text(_formatDateTime(package.updatedAt))),
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: const Icon(Icons.edit, size: 18),
                onPressed: () => _showEditPackageDialog(package),
                tooltip: '编辑',
              ),
              IconButton(
                icon: const Icon(Icons.delete, size: 18),
                onPressed: () => _showDeleteConfirmDialog(package),
                tooltip: '删除',
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _refreshPackages() {
    ref.read(packageProvider.notifier).fetchPackages(
          isActive: _selectedIsActive,
        );
  }

  void _showCreatePackageDialog() {
    showDialog(
      context: context,
      builder: (context) => PackageEditDialog(
        onSave: (request) async {
          final success = await ref.read(packageProvider.notifier).createPackage(request);
          if (success) {
            Navigator.of(context).pop();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('套餐创建成功')),
            );
          }
        },
      ),
    );
  }

  void _showEditPackageDialog(PaymentPackage package) {
    showDialog(
      context: context,
      builder: (context) => PackageEditDialog(
        package: package,
        onSave: (request) async {
          final success = await ref.read(packageProvider.notifier).updatePackage(package.id, request);
          if (success) {
            Navigator.of(context).pop();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('套餐更新成功')),
            );
          }
        },
      ),
    );
  }

  void _showDeleteConfirmDialog(PaymentPackage package) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除套餐"${package.packageName}"吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final success = await ref.read(packageProvider.notifier).deletePackage(package.id);
              if (success) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('套餐删除成功')),
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }
}
