import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../models/order_model.dart';

class OrderDetailDialog extends StatelessWidget {
  final OrderModel order;

  const OrderDetailDialog({
    super.key,
    required this.order,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 600,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '订单详情',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSection('基本信息', [
                      _buildInfoRow('订单号', order.orderNo, copyable: true),
                      _buildInfoRow('用户ID', order.userId, copyable: true),
                      _buildInfoRow('交易ID', order.transactionId ?? '无'),
                      _buildInfoRow('状态', order.statusText),
                    ]),
                    const SizedBox(height: 24),
                    _buildSection('套餐信息', [
                      _buildInfoRow('套餐ID', order.packageId),
                      _buildInfoRow('套餐名称', order.packageName),
                      _buildInfoRow('套餐算力', '${order.packageQuota}'),
                      _buildInfoRow('购买数量', '${order.quantity}'),
                      _buildInfoRow('单价', '¥${(order.unitPrice / 100).toStringAsFixed(2)}'),
                      _buildInfoRow('总算力', '${order.unitQuota * order.quantity}'),
                    ]),
                    const SizedBox(height: 24),
                    _buildSection('支付信息', [
                      _buildInfoRow('支付方式', order.paymentMethodText),
                      _buildInfoRow('订单金额', '¥${(order.orderAmount / 100).toStringAsFixed(2)}'),
                      if (order.fuiouOrderId != null)
                        _buildInfoRow('富友订单号', order.fuiouOrderId!, copyable: true),
                      if (order.fuiouPayAmount != null)
                        _buildInfoRow('富友支付金额', '¥${order.fuiouPayAmount}'),
                      if (order.fuiouPayType != null)
                        _buildInfoRow('富友支付类型', order.fuiouPayType!),
                      if (order.fuiouPayTime != null)
                        _buildInfoRow('富友支付时间', _formatFuiouTime(order.fuiouPayTime!)),
                    ]),
                    const SizedBox(height: 24),
                    _buildSection('时间信息', [
                      _buildInfoRow('创建时间', DateFormat('yyyy-MM-dd HH:mm:ss').format(order.createTime)),
                      _buildInfoRow('过期时间', DateFormat('yyyy-MM-dd HH:mm:ss').format(order.expireTime)),
                      if (order.payTime != null)
                        _buildInfoRow('支付时间', DateFormat('yyyy-MM-dd HH:mm:ss').format(order.payTime!)),
                      _buildInfoRow('更新时间', DateFormat('yyyy-MM-dd HH:mm:ss').format(order.updatedAt)),
                    ]),
                    if (order.paymentUrl.isNotEmpty) ...[
                      const SizedBox(height: 24),
                      _buildSection('支付链接', [
                        _buildInfoRow('支付URL', order.paymentUrl, copyable: true),
                        _buildInfoRow('二维码数据', order.qrCodeData, copyable: true),
                      ]),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('关闭'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value, {bool copyable = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    value,
                    style: TextStyle(
                      fontFamily: copyable ? 'monospace' : null,
                    ),
                  ),
                ),
                if (copyable)
                  IconButton(
                    icon: const Icon(Icons.copy, size: 16),
                    onPressed: () {
                      Clipboard.setData(ClipboardData(text: value));
                    },
                    tooltip: '复制',
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatFuiouTime(String fuiouTime) {
    // 富友时间格式: 20250620202228
    if (fuiouTime.length == 14) {
      try {
        final year = fuiouTime.substring(0, 4);
        final month = fuiouTime.substring(4, 6);
        final day = fuiouTime.substring(6, 8);
        final hour = fuiouTime.substring(8, 10);
        final minute = fuiouTime.substring(10, 12);
        final second = fuiouTime.substring(12, 14);
        return '$year-$month-$day $hour:$minute:$second';
      } catch (e) {
        return fuiouTime;
      }
    }
    return fuiouTime;
  }
}
