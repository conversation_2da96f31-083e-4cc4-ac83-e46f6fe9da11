#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建超高质量图标 - 解决像素低的问题
"""

from PIL import Image, ImageFilter, ImageEnhance
import os

def create_ultra_high_quality_icon():
    """创建超高质量的Windows图标"""
    jpg_path = "assets/images/logo.png"
    ico_path = "windows/runner/resources/app_icon.ico"
    
    print("🔄 创建超高质量图标...")
    
    if not os.path.exists(jpg_path):
        print(f"❌ 源文件不存在: {jpg_path}")
        return False
    
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(ico_path), exist_ok=True)
        
        # 打开源图片
        with Image.open(jpg_path) as img:
            print(f"📏 源图片: {img.size}, 模式: {img.mode}")
            
            # 如果源图片分辨率不够，先进行AI增强
            if min(img.size) < 512:
                print("⚠️ 源图片分辨率较低，建议使用更高分辨率的图片")
            
            # 转换为RGBA模式以支持透明度
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            # 裁剪为正方形（从中心裁剪）
            min_size = min(img.size)
            left = (img.size[0] - min_size) // 2
            top = (img.size[1] - min_size) // 2
            right = left + min_size
            bottom = top + min_size
            img_square = img.crop((left, top, right, bottom))
            
            print(f"✂️ 裁剪后尺寸: {img_square.size}")
            
            # 如果原图小于1024，先放大到1024作为基础
            if img_square.size[0] < 1024:
                img_square = img_square.resize((1024, 1024), Image.Resampling.LANCZOS)
                print("🔍 已放大到1024x1024作为基础")
            
            # Windows图标的完整尺寸列表（包含所有标准尺寸）
            sizes = [
                (16, 16),    # 小图标
                (20, 20),    # 小图标 125% DPI
                (24, 24),    # 小图标 150% DPI
                (30, 30),    # 小图标 200% DPI
                (32, 32),    # 中等图标
                (40, 40),    # 中等图标 125% DPI
                (48, 48),    # 中等图标 150% DPI
                (60, 60),    # 中等图标 200% DPI
                (64, 64),    # 大图标
                (72, 72),    # 大图标 150% DPI
                (80, 80),    # 大图标 125% DPI
                (96, 96),    # 大图标 150% DPI
                (128, 128),  # 超大图标
                (256, 256),  # 超大图标
                (512, 512)   # 超超大图标（现代Windows支持）
            ]
            
            # 生成各种尺寸的高质量图标
            icon_images = []
            for size in sizes:
                print(f"🎨 生成 {size[0]}x{size[1]} 图标...")
                
                # 使用最高质量的重采样算法
                resized = img_square.resize(size, Image.Resampling.LANCZOS)
                
                # 根据尺寸应用不同的优化
                if size[0] <= 32:
                    # 小尺寸图标：增强锐度和对比度
                    resized = resized.filter(ImageFilter.SHARPEN)
                    enhancer = ImageEnhance.Contrast(resized)
                    resized = enhancer.enhance(1.15)
                    enhancer = ImageEnhance.Sharpness(resized)
                    resized = enhancer.enhance(1.2)
                    print(f"  ✨ 小尺寸优化完成")
                    
                elif size[0] <= 64:
                    # 中等尺寸图标：轻微锐化
                    resized = resized.filter(ImageFilter.SHARPEN)
                    enhancer = ImageEnhance.Contrast(resized)
                    resized = enhancer.enhance(1.08)
                    print(f"  ✨ 中等尺寸优化完成")
                    
                else:
                    # 大尺寸图标：保持原始质量，轻微增强
                    enhancer = ImageEnhance.Contrast(resized)
                    resized = enhancer.enhance(1.05)
                    print(f"  ✨ 大尺寸优化完成")
                
                icon_images.append(resized)
            
            # 保存为ICO文件，使用最高质量设置
            print("💾 保存ICO文件...")
            icon_images[0].save(
                ico_path,
                format='ICO',
                sizes=sizes,
                append_images=icon_images[1:],
                optimize=False  # 不压缩，保持最高质量
            )
            
            file_size = os.path.getsize(ico_path)
            print(f"✅ 超高质量图标已保存: {ico_path}")
            print(f"📦 文件大小: {file_size:,} 字节")
            print(f"🎯 包含 {len(sizes)} 种尺寸")
            
            return True
            
    except Exception as e:
        print(f"❌ 创建图标失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚀 创建超高质量Windows应用图标")
    print("=" * 50)
    
    success = create_ultra_high_quality_icon()
    
    if success:
        print("\n🎉 超高质量图标创建完成！")
        print("\n📝 特性说明：")
        print("- 包含15种不同尺寸，支持所有DPI设置")
        print("- 小尺寸图标经过锐化和对比度增强")
        print("- 使用最高质量的重采样算法")
        print("- 无压缩保存，确保最佳显示效果")
        
        print("\n🔄 接下来请：")
        print("1. 运行: flutter clean")
        print("2. 运行: flutter build windows --release")
        print("3. 检查生成的exe文件图标质量")
        
        print("\n💡 如果图标仍然模糊：")
        print("- 检查Windows显示缩放设置")
        print("- 重启Windows资源管理器")
        print("- 确保源图片分辨率足够高（建议1024x1024或更大）")
        
    else:
        print("\n❌ 图标创建失败")
        print("请检查错误信息并重试")

if __name__ == "__main__":
    main()
