/**
 * 八字计算模块
 * 包含计算八字、大运、流年等核心逻辑函数
 */

const { Solar, Lunar, LunarUtil } = require('lunar-javascript');
const utils = require('./utils');

/**
 * 解析出生信息
 * @param {string} birthDate - 出生日期
 * @param {string} birthTime - 出生时间
 * @param {string} calendarType - 历法类型
 * @param {boolean} isLeapMonth - 是否闰月
 * @returns {Object} - 解析结果
 */
function parseBirthInfo(birthDate, birthTime, calendarType, isLeapMonth) {
  try {
    let year, month, day, hour = 0, minute = 0, second = 0;
    
    // 判断是否为公历(阳历)
    const isSolar = calendarType === '阳历' || calendarType === '公历';
    
    // 解析日期
    if (birthDate.includes('-')) {
      // 处理YYYY-MM-DD格式
      const parts = birthDate.split('-');
      year = parseInt(parts[0], 10);
      month = parseInt(parts[1], 10);
      day = parseInt(parts[2], 10);
    } else {
      // 处理中文日期格式
      const yearMatch = birthDate.match(/(\d+)年/);
      const monthMatch = birthDate.match(/(\d+)月/);
      const dayMatch = birthDate.match(/(\d+)[日|号]/);
      
      if (yearMatch) year = parseInt(yearMatch[1], 10);
      if (monthMatch) month = parseInt(monthMatch[1], 10);
      if (dayMatch) day = parseInt(dayMatch[1], 10);
      
      // 处理农历中的特殊年份表示
      if (!yearMatch && birthDate.includes('年')) {
        const chineseNums = '零一二三四五六七八九';
        const yearsText = birthDate.match(/[零一二三四五六七八九]+年/);
        if (yearsText) {
          const yearText = yearsText[0].replace('年', '');
          year = 0;
          for (let i = 0; i < yearText.length; i++) {
            const num = chineseNums.indexOf(yearText[i]);
            if (num >= 0) {
              year = year * 10 + num;
            }
          }
        }
      }
    }
    
    // 验证年月日的有效性
    if (!year || !month || !day) {
      return {
        success: false,
        message: '无法解析出生日期'
      };
    }
    
    // 解析时间
    if (birthTime) {
      if (birthTime.includes(':')) {
        // 处理HH:MM格式
        const parts = birthTime.split(':');
        hour = parseInt(parts[0], 10);
        minute = parts.length > 1 ? parseInt(parts[1], 10) : 0;
      } else {
        // 处理时辰格式
        const timeMap = {
          '子时': 23, '丑时': 1, '寅时': 3, '卯时': 5,
          '辰时': 7, '巳时': 9, '午时': 11, '未时': 13,
          '申时': 15, '酉时': 17, '戌时': 19, '亥时': 21
        };
        
        for (const key in timeMap) {
          if (birthTime.includes(key)) {
            hour = timeMap[key];
            break;
          }
        }
      }
    }
    
    // 根据历法类型创建Solar或Lunar对象
    let solar, lunar;
    
    if (isSolar) {
      // 如果是阳历，直接创建Solar对象
      solar = Solar.fromYmdHms(year, month, day, hour, minute, second);
      lunar = solar.getLunar();
    } else {
      // 如果是农历，创建Lunar对象
      try {
        // 处理闰月情况，isLeapMonth为true且支持闰月时，月份设为负数表示闰月
        const lunarMonth = isLeapMonth ? -month : month;
        lunar = Lunar.fromYmdHms(year, lunarMonth, day, hour, minute, second);
        solar = lunar.getSolar();
      } catch (e) {
        return {
          success: false,
          message: '无效的农历日期: ' + e.message
        };
      }
    }
    
    return {
      success: true,
      solar,
      lunar,
      hour,
      minute
    };
  } catch (error) {
    console.error('解析出生信息出错:', error);
    return {
      success: false,
      message: '解析出生信息失败: ' + error.message
    };
  }
}

/**
 * 计算八字信息
 * @param {Lunar} lunar - 农历对象
 * @param {string} name - 姓名
 * @param {string} gender - 性别
 * @param {string} birthPlace - 出生地
 * @param {Object} settings - 八字设置参数
 * @returns {Object} - 八字信息
 */
function calculateBazi(lunar, name, gender, birthPlace, settings = {}) {
  // 获取八字
  const baziInfo = {};
  
  // 基本信息
  baziInfo.name = name;
  baziInfo.gender = gender;
  baziInfo.birthPlace = birthPlace || '';
  
  // 公历信息
  const solar = lunar.getSolar();
  baziInfo.solarDate = solar.toYmd();
  baziInfo.solarTime = `${solar.getHour().toString().padStart(2, '0')}:${solar.getMinute().toString().padStart(2, '0')}:${solar.getSecond().toString().padStart(2, '0')}`;
  
  // 农历信息
  baziInfo.lunarDate = lunar.toString();
  baziInfo.lunarMonth = lunar.getMonthInChinese();
  baziInfo.lunarDay = lunar.getDayInChinese();
  
  // 八字信息 - 根据BaZiUtil.java中的isGanZhi方法设置参数
  const eightChar = lunar.getEightChar();
  
  // 天干地支
  let yearGan, yearZhi, yearGanZhi;
  let monthGan, monthZhi, monthGanZhi;
  let dayGan, dayZhi, dayGanZhi;
  let timeGan, timeZhi, timeGanZhi;
  
  // 设置年干支
  if (settings.yearGanZhiSet === 0) {
    // 以正月初一作为新年的开始
    yearGan = lunar.getYearGan();
    yearZhi = lunar.getYearZhi();
    yearGanZhi = lunar.getYearInGanZhi();
  } else if (settings.yearGanZhiSet === 1) {
    // 以立春当天作为新年的开始
    yearGan = lunar.getYearGanByLiChun();
    yearZhi = lunar.getYearZhiByLiChun();
    yearGanZhi = lunar.getYearInGanZhiByLiChun();
  } else if (settings.yearGanZhiSet === 2) {
    // 以立春交接的时刻作为新年的开始（精确到分钟）
    yearGan = lunar.getYearGanExact();
    yearZhi = lunar.getYearZhiExact();
    yearGanZhi = lunar.getYearInGanZhiExact();
  } else {
    // 默认以立春交接的精确时刻作为新年的开始（修复：使用精确方法）
    yearGan = lunar.getYearGanExact();
    yearZhi = lunar.getYearZhiExact();
    yearGanZhi = lunar.getYearInGanZhiExact();
  }
  
  // 设置月干支
  if (settings.monthGanZhiSet === 0) {
    // 以节交接当天起算
    monthGan = lunar.getMonthGan();
    monthZhi = lunar.getMonthZhi();
    monthGanZhi = lunar.getMonthInGanZhi();
  } else if (settings.monthGanZhiSet === 1) {
    // 以节交接精确时刻起算（精确到分钟）
    monthGan = lunar.getMonthGanExact();
    monthZhi = lunar.getMonthZhiExact();
    monthGanZhi = lunar.getMonthInGanZhiExact();
  } else {
    // 默认以节交接精确时刻起算（修复：确保使用精确方法）
    monthGan = lunar.getMonthGanExact();
    monthZhi = lunar.getMonthZhiExact();
    monthGanZhi = lunar.getMonthInGanZhiExact();
  }
  
  // 设置日干支
  if (settings.dayGanZhiSet === 0) {
    // 晚子时日干支算当天
    dayGan = lunar.getDayGanExact2();
    dayZhi = lunar.getDayZhiExact2();
    dayGanZhi = lunar.getDayInGanZhiExact2();
  } else if (settings.dayGanZhiSet === 1) {
    // 晚子时日干支算明天
    dayGan = lunar.getDayGanExact();
    dayZhi = lunar.getDayZhiExact();
    dayGanZhi = lunar.getDayInGanZhiExact();
  } else {
    // 默认晚子时日干支算当天
    dayGan = lunar.getDayGanExact2();
    dayZhi = lunar.getDayZhiExact2();
    dayGanZhi = lunar.getDayInGanZhiExact2();
  }
  
  // 设置时干支
  timeGan = lunar.getTimeGan();
  timeZhi = lunar.getTimeZhi();
  timeGanZhi = lunar.getTimeInGanZhi();
  
  // 设置八字信息
  baziInfo.year = { 
    gan: yearGan,
    zhi: yearZhi,
    ganZhi: yearGanZhi
  };
  
  baziInfo.month = { 
    gan: monthGan,
    zhi: monthZhi,
    ganZhi: monthGanZhi
  };
  
  baziInfo.day = { 
    gan: dayGan,
    zhi: dayZhi,
    ganZhi: dayGanZhi
  };
  
  baziInfo.time = { 
    gan: timeGan,
    zhi: timeZhi,
    ganZhi: timeGanZhi
  };
  
  // 八字完整信息
  baziInfo.baziStr = `${baziInfo.year.ganZhi} ${baziInfo.month.ganZhi} ${baziInfo.day.ganZhi} ${baziInfo.time.ganZhi}`;
  
  // 添加日柱元神信息 (性别已在函数参数中获取)
  baziInfo.dayZhuZhuXing = utils.getDayZhuZhuXing(dayGan, gender === '男');
  
  // 四柱主星
  baziInfo.yearGanZhiZhuXing = '年柱主星：' + utils.getYearZhuXing(dayGan, yearGan);
  baziInfo.monthGanZhiZhuXing = '月柱主星：' + utils.getMonthZhuXing(dayGan, monthGan);
  baziInfo.dayGanZhiZhuXing = '日柱主星：' + utils.getDayZhuZhuXing(dayGan, gender === '男');
  baziInfo.timeGanZhiZhuXing = '时柱主星：' + utils.getTimeZhuXing(dayGan, timeGan);
  
  // 天干地支五行
  baziInfo.ganWuXing = [
    { gan: baziInfo.year.gan, wuXing: utils.getWuXingByGan(baziInfo.year.gan) },
    { gan: baziInfo.month.gan, wuXing: utils.getWuXingByGan(baziInfo.month.gan) },
    { gan: baziInfo.day.gan, wuXing: utils.getWuXingByGan(baziInfo.day.gan) },
    { gan: baziInfo.time.gan, wuXing: utils.getWuXingByGan(baziInfo.time.gan) }
  ];
  
  baziInfo.zhiWuXing = [
    { zhi: baziInfo.year.zhi, wuXing: utils.getWuXingByZhi(baziInfo.year.zhi) },
    { zhi: baziInfo.month.zhi, wuXing: utils.getWuXingByZhi(baziInfo.month.zhi) },
    { zhi: baziInfo.day.zhi, wuXing: utils.getWuXingByZhi(baziInfo.day.zhi) },
    { zhi: baziInfo.time.zhi, wuXing: utils.getWuXingByZhi(baziInfo.time.zhi) }
  ];
  
  // 计算纳音五行
  baziInfo.naYin = [
    { ganZhi: baziInfo.year.ganZhi, naYin: utils.getNaYin(baziInfo.year.ganZhi) },
    { ganZhi: baziInfo.month.ganZhi, naYin: utils.getNaYin(baziInfo.month.ganZhi) },
    { ganZhi: baziInfo.day.ganZhi, naYin: utils.getNaYin(baziInfo.day.ganZhi) },
    { ganZhi: baziInfo.time.ganZhi, naYin: utils.getNaYin(baziInfo.time.ganZhi) }
  ];
  
  // 计算地支藏干
  baziInfo.cangGan = [
    { zhi: baziInfo.year.zhi, cangGan: utils.getCangGan(baziInfo.year.zhi) },
    { zhi: baziInfo.month.zhi, cangGan: utils.getCangGan(baziInfo.month.zhi) },
    { zhi: baziInfo.day.zhi, cangGan: utils.getCangGan(baziInfo.day.zhi) },
    { zhi: baziInfo.time.zhi, cangGan: utils.getCangGan(baziInfo.time.zhi) }
  ];
  
  // 计算十神
  baziInfo.shiShen = utils.calculateShiShen(baziInfo.day.gan, [
    baziInfo.year.gan,
    baziInfo.month.gan,
    baziInfo.time.gan
  ]);
  
  // 计算副星（根据日主与藏干的关系）
  baziInfo.fuXing = utils.calculateFuXing(baziInfo.day.gan, baziInfo.cangGan);
  
  // 计算星运（长生十二神）
  baziInfo.starDestiny = utils.calculateStarDestiny(baziInfo.day.gan, [
    baziInfo.year.zhi,
    baziInfo.month.zhi,
    baziInfo.day.zhi,
    baziInfo.time.zhi
  ]);
  
  // 计算空亡
  baziInfo.kongWang = [
    utils.calculateKongWang(baziInfo.year.ganZhi),
    utils.calculateKongWang(baziInfo.month.ganZhi),
    utils.calculateKongWang(baziInfo.day.ganZhi),
    utils.calculateKongWang(baziInfo.time.ganZhi)
  ];
  
  // 计算喜用神 (参照Java版xiYongShen方法实现)
  // 根据日元强弱和各元素的关系，确定喜用神
  const dayGanWuXing = utils.getWuXingByGan(baziInfo.day.gan);
  const wuXingCount = {
    '木': 0,
    '火': 0,
    '土': 0,
    '金': 0,
    '水': 0
  };
  
  // 计算八字中的五行数量
  baziInfo.ganWuXing.forEach(item => {
    wuXingCount[item.wuXing]++;
  });
  
  baziInfo.zhiWuXing.forEach(item => {
    wuXingCount[item.wuXing]++;
  });
  
  baziInfo.cangGan.forEach(item => {
    item.cangGan.forEach(gan => {
      wuXingCount[utils.getWuXingByGan(gan)]++;
    });
  });
  
  baziInfo.wuXingCount = wuXingCount;
  
  // 简单判断日元强弱
  const dayGanCount = wuXingCount[dayGanWuXing];
  const totalCount = Object.values(wuXingCount).reduce((a, b) => a + b, 0);
  const isStrong = dayGanCount >= totalCount / 5;
  
  baziInfo.dayElementStrength = isStrong ? '身强' : '身弱';
  
  // 喜用神判断和相关五行
  const favorableElements = [];
  
  if (isStrong) {
    // 身强则喜克泄
    // 1. 克我者为用 (抑制日主)
    favorableElements.push(utils.getRestrained(dayGanWuXing));
    // 2. 我克者为用 (泄秀日主)
    favorableElements.push(utils.getRestrained(utils.getRestrained(dayGanWuXing)));
    // 3. 我生者为用 (耗泄日主)
    favorableElements.push(utils.getGenerated(dayGanWuXing));
  } else {
    // 身弱则喜生扶
    // 1. 生我者为用 (滋养日主)
    favorableElements.push(utils.getGenerating(dayGanWuXing));
    // 2. 同我者为用 (帮扶日主)
    favorableElements.push(dayGanWuXing);
  }
  
  // 去重
  baziInfo.favorableElements = [...new Set(favorableElements)];
  
  // 元神判断
  baziInfo.yuanShen = gender === '男' ? '元男' : '元女';
  
  // 计算大运
  const isMale = gender === '男';
  baziInfo.destiny = calculateDestiny(lunar, {
    yearGan,
    yearZhi,
    monthGan,
    monthZhi,
    dayGan,
    dayZhi,
    timeGan,
    timeZhi
  }, isMale);
  
  // 计算流年
  baziInfo.yearlyDestiny = calculateYearlyDestiny(lunar.getSolar().getYear(), baziInfo.destiny.startYear);
  
  return baziInfo;
}

/**
 * 计算大运
 * @param {Lunar} lunar - 农历对象
 * @param {Object} settings - 八字设置参数
 * @param {boolean} isMale - 是否为男性
 * @returns {Object} - 大运信息
 */
function calculateDestiny(lunar, settings, isMale) {
  // 从lunar中获取八字对象并设置性别
  const eightChar = lunar.getEightChar();
  // 获取起运对象，需要配置性别和起运流派
  // 起运流派: 1为传统历书的顺推方法，3为实际出生年的节气算法
  // 这里设置为3，对应Java版本中的getYun方法
  const yun = eightChar.getYun(isMale ? 1 : 0, 3);
  
  // 获取起运日期、年龄等信息
  const startYear = yun.getStartYear(); // 起运年数
  const startMonth = yun.getStartMonth(); // 起运月数
  const startDay = yun.getStartDay(); // 起运天数
  const startHour = yun.getStartHour(); // 起运小时数
  
  // 计算起运的公历日期（用于显示）
  const startSolar = yun.getStartSolar();
  const startDate = startSolar.toYmdHms();
  
  // 计算大运相关信息
  const birthYear = lunar.getSolar().getYear();
  const destinyYears = 10; // 每个大运10年
  
  // 计算起运年份 - 使用出生年份加上起运年龄
  const runYear = birthYear + Math.floor(startYear) + 1;
  
  // 直接使用lunar-javascript库提供的方法获取大运
  // 获取12轮大运，与Java版本保持一致
  const daYunArr = yun.getDaYun(12);
  
  // 生成大运信息
  const destinyInfo = {
    startAge: startYear, // 保留原始起运年龄（用于显示）
    startAgeMonth: startMonth,
    startAgeDay: startDay,
    startAgeHour: startHour,
    startDate: startDate,
    destinyList: [],
    startYear: runYear // 使用正确计算的起运年份
  };
  
  // 处理大运信息
  const processedList = [];
  
  // 先计算所有大运信息但不立即添加到结果中
  for (let i = 0; i < daYunArr.length; i++) {
    const daYun = daYunArr[i];
    if (daYun.getGanZhi() === '') continue;
    
    // 计算大运十神
    const shiShen = utils.calculateShiShen(settings.dayGan, [daYun.getGanZhi()[0]])[0];
    
    // 获取起始年龄并进行四舍五入，改为实岁（减1）
    const startAge = Math.round(daYun.getStartAge()); // 原始年龄（虚岁）
    const realStartAge = startAge - 1; // 转换为实岁
    
    // 计算每个大运的开始和结束年份，起始年份提前一年
    const thisStartYear = birthYear + startAge - 1; // 提前一年
    
    processedList.push({
      ganZhi: daYun.getGanZhi(),
      startAge: realStartAge,
      endAge: realStartAge + destinyYears - 1,
      startYear: thisStartYear,
      shiShen: shiShen.shiShen
    });
  }
  
  // 后处理，设置正确的结束年份
  for (let i = 0; i < processedList.length; i++) {
    const current = processedList[i];
    
    // 如果有下一个大运，结束年份与下一个大运的起始年份相同
    if (i < processedList.length - 1) {
      current.endYear = processedList[i + 1].startYear;
    } else {
      // 最后一个大运，结束年份就是开始年份+10
      current.endYear = current.startYear + destinyYears;
    }
    
    destinyInfo.destinyList.push(current);
  }
  
  return destinyInfo;
}

/**
 * 计算流年
 * @param {number} birthYear - 出生年
 * @param {number} startYear - 大运起运年 
 * @returns {Array} - 流年信息
 */
function calculateYearlyDestiny(birthYear, startYear) {
  // 干支纪年 (从起运年计算到起运年+100岁)
  const ganOrder = '甲乙丙丁戊己庚辛壬癸';
  const zhiOrder = '子丑寅卯辰巳午未申酉戌亥';
  
  // 以1864年为甲子元年基准年
  const baseYear = 1864; // 甲子年
  const yearlyInfo = [];
  
  // 计算起运年对应的实际年份
  const runYear = startYear;
  
  // 生成从起运年到起运年+100年的流年信息
  for (let i = 0; i <= 100; i++) {
    const year = runYear + i;
    const yearOffset = year - baseYear;
    
    // 计算干支索引
    const ganIndex = yearOffset % 10;
    const zhiIndex = yearOffset % 12;
    
    // 修正负数索引
    const adjustedGanIndex = ganIndex < 0 ? ganIndex + 10 : ganIndex;
    const adjustedZhiIndex = zhiIndex < 0 ? zhiIndex + 12 : zhiIndex;
    
    const gan = ganOrder[adjustedGanIndex];
    const zhi = zhiOrder[adjustedZhiIndex];
    const ganZhi = gan + zhi;
    
    yearlyInfo.push({
      age: Math.floor(startYear - birthYear) + i,
      year: year,
      ganZhi: ganZhi
    });
  }
  
  return yearlyInfo;
}

// 导出模块
module.exports = {
  parseBirthInfo,
  calculateBazi,
  calculateDestiny,
  calculateYearlyDestiny
}; 