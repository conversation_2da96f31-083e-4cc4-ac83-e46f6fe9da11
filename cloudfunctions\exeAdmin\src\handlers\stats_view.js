const { createBusinessError, ERROR_CODES, formatSuccessResponse } = require('../utils/error_handler')
const { userCollection, modelCollection, agentCollection, pageCollection } = require('../utils/db')
const { checkPermission } = require('../middleware/auth')
const logger = require('../utils/logger')

/**
 * 获取系统统计概览
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 统计数据
 */
async function getSystemStats(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'stats_read')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限查看系统统计', 403)
    }

    logger.info('管理员获取系统统计', {
      adminId: adminAuth.adminId
    })

    // 获取各种统计数据
    const [usersResult, models, agents, pagesResult] = await Promise.all([
      userCollection.getList(1, 1000), // 获取大量用户数据用于统计
      modelCollection.getList(),
      agentCollection.getList(),
      pageCollection.getList({}, 0, 1000) // 获取大量页面数据用于统计
    ])

    // 提取实际的数据数组
    const users = usersResult.data || []
    const pages = pagesResult || []

    // 用户统计
    const userStats = {
      total: users.length,
      active: users.filter(user => user.status === '激活').length,
      inactive: users.filter(user => user.status !== '激活').length,
      withMembership: users.filter(user => user.membership && user.membership.isValid).length,
      totalQuota: users.reduce((sum, user) => sum + (user.availableCount || 0), 0),
      totalUsage: users.reduce((sum, user) => sum + (user.totalUsageCount || 0), 0)
    }

    // 模型统计
    const modelStats = {
      total: models.length,
      active: models.filter(model => model.isActive).length,
      inactive: models.filter(model => !model.isActive).length,
      withApiKey: models.filter(model => model.modelApiKey).length
    }

    // 智能体统计
    const agentStats = {
      total: agents.length,
      active: agents.filter(agent => agent.isActive).length,
      inactive: agents.filter(agent => !agent.isActive).length,
      byType: {}
    }

    // 按类型统计智能体
    agents.forEach(agent => {
      const type = agent.agentType || '未分类'
      agentStats.byType[type] = (agentStats.byType[type] || 0) + 1
    })

    // 页面统计
    const pageStats = {
      total: pages.length,
      active: pages.filter(page => page.isActive).length,
      inactive: pages.filter(page => !page.isActive).length,
      byType: {}
    }

    // 按类型统计页面
    pages.forEach(page => {
      const type = page.pageType || '未分类'
      pageStats.byType[type] = (pageStats.byType[type] || 0) + 1
    })

    // 系统使用统计（模拟数据）
    const usageStats = {
      todayRequests: 1250,
      todayUsers: 85,
      avgResponseTime: 245, // ms
      errorRate: 0.02, // 2%
      peakHour: '14:00-15:00',
      popularModels: [
        { name: 'GPT-3.5 Turbo', usage: 65 },
        { name: 'GPT-4', usage: 30 },
        { name: 'Claude 3 Sonnet', usage: 5 }
      ],
      popularAgents: [
        { name: '八字命理大师', usage: 45 },
        { name: '紫微斗数大师', usage: 35 },
        { name: '通用AI助手', usage: 20 }
      ]
    }

    // 趋势数据（最近7天，模拟数据）
    const trendData = {
      dailyUsers: [78, 82, 75, 88, 92, 85, 89],
      dailyRequests: [1100, 1200, 1050, 1300, 1400, 1250, 1350],
      dailyErrors: [22, 18, 25, 15, 20, 25, 18],
      dates: []
    }

    // 生成最近7天的日期
    for (let i = 6; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      trendData.dates.push(date.toISOString().split('T')[0])
    }

    const responseData = {
      overview: {
        users: userStats,
        models: modelStats,
        agents: agentStats,
        pages: pageStats,
        usage: usageStats
      },
      trends: trendData,
      lastUpdated: new Date().toISOString()
    }

    return formatSuccessResponse(responseData, '获取系统统计成功')

  } catch (error) {
    logger.error('管理员获取系统统计异常', {
      adminId: adminAuth.adminId,
      error: error.message
    })
    throw error
  }
}

/**
 * 获取详细使用报告
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 详细报告
 */
async function getDetailedReport(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'stats_read')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限查看详细报告', 403)
    }

    const { reportType = 'usage', startDate, endDate } = event || {}

    logger.info('管理员获取详细报告', {
      adminId: adminAuth.adminId,
      reportType,
      startDate,
      endDate
    })

    // 根据报告类型生成不同的报告数据（模拟实现）
    let reportData = {}

    switch (reportType) {
      case 'usage':
        reportData = {
          type: '使用情况报告',
          period: `${startDate} 至 ${endDate}`,
          summary: {
            totalRequests: 8750,
            uniqueUsers: 245,
            avgRequestsPerUser: 35.7,
            totalTokensUsed: 2450000,
            avgTokensPerRequest: 280
          },
          topUsers: [
            { username: 'testuser001', requests: 156, tokens: 43200 },
            { username: 'testuser002', requests: 142, tokens: 39800 },
            { username: 'adminuser', requests: 98, tokens: 27440 }
          ],
          hourlyDistribution: Array.from({ length: 24 }, (_, i) => ({
            hour: i,
            requests: Math.floor(Math.random() * 100) + 50
          }))
        }
        break

      case 'performance':
        reportData = {
          type: '性能报告',
          period: `${startDate} 至 ${endDate}`,
          summary: {
            avgResponseTime: 245,
            p95ResponseTime: 580,
            p99ResponseTime: 1200,
            errorRate: 0.02,
            uptime: 99.95
          },
          slowestEndpoints: [
            { endpoint: '/api/chat', avgTime: 450, requests: 3200 },
            { endpoint: '/api/models', avgTime: 180, requests: 1500 },
            { endpoint: '/api/agents', avgTime: 120, requests: 800 }
          ]
        }
        break

      default:
        throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '不支持的报告类型', 400)
    }

    return formatSuccessResponse(reportData, '获取详细报告成功')

  } catch (error) {
    logger.error('管理员获取详细报告异常', {
      adminId: adminAuth.adminId,
      error: error.message
    })
    throw error
  }
}

module.exports = {
  getSystemStats,
  getDetailedReport
}