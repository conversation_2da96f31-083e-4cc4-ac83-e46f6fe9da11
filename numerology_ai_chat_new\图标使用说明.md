# 应用图标配置说明

## 📋 图标配置

本项目已配置为自动使用 `assets/images/logo.png` 作为应用图标。

### 🔧 图标文件位置
- **源图标**: `assets/images/logo.png`
- **Windows图标**: `windows/runner/resources/app_icon.ico`

### 🚀 构建应用

#### 方法1: 使用脚本（推荐）
```bash
# 构建发布版本
build_app.bat

# 运行开发版本
run_app.bat
```

#### 方法2: 手动命令
```bash
# 构建发布版本
flutter build windows --release

# 运行开发版本
flutter run -d windows
```

### 📁 输出文件
构建完成后，可执行文件位于：
`build/windows/x64/runner/Release/numerology_ai_chat.exe`

### 🔄 更换图标
如需更换图标：
1. 替换 `assets/images/logo.png` 文件
2. 运行 `python setup_permanent_icon.py`
3. 重新构建应用

### 💡 注意事项
- 图标文件已集成到项目中，每次构建都会自动应用
- 建议使用正方形、高分辨率的图片作为源图标
- 支持JPG、PNG等常见格式

---
配置时间: 2025年1月2日
