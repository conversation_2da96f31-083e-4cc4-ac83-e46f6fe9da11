/// 地区判断服务
/// 根据地址信息判断所属地区（大陆/台湾/香港/澳门）
class RegionService {
  /// 地区映射表
  static const Map<String, String> _regionMapping = {
    // 台湾省
    '台湾省': '台湾',
    '台湾': '台湾',
    
    // 香港特别行政区
    '香港特别行政区': '香港',
    '香港特區': '香港',
    '香港': '香港',
    
    // 澳门特别行政区
    '澳门特别行政区': '澳门',
    '澳門特別行政區': '澳门',
    '澳门': '澳门',
    '澳門': '澳门',
  };

  /// 台湾地区的城市列表
  static const Set<String> _taiwanCities = {
    '台北市', '新北市', '桃园市', '台中市', '台南市', '高雄市',
    '基隆市', '新竹市', '嘉义市',
    '新竹县', '苗栗县', '彰化县', '南投县', '云林县', '嘉义县',
    '屏东县', '宜兰县', '花莲县', '台东县', '澎湖县', '金门县', '连江县'
  };

  /// 香港地区的区域列表
  static const Set<String> _hongkongDistricts = {
    '中西区', '东区', '南区', '湾仔区',
    '九龙城区', '观塘区', '深水埗区', '黄大仙区', '油尖旺区',
    '离岛区', '葵青区', '北区', '西贡区', '沙田区', '屯门区', '大埔区', '荃湾区', '元朗区'
  };

  /// 澳门地区的区域列表
  static const Set<String> _macauDistricts = {
    '澳门半岛', '氹仔', '路环', '花地玛堂区', '圣安多尼堂区', '大堂区',
    '望德堂区', '风顺堂区', '嘉模堂区', '圣方济各堂区'
  };

  /// 根据地址信息判断所属地区
  /// 返回：大陆、台湾、香港、澳门
  static String getRegionFromAddress({
    String? provinceName,
    String? cityName,
    String? districtName,
  }) {
    // 优先检查省份名称
    if (provinceName != null) {
      final region = _regionMapping[provinceName];
      if (region != null) {
        return region;
      }
    }

    // 检查城市名称
    if (cityName != null) {
      // 检查是否为台湾城市
      if (_taiwanCities.contains(cityName)) {
        return '台湾';
      }
      
      // 检查是否为香港
      if (cityName.contains('香港') || _hongkongDistricts.contains(cityName)) {
        return '香港';
      }
      
      // 检查是否为澳门
      if (cityName.contains('澳门') || cityName.contains('澳門') || _macauDistricts.contains(cityName)) {
        return '澳门';
      }
    }

    // 检查区县名称
    if (districtName != null) {
      // 检查是否为香港区域
      if (_hongkongDistricts.contains(districtName)) {
        return '香港';
      }
      
      // 检查是否为澳门区域
      if (_macauDistricts.contains(districtName)) {
        return '澳门';
      }
    }

    // 默认返回大陆
    return '大陆';
  }

  /// 根据AddressSelectionResult判断地区
  static String getRegionFromAddressResult(dynamic addressResult) {
    if (addressResult == null) return '大陆';
    
    try {
      final provinceName = addressResult.provinceName as String?;
      final cityName = addressResult.cityName as String?;
      final districtName = addressResult.districtName as String?;
      
      return getRegionFromAddress(
        provinceName: provinceName,
        cityName: cityName,
        districtName: districtName,
      );
    } catch (e) {
      // 如果解析失败，默认返回大陆
      return '大陆';
    }
  }

  /// 检查指定地区是否支持夏令时检查
  static bool supportsRegion(String region) {
    return ['大陆', '台湾', '香港', '澳门'].contains(region);
  }

  /// 获取地区的显示名称
  static String getRegionDisplayName(String region) {
    switch (region) {
      case '大陆':
        return '中国大陆';
      case '台湾':
        return '台湾地区';
      case '香港':
        return '香港特别行政区';
      case '澳门':
        return '澳门特别行政区';
      default:
        return region;
    }
  }
}
