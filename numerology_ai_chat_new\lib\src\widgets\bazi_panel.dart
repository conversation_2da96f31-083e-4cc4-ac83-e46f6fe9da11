import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../models/bazi_model.dart';
import '../providers/chat_provider.dart';
import '../providers/bazi_history_provider.dart';
import '../services/bazi_service.dart';
import '../services/address_service.dart';
import '../services/daylight_saving_service.dart';
import '../services/region_service.dart';
import 'address_selector.dart';

// Bazi Panel Widget
// This panel is responsible for handling Bazi calculation input and display.



/// 排盘面板
class BaziPanel extends ConsumerStatefulWidget {
  final bool showTitle;
  final VoidCallback? onHelpPressed;

  const BaziPanel({
    super.key,
    this.showTitle = true,
    this.onHelpPressed,
  });

  @override
  ConsumerState<BaziPanel> createState() => _BaziPanelState();
}

class _BaziPanelState extends ConsumerState<BaziPanel> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _birthPlaceController = TextEditingController();
  final BaziService _baziService = BaziService();

  // 日期时间输入控制器
  final _yearController = TextEditingController();
  final _monthController = TextEditingController();
  final _dayController = TextEditingController();
  final _hourController = TextEditingController();
  final _minuteController = TextEditingController();

  // 焦点节点
  final _yearFocusNode = FocusNode();
  final _monthFocusNode = FocusNode();
  final _dayFocusNode = FocusNode();
  final _hourFocusNode = FocusNode();
  final _minuteFocusNode = FocusNode();

  // 自动跳转控制标志
  bool _isAutoJumping = false;

  // 记录上一次的输入值，用于判断是否为增量输入
  String _lastYearValue = '';
  String _lastMonthValue = '';
  String _lastDayValue = '';
  String _lastHourValue = '';
  String _lastMinuteValue = '';

  DateTime? _selectedDateTime;
  Gender _selectedGender = Gender.male;
  CalendarType _calendarType = CalendarType.solar;
  bool _isLeapMonth = false;
  bool _isCalculating = false;
  BaziResultModel? _currentResult;

  // 地址选择相关
  AddressSelectionResult? _selectedAddress;
  bool _useDetailedAddress = true; // 默认使用精确地址

  // 真太阳时计算相关
  bool _considerDaylightSaving = true;
  bool _enableSolarTimeCalculation = true;

  // 子时处理设置
  int _ziTimeHandling = 0; // 0: 晚子时（子时算当天），1: 早子时（子时算第二天）

  @override
  void dispose() {
    _nameController.dispose();
    _birthPlaceController.dispose();
    _yearController.dispose();
    _monthController.dispose();
    _dayController.dispose();
    _hourController.dispose();
    _minuteController.dispose();

    _yearFocusNode.dispose();
    _monthFocusNode.dispose();
    _dayFocusNode.dispose();
    _hourFocusNode.dispose();
    _minuteFocusNode.dispose();

    super.dispose();
  }

  void _updateDateTime() {
    final year = int.tryParse(_yearController.text);
    final month = int.tryParse(_monthController.text);
    final day = int.tryParse(_dayController.text);
    final hour = int.tryParse(_hourController.text);
    final minute = int.tryParse(_minuteController.text);

    if (year != null && month != null && day != null && hour != null && minute != null) {
      try {
        final dateTime = DateTime(year, month, day, hour, minute);
        setState(() {
          _selectedDateTime = dateTime;
        });
      } catch (e) {
        // 无效日期，不更新
        setState(() {
          _selectedDateTime = null;
        });
      }
    } else {
      setState(() {
        _selectedDateTime = null;
      });
    }
  }

  /// 检查是否应该显示夏令时选项
  bool _shouldShowDaylightSavingOption() {
    if (_selectedDateTime == null) return false;

    // 根据选择的地址判断地区
    final region = _getSelectedRegion();

    // 检查选择的日期是否在夏令时期间
    final dstInfo = DaylightSavingService.checkDaylightSaving(_selectedDateTime!, region: region);
    return dstInfo.isDaylightSaving;
  }

  /// 获取夏令时描述信息
  String _getDaylightSavingDescription() {
    if (_selectedDateTime == null) return '夏令时调整';

    // 根据选择的地址判断地区
    final region = _getSelectedRegion();

    final dstInfo = DaylightSavingService.checkDaylightSaving(_selectedDateTime!, region: region);
    if (dstInfo.isDaylightSaving && dstInfo.description != null) {
      final regionDisplayName = RegionService.getRegionDisplayName(region);
      return '${dstInfo.year}年${regionDisplayName}${dstInfo.description}实施夏令时';
    }

    return '夏令时调整';
  }

  /// 获取当前选择的地区
  String _getSelectedRegion() {
    if (_useDetailedAddress && _selectedAddress != null) {
      return RegionService.getRegionFromAddressResult(_selectedAddress);
    }
    return '大陆'; // 默认大陆
  }

  /// 智能跳转到下一个输入框
  /// 优化版本：只在用户正常输入完成时跳转，避免干扰删除、复制粘贴等操作
  void _handleAutoJump(String value, String fieldType) {
    if (_isAutoJumping) return; // 防止递归调用

    // 获取上一次的值
    String lastValue = '';
    switch (fieldType) {
      case 'year':
        lastValue = _lastYearValue;
        _lastYearValue = value;
        break;
      case 'month':
        lastValue = _lastMonthValue;
        _lastMonthValue = value;
        break;
      case 'day':
        lastValue = _lastDayValue;
        _lastDayValue = value;
        break;
      case 'hour':
        lastValue = _lastHourValue;
        _lastHourValue = value;
        break;
      case 'minute':
        lastValue = _lastMinuteValue;
        _lastMinuteValue = value;
        break;
    }

    // 只有在值增长且达到目标长度时才跳转
    // 这样可以避免删除、选择、粘贴等操作时的误跳转
    bool isIncremental = value.length > lastValue.length;
    if (!isIncremental) return;

    bool shouldJump = false;
    FocusNode? nextFocus;

    switch (fieldType) {
      case 'year':
        // 年份：4位数字时跳转
        if (value.length == 4) {
          final year = int.tryParse(value);
          if (year != null && year >= 1900 && year <= DateTime.now().year) {
            shouldJump = true;
            nextFocus = _monthFocusNode;
          }
        }
        break;
      case 'month':
        // 月份：2位数字时跳转
        if (value.length == 2) {
          final month = int.tryParse(value);
          if (month != null && month >= 1 && month <= 12) {
            shouldJump = true;
            nextFocus = _dayFocusNode;
          }
        }
        break;
      case 'day':
        // 日期：2位数字时跳转
        if (value.length == 2) {
          final day = int.tryParse(value);
          if (day != null && day >= 1 && day <= 31) {
            shouldJump = true;
            nextFocus = _hourFocusNode;
          }
        }
        break;
      case 'hour':
        // 小时：2位数字时跳转
        if (value.length == 2) {
          final hour = int.tryParse(value);
          if (hour != null && hour >= 0 && hour <= 23) {
            shouldJump = true;
            nextFocus = _minuteFocusNode;
          }
        }
        break;
      case 'minute':
        // 分钟：2位数字时完成输入（失去焦点）
        if (value.length == 2) {
          final minute = int.tryParse(value);
          if (minute != null && minute >= 0 && minute <= 59) {
            // 使用WidgetsBinding.instance.addPostFrameCallback确保在下一帧执行
            // 这样可以避免与当前的键盘事件冲突
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                _minuteFocusNode.unfocus();
              }
            });
          }
        }
        break;
    }

    if (shouldJump && nextFocus != null) {
      _isAutoJumping = true;
      // 使用WidgetsBinding.instance.addPostFrameCallback确保在下一帧执行
      // 这样可以避免与当前的键盘事件和文本输入处理冲突
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          nextFocus!.requestFocus();
          _isAutoJumping = false;
        }
      });
    }
  }

  Future<void> _calculateBazi() async {
    // 验证表单
    if (!_formKey.currentState!.validate() || _selectedDateTime == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请完整填写所有信息')),
      );
      return;
    }

    // 验证地址信息
    if (_useDetailedAddress && _selectedAddress == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请选择完整的出生地址')),
      );
      return;
    }

    setState(() {
      _isCalculating = true;
    });

    try {
      // 确定出生地信息
      String birthPlace;
      double? latitude;
      double? longitude;

      if (_useDetailedAddress && _selectedAddress != null) {
        // 使用详细地址选择
        birthPlace = _selectedAddress!.shortAddress;
        latitude = _selectedAddress!.latitude;
        longitude = _selectedAddress!.longitude;
      } else {
        // 不使用地址
        birthPlace = '未指定';
        latitude = null;
        longitude = null;
      }

      final input = BaziInputModel(
        name: _nameController.text.trim(),
        birthDateTime: _selectedDateTime!,
        gender: _selectedGender,
        calendarType: _calendarType,
        isLeapMonth: _isLeapMonth,
        birthPlace: birthPlace,
        latitude: latitude,
        longitude: longitude,
        considerDaylightSaving: _considerDaylightSaving,
        enableSolarTimeCalculation: _enableSolarTimeCalculation,
        ziTimeHandling: _ziTimeHandling,
      );

      final result = await _baziService.calculateBazi(input);
      
      setState(() {
        _currentResult = result;
        _isCalculating = false;
      });

      // 将排盘结果传递给聊天提供者
      if (mounted) {
        ref.read(chatProvider.notifier).setBaziResult(result);

        // 通知历史记录刷新
        ref.read(baziHistoryProvider.notifier).notifyHistoryChanged();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('排盘完成！已应用到AI对话中'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isCalculating = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('排盘失败: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 标题（可选）
            if (widget.showTitle) ...[
              Row(
                children: [
                  Icon(
                    Icons.calculate_outlined,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '八字排盘',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  if (widget.onHelpPressed != null)
                    IconButton(
                      icon: const Icon(Icons.help_outline),
                      onPressed: widget.onHelpPressed,
                    ),
                ],
              ),
              const SizedBox(height: 16),
            ],

            // 输入表单
            Expanded(
              child: SingleChildScrollView(
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildInputForm(),
                      const SizedBox(height: 24),
                      _buildCalculateButton(),
                      const SizedBox(height: 24),
                      _buildResultDisplay(),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInputForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // 姓名输入
        TextFormField(
          controller: _nameController,
          textInputAction: TextInputAction.next,
          decoration: const InputDecoration(
            labelText: '姓名',
            prefixIcon: Icon(Icons.person_outline),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入姓名';
            }
            return null;
          },
        ),
        
        const SizedBox(height: 16),
        
        // 性别选择
        Row(
          children: [
            const Icon(Icons.wc_outlined),
            const SizedBox(width: 12),
            const Text('性别:'),
            const SizedBox(width: 16),
            Expanded(
              child: SegmentedButton<Gender>(
                segments: Gender.values.map((gender) {
                  return ButtonSegment<Gender>(
                    value: gender,
                    label: Text(gender.displayName),
                  );
                }).toList(),
                selected: {_selectedGender},
                onSelectionChanged: (Set<Gender> selection) {
                  setState(() {
                    _selectedGender = selection.first;
                  });
                },
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // 出生时间输入
        _buildDateTimeInput(),
        
        const SizedBox(height: 16),
        
        // 历法类型选择
        Row(
          children: [
            const Icon(Icons.calendar_today_outlined),
            const SizedBox(width: 12),
            const Text('历法:'),
            const SizedBox(width: 16),
            Expanded(
              child: SegmentedButton<CalendarType>(
                segments: CalendarType.values.map((type) {
                  return ButtonSegment<CalendarType>(
                    value: type,
                    label: Text(type.displayName),
                  );
                }).toList(),
                selected: {_calendarType},
                onSelectionChanged: (Set<CalendarType> selection) {
                  setState(() {
                    _calendarType = selection.first;
                  });
                },
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // 子时处理设置
        _buildZiTimeHandlingSection(),

        const SizedBox(height: 16),

        // 闰月选择（仅农历时显示）
        if (_calendarType == CalendarType.lunar)
          CheckboxListTile(
            title: const Text('闰月'),
            subtitle: const Text('如果出生在闰月请勾选'),
            value: _isLeapMonth,
            onChanged: (value) {
              setState(() {
                _isLeapMonth = value ?? false;
              });
            },
            controlAffinity: ListTileControlAffinity.leading,
          ),
        
        const SizedBox(height: 16),

        // 出生地选择模式切换
        Row(
          children: [
            const Icon(Icons.location_on_outlined),
            const SizedBox(width: 12),
            const Text('出生地:'),
            const Spacer(),
            Switch(
              value: _useDetailedAddress,
              onChanged: (value) {
                setState(() {
                  _useDetailedAddress = value;
                  if (!value) {
                    _selectedAddress = null;
                    // 清空简单地址输入
                    _birthPlaceController.clear();
                  }
                });
              },
            ),
            const SizedBox(width: 8),
            Text(
              _useDetailedAddress ? '精确地址' : '不使用地址',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),

        const SizedBox(height: 8),

        // 地址选择说明
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                _useDetailedAddress ? Icons.location_on : Icons.location_off,
                size: 16,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _useDetailedAddress
                      ? '使用精确地址进行真太阳时计算，提高八字精度'
                      : '不使用地址信息，使用北京时间进行八字计算',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 12),

        // 出生地输入
        if (_useDetailedAddress) ...[
          // 详细地址选择
          AddressSelector(
            onAddressSelected: (address) {
              setState(() {
                _selectedAddress = address;
              });
            },
            initialAddress: _selectedAddress,
            enableSolarTime: true,
          ),
        ],

        // 真太阳时计算选项（仅在使用详细地址时显示）
        if (_useDetailedAddress && _selectedAddress != null) ...[
          const SizedBox(height: 16),

          // 真太阳时计算开关
          SwitchListTile(
            title: const Text('真太阳时计算'),
            subtitle: const Text('根据地理位置修正时间，提高八字精度'),
            value: _enableSolarTimeCalculation,
            onChanged: (value) {
              setState(() {
                _enableSolarTimeCalculation = value;
                // 如果关闭真太阳时计算，也关闭夏令时考虑
                if (!value) {
                  _considerDaylightSaving = false;
                }
              });
            },
            secondary: const Icon(Icons.schedule),
          ),

          // 夏令时考虑开关（仅在启用真太阳时计算且在夏令时期间时显示）
          if (_enableSolarTimeCalculation && _shouldShowDaylightSavingOption())
            SwitchListTile(
              title: const Text('考虑夏令时'),
              subtitle: Text(_getDaylightSavingDescription()),
              value: _considerDaylightSaving,
              onChanged: (value) {
                setState(() {
                  _considerDaylightSaving = value;
                });
              },
              secondary: const Icon(Icons.wb_sunny_outlined),
            ),
        ],
      ],
    );
  }

  Widget _buildDateTimeInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Row(
          children: [
            const Icon(Icons.access_time),
            const SizedBox(width: 12),
            Text(
              '出生时间',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // 日期输入行
        Row(
          children: [
            // 年
            Expanded(
              flex: 2,
              child: TextFormField(
                controller: _yearController,
                focusNode: _yearFocusNode,
                keyboardType: TextInputType.number,
                textInputAction: TextInputAction.next,
                maxLength: 4,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
                decoration: const InputDecoration(
                  labelText: '年',
                  hintText: '1990',
                  isDense: true,
                  counterText: '', // 隐藏字符计数器
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入年份';
                  }
                  final year = int.tryParse(value);
                  if (year == null || year < 1900 || year > DateTime.now().year) {
                    return '年份无效';
                  }
                  return null;
                },
                onChanged: (value) {
                  _updateDateTime();
                  _handleAutoJump(value, 'year');
                },
                onFieldSubmitted: (_) {
                  _monthFocusNode.requestFocus();
                },
              ),
            ),
            const SizedBox(width: 8),

            // 月
            Expanded(
              child: TextFormField(
                controller: _monthController,
                focusNode: _monthFocusNode,
                keyboardType: TextInputType.number,
                textInputAction: TextInputAction.next,
                maxLength: 2,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
                decoration: const InputDecoration(
                  labelText: '月',
                  hintText: '01',
                  isDense: true,
                  counterText: '', // 隐藏字符计数器
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入月份';
                  }
                  final month = int.tryParse(value);
                  if (month == null || month < 1 || month > 12) {
                    return '月份无效';
                  }
                  return null;
                },
                onChanged: (value) {
                  _updateDateTime();
                  _handleAutoJump(value, 'month');
                },
                onFieldSubmitted: (_) {
                  _dayFocusNode.requestFocus();
                },
              ),
            ),
            const SizedBox(width: 8),

            // 日
            Expanded(
              child: TextFormField(
                controller: _dayController,
                focusNode: _dayFocusNode,
                keyboardType: TextInputType.number,
                textInputAction: TextInputAction.next,
                maxLength: 2,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
                decoration: const InputDecoration(
                  labelText: '日',
                  hintText: '01',
                  isDense: true,
                  counterText: '', // 隐藏字符计数器
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入日期';
                  }
                  final day = int.tryParse(value);
                  if (day == null || day < 1 || day > 31) {
                    return '日期无效';
                  }
                  return null;
                },
                onChanged: (value) {
                  _updateDateTime();
                  _handleAutoJump(value, 'day');
                },
                onFieldSubmitted: (_) {
                  _hourFocusNode.requestFocus();
                },
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // 时间输入行
        Row(
          children: [
            // 时
            Expanded(
              child: TextFormField(
                controller: _hourController,
                focusNode: _hourFocusNode,
                keyboardType: TextInputType.number,
                textInputAction: TextInputAction.next,
                maxLength: 2,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
                decoration: const InputDecoration(
                  labelText: '时',
                  hintText: '12',
                  isDense: true,
                  counterText: '', // 隐藏字符计数器
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入小时';
                  }
                  final hour = int.tryParse(value);
                  if (hour == null || hour < 0 || hour > 23) {
                    return '小时无效';
                  }
                  return null;
                },
                onChanged: (value) {
                  _updateDateTime();
                  _handleAutoJump(value, 'hour');
                },
                onFieldSubmitted: (_) {
                  _minuteFocusNode.requestFocus();
                },
              ),
            ),
            const SizedBox(width: 8),

            // 分
            Expanded(
              child: TextFormField(
                controller: _minuteController,
                focusNode: _minuteFocusNode,
                keyboardType: TextInputType.number,
                textInputAction: TextInputAction.next,
                maxLength: 2,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
                decoration: const InputDecoration(
                  labelText: '分',
                  hintText: '00',
                  isDense: true,
                  counterText: '', // 隐藏字符计数器
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入分钟';
                  }
                  final minute = int.tryParse(value);
                  if (minute == null || minute < 0 || minute > 59) {
                    return '分钟无效';
                  }
                  return null;
                },
                onChanged: (value) {
                  _updateDateTime();
                  _handleAutoJump(value, 'minute');
                },
                onFieldSubmitted: (_) {
                  // 跳转到历法选择
                  FocusScope.of(context).nextFocus();
                },
              ),
            ),

            const SizedBox(width: 16),

            // 当前时间显示
            Expanded(
              flex: 2,
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                  ),
                ),
                child: Text(
                  _selectedDateTime == null
                      ? '请输入完整时间'
                      : DateFormat('yyyy年MM月dd日 HH:mm').format(_selectedDateTime!),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: _selectedDateTime == null
                        ? Theme.of(context).colorScheme.onSurface.withOpacity(0.6)
                        : Theme.of(context).colorScheme.primary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 8),

        // 提示文本
        Text(
          '请输入准确的出生时间（24小时制），时间越精确，排盘结果越准确',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
      ],
    );
  }

  Widget _buildCalculateButton() {
    return ElevatedButton.icon(
      onPressed: _isCalculating ? null : _calculateBazi,
      icon: _isCalculating
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : const Icon(Icons.calculate),
      label: Text(_isCalculating ? '排盘中...' : '开始排盘'),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
      ),
    );
  }

  Widget _buildResultDisplay() {
    if (_currentResult == null) {
      return Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
          ),
        ),
        child: Column(
          children: [
            Icon(
              Icons.auto_awesome_outlined,
              size: 48,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              '排盘结果将在这里显示',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '排盘完成',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '姓名：${_currentResult!.input.name}',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 4),
          Text(
            '四柱：${_currentResult!.fourPillars}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          // 显示真太阳时信息（如果有的话）
          if (_currentResult!.detailedData['solarTimeInfo'] != null)
            _buildSolarTimeInfo(_currentResult!.detailedData['solarTimeInfo']),
          const SizedBox(height: 12),
          Text(
            '已应用到AI对话中，可以开始咨询了！',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建真太阳时信息显示
  Widget _buildSolarTimeInfo(Map<String, dynamic> solarTimeInfo) {
    final timeDiffMinutes = solarTimeInfo['timeDiffMinutes'] as int? ?? 0;
    final longitude = solarTimeInfo['longitude'] as double? ?? 0.0;
    final longitudeDiff = solarTimeInfo['longitudeDiff'] as double? ?? 0.0;
    final timeDiffText = solarTimeInfo['timeDiffText'] as String? ?? '';
    final daylightSaving = solarTimeInfo['daylightSaving'] as Map<String, dynamic>?;
    final originalTime = solarTimeInfo['originalTime'] as String?;
    final solarTime = solarTimeInfo['solarTime'] as String?;

    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondaryContainer.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.secondary.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.access_time,
                size: 16,
                color: Theme.of(context).colorScheme.secondary,
              ),
              const SizedBox(width: 4),
              Text(
                '真太阳时计算',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.secondary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // 时间对比信息
          if (originalTime != null && solarTime != null) ...[
            _buildTimeComparisonRow('输入时间', originalTime),
            const SizedBox(height: 4),
            _buildTimeComparisonRow('真太阳时', solarTime),
            const SizedBox(height: 8),
          ],

          // 地理位置信息
          Text(
            '经度: ${longitude.toStringAsFixed(4)}°，与北京时间基准经度相差${longitudeDiff.abs().toStringAsFixed(4)}°',
            style: Theme.of(context).textTheme.bodySmall,
          ),

          // 时间差异信息
          if (timeDiffMinutes != 0) ...[
            const SizedBox(height: 4),
            Text(
              '时间差异：真太阳时比北京时间$timeDiffText',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
                color: timeDiffMinutes > 0
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.tertiary,
              ),
            ),
          ] else ...[
            const SizedBox(height: 4),
            Text(
              '时间差异：真太阳时与北京时间相同',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],

          // 夏令时信息
          if (daylightSaving != null && daylightSaving['isDaylightSaving'] == true) ...[
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.wb_sunny_outlined,
                  size: 14,
                  color: Theme.of(context).colorScheme.tertiary,
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    '该时期实行夏令时，已自动调整',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.tertiary,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// 构建时间对比行
  Widget _buildTimeComparisonRow(String label, String timeString) {
    // 解析ISO时间字符串并格式化
    String formattedTime = timeString;
    try {
      final dateTime = DateTime.parse(timeString);
      formattedTime = '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}';
    } catch (e) {
      // 如果解析失败，使用原始字符串
    }

    return Row(
      children: [
        SizedBox(
          width: 60,
          child: Text(
            '$label:',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Text(
          formattedTime,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontFamily: 'monospace',
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  /// 构建子时处理设置区域
  Widget _buildZiTimeHandlingSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.nightlight_round_outlined),
            const SizedBox(width: 12),
            Text(
              '子时处理规则',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),

        // 子时处理规则选择
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
            ),
          ),
          child: Column(
            children: [
              RadioListTile<int>(
                title: const Text('夜子时（当代常用）'),
                subtitle: const Text('当日23:00-00:00出生：\n日柱取当日干支，时柱按次日日干推算（五鼠遁规则）'),
                value: 0,
                groupValue: _ziTimeHandling,
                onChanged: (value) {
                  setState(() {
                    _ziTimeHandling = value ?? 0;
                  });
                },
                contentPadding: EdgeInsets.zero,
                dense: true,
              ),
              RadioListTile<int>(
                title: const Text('子时换日（传统古法）'),
                subtitle: const Text('当日23:00-24:00出生：\n日柱取次日干支，时柱按次日日干推算'),
                value: 1,
                groupValue: _ziTimeHandling,
                onChanged: (value) {
                  setState(() {
                    _ziTimeHandling = value ?? 0;
                  });
                },
                contentPadding: EdgeInsets.zero,
                dense: true,
              ),
            ],
          ),
        ),

        const SizedBox(height: 8),

        // 说明文字
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '此规则仅针对当日23:00至次日00:00（夜子时）出生者有效。',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
