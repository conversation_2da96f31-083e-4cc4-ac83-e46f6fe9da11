# API地址缓存修复报告

## 问题描述

旧版本的程序会将go中转API地址缓存到本地，而新版本登录后，发现有缓存就没有从云函数或数据库中去获取最新的API地址，从而导致使用的是旧版本的API地址。这明显是不合理的，go中转API地址必须要从云端来获取。

## 问题根源分析

### 原有缓存机制的问题

在`SystemConfigService.getGoProxyApiUrl()`方法中，存在三层缓存机制：

1. **内存缓存**：30分钟有效期 ✅ 正常
2. **本地存储缓存**：持久化存储，**没有过期检查** ❌ 问题所在
3. **云函数获取**：从数据库获取最新配置 ✅ 正常

**核心问题**：本地存储缓存没有时间戳验证，旧版本程序缓存的API地址会一直被使用，即使内存缓存过期了，也会从本地存储中读取旧的API地址，而不会去云函数获取最新的。

## 修复方案

### 1. 修改SystemConfigService缓存逻辑

#### 1.1 添加强制刷新参数

```dart
/// 获取Go代理API地址
/// [forceRefresh] 是否强制从云端刷新，忽略所有缓存
Future<String> getGoProxyApiUrl({bool forceRefresh = false}) async {
```

#### 1.2 本地存储缓存添加时间戳验证

```dart
// 检查本地存储缓存（添加时间戳验证）
final result = await _storageService.get<String>(AppConstants.goProxyApiUrlKey);
final timestampResult = await _storageService.get<String>('${AppConstants.goProxyApiUrlKey}_timestamp');

if (result.isSuccess && result.data != null && 
    timestampResult.isSuccess && timestampResult.data != null) {
  try {
    final cacheTime = DateTime.parse(timestampResult.data!);
    final cacheAge = DateTime.now().difference(cacheTime);
    
    // 本地存储缓存也有30分钟的有效期
    if (cacheAge.compareTo(_cacheExpiry) < 0) {
      // 使用缓存
    } else {
      // 缓存过期，从云端获取
    }
  } catch (e) {
    // 时间戳解析失败，从云端获取
  }
}
```

#### 1.3 缓存存储时同时保存时间戳

```dart
// 缓存到内存和本地存储（带时间戳）
_cachedGoProxyApiUrl = configValue;
_cacheTime = DateTime.now();
await _storageService.set(AppConstants.goProxyApiUrlKey, configValue);
await _storageService.set('${AppConstants.goProxyApiUrlKey}_timestamp', _cacheTime!.toIso8601String());
```

#### 1.4 完善缓存清除功能

```dart
/// 清除缓存
Future<void> clearCache() async {
  print('SystemConfigService: 清除所有API地址缓存');
  _cachedGoProxyApiUrl = null;
  _cacheTime = null;
  await _storageService.delete(AppConstants.goProxyApiUrlKey);
  await _storageService.delete('${AppConstants.goProxyApiUrlKey}_timestamp');
}
```

#### 1.5 添加强制刷新方法

```dart
/// 强制刷新API地址（登录后调用）
Future<String> forceRefreshGoProxyApiUrl() async {
  print('SystemConfigService: 强制刷新Go代理API地址');
  return await getGoProxyApiUrl(forceRefresh: true);
}
```

### 2. 在AuthProvider中集成API地址刷新

#### 2.1 添加SystemConfigService依赖

```dart
class AuthProvider extends ChangeNotifier {
  final AuthService _authService;
  final StorageService _storageService;
  final SystemConfigService _systemConfigService;  // 新增
  
  AuthProvider(this._authService, this._storageService, this._systemConfigService) {
```

#### 2.2 登录成功后强制刷新API地址

```dart
// 登录成功后强制刷新API地址，确保使用最新配置
try {
  debugPrint('AuthProvider: 登录成功，开始刷新API地址配置');
  await _systemConfigService.forceRefreshGoProxyApiUrl();
  debugPrint('AuthProvider: API地址配置刷新成功');
} catch (e) {
  debugPrint('AuthProvider: API地址配置刷新失败: $e');
  // 不影响登录流程，只记录错误
}
```

#### 2.3 更新Provider定义

```dart
final authProvider = ChangeNotifierProvider<AuthProvider>((ref) {
  final authService = ref.read(authServiceProvider);
  final storageService = ref.read(authStorageServiceProvider);
  final systemConfigService = ref.read(systemConfigServiceProvider);  // 新增
  final provider = AuthProvider(authService, storageService, systemConfigService);
  return provider;
});
```

## 修复效果

### 1. 解决旧版本缓存问题

- ✅ 旧版本缓存（无时间戳）会被检测为无效并忽略
- ✅ 本地存储缓存现在有30分钟的有效期
- ✅ 过期缓存会自动从云端重新获取

### 2. 确保登录后使用最新配置

- ✅ 每次登录成功后都会强制刷新API地址
- ✅ 忽略所有缓存，直接从云端获取最新配置
- ✅ 不影响登录流程，即使刷新失败也不会阻止登录

### 3. 提供完整的缓存管理

- ✅ 内存缓存：30分钟有效期，提高性能
- ✅ 本地存储缓存：30分钟有效期，带时间戳验证
- ✅ 强制刷新：登录时或需要时可强制从云端获取
- ✅ 缓存清除：完整清除所有相关缓存数据

## 测试验证

运行测试文件 `test_api_cache_fix.dart` 验证修复效果：

```bash
dart run test_api_cache_fix.dart
```

测试结果：
- ✅ SystemConfigService缓存逻辑已修复
- ✅ AuthProvider集成已完成
- ✅ 缓存清除功能已完善
- ✅ 时间戳验证功能已实现

## 总结

通过这次修复，彻底解决了旧版本API地址缓存影响新版本使用的问题：

1. **根本解决**：本地存储缓存增加时间戳验证，确保缓存有效期
2. **主动刷新**：登录后强制从云端获取最新API地址
3. **向后兼容**：旧版本缓存（无时间戳）会被自动忽略
4. **性能优化**：保留内存缓存机制，提高响应速度
5. **完整管理**：提供完整的缓存清除和管理功能

现在，go中转API地址必须从云端获取，确保了系统的一致性和可维护性。
