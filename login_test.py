#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def login_and_get_token():
    """登录并获取token"""
    
    # 云函数URL
    api_url = "https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction"
    
    # 登录数据
    request_data = {
        "action": "login",
        "username": "demo",
        "password": "123456"
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print("开始登录...")
        
        # 发送请求
        response = requests.post(api_url, json=request_data, headers=headers, timeout=30)
        response.raise_for_status()
        
        # 解析响应
        data = response.json()
        print(f"响应状态码: {response.status_code}")
        print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        # 检查结果
        if data.get("code") == 0:
            print("✅ 登录成功")
            tokens = data.get("data", {}).get("data", {}).get("tokens", {})
            access_token = tokens.get("accessToken")
            if access_token:
                print(f"Access Token: {access_token}")
                return access_token
            else:
                print("❌ 未获取到access token")
                return None
        else:
            print(f"❌ 登录失败: {data.get('message', '未知错误')}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")
        return None

def test_simulate_payment_with_token(token):
    """使用token测试模拟支付功能"""
    
    # 云函数URL
    api_url = "https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction"
    
    # 测试订单ID（超值套餐）
    order_id = "c0a2d8e4685068b502f851c6348b927b"
    
    # 请求数据
    request_data = {
        "action": "simulatePayment",
        "orderId": order_id,
        "success": True
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    try:
        print(f"\n开始测试模拟支付...")
        print(f"订单ID: {order_id}")
        
        # 发送请求
        response = requests.post(api_url, json=request_data, headers=headers, timeout=30)
        response.raise_for_status()
        
        # 解析响应
        data = response.json()
        print(f"响应状态码: {response.status_code}")
        print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        # 检查结果
        if data.get("code") == 0:
            print("✅ 模拟支付请求成功")
            payment_data = data.get("data", {}).get("data", {})
            if payment_data.get("success"):
                print(f"✅ 支付成功，充值算力: {payment_data.get('quotaAdded', 0)}")
            else:
                print("❌ 支付失败")
        else:
            print(f"❌ 请求失败: {data.get('message', '未知错误')}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")

def test_get_user_info_with_token(token):
    """使用token测试获取用户信息"""
    
    # 云函数URL
    api_url = "https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction"
    
    # 请求数据
    request_data = {
        "action": "getUserInfo"
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    try:
        print(f"\n开始获取用户信息...")
        
        # 发送请求
        response = requests.post(api_url, json=request_data, headers=headers, timeout=30)
        response.raise_for_status()
        
        # 解析响应
        data = response.json()
        print(f"响应状态码: {response.status_code}")
        print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        # 检查结果
        if data.get("code") == 0:
            print("✅ 获取用户信息成功")
            user_data = data.get("data", {}).get("data", {}).get("user", {})
            print(f"用户算力: {user_data.get('availableCount', 0)}")
            return user_data.get('availableCount', 0)
        else:
            print(f"❌ 请求失败: {data.get('message', '未知错误')}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")
        return None

if __name__ == "__main__":
    # 登录获取token
    token = login_and_get_token()
    
    if token:
        # 获取支付前的用户算力
        print("\n=== 支付前用户信息 ===")
        before_quota = test_get_user_info_with_token(token)
        
        # 测试模拟支付
        print("\n=== 执行模拟支付 ===")
        test_simulate_payment_with_token(token)
        
        # 获取支付后的用户算力
        print("\n=== 支付后用户信息 ===")
        after_quota = test_get_user_info_with_token(token)
        
        # 计算差值
        if before_quota is not None and after_quota is not None:
            diff = after_quota - before_quota
            print(f"\n=== 算力变化 ===")
            print(f"支付前算力: {before_quota}")
            print(f"支付后算力: {after_quota}")
            print(f"算力变化: {diff}")
    else:
        print("❌ 无法获取token，测试终止")
