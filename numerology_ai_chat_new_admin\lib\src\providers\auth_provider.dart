
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:numerology_ai_chat_admin/src/services/admin_api_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

// 认证状态
class AuthState {
  final String? token;
  final bool isLoading;
  final String? error;

  AuthState({this.token, this.isLoading = false, this.error});

  bool get isAuthenticated => token != null;
}

// AuthProvider
class AuthNotifier extends StateNotifier<AuthState> {
  final AdminApiService _apiService;

  AuthNotifier(this._apiService) : super(AuthState()) {
    _loadToken();
  }

  Future<void> _loadToken() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('admin_token');
    if (token != null) {
      // 验证token是否仍然有效
      try {
        final response = await _apiService.getSystemStats(token);
        if (response['code'] == 0) {
          state = AuthState(token: token);
        } else {
          // Token无效，清除保存的token
          await prefs.remove('admin_token');
          state = AuthState();
        }
      } catch (e) {
        // Token验证失败，清除保存的token
        await prefs.remove('admin_token');
        state = AuthState();
      }
    }
  }

  Future<void> login(String adminAccount, String adminPassword) async {
    state = AuthState(isLoading: true);
    try {
      final response = await _apiService.adminLogin(adminAccount, adminPassword);
      
      // 打印完整的响应日志，用于调试
      print('Login Response: $response');

      if (response['code'] == 0 && response['data'] != null && response['data']['success'] == true) {
        // 修正了获取token的路径
        final token = response['data']['data']['tokens']['accessToken'];
        if (token != null) {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('admin_token', token);
          state = AuthState(token: token);
        } else {
          state = AuthState(error: '未能从响应中获取token');
        }
      } else {
        state = AuthState(error: response['message'] ?? '登录失败');
      }
    } catch (e) {
      print('Login Error: $e');
      state = AuthState(error: e.toString());
    }
  }

  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('admin_token');
    state = AuthState();
  }
}

// API服务提供者
final adminApiServiceProvider = Provider((ref) => AdminApiService());

// Auth状态提供者
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final apiService = ref.watch(adminApiServiceProvider);
  return AuthNotifier(apiService);
});
