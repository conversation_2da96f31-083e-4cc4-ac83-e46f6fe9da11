import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../core/constants/app_constants.dart';
import '../providers/auth_provider.dart';
import '../providers/payment_package_provider.dart';
import '../providers/purchase_history_provider.dart';
import '../models/payment_package_model.dart';
import '../services/payment_package_service.dart';

/// 购买次数页面
class PurchaseScreen extends ConsumerStatefulWidget {
  const PurchaseScreen({super.key});

  @override
  ConsumerState<PurchaseScreen> createState() => _PurchaseScreenState();
}

class _PurchaseScreenState extends ConsumerState<PurchaseScreen> {
  @override
  void initState() {
    super.initState();
    // 延迟到下一帧执行初始化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 刷新用户信息以显示最新算力
      _refreshUserInfo();
      // 加载套餐数据
      _loadPackages();
    });
  }

  /// 刷新用户信息
  Future<void> _refreshUserInfo() async {
    await ref.read(authProvider.notifier).refreshUser();
  }

  /// 加载套餐数据
  Future<void> _loadPackages() async {
    ref.read(autoLoadPaymentPackagesProvider);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('充值算力'),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Card(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(
                      Icons.shopping_cart_outlined,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const Gap(8),
                    Text(
                      '充值算力',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                  ],
                ),
              ),
              const Divider(height: 1),
              Expanded(
                child: _buildPurchaseOptions(context),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPurchaseOptions(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final packageState = ref.watch(paymentPackageProvider);

        if (packageState.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (packageState.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Theme.of(context).colorScheme.error,
                ),
                const Gap(16),
                Text(
                  '加载套餐失败',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Gap(8),
                Text(
                  packageState.error!,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                  textAlign: TextAlign.center,
                ),
                const Gap(16),
                ElevatedButton(
                  onPressed: () => ref.read(paymentPackageProvider.notifier).refresh(),
                  child: const Text('重试'),
                ),
              ],
            ),
          );
        }

        if (packageState.packages.isEmpty) {
          return const Center(
            child: Text('暂无可用套餐'),
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            await ref.read(paymentPackageProvider.notifier).refresh();
            await _refreshUserInfo();
          },
          child: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              _buildPurchaseSection(
                context,
                '推荐套餐',
                packageState.packages.map((package) =>
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: _buildPurchaseCard(context, package),
                  )
                ).toList(),
              ),
              const Gap(24),
              _buildPurchaseSection(
                context,
                '购买说明',
                [
                  _buildInfoCard(context),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPurchaseSection(BuildContext context, String title, List<Widget> children) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
        ),
        const Gap(8),
        ...children,
      ],
    );
  }

  Widget _buildPurchaseCard(BuildContext context, PaymentPackageModel package) {
    final theme = Theme.of(context);

    return Card(
      elevation: package.isRecommended ? 4 : 2,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: package.isRecommended
                ? theme.colorScheme.primary
                : theme.colorScheme.outline.withOpacity(0.3),
            width: package.isRecommended ? 2 : 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              package.packageName,
                              style: theme.textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            if (package.isRecommended) ...[
                              const Gap(8),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: theme.colorScheme.primary,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  '推荐',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.onPrimary,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                            // 显示标签
                            ...package.tags.map((tag) => Padding(
                              padding: const EdgeInsets.only(left: 8),
                              child: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: theme.colorScheme.secondary.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  tag,
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.secondary,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            )),
                          ],
                        ),
                        const Gap(4),
                        Text(
                          package.packageDescription,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const Gap(16),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              package.formattedPrice,
                              style: theme.textTheme.headlineMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.primary,
                              ),
                            ),
                            if (package.hasDiscount) ...[
                              const Gap(8),
                              Text(
                                package.formattedOriginalPrice,
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: theme.colorScheme.onSurface.withOpacity(0.5),
                                  decoration: TextDecoration.lineThrough,
                                ),
                              ),
                            ],
                            const Gap(8),
                            Text(
                              '${package.quotaCount}算力',
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: theme.colorScheme.onSurface.withOpacity(0.8),
                              ),
                            ),
                          ],
                        ),
                        const Gap(4),
                        Row(
                          children: [
                            Text(
                              '平均 ${package.formattedPricePerQuota}/算力',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurface.withOpacity(0.6),
                              ),
                            ),
                            if (package.hasDiscount) ...[
                              const Gap(8),
                              Text(
                                package.formattedSavedAmount,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.error,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () => _handlePurchase(context, package),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: package.isRecommended
                          ? theme.colorScheme.primary
                          : theme.colorScheme.secondary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                    child: const Text('立即购买'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const Gap(8),
                Text(
                  '购买须知',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const Gap(12),
            _buildInfoItem(context, '• 购买的算力永久有效，无使用期限'),
            _buildInfoItem(context, '• 每次AI对话根据智能体和模型消耗不同算力'),
            _buildInfoItem(context, '• 支持微信、支付宝等多种支付方式'),
            _buildInfoItem(context, '• 购买后算力将自动充值到您的账户'),
            _buildInfoItem(context, '• 支付遇到问题请联系客服：18595672129（微信同号）'),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(BuildContext context, String text) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        text,
        style: theme.textTheme.bodyMedium?.copyWith(
          color: theme.colorScheme.onSurface.withOpacity(0.8),
        ),
      ),
    );
  }

  void _handlePurchase(BuildContext context, PaymentPackageModel package) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('确认购买 ${package.packageName}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('套餐：${package.packageName}'),
            const Gap(8),
            Text('价格：${package.formattedPrice}'),
            if (package.hasDiscount) ...[
              const Gap(4),
              Text(
                '原价：${package.formattedOriginalPrice}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                  decoration: TextDecoration.lineThrough,
                ),
              ),
              Text(
                '节省：${package.formattedSavedAmount}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.error,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
            const Gap(8),
            Text('算力：${package.quotaCount}算力'),
            const Gap(8),
            Text('单价：${package.formattedPricePerQuota}/算力'),
            const Gap(16),
            Text(
              '确定要购买此套餐吗？',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showPaymentDialog(context, package);
            },
            child: const Text('确认购买'),
          ),
        ],
      ),
    );
  }

  void _showPaymentDialog(BuildContext context, PaymentPackageModel package) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PaymentDialog(
        package: package,
      ),
    );
  }
}

/// 支付对话框
class PaymentDialog extends ConsumerStatefulWidget {
  final PaymentPackageModel package;

  const PaymentDialog({
    super.key,
    required this.package,
  });

  @override
  ConsumerState<PaymentDialog> createState() => _PaymentDialogState();
}

class _PaymentDialogState extends ConsumerState<PaymentDialog> {
  bool _isPaymentProcessing = false;
  bool _showResult = false;
  bool _paymentSuccess = false;
  String? _orderId;
  String? _errorMessage;
  bool _isCreatingOrder = false; // 改为false，先显示支付方式选择
  bool _showPaymentMethodSelection = true; // 显示支付方式选择
  String? _paymentUrl;
  String? _qrCodeData;
  String _selectedPaymentMethod = 'WECHAT'; // 默认选择微信支付
  Timer? _statusCheckTimer;

  @override
  void initState() {
    super.initState();
    // 不再自动创建订单，先让用户选择支付方式
  }

  @override
  void dispose() {
    _statusCheckTimer?.cancel();
    super.dispose();
  }

  /// 创建订单
  Future<void> _createOrder() async {
    try {
      final authState = ref.read(authProvider);
      final token = authState.token;

      if (token == null) {
        setState(() {
          _errorMessage = '用户未登录';
          _isCreatingOrder = false;
        });
        return;
      }

      final service = PaymentPackageService();
      final result = await service.createPurchaseOrder(
        token: token,
        packageId: widget.package.id,
        quantity: 1,
        paymentMethod: _selectedPaymentMethod, // 传递选择的支付方式
      );

      if (result != null && result['order'] != null && result['payment'] != null) {
        final order = result['order'];
        final payment = result['payment'];
        setState(() {
          _orderId = order['_id'];
          _paymentUrl = payment['paymentUrl'];
          _qrCodeData = payment['qrCodeData'];
          _isCreatingOrder = false;
        });

        // 如果有支付URL，开始定时查询支付状态
        if (_paymentUrl != null) {
          _startStatusCheck();
        }
      } else {
        setState(() {
          _errorMessage = '创建订单失败';
          _isCreatingOrder = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = '创建订单失败: $e';
        _isCreatingOrder = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      child: Container(
        width: 400,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            Row(
              children: [
                Icon(
                  Icons.payment,
                  color: theme.colorScheme.primary,
                ),
                const Gap(8),
                Expanded(
                  child: Text(
                    '支付 ${widget.package.packageName}',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: (_showResult || _isPaymentProcessing) ? null : () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),

            const Divider(),
            const Gap(16),

            if (_showPaymentMethodSelection) ...[
              // 支付方式选择
              _buildPaymentMethodSelection(theme),
            ] else if (_isCreatingOrder) ...[
              // 创建订单中
              _buildCreatingOrder(theme),
            ] else if (_errorMessage != null) ...[
              // 创建订单失败
              _buildOrderError(theme),
            ] else if (!_showResult) ...[
              // 支付信息
              _buildPaymentInfo(theme),
              const Gap(24),

              // 二维码
              _buildQRCode(theme),
              const Gap(24),

              // 支付按钮
              _buildPaymentButtons(theme),
            ] else ...[
              // 支付结果
              _buildPaymentResult(theme),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethodSelection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 套餐信息
        _buildPaymentInfo(theme),
        const Gap(24),

        // 支付方式选择标题
        Text(
          '选择支付方式',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Gap(16),

        // 微信支付选项
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: _selectedPaymentMethod == 'WECHAT'
                  ? theme.colorScheme.primary
                  : theme.colorScheme.outline,
              width: _selectedPaymentMethod == 'WECHAT' ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ListTile(
            leading: Icon(
              Icons.wechat,
              color: Colors.green,
              size: 32,
            ),
            title: const Text('微信支付'),
            subtitle: const Text('使用微信扫码支付'),
            trailing: Radio<String>(
              value: 'WECHAT',
              groupValue: _selectedPaymentMethod,
              onChanged: (value) {
                setState(() {
                  _selectedPaymentMethod = value!;
                });
              },
            ),
            onTap: () {
              setState(() {
                _selectedPaymentMethod = 'WECHAT';
              });
            },
          ),
        ),
        const Gap(12),

        // 支付宝支付选项
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: _selectedPaymentMethod == 'ALIPAY'
                  ? theme.colorScheme.primary
                  : theme.colorScheme.outline,
              width: _selectedPaymentMethod == 'ALIPAY' ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ListTile(
            leading: Icon(
              Icons.account_balance_wallet,
              color: Colors.blue,
              size: 32,
            ),
            title: const Text('支付宝支付'),
            subtitle: const Text('使用支付宝扫码支付'),
            trailing: Radio<String>(
              value: 'ALIPAY',
              groupValue: _selectedPaymentMethod,
              onChanged: (value) {
                setState(() {
                  _selectedPaymentMethod = value!;
                });
              },
            ),
            onTap: () {
              setState(() {
                _selectedPaymentMethod = 'ALIPAY';
              });
            },
          ),
        ),
        const Gap(24),

        // 确认按钮
        Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('取消'),
              ),
            ),
            const Gap(16),
            Expanded(
              child: ElevatedButton(
                onPressed: () {
                  setState(() {
                    _showPaymentMethodSelection = false;
                    _isCreatingOrder = true;
                  });
                  _createOrder();
                },
                child: const Text('确认支付'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCreatingOrder(ThemeData theme) {
    return Column(
      children: [
        const CircularProgressIndicator(),
        const Gap(16),
        Text(
          '正在创建订单...',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildOrderError(ThemeData theme) {
    return Column(
      children: [
        Icon(
          Icons.error_outline,
          size: 64,
          color: theme.colorScheme.error,
        ),
        const Gap(16),
        Text(
          '创建订单失败',
          style: theme.textTheme.titleMedium?.copyWith(
            color: theme.colorScheme.error,
          ),
        ),
        const Gap(8),
        Text(
          _errorMessage ?? '未知错误',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
          textAlign: TextAlign.center,
        ),
        const Gap(24),
        Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('关闭'),
              ),
            ),
            const Gap(16),
            Expanded(
              child: ElevatedButton(
                onPressed: () {
                  setState(() {
                    _errorMessage = null;
                    _isCreatingOrder = false;
                    _showPaymentMethodSelection = true; // 回到支付方式选择
                  });
                },
                child: const Text('重新选择'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPaymentInfo(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.3),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('套餐名称：', style: theme.textTheme.bodyMedium),
              Text(widget.package.packageName, style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold)),
            ],
          ),
          const Gap(8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('购买算力：', style: theme.textTheme.bodyMedium),
              Text('${widget.package.quotaCount}算力', style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold)),
            ],
          ),
          const Gap(8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('支付金额：', style: theme.textTheme.bodyMedium),
              Text(
                widget.package.formattedPrice,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQRCode(ThemeData theme) {
    // 根据选择的支付方式显示不同的提示文字
    String paymentTip;
    if (_selectedPaymentMethod == 'WECHAT') {
      paymentTip = '请使用微信扫码支付';
    } else if (_selectedPaymentMethod == 'ALIPAY') {
      paymentTip = '请使用支付宝扫码支付';
    } else {
      paymentTip = '请使用微信或支付宝扫码支付';
    }

    return Column(
      children: [
        Text(
          paymentTip,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
        const Gap(16),
        Container(
          width: 200,
          height: 200,
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: theme.colorScheme.outline),
            borderRadius: BorderRadius.circular(8),
          ),
          child: _qrCodeData != null
              ? QrImageView(
                  data: _qrCodeData!,
                  version: QrVersions.auto,
                  size: 200.0,
                  backgroundColor: Colors.white,
                )
              : Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.qr_code,
                      size: 120,
                      color: theme.colorScheme.onSurface.withOpacity(0.3),
                    ),
                    const Gap(8),
                    Text(
                      '生成二维码中...',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.5),
                      ),
                    ),
                  ],
                ),
        ),
        const Gap(16),
        if (_orderId != null) ...[
          Text(
            '订单号：$_orderId',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          const Gap(8),
        ],
        if (_paymentUrl != null) ...[
          Text(
            '支付链接已生成，请扫码支付',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildPaymentButtons(ThemeData theme) {
    return Column(
      children: [
        if (_isPaymentProcessing) ...[
          const CircularProgressIndicator(),
          const Gap(16),
          Text(
            '正在查询支付状态...',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ] else ...[
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消支付'),
                ),
              ),
              const Gap(16),
              Expanded(
                child: ElevatedButton(
                  onPressed: _manualCheckPaymentStatus,
                  child: const Text('查询支付状态'),
                ),
              ),
            ],
          ),
          const Gap(12),
          Text(
            '扫码支付后点击"查询支付状态"确认支付结果',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          const Gap(8),
          Text(
            '系统每5秒自动查询一次支付状态',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.primary.withOpacity(0.7),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildPaymentResult(ThemeData theme) {
    final isError = _errorMessage != null;
    final success = _paymentSuccess && !isError;

    return Column(
      children: [
        Icon(
          success ? Icons.check_circle : Icons.error,
          size: 80,
          color: success ? Colors.green : Colors.red,
        ),
        const Gap(16),
        Text(
          success ? '支付成功！' : (isError ? '支付失败！' : '支付失败！'),
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: success ? Colors.green : Colors.red,
          ),
        ),
        const Gap(8),
        Text(
          success
              ? '${widget.package.quotaCount}算力已充值到您的账户'
              : (isError ? _errorMessage! : '支付过程中出现问题，请重试或联系客服'),
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
          textAlign: TextAlign.center,
        ),
        const Gap(24),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ),
      ],
    );
  }

  /// 开始定时查询支付状态
  void _startStatusCheck() {
    _statusCheckTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _checkPaymentStatus();
    });
  }

  /// 停止定时查询
  void _stopStatusCheck() {
    _statusCheckTimer?.cancel();
    _statusCheckTimer = null;
  }

  /// 查询支付状态
  Future<void> _checkPaymentStatus() async {
    if (_orderId == null) return;

    try {
      final authState = ref.read(authProvider);
      final token = authState.token;

      if (token == null) return;

      final service = PaymentPackageService();
      final result = await service.queryPaymentStatus(
        token: token,
        orderId: _orderId!,
      );

      if (result != null && result['success'] == true) {
        // 云函数返回的数据结构是嵌套的：data.data.paymentStatus
        final paymentData = result['data'];
        final paymentStatus = paymentData?['paymentStatus'];

        print('支付状态查询结果: $result'); // 调试日志
        print('支付状态: $paymentStatus'); // 调试日志

        if (paymentStatus == 'SUCCESS') {
          // 支付成功
          _stopStatusCheck();
          setState(() {
            _isPaymentProcessing = false;
            _showResult = true;
            _paymentSuccess = true;
          });

          // 刷新用户信息和购买历史
          ref.read(authProvider.notifier).refreshUser();
          ref.read(purchaseHistoryProvider.notifier).refresh();
        } else if (paymentStatus == 'FAILED' || paymentStatus == 'CANCELLED') {
          // 支付失败或取消
          _stopStatusCheck();
          setState(() {
            _isPaymentProcessing = false;
            _showResult = true;
            _paymentSuccess = false;
            _errorMessage = paymentStatus == 'FAILED' ? '支付失败' : '支付已取消';
          });
        } else {
          print('支付状态未变化，继续等待: $paymentStatus'); // 调试日志
        }
      } else {
        print('查询支付状态响应异常: $result'); // 调试日志
      }
    } catch (e) {
      // 查询失败，继续等待
      print('查询支付状态失败: $e');
    }
  }

  /// 手动查询支付状态
  Future<void> _manualCheckPaymentStatus() async {
    if (_orderId == null) return;

    setState(() {
      _isPaymentProcessing = true;
    });

    try {
      await _checkPaymentStatus();

      // 如果还没有结果，继续显示查询状态
      if (!_showResult) {
        setState(() {
          _isPaymentProcessing = false;
        });
        print('手动查询支付状态：未获得最终结果，继续等待'); // 调试日志
      }
    } catch (e) {
      print('手动查询支付状态异常: $e'); // 调试日志
      setState(() {
        _isPaymentProcessing = false;
        _errorMessage = '查询支付状态失败: $e';
      });
    }
  }



  void _simulatePayment() async {
    if (_orderId == null) {
      setState(() {
        _errorMessage = '订单ID不存在';
      });
      return;
    }

    setState(() {
      _isPaymentProcessing = true;
    });

    try {
      // 模拟支付查询延迟
      await Future.delayed(const Duration(seconds: 2));

      // 随机模拟支付成功或失败 (80%成功率)
      final success = DateTime.now().millisecond % 10 < 8;

      final authState = ref.read(authProvider);
      final token = authState.token;

      if (token == null) {
        setState(() {
          _isPaymentProcessing = false;
          _errorMessage = '用户未登录';
        });
        return;
      }

      final service = PaymentPackageService();
      final result = await service.simulatePayment(
        token: token,
        orderId: _orderId!,
        success: success,
      );

      if (result != null) {
        setState(() {
          _isPaymentProcessing = false;
          _showResult = true;
          _paymentSuccess = result['success'] ?? false;
        });

        // 如果支付成功，刷新用户信息和购买历史
        if (_paymentSuccess) {
          ref.read(authProvider.notifier).refreshUser();
          ref.read(purchaseHistoryProvider.notifier).refresh();
        }
      } else {
        setState(() {
          _isPaymentProcessing = false;
          _errorMessage = '支付处理失败';
        });
      }
    } catch (e) {
      setState(() {
        _isPaymentProcessing = false;
        _errorMessage = '支付处理失败: $e';
      });
    }
  }
}
