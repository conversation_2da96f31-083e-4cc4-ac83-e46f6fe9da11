import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../providers/order_provider.dart';
import '../models/order_model.dart';
import '../widgets/order_detail_dialog.dart';

class OrderManagementScreen extends ConsumerStatefulWidget {
  const OrderManagementScreen({super.key});

  @override
  ConsumerState<OrderManagementScreen> createState() => _OrderManagementScreenState();
}

class _OrderManagementScreenState extends ConsumerState<OrderManagementScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedStatus = '';
  String _selectedPaymentMethod = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(orderProvider.notifier).loadOrders();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final orderState = ref.watch(orderProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('订单管理'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Column(
        children: [
          // 搜索和筛选区域
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: TextField(
                        controller: _searchController,
                        decoration: const InputDecoration(
                          labelText: '搜索订单号或用户ID',
                          prefixIcon: Icon(Icons.search),
                          border: OutlineInputBorder(),
                        ),
                        onSubmitted: (value) {
                          ref.read(orderProvider.notifier).searchOrders(value);
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedStatus.isEmpty ? null : _selectedStatus,
                        decoration: const InputDecoration(
                          labelText: '订单状态',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: '', child: Text('全部状态')),
                          DropdownMenuItem(value: 'PENDING', child: Text('待支付')),
                          DropdownMenuItem(value: 'COMPLETED', child: Text('已完成')),
                          DropdownMenuItem(value: 'CANCELLED', child: Text('已取消')),
                          DropdownMenuItem(value: 'EXPIRED', child: Text('已过期')),
                          DropdownMenuItem(value: 'REFUNDED', child: Text('已退款')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedStatus = value ?? '';
                          });
                          ref.read(orderProvider.notifier).filterByStatus(_selectedStatus);
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedPaymentMethod.isEmpty ? null : _selectedPaymentMethod,
                        decoration: const InputDecoration(
                          labelText: '支付方式',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: '', child: Text('全部方式')),
                          DropdownMenuItem(value: 'WECHAT', child: Text('微信支付')),
                          DropdownMenuItem(value: 'ALIPAY', child: Text('支付宝')),
                          DropdownMenuItem(value: 'BANK', child: Text('银行卡')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedPaymentMethod = value ?? '';
                          });
                          ref.read(orderProvider.notifier).filterByPaymentMethod(_selectedPaymentMethod);
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    ElevatedButton.icon(
                      onPressed: () {
                        ref.read(orderProvider.notifier).refreshOrders();
                      },
                      icon: const Icon(Icons.refresh),
                      label: const Text('刷新'),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const Divider(),
          // 订单列表
          Expanded(
            child: orderState.isLoading
                ? const Center(child: CircularProgressIndicator())
                : orderState.error != null
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              '错误: ${orderState.error}',
                              style: const TextStyle(color: Colors.red),
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: () {
                                ref.read(orderProvider.notifier).refreshOrders();
                              },
                              child: const Text('重试'),
                            ),
                          ],
                        ),
                      )
                    : orderState.orders.isEmpty
                        ? const Center(child: Text('暂无订单数据'))
                        : Column(
                            children: [
                              Expanded(
                                child: SingleChildScrollView(
                                  scrollDirection: Axis.horizontal,
                                  child: DataTable(
                                    columns: const [
                                      DataColumn(label: Text('订单号')),
                                      DataColumn(label: Text('用户ID')),
                                      DataColumn(label: Text('套餐名称')),
                                      DataColumn(label: Text('金额')),
                                      DataColumn(label: Text('支付方式')),
                                      DataColumn(label: Text('状态')),
                                      DataColumn(label: Text('创建时间')),
                                      DataColumn(label: Text('操作')),
                                    ],
                                    rows: orderState.orders.map((order) {
                                      return DataRow(
                                        cells: [
                                          DataCell(
                                            Text(
                                              order.orderNo,
                                              style: const TextStyle(fontFamily: 'monospace'),
                                            ),
                                          ),
                                          DataCell(
                                            Text(
                                              order.userId.length > 8 
                                                  ? '${order.userId.substring(0, 8)}...'
                                                  : order.userId,
                                              style: const TextStyle(fontFamily: 'monospace'),
                                            ),
                                          ),
                                          DataCell(Text(order.packageName)),
                                          DataCell(Text('¥${(order.orderAmount / 100).toStringAsFixed(2)}')),
                                          DataCell(Text(order.paymentMethodText)),
                                          DataCell(
                                            Container(
                                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                              decoration: BoxDecoration(
                                                color: _getStatusColor(order.status),
                                                borderRadius: BorderRadius.circular(4),
                                              ),
                                              child: Text(
                                                order.statusText,
                                                style: const TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 12,
                                                ),
                                              ),
                                            ),
                                          ),
                                          DataCell(
                                            Text(DateFormat('yyyy-MM-dd HH:mm').format(order.createTime)),
                                          ),
                                          DataCell(
                                            Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                IconButton(
                                                  icon: const Icon(Icons.visibility),
                                                  onPressed: () => _showOrderDetail(order),
                                                  tooltip: '查看详情',
                                                ),
                                                if (order.status == 'PENDING')
                                                  PopupMenuButton<String>(
                                                    icon: const Icon(Icons.more_vert),
                                                    onSelected: (value) => _updateOrderStatus(order.id, value),
                                                    itemBuilder: (context) => [
                                                      const PopupMenuItem(
                                                        value: 'CANCELLED',
                                                        child: Text('取消订单'),
                                                      ),
                                                      const PopupMenuItem(
                                                        value: 'EXPIRED',
                                                        child: Text('标记过期'),
                                                      ),
                                                    ],
                                                  ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      );
                                    }).toList(),
                                  ),
                                ),
                              ),
                              // 分页控件
                              if (orderState.totalPages > 1)
                                Container(
                                  padding: const EdgeInsets.all(16),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      IconButton(
                                        onPressed: orderState.currentPage > 1
                                            ? () => ref.read(orderProvider.notifier).loadPage(orderState.currentPage - 1)
                                            : null,
                                        icon: const Icon(Icons.chevron_left),
                                      ),
                                      Text('${orderState.currentPage} / ${orderState.totalPages}'),
                                      IconButton(
                                        onPressed: orderState.currentPage < orderState.totalPages
                                            ? () => ref.read(orderProvider.notifier).loadPage(orderState.currentPage + 1)
                                            : null,
                                        icon: const Icon(Icons.chevron_right),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'PENDING':
        return Colors.orange;
      case 'COMPLETED':
        return Colors.green;
      case 'CANCELLED':
        return Colors.red;
      case 'EXPIRED':
        return Colors.grey;
      case 'REFUNDED':
        return Colors.purple;
      default:
        return Colors.blue;
    }
  }

  void _showOrderDetail(OrderModel order) {
    showDialog(
      context: context,
      builder: (context) => OrderDetailDialog(order: order),
    );
  }

  void _updateOrderStatus(String orderId, String newStatus) async {
    final success = await ref.read(orderProvider.notifier).updateOrderStatus(orderId, newStatus);
    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('订单状态更新成功')),
      );
    }
  }
}
