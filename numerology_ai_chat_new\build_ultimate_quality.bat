@echo off
echo Ultimate Quality Icon Build Script
echo ========================================
echo Generating high-quality icons and building app
echo ========================================
echo.

echo Step 1: Generate high-quality icons...
python scripts\generate_ultimate_icon.py
if %errorlevel% neq 0 (
    echo ERROR: Icon generation failed!
    echo TIP: Make sure Python and PIL are installed
    echo Install command: pip install Pillow
    pause
    exit /b 1
)

echo.
echo Step 2: Clean build cache...
flutter clean

echo.
echo Step 3: Get dependencies...
flutter pub get

echo.
echo Step 4: Verify icon quality...
python -c "import os; ico_path='windows/runner/resources/app_icon.ico'; size=os.path.getsize(ico_path) if os.path.exists(ico_path) else 0; print('Icon file size:', size, 'bytes'); print('Quality check:', 'PASS' if size > 10000 else 'LOW')"

echo.
echo Step 5: Build application (Release mode)...
flutter build windows --release

if %errorlevel% equ 0 (
    echo.
    echo BUILD COMPLETED!
    echo ========================================
    echo Executable location:
    echo    build\windows\x64\runner\Release\numerology_ai_chat.exe
    echo.
    echo Icon optimization:
    echo    - Using high-quality 256x256 icon
    echo    - Contains 8 different sizes
    echo    - Supports 4K displays and high DPI
    echo    - Clear display on all Windows versions
    echo.
    echo If icon still shows as default:
    echo    1. Restart Windows Explorer
    echo    2. Clear Windows icon cache
    echo    3. Make sure exe file is newly built
    echo ========================================
) else (
    echo.
    echo BUILD FAILED!
    echo Please check error messages above
)

echo.
pause
