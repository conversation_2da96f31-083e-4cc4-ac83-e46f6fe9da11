import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/app_constants.dart';
import '../errors/app_error.dart';

/// 存储服务接口
abstract class StorageService {
  Future<void> init();
  Future<ErrorResult<T?>> get<T>(String key, {String? boxName});
  Future<ErrorResult<void>> set<T>(String key, T value, {String? boxName});
  Future<ErrorResult<void>> delete(String key, {String? boxName});
  Future<ErrorResult<void>> clear({String? boxName});
  Future<ErrorResult<List<String>>> getKeys({String? boxName});
  Future<void> close();
}

/// Hive存储服务实现
class HiveStorageService implements StorageService {
  static final Map<String, Box> _boxes = {};

  @override
  Future<void> init() async {
    try {
      await Hive.initFlutter();
      
      // 注册适配器（如果需要）
      // Hive.registerAdapter(UserModelAdapter());
      
      // 打开所有需要的盒子
      await _openBox(HiveBoxes.settings);
      await _openBox(HiveBoxes.conversations);
      await _openBox(HiveBoxes.userProfiles);
      await _openBox(HiveBoxes.cache);
    } catch (e) {
      throw AppError.storage(
        message: '存储服务初始化失败',
        operation: 'init',
      );
    }
  }

  Future<void> _openBox(String boxName) async {
    if (!_boxes.containsKey(boxName)) {
      _boxes[boxName] = await Hive.openBox(boxName);
    }
  }

  Box _getBox(String? boxName) {
    final name = boxName ?? HiveBoxes.settings;
    final box = _boxes[name];
    if (box == null) {
      throw AppError.storage(
        message: '存储盒子未初始化: $name',
        operation: 'getBox',
      );
    }
    return box;
  }

  @override
  Future<ErrorResult<T?>> get<T>(String key, {String? boxName}) async {
    try {
      final box = _getBox(boxName);
      final value = box.get(key) as T?;
      return ErrorResult.success(value);
    } catch (e) {
      return ErrorResult.failure(
        AppError.storage(
          message: '读取数据失败: $e',
          operation: 'get',
        ),
      );
    }
  }

  @override
  Future<ErrorResult<void>> set<T>(String key, T value, {String? boxName}) async {
    try {
      final box = _getBox(boxName);
      await box.put(key, value);
      return const ErrorResult.success(null);
    } catch (e) {
      return ErrorResult.failure(
        AppError.storage(
          message: '保存数据失败: $e',
          operation: 'set',
        ),
      );
    }
  }

  @override
  Future<ErrorResult<void>> delete(String key, {String? boxName}) async {
    try {
      final box = _getBox(boxName);
      await box.delete(key);
      return const ErrorResult.success(null);
    } catch (e) {
      return ErrorResult.failure(
        AppError.storage(
          message: '删除数据失败: $e',
          operation: 'delete',
        ),
      );
    }
  }

  @override
  Future<ErrorResult<void>> clear({String? boxName}) async {
    try {
      final box = _getBox(boxName);
      await box.clear();
      return const ErrorResult.success(null);
    } catch (e) {
      return ErrorResult.failure(
        AppError.storage(
          message: '清空数据失败: $e',
          operation: 'clear',
        ),
      );
    }
  }

  @override
  Future<ErrorResult<List<String>>> getKeys({String? boxName}) async {
    try {
      final box = _getBox(boxName);
      final keys = box.keys.cast<String>().toList();
      return ErrorResult.success(keys);
    } catch (e) {
      return ErrorResult.failure(
        AppError.storage(
          message: '获取键列表失败: $e',
          operation: 'getKeys',
        ),
      );
    }
  }

  @override
  Future<void> close() async {
    try {
      for (final box in _boxes.values) {
        await box.close();
      }
      _boxes.clear();
    } catch (e) {
      throw AppError.storage(
        message: '关闭存储服务失败: $e',
        operation: 'close',
      );
    }
  }
}

/// 存储服务提供者
final storageServiceProvider = Provider<StorageService>((ref) {
  return HiveStorageService();
});

/// 存储服务初始化提供者
final storageInitProvider = FutureProvider<void>((ref) async {
  final storageService = ref.read(storageServiceProvider);
  await storageService.init();
});

/// 存储辅助类
class StorageHelper {
  final StorageService _storageService;

  StorageHelper(this._storageService);

  /// 保存用户设置
  Future<ErrorResult<void>> saveUserSettings(Map<String, dynamic> settings) {
    return _storageService.set(
      AppConstants.settingsKey,
      settings,
      boxName: HiveBoxes.settings,
    );
  }

  /// 获取用户设置
  Future<ErrorResult<Map<String, dynamic>?>> getUserSettings() {
    return _storageService.get<Map<String, dynamic>>(
      AppConstants.settingsKey,
      boxName: HiveBoxes.settings,
    );
  }

  /// 保存认证令牌
  Future<ErrorResult<void>> saveAuthToken(String token) {
    return _storageService.set(
      AppConstants.authTokenKey,
      token,
      boxName: HiveBoxes.settings,
    );
  }

  /// 获取认证令牌
  Future<ErrorResult<String?>> getAuthToken() {
    return _storageService.get<String>(
      AppConstants.authTokenKey,
      boxName: HiveBoxes.settings,
    );
  }

  /// 清除认证数据
  Future<ErrorResult<void>> clearAuthData() async {
    await _storageService.delete(
      AppConstants.authTokenKey,
      boxName: HiveBoxes.settings,
    );
    return _storageService.delete(
      AppConstants.userDataKey,
      boxName: HiveBoxes.settings,
    );
  }
}

/// 存储辅助类提供者
final storageHelperProvider = Provider<StorageHelper>((ref) {
  final storageService = ref.read(storageServiceProvider);
  return StorageHelper(storageService);
});
