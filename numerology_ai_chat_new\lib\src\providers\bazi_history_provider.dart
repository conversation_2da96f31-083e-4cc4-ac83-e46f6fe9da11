import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 八字历史记录刷新通知提供者
class BaziHistoryNotifier extends ChangeNotifier {
  int _refreshCounter = 0;
  
  /// 获取刷新计数器
  int get refreshCounter => _refreshCounter;
  
  /// 通知历史记录需要刷新
  void notifyHistoryChanged() {
    _refreshCounter++;
    notifyListeners();
  }
}

/// 八字历史记录提供者
final baziHistoryProvider = ChangeNotifierProvider<BaziHistoryNotifier>((ref) {
  return BaziHistoryNotifier();
});
