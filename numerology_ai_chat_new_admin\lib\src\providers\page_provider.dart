import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:numerology_ai_chat_admin/src/models/page_model.dart';
import 'package:numerology_ai_chat_admin/src/services/admin_api_service.dart';
import 'package:numerology_ai_chat_admin/src/providers/auth_provider.dart';

class PageState {
  final List<PageModel>? pages;
  final bool isLoading;
  final String? error;
  final int total;
  final int currentPage;
  final int pageSize;

  PageState({
    this.pages,
    this.isLoading = false,
    this.error,
    this.total = 0,
    this.currentPage = 1,
    this.pageSize = 20,
  });

  PageState copyWith({
    List<PageModel>? pages,
    bool? isLoading,
    String? error,
    int? total,
    int? currentPage,
    int? pageSize,
  }) {
    return PageState(
      pages: pages ?? this.pages,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      total: total ?? this.total,
      currentPage: currentPage ?? this.currentPage,
      pageSize: pageSize ?? this.pageSize,
    );
  }
}

class PageNotifier extends StateNotifier<PageState> {
  final AdminApiService _apiService;
  final Ref _ref;

  PageNotifier(this._apiService, this._ref) : super(PageState());

  Future<void> fetchPages({
    String? pageType,
    bool? isActive,
    int page = 1,
    int pageSize = 20,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final authState = _ref.read(authProvider);
      if (authState.token == null) {
        throw Exception('未登录');
      }

      final response = await _apiService.getPageList(
        authState.token!,
        pageType: pageType,
        isActive: isActive,
        page: page,
        pageSize: pageSize,
      );

      if (response['code'] == 0) {
        final data = response['data'];
        final List<dynamic> pageList = data['pages'] ?? [];
        final pages = pageList.map((json) => PageModel.fromJson(json)).toList();

        state = state.copyWith(
          pages: pages,
          isLoading: false,
          total: data['total'] ?? 0,
          currentPage: page,
          pageSize: pageSize,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response['message'] ?? '获取页面列表失败',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<bool> createPage(CreatePageRequest request) async {
    try {
      final authState = _ref.read(authProvider);
      if (authState.token == null) {
        throw Exception('未登录');
      }

      final response = await _apiService.createPage(
        authState.token!,
        request.toJson(),
      );

      if (response['code'] == 0) {
        // 刷新页面列表
        await fetchPages(
          page: state.currentPage,
          pageSize: state.pageSize,
        );
        return true;
      } else {
        state = state.copyWith(error: response['message'] ?? '创建页面失败');
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  Future<bool> updatePage(String pageId, UpdatePageRequest request) async {
    try {
      final authState = _ref.read(authProvider);
      if (authState.token == null) {
        throw Exception('未登录');
      }

      final response = await _apiService.updatePage(
        authState.token!,
        pageId,
        request.toJson(),
      );

      if (response['code'] == 0) {
        // 刷新页面列表
        await fetchPages(
          page: state.currentPage,
          pageSize: state.pageSize,
        );
        return true;
      } else {
        state = state.copyWith(error: response['message'] ?? '更新页面失败');
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  Future<bool> deletePage(String pageId) async {
    try {
      final authState = _ref.read(authProvider);
      if (authState.token == null) {
        throw Exception('未登录');
      }

      final response = await _apiService.deletePage(authState.token!, pageId);

      if (response['code'] == 0) {
        // 刷新页面列表
        await fetchPages(
          page: state.currentPage,
          pageSize: state.pageSize,
        );
        return true;
      } else {
        state = state.copyWith(error: response['message'] ?? '删除页面失败');
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

final pageProvider = StateNotifierProvider<PageNotifier, PageState>((ref) {
  return PageNotifier(AdminApiService(), ref);
});
