import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

import '../models/conversation_model.dart';
import '../models/chat_message.dart';
import '../models/agent_model.dart';
import '../models/ai_model.dart';
import '../models/bazi_model.dart';
import '../core/storage/storage_service.dart';
import '../core/constants/app_constants.dart';

/// 聊天记录存储服务
class ChatStorageService {
  static const String _conversationsIndexKey = 'conversations_index';
  static const String _conversationsDir = 'conversations';
  static const String _indexFileName = 'conversations_index.json';

  final StorageService _storageService;
  bool _isInitialized = false;
  String? _storagePath;

  ChatStorageService(this._storageService);

  /// 确保存储服务已初始化
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      try {
        // 验证存储路径
        await _validateStoragePath();
        _isInitialized = true;
        print('聊天存储服务初始化成功，存储路径: $_storagePath');
      } catch (e) {
        print('存储服务初始化失败: $e');
        throw Exception('存储服务初始化失败: $e');
      }
    }
  }

  /// 验证存储路径可用性
  Future<void> _validateStoragePath() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      _storagePath = appDir.path;

      // 测试写入权限
      final testFile = File(path.join(appDir.path, 'storage_test.tmp'));
      await testFile.writeAsString('test');
      await testFile.delete();

      print('存储路径验证成功: $_storagePath');
    } catch (e) {
      print('存储路径验证失败: $e');
      rethrow;
    }
  }

  /// 获取对话存储目录
  Future<Directory> _getConversationsDir() async {
    final appDir = await getApplicationDocumentsDirectory();
    final conversationsDir = Directory(path.join(appDir.path, _conversationsDir));
    if (!await conversationsDir.exists()) {
      await conversationsDir.create(recursive: true);
    }
    return conversationsDir;
  }

  /// 获取对话文件路径
  Future<File> _getConversationFile(String conversationId) async {
    final dir = await _getConversationsDir();
    return File(path.join(dir.path, '$conversationId.json'));
  }

  /// 保存对话索引
  Future<void> _saveConversationIndex(List<ConversationSummary> summaries) async {
    try {
      final indexFile = await _getConversationIndexFile();
      final indexData = summaries.map((s) => s.toJson()).toList();
      await indexFile.writeAsString(json.encode(indexData));
      print('对话索引保存成功，共 ${summaries.length} 个对话');
    } catch (e) {
      print('保存对话索引失败: $e');
      throw Exception('保存对话索引失败: $e');
    }
  }

  /// 获取对话索引文件
  Future<File> _getConversationIndexFile() async {
    final dir = await _getConversationsDir();
    return File(path.join(dir.path, _indexFileName));
  }

  /// 获取对话索引
  Future<List<ConversationSummary>> _getConversationIndex() async {
    await _ensureInitialized();

    try {
      final indexFile = await _getConversationIndexFile();

      if (!await indexFile.exists()) {
        return [];
      }

      final content = await indexFile.readAsString();
      if (content.isEmpty) {
        return [];
      }

      final List<dynamic> jsonList = json.decode(content);
      return jsonList.map((item) => ConversationSummary.fromJson(item as Map<String, dynamic>)).toList();
    } catch (e) {
      print('读取对话索引失败: $e');
      return [];
    }
  }

  /// 保存对话
  Future<void> saveConversation(ConversationModel conversation) async {
    try {
      // 1. 保存消息到文件
      await _saveConversationMessages(conversation);
      
      // 2. 更新对话索引
      await _updateConversationIndex(conversation);
      
      print('对话保存成功: ${conversation.id}');
    } catch (e) {
      print('保存对话失败: $e');
      rethrow;
    }
  }

  /// 保存对话消息到文件
  Future<void> _saveConversationMessages(ConversationModel conversation) async {
    final file = await _getConversationFile(conversation.id);

    // 限制消息数量
    final messages = conversation.messages.length > AppConstants.maxMessagesPerConversation
        ? conversation.messages.sublist(conversation.messages.length - AppConstants.maxMessagesPerConversation)
        : conversation.messages;

    // 使用displayTitle确保标题一致性
    final conversationData = {
      'id': conversation.id,
      'title': conversation.displayTitle, // 使用动态标题
      'created_at': conversation.createdAt.toIso8601String(),
      'updated_at': conversation.updatedAt.toIso8601String(),
      'selected_agent': conversation.selectedAgent?.toJson(),
      'selected_model': conversation.selectedModel?.toJson(),
      'bazi_data': conversation.baziData?.toJson(),
      'messages': messages.map((m) => _messageToJson(m)).toList(),
    };

    await file.writeAsString(json.encode(conversationData));
    print('对话文件保存成功: ${conversation.id} - ${conversation.displayTitle}');
  }

  /// 消息转JSON，处理图片附件
  Map<String, dynamic> _messageToJson(ChatMessage message) {
    final messageJson = message.toJson();
    
    // 处理图片附件，确保本地路径有效
    if (message.images != null && message.images!.isNotEmpty) {
      final validImages = <Map<String, dynamic>>[];
      
      for (final image in message.images!) {
        final imageJson = image.toJson();
        
        // 检查本地文件是否存在
        if (image.localPath != null) {
          final file = File(image.localPath!);
          if (file.existsSync()) {
            validImages.add(imageJson);
          } else if (image.base64Data != null) {
            // 如果本地文件不存在但有base64数据，尝试恢复文件
            try {
              _restoreImageFromBase64(image);
              validImages.add(imageJson);
            } catch (e) {
              print('恢复图片文件失败: $e');
              // 只保留base64数据
              imageJson['local_path'] = null;
              validImages.add(imageJson);
            }
          }
        } else if (image.base64Data != null) {
          // 只有base64数据的情况
          validImages.add(imageJson);
        }
      }
      
      messageJson['images'] = validImages;
    }
    
    return messageJson;
  }

  /// 从base64数据恢复图片文件
  Future<void> _restoreImageFromBase64(ImageAttachment image) async {
    if (image.base64Data == null || image.localPath == null) return;
    
    try {
      final bytes = base64Decode(image.base64Data!);
      final file = File(image.localPath!);
      
      // 确保目录存在
      await file.parent.create(recursive: true);
      await file.writeAsBytes(bytes);
    } catch (e) {
      print('从base64恢复图片失败: $e');
      rethrow;
    }
  }

  /// 更新对话索引
  Future<void> _updateConversationIndex(ConversationModel conversation) async {
    final summaries = await _getConversationIndex();

    // 查找现有记录
    final existingIndex = summaries.indexWhere((s) => s.id == conversation.id);

    // 使用displayTitle确保标题一致性
    final summary = ConversationSummary(
      id: conversation.id,
      title: conversation.displayTitle, // 使用动态标题
      createdAt: conversation.createdAt,
      updatedAt: conversation.updatedAt,
      messageCount: conversation.messages.length,
      hasImages: conversation.messages.any((m) => m.hasImages),
      selectedAgentId: conversation.selectedAgent?.id,
      selectedModelId: conversation.selectedModel?.id,
    );

    print('更新对话索引标题: ${conversation.id} -> ${summary.title}');

    if (existingIndex >= 0) {
      summaries[existingIndex] = summary;
    } else {
      summaries.add(summary);
    }

    // 按更新时间倒序排列
    summaries.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

    // 限制对话数量
    if (summaries.length > AppConstants.maxConversations) {
      final toRemove = summaries.sublist(AppConstants.maxConversations);
      for (final summary in toRemove) {
        await _deleteConversationFile(summary.id);
      }
      summaries.removeRange(AppConstants.maxConversations, summaries.length);
    }

    await _saveConversationIndex(summaries);
    print('对话索引更新成功: ${conversation.id} - ${summary.title}');
  }

  /// 删除对话文件
  Future<void> _deleteConversationFile(String conversationId) async {
    try {
      final file = await _getConversationFile(conversationId);
      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      print('删除对话文件失败: $e');
    }
  }

  /// 加载所有对话摘要
  Future<List<ConversationSummary>> loadConversationSummaries() async {
    try {
      // 强制重建索引以确保标题正确
      print('开始重建索引以更新标题...');
      final allSummaries = await _rebuildIndexFromFiles();

      // 保存更新后的索引
      await _saveConversationIndex(allSummaries);
      print('索引重建完成，共 ${allSummaries.length} 个对话');

      return allSummaries;
    } catch (e) {
      print('加载对话索引失败: $e');
      return [];
    }
  }

  /// 从文件系统重建对话索引
  Future<List<ConversationSummary>> _rebuildIndexFromFiles() async {
    try {
      final conversationsDir = await _getConversationsDir();
      if (!await conversationsDir.exists()) {
        return [];
      }

      final summaries = <ConversationSummary>[];
      final files = await conversationsDir.list().toList();

      for (final file in files) {
        if (file is File && file.path.endsWith('.json') && !file.path.endsWith(_indexFileName)) {
          try {
            final content = await file.readAsString();
            final data = json.decode(content) as Map<String, dynamic>;

            // 生成正确的显示标题
            String displayTitle = data['title'] as String;
            bool titleUpdated = false;

            print('处理对话文件: ${data['id']}, 原标题: "$displayTitle"');

            // 如果标题是"新对话"，尝试从消息中生成标题
            if (displayTitle == '新对话') {
              final messages = data['messages'] as List;
              print('检测到"新对话"标题，消息数: ${messages.length}');

              if (messages.isNotEmpty) {
                // 查找第一条用户消息
                for (final messageData in messages) {
                  if (messageData is Map<String, dynamic> &&
                      messageData['sender'] == 'user' &&
                      messageData['content'] != null) {
                    final content = (messageData['content'] as String).trim();
                    if (content.isNotEmpty) {
                      displayTitle = content.length > 20 ? '${content.substring(0, 20)}...' : content;
                      titleUpdated = true;
                      print('生成新标题: "$displayTitle"');
                      break;
                    }
                  }
                }
              }
            }

            // 如果标题被更新，同步更新文件
            if (titleUpdated) {
              try {
                data['title'] = displayTitle;
                await file.writeAsString(json.encode(data));
                print('同步更新文件标题: ${data['id']} -> $displayTitle');
              } catch (e) {
                print('更新文件标题失败: $e');
              }
            }

            final summary = ConversationSummary(
              id: data['id'] as String,
              title: displayTitle, // 使用生成的显示标题
              createdAt: DateTime.parse(data['created_at'] as String),
              updatedAt: DateTime.parse(data['updated_at'] as String),
              messageCount: (data['messages'] as List).length,
              hasImages: _hasImagesInMessages(data['messages'] as List),
              selectedAgentId: data['selected_agent']?['id'] as String?,
              selectedModelId: data['selected_model']?['id'] as String?,
            );

            summaries.add(summary);
          } catch (e) {
            print('解析对话文件失败: ${file.path}, 错误: $e');
          }
        }
      }

      // 按更新时间倒序排列
      summaries.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

      print('从文件系统重建索引完成，共找到 ${summaries.length} 个对话');
      return summaries;
    } catch (e) {
      print('重建索引失败: $e');
      return [];
    }
  }

  /// 检查消息列表中是否包含图片
  bool _hasImagesInMessages(List<dynamic> messages) {
    try {
      for (final message in messages) {
        if (message is Map<String, dynamic> && message['images'] != null) {
          final images = message['images'] as List<dynamic>?;
          if (images != null && images.isNotEmpty) {
            return true;
          }
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// 加载完整对话
  Future<ConversationModel?> loadConversation(String conversationId) async {
    try {
      final file = await _getConversationFile(conversationId);
      
      if (!await file.exists()) {
        print('对话文件不存在: $conversationId');
        return null;
      }
      
      final content = await file.readAsString();
      final data = json.decode(content) as Map<String, dynamic>;
      
      return _conversationFromJson(data);
    } catch (e) {
      print('加载对话失败: $e');
      return null;
    }
  }

  /// 从JSON创建对话模型
  ConversationModel _conversationFromJson(Map<String, dynamic> data) {
    final messages = (data['messages'] as List<dynamic>)
        .map((m) => _messageFromJson(m as Map<String, dynamic>))
        .toList();

    final conversation = ConversationModel(
      id: data['id'] as String,
      title: data['title'] as String,
      createdAt: DateTime.parse(data['created_at'] as String),
      updatedAt: DateTime.parse(data['updated_at'] as String),
      messages: messages,
      selectedAgent: data['selected_agent'] != null
          ? AgentModel.fromJson(data['selected_agent'] as Map<String, dynamic>)
          : null,
      selectedModel: data['selected_model'] != null
          ? AIModel.fromJson(data['selected_model'] as Map<String, dynamic>)
          : null,
      baziData: data['bazi_data'] != null
          ? BaziResultModel.fromJson(data['bazi_data'] as Map<String, dynamic>)
          : null,
    );

    // 如果存储的标题是"新对话"但有用户消息，更新为动态标题
    if (conversation.title == '新对话' && conversation.messages.isNotEmpty) {
      final firstUserMessage = conversation.messages
          .where((m) => m.isUser && m.content.trim().isNotEmpty)
          .firstOrNull;

      if (firstUserMessage != null) {
        final content = firstUserMessage.content.trim();
        final newTitle = content.length > 20 ? '${content.substring(0, 20)}...' : content;

        // 异步更新文件中的标题
        Future.microtask(() async {
          try {
            final updatedConversation = conversation.copyWith(title: newTitle);
            await _saveConversationMessages(updatedConversation);
            print('自动更新对话标题: ${conversation.id} -> $newTitle');
          } catch (e) {
            print('更新对话标题失败: $e');
          }
        });

        return conversation.copyWith(title: newTitle);
      }
    }

    return conversation;
  }

  /// 从JSON创建消息模型
  ChatMessage _messageFromJson(Map<String, dynamic> data) {
    List<ImageAttachment>? images;
    if (data['images'] != null) {
      images = (data['images'] as List<dynamic>)
          .map((i) => ImageAttachment.fromJson(i as Map<String, dynamic>))
          .toList();
    }

    // 手动构建ChatMessage，因为需要处理图片附件
    return ChatMessage(
      id: data['id'] as String,
      content: data['content'] as String,
      sender: MessageSender.values.firstWhere((e) => e.name == data['sender']),
      status: MessageStatus.values.firstWhere((e) => e.name == data['status']),
      timestamp: DateTime.parse(data['timestamp'] as String),
      agentId: data['agent_id'] as String?,
      baziData: data['bazi_data'] as String?,
      systemMessageType: data['system_message_type'] != null
          ? SystemMessageType.values.firstWhere((e) => e.name == data['system_message_type'])
          : null,
      metadata: data['metadata'] as Map<String, dynamic>?,
      messageType: data['message_type'] != null
          ? MessageType.values.firstWhere((e) => e.name == data['message_type'])
          : MessageType.text,
      images: images,
      laymanVersion: data['layman_version'] as String?,
      isLaymanDisplay: data['is_layman_display'] as bool? ?? false,
    );
  }

  /// 删除对话
  Future<void> deleteConversation(String conversationId) async {
    try {
      // 1. 删除对话文件
      await _deleteConversationFile(conversationId);
      
      // 2. 从索引中移除
      final summaries = await _getConversationIndex();
      summaries.removeWhere((s) => s.id == conversationId);
      await _saveConversationIndex(summaries);
      
      print('对话删除成功: $conversationId');
    } catch (e) {
      print('删除对话失败: $e');
      rethrow;
    }
  }

  /// 清理过期对话
  Future<void> cleanupOldConversations({int? maxAge}) async {
    try {
      final summaries = await _getConversationIndex();
      final cutoffDate = DateTime.now().subtract(Duration(days: maxAge ?? AppConstants.conversationCleanupDays));

      final toRemove = summaries.where((s) => s.updatedAt.isBefore(cutoffDate)).toList();

      for (final summary in toRemove) {
        await deleteConversation(summary.id);
      }

      print('清理了 ${toRemove.length} 个过期对话');
    } catch (e) {
      print('清理过期对话失败: $e');
    }
  }

  /// 调试方法：获取存储状态信息
  Future<Map<String, dynamic>> getStorageDebugInfo() async {
    try {
      await _ensureInitialized();

      final conversationsDir = await _getConversationsDir();
      final indexFile = await _getConversationIndexFile();
      final summaries = await _getConversationIndex();

      // 检查对话文件
      final conversationFiles = <String>[];
      final fileDetails = <Map<String, dynamic>>[];

      if (await conversationsDir.exists()) {
        final files = await conversationsDir.list().toList();
        for (final file in files) {
          if (file is File && file.path.endsWith('.json') && !file.path.endsWith(_indexFileName)) {
            conversationFiles.add(file.path);

            // 读取文件详情
            try {
              final content = await file.readAsString();
              final data = json.decode(content) as Map<String, dynamic>;
              fileDetails.add({
                'file_path': file.path,
                'id': data['id'],
                'title': data['title'],
                'message_count': (data['messages'] as List).length,
                'updated_at': data['updated_at'],
              });
            } catch (e) {
              fileDetails.add({
                'file_path': file.path,
                'error': e.toString(),
              });
            }
          }
        }
      }

      return {
        'storage_path': _storagePath,
        'conversations_dir_exists': await conversationsDir.exists(),
        'conversations_dir_path': conversationsDir.path,
        'index_file_exists': await indexFile.exists(),
        'index_file_path': indexFile.path,
        'summaries_count': summaries.length,
        'conversation_files_count': conversationFiles.length,
        'conversation_files': conversationFiles,
        'file_details': fileDetails,
        'summaries': summaries.map((s) => {
          'id': s.id,
          'title': s.title,
          'message_count': s.messageCount,
          'updated_at': s.updatedAt.toIso8601String(),
        }).toList(),
      };
    } catch (e) {
      return {
        'error': e.toString(),
        'storage_path': _storagePath,
      };
    }
  }

  /// 调试方法：对比内存状态与存储状态
  Future<Map<String, dynamic>> compareMemoryWithStorage(List<ConversationModel> memoryConversations) async {
    try {
      final storageInfo = await getStorageDebugInfo();
      final summaries = await _getConversationIndex();

      final memoryInfo = memoryConversations.map((c) => {
        'id': c.id,
        'title': c.displayTitle,
        'message_count': c.messages.length,
        'updated_at': c.updatedAt.toIso8601String(),
      }).toList();

      final storageInfo2 = summaries.map((s) => {
        'id': s.id,
        'title': s.title,
        'message_count': s.messageCount,
        'updated_at': s.updatedAt.toIso8601String(),
      }).toList();

      // 检查不一致的对话
      final inconsistencies = <Map<String, dynamic>>[];
      for (final memory in memoryInfo) {
        Map<String, dynamic>? storage;
        try {
          storage = storageInfo2.firstWhere(
            (s) => s['id'] == memory['id'],
          );
        } catch (e) {
          storage = null;
        }

        if (storage == null) {
          inconsistencies.add({
            'type': 'missing_in_storage',
            'id': memory['id'],
            'memory': memory,
          });
        } else if (memory['title'] != storage['title'] ||
                   memory['message_count'] != storage['message_count']) {
          inconsistencies.add({
            'type': 'data_mismatch',
            'id': memory['id'],
            'memory': memory,
            'storage': storage,
          });
        }
      }

      return {
        'memory_conversations': memoryInfo,
        'storage_conversations': storageInfo2,
        'inconsistencies': inconsistencies,
        'storage_debug': storageInfo,
      };
    } catch (e) {
      return {
        'error': e.toString(),
      };
    }
  }

  /// 清除所有聊天记录
  Future<void> clearAllConversations() async {
    try {
      await _ensureInitialized();

      // 1. 获取对话目录
      final conversationsDir = await _getConversationsDir();

      if (await conversationsDir.exists()) {
        // 2. 删除所有对话文件
        final files = await conversationsDir.list().toList();
        for (final file in files) {
          if (file is File) {
            await file.delete();
          }
        }

        // 3. 删除整个对话目录
        await conversationsDir.delete(recursive: true);
      }

      print('所有聊天记录已清除');
    } catch (e) {
      print('清除聊天记录失败: $e');
      rethrow;
    }
  }
}

/// 对话摘要模型
class ConversationSummary {
  final String id;
  final String title;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int messageCount;
  final bool hasImages;
  final String? selectedAgentId;
  final String? selectedModelId;

  const ConversationSummary({
    required this.id,
    required this.title,
    required this.createdAt,
    required this.updatedAt,
    required this.messageCount,
    required this.hasImages,
    this.selectedAgentId,
    this.selectedModelId,
  });

  factory ConversationSummary.fromJson(Map<String, dynamic> json) {
    return ConversationSummary(
      id: json['id'] as String,
      title: json['title'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      messageCount: json['message_count'] as int,
      hasImages: json['has_images'] as bool,
      selectedAgentId: json['selected_agent_id'] as String?,
      selectedModelId: json['selected_model_id'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'message_count': messageCount,
      'has_images': hasImages,
      'selected_agent_id': selectedAgentId,
      'selected_model_id': selectedModelId,
    };
  }
}
