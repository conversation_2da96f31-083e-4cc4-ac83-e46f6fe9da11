import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_error.freezed.dart';

/// 应用错误基类
@freezed
class AppError with _$AppError {
  const factory AppError.network({
    required String message,
    String? code,
    dynamic originalError,
  }) = NetworkError;

  const factory AppError.authentication({
    required String message,
    String? code,
  }) = AuthenticationError;

  const factory AppError.validation({
    required String message,
    required String field,
  }) = ValidationError;

  const factory AppError.server({
    required String message,
    int? statusCode,
    String? code,
  }) = ServerError;

  const factory AppError.storage({
    required String message,
    String? operation,
  }) = StorageError;

  const factory AppError.ai({
    required String message,
    String? model,
    String? code,
  }) = AIError;

  const factory AppError.unknown({
    required String message,
    dynamic originalError,
  }) = UnknownError;
}

/// 错误处理扩展
extension AppErrorExtension on AppError {
  /// 获取用户友好的错误消息
  String get userMessage {
    return when(
      network: (message, code, originalError) => '网络连接失败：$message',
      authentication: (message, code) => '认证失败：$message',
      validation: (message, field) => '输入验证失败：$message',
      server: (message, statusCode, code) => '服务器错误：$message',
      storage: (message, operation) => '数据存储错误：$message',
      ai: (message, model, code) => 'AI服务错误：$message',
      unknown: (message, originalError) => '未知错误：$message',
    );
  }

  /// 获取错误代码
  String? get errorCode {
    return when(
      network: (message, code, originalError) => code,
      authentication: (message, code) => code,
      validation: (message, field) => 'VALIDATION_ERROR',
      server: (message, statusCode, code) => code ?? statusCode?.toString(),
      storage: (message, operation) => 'STORAGE_ERROR',
      ai: (message, model, code) => code,
      unknown: (message, originalError) => 'UNKNOWN_ERROR',
    );
  }

  /// 是否为致命错误
  bool get isFatal {
    return when(
      network: (message, code, originalError) => false,
      authentication: (message, code) => true,
      validation: (message, field) => false,
      server: (message, statusCode, code) => statusCode != null && statusCode >= 500,
      storage: (message, operation) => false,
      ai: (message, model, code) => false,
      unknown: (message, originalError) => true,
    );
  }

  /// 是否需要重试
  bool get shouldRetry {
    return when(
      network: (message, code, originalError) => true,
      authentication: (message, code) => false,
      validation: (message, field) => false,
      server: (message, statusCode, code) => statusCode != null && statusCode >= 500,
      storage: (message, operation) => true,
      ai: (message, model, code) => true,
      unknown: (message, originalError) => false,
    );
  }
}

/// 错误处理结果
@freezed
class ErrorResult<T> with _$ErrorResult<T> {
  const factory ErrorResult.success(T data) = Success<T>;
  const factory ErrorResult.failure(AppError error) = Failure<T>;
}

/// 错误处理结果扩展
extension ErrorResultExtension<T> on ErrorResult<T> {
  /// 是否成功
  bool get isSuccess => this is Success<T>;

  /// 是否失败
  bool get isFailure => this is Failure<T>;

  /// 获取数据（如果成功）
  T? get data => when(
        success: (data) => data,
        failure: (error) => null,
      );

  /// 获取错误（如果失败）
  AppError? get error => when(
        success: (data) => null,
        failure: (error) => error,
      );

  /// 转换数据
  ErrorResult<R> map<R>(R Function(T) mapper) {
    return when(
      success: (data) => ErrorResult.success(mapper(data)),
      failure: (error) => ErrorResult.failure(error),
    );
  }

  /// 异步转换数据
  Future<ErrorResult<R>> mapAsync<R>(Future<R> Function(T) mapper) async {
    return when(
      success: (data) async {
        try {
          final result = await mapper(data);
          return ErrorResult.success(result);
        } catch (e) {
          return ErrorResult.failure(
            AppError.unknown(message: e.toString(), originalError: e),
          );
        }
      },
      failure: (error) => ErrorResult.failure(error),
    );
  }
}
