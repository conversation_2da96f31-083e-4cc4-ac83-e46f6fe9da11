import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/bazi_model.dart';
import '../services/bazi_service.dart';
import '../services/bazi_storage_service.dart';
import '../providers/chat_provider.dart';

/// 八字选择组件
class BaziSelector extends ConsumerStatefulWidget {
  const BaziSelector({super.key});

  @override
  ConsumerState<BaziSelector> createState() => _BaziSelectorState();
}

class _BaziSelectorState extends ConsumerState<BaziSelector> {
  final BaziService _baziService = BaziService();

  // 八字记录列表
  List<BaziRecordSummary> _baziRecords = [];
  BaziRecordSummary? _selectedRecord;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadBaziRecords();
  }

  @override
  void didUpdateWidget(BaziSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当组件更新时，同步选择状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _syncSelectedRecord();
    });
  }

  /// 加载八字记录
  Future<void> _loadBaziRecords() async {
    setState(() => _isLoading = true);

    try {
      final records = await _baziService.getAllBaziRecords();
      setState(() {
        _baziRecords = records;
        _isLoading = false;
      });

      // 同步当前选择的八字记录
      _syncSelectedRecord();
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('加载八字记录失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 同步当前选择的八字记录
  void _syncSelectedRecord() {
    final chatState = ref.read(chatProvider);
    final currentBaziData = chatState.currentBaziData;

    if (currentBaziData != null && _baziRecords.isNotEmpty) {
      // 尝试根据八字数据找到对应的记录
      // 这里可以通过比较八字文本或其他唯一标识来匹配
      for (final record in _baziRecords) {
        if (currentBaziData.contains(record.fourPillars) ||
            currentBaziData.contains(record.name)) {
          setState(() => _selectedRecord = record);
          break;
        }
      }
    }
  }



  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final chatState = ref.watch(chatProvider);
    final selectedAgent = chatState.selectedAgent;

    // 只有当选择了需要八字的智能体时才显示
    if (selectedAgent == null || !selectedAgent.requiresBazi) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 标题栏
            _buildBaziTitleRow(theme),

            const SizedBox(height: 8),

            // 八字下拉选择框
            _buildBaziDropdown(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildBaziTitleRow(ThemeData theme) {
    final chatState = ref.watch(chatProvider);
    final canSwitch = chatState.canSetBazi;
    final hasStartedConversation = chatState.currentConversation != null && !chatState.currentConversation!.isEmpty;

    return Row(
      children: [
        Icon(
          Icons.calculate_outlined,
          color: theme.colorScheme.primary,
          size: 20,
        ),
        const SizedBox(width: 8),
        Text(
          '八字选择',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        // 对话进行中的提示
        if (hasStartedConversation && !canSwitch) ...[
          const SizedBox(width: 8),
          Icon(
            Icons.lock_outline,
            size: 14,
            color: theme.colorScheme.onSurface.withOpacity(0.4),
          ),
          const SizedBox(width: 4),
          Text(
            '对话进行中',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.5),
              fontSize: 11,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
        const Spacer(),
        // 打开文件位置按钮
        if (_selectedRecord?.filePath != null)
          IconButton(
            onPressed: () => _openFileLocation(_selectedRecord!),
            icon: Icon(
              Icons.folder_open,
              size: 18,
              color: theme.colorScheme.primary,
            ),
            tooltip: '打开文件位置',
          ),
        // 刷新按钮
        IconButton(
          onPressed: _loadBaziRecords,
          icon: Icon(
            Icons.refresh,
            size: 18,
            color: theme.colorScheme.primary,
          ),
          tooltip: '刷新列表',
        ),
      ],
    );
  }

  /// 构建八字下拉选择框
  Widget _buildBaziDropdown(ThemeData theme) {
    final chatState = ref.watch(chatProvider);
    final canSwitch = chatState.canSetBazi;
    final isTyping = chatState.isTyping;
    final hasStartedConversation = chatState.currentConversation != null && !chatState.currentConversation!.isEmpty;

    if (_isLoading) {
      return Container(
        height: 56,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        decoration: BoxDecoration(
          border: Border.all(color: theme.colorScheme.outline.withOpacity(0.3)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        ),
      );
    }

    return _buildBaziDropdownCore(theme, canSwitch, isTyping);
  }

  Widget _buildBaziDropdownCore(ThemeData theme, bool canSwitch, bool isTyping) {
    return Container(
      height: 56,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(
          color: canSwitch
              ? theme.colorScheme.outline.withOpacity(0.3)
              : theme.colorScheme.outline.withOpacity(0.1),
        ),
        borderRadius: BorderRadius.circular(8),
        color: canSwitch ? null : theme.colorScheme.surface.withOpacity(0.5),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: _selectedRecord?.id,
          hint: Row(
            children: [
              Icon(
                Icons.calculate_outlined,
                size: 16,
                color: canSwitch
                    ? theme.colorScheme.onSurface.withOpacity(0.6)
                    : theme.colorScheme.onSurface.withOpacity(0.3),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  canSwitch
                      ? (_baziRecords.isEmpty ? '暂无八字记录' : '请选择八字')
                      : (_selectedRecord != null
                          ? '${_selectedRecord!.name} ${_selectedRecord!.fourPillars}'
                          : (isTyping ? 'AI正在回复中...' : '对话已开始')),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: canSwitch
                        ? theme.colorScheme.onSurface.withOpacity(0.6)
                        : theme.colorScheme.onSurface.withOpacity(0.3),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          isExpanded: true,
          menuMaxHeight: 300, // 限制下拉菜单高度
          items: canSwitch && _baziRecords.isNotEmpty
              ? _baziRecords.map<DropdownMenuItem<String>>((record) {
                  return DropdownMenuItem<String>(
                    value: record.id,
                    child: _buildBaziDropdownItem(record, theme),
                  );
                }).toList()
              : [],
          onChanged: canSwitch && _baziRecords.isNotEmpty ? (recordId) {
            if (recordId != null) {
              final record = _baziRecords.firstWhere((r) => r.id == recordId);
              _selectRecord(record);
            }
          } : (value) {
            // 显示不可操作的提示
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  _baziRecords.isEmpty
                      ? '暂无八字记录，请先在排盘页面创建八字'
                      : (isTyping ? 'AI正在回复中，无法切换八字' : '对话已开始，无法切换八字')
                ),
                duration: const Duration(seconds: 2),
                behavior: SnackBarBehavior.floating,
                margin: const EdgeInsets.all(16),
              ),
            );
          },
        ),
      ),
    );
  }

  /// 构建八字下拉选择项
  Widget _buildBaziDropdownItem(BaziRecordSummary record, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            record.name,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 2),
          Text(
            '${record.gender} • ${record.fullBirthInfo}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
              fontSize: 11,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// 构建八字记录列表
  Widget _buildBaziRecordsList(ThemeData theme) {
    if (_isLoading) {
      return Container(
        height: 200,
        decoration: BoxDecoration(
          border: Border.all(color: theme.colorScheme.outline.withOpacity(0.5)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_baziRecords.isEmpty) {
      return Container(
        height: 200,
        decoration: BoxDecoration(
          border: Border.all(color: theme.colorScheme.outline.withOpacity(0.5)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.folder_open_outlined,
                size: 48,
                color: theme.colorScheme.onSurface.withOpacity(0.4),
              ),
              const SizedBox(height: 8),
              Text(
                '暂无八字记录',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '请先在排盘页面创建八字',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.5),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      height: 200,
      decoration: BoxDecoration(
        border: Border.all(color: theme.colorScheme.outline.withOpacity(0.5)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListView.builder(
        itemCount: _baziRecords.length,
        itemBuilder: (context, index) {
          final record = _baziRecords[index];
          final isSelected = _selectedRecord?.id == record.id;

          return _buildBaziRecordItem(record, isSelected, theme);
        },
      ),
    );
  }

  /// 构建八字记录项
  Widget _buildBaziRecordItem(BaziRecordSummary record, bool isSelected, ThemeData theme) {
    final chatState = ref.watch(chatProvider);
    final canSelect = chatState.canSetBazi;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: !canSelect
            ? theme.colorScheme.surface.withOpacity(0.5)
            : (isSelected
                ? theme.colorScheme.primary.withOpacity(0.1)
                : theme.colorScheme.surface),
        border: Border.all(
          color: !canSelect
              ? theme.colorScheme.outline.withOpacity(0.1)
              : (isSelected
                  ? theme.colorScheme.primary
                  : theme.colorScheme.outline.withOpacity(0.3)),
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        dense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        leading: CircleAvatar(
          radius: 16,
          backgroundColor: !canSelect
              ? theme.colorScheme.primary.withOpacity(0.05)
              : theme.colorScheme.primary.withOpacity(0.1),
          child: Text(
            record.name.isNotEmpty ? record.name[0] : '?',
            style: TextStyle(
              color: !canSelect
                  ? theme.colorScheme.primary.withOpacity(0.3)
                  : theme.colorScheme.primary,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          record.name,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
            color: !canSelect
                ? theme.colorScheme.onSurface.withOpacity(0.3)
                : null,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${record.gender} • ${record.fullBirthInfo}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: !canSelect
                    ? theme.colorScheme.onSurface.withOpacity(0.2)
                    : theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            if (record.fourPillars.isNotEmpty)
              Text(
                record.fourPillars,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: !canSelect
                      ? theme.colorScheme.primary.withOpacity(0.3)
                      : theme.colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 打开文件位置按钮
            if (record.filePath != null)
              IconButton(
                onPressed: () => _openFileLocation(record),
                icon: Icon(
                  Icons.folder_open,
                  size: 18,
                  color: theme.colorScheme.primary,
                ),
                tooltip: '打开文件位置',
              ),
            // 删除按钮
            IconButton(
              onPressed: () => _deleteRecord(record),
              icon: Icon(
                Icons.delete_outline,
                size: 18,
                color: theme.colorScheme.error,
              ),
              tooltip: '删除记录',
            ),
          ],
        ),
        onTap: () => _selectRecord(record),
      ),
    );
  }

  /// 选择八字记录
  Future<void> _selectRecord(BaziRecordSummary record) async {
    setState(() => _selectedRecord = record);

    try {
      // 读取txt文件的完整内容
      String baziText = record.fourPillars; // 默认使用四柱信息

      if (record.filePath != null) {
        try {
          baziText = await _baziService.readBaziFile(record.filePath!);
        } catch (e) {
          print('读取八字文件失败，使用四柱信息: $e');
          // 如果读取文件失败，继续使用四柱信息作为降级方案
        }
      }

      // 创建BaziResultModel用于聊天
      final baziResult = BaziResultModel(
        id: record.id,
        input: BaziInputModel(
          name: record.name,
          birthDateTime: DateTime.parse('${record.birthDate}T${record.birthTime}:00'),
          gender: record.gender == '男' ? Gender.male : Gender.female,
          calendarType: record.calendarType == '公历' ? CalendarType.solar : CalendarType.lunar,
          birthPlace: record.birthPlace,
        ),
        baziText: baziText, // 使用完整的txt文件内容
        detailedData: {
          'four_pillars': record.fourPillars,
          'file_path': record.filePath,
          'file_name': record.fileName,
        },
        createdAt: record.createdAt,
      );

      // 设置到聊天状态中
      ref.read(chatProvider.notifier).setBaziResult(baziResult);

      // 显示选择反馈
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已选择八字：${record.name} ${record.fourPillars}'),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('选择八字失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 打开文件位置
  Future<void> _openFileLocation(BaziRecordSummary record) async {
    if (record.filePath == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('文件路径不存在'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    try {
      await _baziService.openFileLocation(record.filePath!);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('打开文件位置失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 删除记录
  Future<void> _deleteRecord(BaziRecordSummary record) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除 ${record.name} 的八字记录吗？\n这将同时删除本地文件。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _baziService.deleteBaziRecord(record.id);

        // 如果删除的是当前选中的记录，清除选择
        if (_selectedRecord?.id == record.id) {
          setState(() => _selectedRecord = null);
          ref.read(chatProvider.notifier).clearBaziResult();
        }

        // 重新加载列表
        await _loadBaziRecords();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('已删除 ${record.name} 的八字记录'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('删除失败: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

}
