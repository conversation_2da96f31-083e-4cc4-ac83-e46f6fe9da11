@echo off
chcp 65001 >nul
echo ════════════════════════════════════════════
echo    高质量图标开发运行脚本
echo ════════════════════════════════════════════
echo.

echo 🔍 步骤1: 检查源图片文件...
if not exist "assets\images\logo.png" (
    echo ❌ 源图片文件不存在: assets\images\logo.png
    echo 💡 请确保将您的logo图片放在 assets\images\logo.png
    pause
    exit /b 1
)
echo ✅ 源图片文件存在

echo.
echo 🔄 步骤2: 生成高质量图标 (256x256)...
dart run flutter_launcher_icons
if %errorlevel% neq 0 (
    echo ❌ 图标生成失败！
    echo 💡 可能原因:
    echo    - flutter_launcher_icons插件未安装
    echo    - pubspec.yaml配置有误
    echo    - 源图片格式不支持
    echo.
    echo 📝 解决方案:
    echo    1. 运行: flutter pub get
    echo    2. 检查pubspec.yaml中的flutter_launcher_icons配置
    pause
    exit /b 1
)
echo ✅ 图标生成完成

echo.
echo 🔄 步骤3: 获取依赖包...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ 获取依赖包失败！
    pause
    exit /b 1
)
echo ✅ 依赖包获取完成

echo.
echo 🔄 步骤4: 运行应用（开发模式）...
echo 💡 应用将在开发模式下运行，支持热重载
echo 💡 图标已设置为高质量256x256分辨率
echo 📝 按 Ctrl+C 停止应用
echo.
flutter run -d windows
