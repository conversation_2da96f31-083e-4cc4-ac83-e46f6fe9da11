# 额度消耗历史功能执行计划

## 一、需求分析

### 1.1 功能目标
在个人主页新增额度消耗历史功能，让用户能够查看每次AI对话的算力消耗详情，包括：
- 消耗时间
- 消耗的算力数量
- 使用的智能体
- 使用的模型
- 算力档次信息
- 消耗前后的余额变化

### 1.2 现状分析
- ✅ 已有算力扣费逻辑（云函数updateUsage）
- ✅ 已有购买历史显示功能
- ✅ 已有算力计算服务（PowerCalculationService）
- ❌ 缺少消耗历史的结构化存储
- ❌ 缺少消耗历史的前端显示

## 二、技术方案

### 2.1 数据库设计
**新建集合**: `exe_usage_history`

```json
{
  "_id": "ObjectId",
  "userId": "String",              // 用户ID
  "agentId": "String",             // 智能体ID
  "agentName": "String",           // 智能体名称（冗余字段，便于查询显示）
  "modelId": "String",             // 模型ID
  "modelName": "String",           // 模型显示名称（冗余字段）
  "pricingTierId": "String",       // 算力档次ID
  "tierName": "String",            // 档次名称（冗余字段）
  "modelLevel": "String",          // 模型等级：初级/高级
  "powerCost": "Number",           // 本次消耗的算力
  "balanceBefore": "Number",       // 消耗前余额
  "balanceAfter": "Number",        // 消耗后余额
  "consumeTime": "Date",           // 消耗时间
  "description": "String",         // 消耗描述，如"标准档次 + 高级模型 = 20算力"
  "createdAt": "Date"              // 创建时间
}
```

**索引设计**:
- `userId`: 普通索引（主要查询条件）
- `consumeTime`: 普通索引（按时间排序）
- `userId + consumeTime`: 复合索引（用户查询优化）

### 2.2 架构改动点

#### 2.2.1 云函数改动
1. **新增数据库操作模块**
   - 在`cloudfunctions/exeFunction/src/utils/db.js`中新增`usageHistoryCollection`
   - 实现创建消耗记录的方法
   - 在`COLLECTIONS`常量中添加`USAGE_HISTORY: 'exe_usage_history'`

2. **修改updateUsage处理逻辑**
   - 在`cloudfunctions/exeFunction/src/handlers/user_info.js`中
   - **使用事务确保数据一致性**: 扣费和创建消耗记录必须同时成功或同时失败
   - 获取智能体和模型的详细信息用于记录（通过agentId和modelId查询）
   - 在事务中同时执行用户扣费和消耗记录创建

3. **新增查询接口**
   - 新增`getUserUsageHistory`接口，支持分页查询用户的消耗历史
   - 支持按时间倒序排列，分页加载

#### 2.2.2 Go代理服务改动
**重要**: Go程序已经在调用云函数扣费，**无需修改**，因为：
- Go程序已经通过`UpdateUsageWithPowerCalculation`传递了`agentID`和`modelID`
- 云函数接收到这些参数后会自动创建消耗记录
- Go程序的异步调用机制保持不变

#### 2.2.3 前端改动
1. **新增数据模型**
   - 创建`UsageHistoryModel`

2. **新增服务层**
   - 创建`UsageHistoryService`调用云函数接口

3. **新增状态管理**
   - 创建`UsageHistoryProvider`管理消耗历史状态

4. **修改个人中心页面**
   - 在个人中心添加消耗历史Tab或区域
   - 实现消耗历史列表显示

## 三、详细实施步骤

### 3.1 第一阶段：数据库和云函数改动

#### 步骤1: 创建数据库集合
- 使用mcp工具在数据库中创建`exe_usage_history`集合
- 创建必要的索引

#### 步骤2: 修改云函数数据库操作模块
- 在`db.js`中新增`usageHistoryCollection`对象
- 实现`create`方法用于创建消耗记录
- 实现`getUserHistory`方法用于查询用户消耗历史

#### 步骤3: 修改updateUsage逻辑
- **使用数据库事务**: 使用`db.runTransaction()`包装扣费和记录创建操作
- **获取详细信息**: 在事务开始前获取智能体和模型的详细信息
- **原子操作**: 确保用户扣费和消耗记录创建同时成功或失败
- **错误回滚**: 如果任一操作失败，自动回滚整个事务

#### 步骤4: 新增查询接口
- 在`user_info.js`中新增`getUserUsageHistory`函数
- 支持分页查询，按时间倒序排列
- 使用mcp工具部署云函数

### 3.2 第二阶段：前端功能实现

#### 步骤5: 创建前端数据模型
- 创建`UsageHistoryModel`类
- 实现JSON序列化和格式化方法

#### 步骤6: 创建服务层
- 创建`UsageHistoryService`
- 实现调用云函数获取消耗历史的方法

#### 步骤7: 创建状态管理
- 创建`UsageHistoryProvider`
- 实现加载、刷新、分页等功能

#### 步骤8: 修改个人中心页面
- 在个人中心添加消耗历史显示区域
- 实现列表展示、下拉刷新、上拉加载更多

### 3.3 第三阶段：测试和优化

#### 步骤9: 功能测试
- 测试算力扣费时是否正确创建消耗记录
- 测试前端消耗历史显示是否正常
- 测试分页加载功能

#### 步骤10: 性能优化
- 检查数据库查询性能
- 优化前端列表渲染性能

## 四、风险分析与应对

### 4.1 潜在风险

#### 风险1: 数据一致性问题
**描述**: 算力扣费成功但消耗记录创建失败
**影响**: 用户看不到某些消耗记录
**应对**: 
- 使用数据库事务确保原子性
- 添加重试机制
- 添加数据修复脚本

#### 风险2: 性能问题
**描述**: 消耗记录数量大，查询性能下降
**影响**: 个人中心加载缓慢
**应对**:
- 合理设计索引
- 实现分页查询
- 考虑数据归档策略

#### 风险3: 存储空间问题
**描述**: 消耗记录积累过多占用存储空间
**影响**: 数据库存储成本增加
**应对**:
- 设计数据清理策略（如保留最近1年的记录）
- 考虑数据压缩存储

#### 风险4: 冗余字段同步问题
**描述**: 智能体名称、模型名称等冗余字段可能与主表不一致
**影响**: 显示信息不准确
**应对**:
- 在创建记录时获取最新的名称信息
- 定期检查和修复数据一致性

### 4.2 回滚方案
如果出现严重问题，可以：
1. 暂时关闭消耗历史记录功能（注释掉创建记录的代码）
2. 隐藏前端消耗历史显示
3. 保留数据库集合，待问题解决后重新启用

## 五、数据库结构文档更新

需要在`doc/exe_数据库结构.md`中新增：

### 10. 额度消耗历史表 (exe_usage_history)

用于存储用户每次AI对话的算力消耗详细记录。

```json
{
  "_id": "ObjectId",
  "userId": "String",
  "agentId": "String", 
  "agentName": "String",
  "modelId": "String",
  "modelName": "String",
  "pricingTierId": "String",
  "tierName": "String", 
  "modelLevel": "String",
  "powerCost": "Number",
  "balanceBefore": "Number",
  "balanceAfter": "Number", 
  "consumeTime": "Date",
  "description": "String",
  "createdAt": "Date"
}
```

**索引设计：**
- `userId`: 普通索引
- `consumeTime`: 普通索引  
- `userId + consumeTime`: 复合索引

## 六、技术实现细节

### 6.1 关键代码改动点

#### 云函数改动
1. **db.js新增内容**:
```javascript
// 添加集合常量
const COLLECTIONS = {
  // ... 现有集合
  USAGE_HISTORY: 'exe_usage_history'
}

// 额度消耗历史集合操作
const usageHistoryCollection = {
  async create(historyData) {
    return await getCollection(COLLECTIONS.USAGE_HISTORY).add({
      data: {
        ...historyData,
        createdAt: new Date()
      }
    })
  },

  async getUserHistory(userId, page = 1, limit = 20) {
    const offset = (page - 1) * limit
    return await getCollection(COLLECTIONS.USAGE_HISTORY)
      .where({ userId })
      .orderBy('consumeTime', 'desc')
      .skip(offset)
      .limit(limit)
      .get()
  }
}
```

2. **user_info.js修改updateUsage函数**:
使用事务确保扣费和记录创建的原子性:
```javascript
// 使用事务包装关键操作
await db.runTransaction(async (transaction) => {
  // 在事务中执行扣费
  await transaction.collection(COLLECTIONS.USERS)
    .doc(userId)
    .update({
      data: {
        availableCount: _.inc(-powerCost),
        totalUsageCount: _.inc(powerCost),
        updatedAt: new Date()
      }
    })

  // 在事务中创建消耗记录
  await transaction.collection(COLLECTIONS.USAGE_HISTORY)
    .add({
      data: usageHistoryData
    })
})
```

#### 前端改动
1. **新增UsageHistoryModel**
2. **新增UsageHistoryService**
3. **新增UsageHistoryProvider**
4. **修改ProfileScreen**添加消耗历史Tab

### 6.2 数据流程图
```
用户发起AI对话 → Go代理处理AI请求 → AI响应完成 →
Go异步调用云函数updateUsage(agentId, modelId) →
云函数事务处理: [扣减算力 + 创建消耗记录] →
前端查询消耗历史显示
```

**关键点说明**:
- Go程序已经在传递`agentId`和`modelId`给云函数
- 云函数接收到这些参数后，会查询智能体和模型的详细信息
- 所有信息都在云函数中组装成消耗记录

### 6.3 错误处理策略
1. **云函数层面**: 使用try-catch包装，确保主要扣费逻辑不受影响
2. **前端层面**: 优雅降级，消耗历史加载失败不影响其他功能
3. **数据层面**: 定期数据一致性检查和修复

## 七、测试计划

### 7.1 单元测试
- 云函数消耗记录创建功能
- 前端数据模型序列化/反序列化
- 状态管理逻辑

### 7.2 集成测试
- 完整的对话-扣费-记录流程
- 分页查询功能
- 错误场景处理

### 7.3 性能测试
- 大量消耗记录下的查询性能
- 前端列表渲染性能
- 数据库索引效果验证

## 八、上线计划

### 8.1 灰度发布
1. 先在测试环境完整验证
2. 生产环境先创建数据库集合和索引
3. 部署云函数改动（向后兼容）
4. 部署前端改动
5. 监控数据创建和查询情况

### 8.2 监控指标
- 消耗记录创建成功率
- 查询响应时间
- 错误率统计
- 存储空间增长趋势

## 九、预期效果

实施完成后，用户将能够：
1. 在个人中心查看详细的算力消耗历史
2. 了解每次对话的具体消耗情况
3. 追踪算力使用趋势
4. 更好地管理自己的算力使用

该功能将提升用户体验，增加产品的透明度和用户粘性。

## 十、后续优化方向

1. **数据分析**: 基于消耗历史提供使用统计和趋势分析
2. **智能推荐**: 根据使用习惯推荐合适的算力套餐
3. **成本优化**: 分析用户使用模式，优化算力定价策略
4. **用户画像**: 基于消耗数据构建用户行为画像
