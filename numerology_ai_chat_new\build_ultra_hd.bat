@echo off
chcp 65001 >nul
echo ════════════════════════════════════════════════════════════════════════════════
echo    💎 超高清图标构建脚本
echo ════════════════════════════════════════════════════════════════════════════════
echo 📝 将logo.png转换为超高清图标并构建应用
echo 💎 使用PNG嵌入技术，支持4K/8K显示器
echo ════════════════════════════════════════════════════════════════════════════════
echo.

echo 💎 步骤1: 生成超高清图标...
python create_ultra_hd_icon.py
if %errorlevel% neq 0 (
    echo ❌ 超高清图标生成失败！
    echo 💡 请确保安装了PIL库: pip install Pillow
    pause
    exit /b 1
)

echo.
echo 🧹 步骤2: 完全清理构建缓存...
flutter clean
if exist build rmdir /s /q build 2>nul

echo.
echo 📦 步骤3: 重新获取依赖...
flutter pub get

echo.
echo 🔨 步骤4: 构建超高清图标应用...
flutter build windows --release

echo.
echo 💎 超高清图标构建完成！
echo ════════════════════════════════════════════════════════════════════════════════
echo 📁 新的exe文件: build\windows\x64\runner\Release\numerology_ai_chat.exe
echo.

echo 🔍 步骤5: 验证图标文件质量...
if exist "windows\runner\resources\app_icon.ico" (
    echo ✅ ICO文件存在
    for %%A in ("windows\runner\resources\app_icon.ico") do (
        echo 📦 文件大小: %%~zA 字节
        if %%~zA gtr 1000000 (
            echo 💎 超高清图标质量: 极佳 ^(文件大小 ^> 1MB^)
        ) else if %%~zA gtr 100000 (
            echo ✨ 高清图标质量: 优秀 ^(文件大小 ^> 100KB^)
        ) else if %%~zA gtr 50000 (
            echo 📊 图标质量: 良好 ^(文件大小 ^> 50KB^)
        ) else (
            echo ⚠️ 图标质量可能不够高 ^(文件大小较小^)
        )
    )
) else (
    echo ❌ ICO文件不存在
)

echo.
echo 📝 超高清图标说明:
echo ════════════════════════════════════════════════════════════════════════════════
echo 💎 技术特点:
echo    - 使用PNG格式嵌入ICO文件
echo    - 包含9种关键尺寸: 16x16 到 512x512
echo    - 支持高DPI显示器和4K/8K显示器
echo    - 文件大小约1.4MB，质量极高
echo    - 在所有Windows版本中都能正确显示
echo.
echo 🎯 显示效果:
echo    - 在普通显示器上: 清晰锐利
echo    - 在高DPI显示器上: 完美无瑕
echo    - 在4K显示器上: 超清细节
echo    - 在8K显示器上: 极致清晰
echo.
echo 🔧 如果图标仍然显示为默认图标:
echo    1. 按 Ctrl+Shift+Esc 打开任务管理器
echo    2. 找到 "Windows资源管理器" 进程
echo    3. 右键选择 "重新启动"
echo    4. 或者重启计算机以完全清除图标缓存
echo.
echo 💡 提示: 
echo    - 超高清图标在高分辨率显示器上效果最佳
echo    - 图标文件较大是正常的，这保证了最高质量
echo    - 如果需要减小文件大小，可以使用 create_hd_icon.py
echo.
pause
