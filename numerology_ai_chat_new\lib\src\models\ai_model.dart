/// AI模型（匹配云函数数据结构）
class AIModel {
  final String id;
  final String modelName;
  final String modelDisplayName;
  final String modelApiUrl;
  final int maxTokens;
  final double temperature;
  final bool isActive;
  final int sortOrder;
  final String description;
  final String? modelLevel; // 模型等级（初级/高级）
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? createdBy;
  final String? updatedBy;
  final bool? hasApiKey; // 管理员视角才有

  const AIModel({
    required this.id,
    required this.modelName,
    required this.modelDisplayName,
    required this.modelApiUrl,
    required this.maxTokens,
    required this.temperature,
    required this.isActive,
    required this.sortOrder,
    required this.description,
    this.modelLevel,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
    this.hasApiKey,
  });

  /// 从JSON创建AI模型（匹配云函数返回格式）
  factory AIModel.fromJson(Map<String, dynamic> json) {
    return AIModel(
      id: json['_id'] as String? ?? json['id'] as String,
      modelName: json['modelName'] as String,
      modelDisplayName: json['modelDisplayName'] as String,
      modelApiUrl: json['modelApiUrl'] as String,
      maxTokens: json['maxTokens'] as int,
      temperature: (json['temperature'] as num).toDouble(),
      isActive: json['isActive'] as bool? ?? true,
      sortOrder: json['sortOrder'] as int? ?? 0,
      description: json['description'] as String,
      modelLevel: json['modelLevel'] as String?,
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt'] as String) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt'] as String) : null,
      createdBy: json['createdBy'] as String?,
      updatedBy: json['updatedBy'] as String?,
      hasApiKey: json['hasApiKey'] as bool?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'modelName': modelName,
      'modelDisplayName': modelDisplayName,
      'modelApiUrl': modelApiUrl,
      'maxTokens': maxTokens,
      'temperature': temperature,
      'isActive': isActive,
      'sortOrder': sortOrder,
      'description': description,
      if (modelLevel != null) 'modelLevel': modelLevel,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'createdBy': createdBy,
      'updatedBy': updatedBy,
      if (hasApiKey != null) 'hasApiKey': hasApiKey,
    };
  }

  /// 复制并修改AI模型
  AIModel copyWith({
    String? id,
    String? modelName,
    String? modelDisplayName,
    String? modelApiUrl,
    int? maxTokens,
    double? temperature,
    bool? isActive,
    int? sortOrder,
    String? description,
    String? modelLevel,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
    bool? hasApiKey,
  }) {
    return AIModel(
      id: id ?? this.id,
      modelName: modelName ?? this.modelName,
      modelDisplayName: modelDisplayName ?? this.modelDisplayName,
      modelApiUrl: modelApiUrl ?? this.modelApiUrl,
      maxTokens: maxTokens ?? this.maxTokens,
      temperature: temperature ?? this.temperature,
      isActive: isActive ?? this.isActive,
      sortOrder: sortOrder ?? this.sortOrder,
      description: description ?? this.description,
      modelLevel: modelLevel ?? this.modelLevel,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      hasApiKey: hasApiKey ?? this.hasApiKey,
    );
  }

  /// 是否可用
  bool get isAvailable => isActive;

  /// 获取模型类型颜色（根据模型名称）
  String get typeColor {
    if (modelName.toLowerCase().contains('gpt')) {
      return '#10B981';
    } else if (modelName.toLowerCase().contains('claude')) {
      return '#8B5CF6';
    } else if (modelName.toLowerCase().contains('gemini')) {
      return '#F59E0B';
    } else if (modelName.toLowerCase().contains('deepseek')) {
      return '#06B6D4';
    }
    return '#6B7280';
  }

  /// 获取模型类型图标
  String get typeIcon {
    if (modelName.toLowerCase().contains('gpt')) {
      return '🤖';
    } else if (modelName.toLowerCase().contains('claude')) {
      return '🧠';
    } else if (modelName.toLowerCase().contains('gemini')) {
      return '⭐';
    } else if (modelName.toLowerCase().contains('deepseek')) {
      return '🚀';
    }
    return '💬';
  }

  @override
  String toString() {
    return 'AIModel(id: $id, modelName: $modelName, '
        'modelDisplayName: $modelDisplayName, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AIModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}