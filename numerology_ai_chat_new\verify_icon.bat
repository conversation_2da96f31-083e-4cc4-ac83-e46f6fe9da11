@echo off
echo 验证图标是否正确应用...
if exist "build\windows\x64\runner\Release\numerology_ai_chat.exe" (
    echo ✅ Release版本存在
    echo 📁 位置: build\windows\x64\runner\Release\numerology_ai_chat.exe
) else (
    echo ❌ Release版本不存在，请先构建
)

if exist "build\windows\x64\runner\Debug\numerology_ai_chat.exe" (
    echo ✅ Debug版本存在  
    echo 📁 位置: build\windows\x64\runner\Debug\numerology_ai_chat.exe
) else (
    echo ❌ Debug版本不存在
)

echo.
echo 💡 如果图标仍然不正确，请：
echo 1. 重启Windows资源管理器
echo 2. 清理图标缓存
echo 3. 重新构建应用
pause
