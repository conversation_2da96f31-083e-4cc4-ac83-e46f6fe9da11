const cloud = require('wx-server-sdk')
const { errorHandler } = require('./src/middleware/error_handler')
const adminAuthHandler = require('./src/handlers/admin_auth')
const userManageHandler = require('./src/handlers/user_management')
const agentManageHandler = require('./src/handlers/agent_manage')
const modelManageHandler = require('./src/handlers/model_management')
const pageManageHandler = require('./src/handlers/page_manage')
const { updateAdminPassword } = require('./src/handlers/update_admin_password')
const logViewHandler = require('./src/handlers/log_view')
const statsViewHandler = require('./src/handlers/stats_view')
const systemConfigHandler = require('./src/handlers/system_config_handler')
const packageManagementHandler = require('./src/handlers/package_management')
const orderManagementHandler = require('./src/handlers/order_management')
const paymentLogManagementHandler = require('./src/handlers/payment_log_management')
const usageHistoryManagementHandler = require('./src/handlers/usage_history_management')
const analyticsHandler = require('./src/handlers/analytics_handler')
const { adminAuthMiddleware } = require('./src/middleware/auth')
const logger = require('./src/utils/logger')

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 格式化成功响应 (兼容HTTP触发器)
function createSuccessResponse(data) {
  return {
    isBase64Encoded: false,
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type,Authorization,admin-token',
    },
    body: JSON.stringify({
      code: 0,
      message: '成功',
      data: data
    })
  };
}

// 格式化错误响应 (兼容HTTP触发器)
function createErrorResponse(error) {
  const { code, message } = errorHandler(error);
  return {
    isBase64Encoded: false,
    statusCode: 200, // API网关要求200，业务错误通过code判断
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type,Authorization,admin-token',
    },
    body: JSON.stringify({
      code,
      message,
      data: null
    })
  };
}

/**
 * 管理员云函数入口函数
 * 通过action参数路由到不同的处理器
 */
exports.main = async (event, context) => {
  // 处理CORS预检请求
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, admin-token',
        'Access-Control-Max-Age': '86400'
      },
      body: ''
    };
  }

  try {
    let payload;
    // 兼容API网关和直接调用
    if (event.body) {
      try {
        payload = JSON.parse(event.body);
      } catch(e) {
        throw new Error('无效的JSON请求体');
      }
    } else {
      payload = event;
    }
    
    // 从请求头获取token
    const token = event.headers ? (event.headers['authorization'] || event.headers['Authorization'] || event.headers['admin-token']) : null;
    if (token) {
        // 如果是从 Authorization 头获取的，可能带有 Bearer 前缀
        payload.token = token.startsWith('Bearer ') ? token.substring(7) : token;
    }
    
    const { action, ...params } = payload;

    if (!action) {
      throw new Error('缺少action参数');
    }

    logger.info(`Admin Action: ${action}`, { params, context });

    // 定义需要鉴权的actions
    const protectedActions = new Set([
      'listUsers', 'createUser', 'updateUserMembership', 'updateUserQuota', 'updateUserStatus',
      'listAgents', 'createAgent', 'updateAgent', 'deleteAgent',
      'listModels', 'createModel', 'updateModel', 'deleteModel',
      'listPages', 'createPage', 'updatePage', 'deletePage',
      'listSystemConfigs', 'createSystemConfig', 'updateSystemConfig', 'deleteSystemConfig',
      'listPackages', 'createPackage', 'updatePackage', 'deletePackage',
      'listOrders', 'getOrderDetail', 'updateOrderStatus', 'getOrderStats',
      'listPaymentLogs', 'getPaymentLogDetail', 'getPaymentLogStats', 'deletePaymentLog',
      'listUsageHistories', 'getUsageHistoryDetail', 'getUsageHistoryStats', 'deleteUsageHistory',
      'getOverallStats', 'getTrendData',
      'viewLogs', 'stats'
    ]);

    // 如果action需要鉴权，则执行鉴权中间件
    if (protectedActions.has(action)) {
      const adminAuth = await adminAuthMiddleware(payload);
      payload.adminAuth = adminAuth; // 将管理员信息添加到payload中
    }
    
    let result;
    // 路由分发
    switch (action) {
      // 管理员认证
      case 'adminLogin':
        result = await adminAuthHandler.login(params);
        break;
      case 'updateAdminPassword':
        result = await updateAdminPassword(params);
        break;
      
      // 用户管理
      case 'listUsers':
        result = await userManageHandler.getUserList(payload, payload.adminAuth);
        break;
      case 'createUser':
        result = await userManageHandler.createUserByAdmin(payload, payload.adminAuth);
        break;
      case 'updateUserMembership':
        result = await userManageHandler.updateUserMembershipByAdmin(payload, payload.adminAuth);
        break;
      case 'updateUserQuota':
        result = await userManageHandler.updateUserQuotaByAdmin(payload, payload.adminAuth);
        break;
      case 'updateUserStatus':
        result = await userManageHandler.updateUserStatusByAdmin(payload, payload.adminAuth);
        break;
      
      // 智能体管理
      case 'listAgents':
        result = await agentManageHandler.getAgentList(payload, payload.adminAuth);
        break;
      case 'createAgent':
        result = await agentManageHandler.createAgentByAdmin(payload, payload.adminAuth);
        break;
      case 'updateAgent':
        result = await agentManageHandler.updateAgentByAdmin(payload, payload.adminAuth);
        break;
      case 'deleteAgent':
        result = await agentManageHandler.deleteAgentByAdmin(payload, payload.adminAuth);
        break;

      // 模型管理
      case 'listModels':
        result = await modelManageHandler.getModelList(payload, payload.adminAuth);
        break;
      case 'createModel':
        result = await modelManageHandler.createModelByAdmin(payload, payload.adminAuth);
        break;
      case 'updateModel':
        result = await modelManageHandler.updateModelByAdmin(payload, payload.adminAuth);
        break;
      case 'deleteModel':
        result = await modelManageHandler.deleteModelByAdmin(payload, payload.adminAuth);
        break;

      // 页面配置管理
      case 'listPages':
        result = await pageManageHandler.getPageList(payload, payload.adminAuth);
        break;
      case 'createPage':
        result = await pageManageHandler.createPageByAdmin(payload, payload.adminAuth);
        break;
      case 'updatePage':
        result = await pageManageHandler.updatePageByAdmin(payload, payload.adminAuth);
        break;
      case 'deletePage':
        result = await pageManageHandler.deletePageByAdmin(payload, payload.adminAuth);
        break;

      // 系统配置管理
      case 'listSystemConfigs':
        result = await systemConfigHandler.getSystemConfigs(payload, payload.adminAuth);
        break;
      case 'createSystemConfig':
        result = await systemConfigHandler.createSystemConfig(payload, payload.adminAuth);
        break;
      case 'updateSystemConfig':
        result = await systemConfigHandler.updateSystemConfig(payload, payload.adminAuth);
        break;
      case 'deleteSystemConfig':
        result = await systemConfigHandler.deleteSystemConfig(payload, payload.adminAuth);
        break;

      // 套餐管理
      case 'listPackages':
        result = await packageManagementHandler.getPackageList(payload, payload.adminAuth);
        break;
      case 'createPackage':
        result = await packageManagementHandler.createPackage(payload, payload.adminAuth);
        break;
      case 'updatePackage':
        result = await packageManagementHandler.updatePackage(payload, payload.adminAuth);
        break;
      case 'deletePackage':
        result = await packageManagementHandler.deletePackage(payload, payload.adminAuth);
        break;

      // 订单管理
      case 'listOrders':
        result = await orderManagementHandler.listOrders(payload);
        break;
      case 'getOrderDetail':
        result = await orderManagementHandler.getOrderDetail(payload);
        break;
      case 'updateOrderStatus':
        result = await orderManagementHandler.updateOrderStatus(payload);
        break;
      case 'getOrderStats':
        result = await orderManagementHandler.getOrderStats(payload);
        break;

      // 支付日志管理
      case 'listPaymentLogs':
        result = await paymentLogManagementHandler.listPaymentLogs(payload);
        break;
      case 'getPaymentLogDetail':
        result = await paymentLogManagementHandler.getPaymentLogDetail(payload);
        break;
      case 'getPaymentLogStats':
        result = await paymentLogManagementHandler.getPaymentLogStats(payload);
        break;
      case 'deletePaymentLog':
        result = await paymentLogManagementHandler.deletePaymentLog(payload);
        break;

      // 使用历史管理
      case 'listUsageHistories':
        result = await usageHistoryManagementHandler.listUsageHistories(payload);
        break;
      case 'getUsageHistoryDetail':
        result = await usageHistoryManagementHandler.getUsageHistoryDetail(payload);
        break;
      case 'getUsageHistoryStats':
        result = await usageHistoryManagementHandler.getUsageHistoryStats(payload);
        break;
      case 'deleteUsageHistory':
        result = await usageHistoryManagementHandler.deleteUsageHistory(payload);
        break;

      // 分析统计
      case 'getOverallStats':
        result = await analyticsHandler.getOverallStats(payload);
        break;
      case 'getTrendData':
        result = await analyticsHandler.getTrendData(payload);
        break;

      // 日志和统计功能
      case 'viewLogs':
        result = await logViewHandler.viewLogs(payload, payload.adminAuth);
        break;

      case 'stats':
        result = await statsViewHandler.getSystemStats(payload, payload.adminAuth);
        break;

      default:
        throw new Error(`Unknown admin action: ${action}`);
    }
    return createSuccessResponse(result);
  } catch (error) {
    return createErrorResponse(error);
  }
}