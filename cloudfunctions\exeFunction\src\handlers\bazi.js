const baziCalculator = require('../services/bazi/baziCalculator')
const resultFormatter = require('../services/bazi/resultFormatter')
const validators = require('../services/bazi/validators')
const solarTimeCalculator = require('../services/bazi/solarTimeCalculator')
const logger = require('../utils/logger')

/**
 * 八字分析处理器
 * 提供八字排盘和分析功能
 */
class BaziHandler {
  /**
   * 执行八字分析
   * @param {Object} payload - 请求参数
   * @returns {Object} - 八字分析结果
   */
  async analyze(payload) {
    try {
      logger.info('开始八字分析', { payload });

      // 1. 参数验证
      const validationResult = validators.validateRequestParams(payload)
      if (!validationResult.valid) {
        throw new Error(validationResult.message)
      }
      
      // 使用清理后的数据
      const {
        name,
        gender,
        birthDate,
        birthTime,
        birthPlace,
        calendarType,
        isLeapMonth,
        yearGanZhiSet,
        monthGanZhiSet,
        dayGanZhiSet,
        hourGanZhiSet,
        latitude,
        longitude,
        region,
        considerDaylightSaving,
        enableSolarTimeCalculation,
        ziTimeHandling
      } = validationResult.cleanData
      
      // 2. 真太阳时计算（根据用户选择和经纬度可用性）
      let adjustedBirthDate = birthDate
      let adjustedBirthTime = birthTime
      let solarTimeInfo = null

      const shouldCalculateSolarTime =
        enableSolarTimeCalculation &&
        latitude &&
        longitude &&
        solarTimeCalculator.validateCoordinates(latitude, longitude)

      if (shouldCalculateSolarTime) {
        try {
          // 构造完整的出生日期时间
          // 确保时间格式正确，避免重复添加秒数
          let timeStr = birthTime;
          if (!timeStr.includes(':')) {
            // 如果没有冒号，假设是小时数
            timeStr = `${timeStr}:00:00`;
          } else if (timeStr.split(':').length === 2) {
            // 如果只有小时和分钟，添加秒数
            timeStr = `${timeStr}:00`;
          }
          // 如果已经有秒数，直接使用

          const birthDateTime = new Date(`${birthDate}T${timeStr}`)
          const birthDateOnly = new Date(birthDate)

          // 验证构造的Date对象是否有效
          if (isNaN(birthDateTime.getTime()) || isNaN(birthDateOnly.getTime())) {
            throw new Error(`无效的日期时间格式: ${birthDate}T${timeStr}`)
          }

          // 计算真太阳时（传递夏令时考虑选项）
          const solarTime = solarTimeCalculator.calculateSolarTime(
            birthDateTime,
            longitude,
            birthDateOnly,
            region || '大陆',
            considerDaylightSaving
          )

          // 更新出生日期和时间为真太阳时（重要：包括日期变化）
          adjustedBirthDate = solarTime.toISOString().substring(0, 10) // YYYY-MM-DD
          adjustedBirthTime = solarTime.toTimeString().substring(0, 8)  // HH:MM:SS

          // 记录真太阳时信息
          solarTimeInfo = solarTimeCalculator.getTimeDifferenceInfo(
            birthDateTime,
            solarTime,
            longitude,
            region || '大陆',
            considerDaylightSaving
          )

          // 添加用户的夏令时选择信息
          solarTimeInfo.considerDaylightSaving = considerDaylightSaving

          logger.info('真太阳时计算完成', {
            originalDate: birthDate,
            originalTime: birthTime,
            adjustedDate: adjustedBirthDate,
            adjustedTime: adjustedBirthTime,
            longitude,
            timeDiff: solarTimeInfo.timeDiffMinutes,
            considerDaylightSaving,
            enableSolarTimeCalculation
          })
        } catch (error) {
          logger.warn('真太阳时计算失败，使用原始时间', { error: error.message })
        }
      } else {
        logger.info('跳过真太阳时计算', {
          enableSolarTimeCalculation,
          hasCoordinates: !!(latitude && longitude),
          reason: !enableSolarTimeCalculation ? '用户关闭' : '缺少经纬度'
        })
      }

      // 3. 解析出生日期和时间（使用真太阳时校正后的日期和时间）
      const birthInfo = baziCalculator.parseBirthInfo(adjustedBirthDate, adjustedBirthTime, calendarType, isLeapMonth)
      if (!birthInfo.success) {
        throw new Error(birthInfo.message)
      }
      
      // 4. 计算八字信息
      const baziSettings = {
        yearGanZhiSet,
        monthGanZhiSet,
        dayGanZhiSet: ziTimeHandling, // 使用用户选择的子时处理方式
        hourGanZhiSet
      }
      
      const baziInfo = baziCalculator.calculateBazi(
        birthInfo.lunar,
        name,
        gender,
        birthPlace,
        baziSettings
      )

      // 添加真太阳时信息到baziInfo中
      if (solarTimeInfo) {
        baziInfo.solarTimeInfo = solarTimeInfo;
        baziInfo.originalDate = birthDate; // 保存原始输入日期
        baziInfo.originalTime = birthTime; // 保存原始输入时间
      }

      // 5. 生成文本格式结果
      const textResult = resultFormatter.generateTextResult(baziInfo)
      
      // 6. 返回分析结果
      const responseData = {
        // 基本信息
        name: baziInfo.name,
        gender: baziInfo.gender,
        birthPlace: baziInfo.birthPlace,
        solarDate: baziInfo.solarDate,
        solarTime: baziInfo.solarTime,
        lunarDate: baziInfo.lunarDate,
        
        // 八字信息
        baziStr: baziInfo.baziStr,
        
        // 年月日时柱详细信息
        year: baziInfo.year,
        yearGanZhiZhuXing: baziInfo.yearGanZhiZhuXing,
        month: baziInfo.month,
        monthGanZhiZhuXing: baziInfo.monthGanZhiZhuXing,
        day: baziInfo.day,
        dayGanZhiZhuXing: baziInfo.dayGanZhiZhuXing,
        time: baziInfo.time,
        timeGanZhiZhuXing: baziInfo.timeGanZhiZhuXing,
        
        // 其他分析结果
        ganWuXing: baziInfo.ganWuXing,
        zhiWuXing: baziInfo.zhiWuXing,
        cangGan: baziInfo.cangGan,
        shiShen: baziInfo.shiShen,
        kongWang: baziInfo.kongWang,
        naYin: baziInfo.naYin,
        wuXingCount: baziInfo.wuXingCount,
        dayElementStrength: baziInfo.dayElementStrength,
        favorableElements: baziInfo.favorableElements,
        destiny: baziInfo.destiny,
        yearlyDestiny: baziInfo.yearlyDestiny,
        
        // 自然语言格式的八字分析结果
        textResult: textResult,

        // 真太阳时信息（如果计算了的话）
        solarTimeInfo: solarTimeInfo
      }

      logger.info('八字分析成功', { name, gender });
      return responseData
      
    } catch (error) {
      logger.error('八字分析失败', { error: error.message, stack: error.stack });
      throw error
    }
  }
}

module.exports = new BaziHandler()
