# 数据库结构设计

## 概述

本项目使用腾讯云开发文档型数据库（MongoDB）作为数据存储方案。数据库包含13个主要集合，用于存储用户信息、智能体配置、模型配置、管理员信息、页面配置、购买订单信息、算力套餐信息、算力档次配置、额度消耗历史、系统配置信息、激活码核销记录和管理员算力操作记录。

**注意：** 由于云环境与其他项目共享，所有集合名称都使用 `exe_` 前缀进行命名。

## 集合命名规范

为了避免与其他项目的数据库集合冲突，本项目所有集合都使用统一的命名前缀：

- **前缀规则**：`exe_` + 集合名称
- **示例**：
  - 用户表：`exe_users`
  - 智能体表：`exe_agents`
  - 模型表：`exe_models`
  - 管理员表：`exe_admins`
  - 页面配置表：`exe_pages`
  - 购买订单表：`exe_purchase_orders`
  - 算力套餐表：`exe_payment_packages`
  - 算力档次表：`exe_pricing_tiers`
  - 版本管理表：`exe_app_versions`
  - 额度消耗历史表：`exe_usage_history`
  - 系统配置表：`exe_system_config`
  - 激活码核销表：`exe_activation_codes`
  - 管理员算力操作记录表：`exe_quota_operations`

## 数据库集合设计

### 1. 用户表 (exe_users)

用于存储用户基本信息、会员状态和使用记录。

```json
{
  "_id": "ObjectId",           // MongoDB自动生成的唯一标识
  "username": "String",         // 用户名
  "password": "String",         // 密码（加密存储）
  "refreshToken": "String",       // 刷新令牌，用于自动续期（唯一）
  "refreshTokenExpiresAt": "Date",// 刷新令牌过期时间
  "availableCount": "Number",   // 可用次数
  "isWeakAdmin": "Boolean",     // 是否为管理员（默认false）
  "purchaseHistory": [          // 额度变更历史（管理员操作日志）
    {
      "date": "Date",           // 操作日期
      "type": "String",         // 操作类型: "额度充值"
      "details": {
          "addedCount": "Number"      // 可选, 充值的次数
      },
      "reason": "String",       // 备注，例如 "系统赠送", "活动奖励"
      "operator": "String"      // 操作的管理员账号
    }
  ],
  "totalUsageCount": "Number",  // 总使用次数
  "createdAt": "Date",          // 创建时间
  "updatedAt": "Date",          // 更新时间
  "lastLoginAt": "Date",        // 最后登录时间
  "status": "String",           // 账户状态：激活/禁用/暂停
  "email": "String",            // 邮箱（可选）
  "phone": "String"             // 手机号（可选）
}
```

**字段说明：**
- `isWeakAdmin`: 管理员权限标识，拥有此权限的用户可以创建激活码和给用户充值，但不能访问全局管理后台

**索引设计：**
- `username`: 唯一索引
- `refreshToken`: 唯一索引
- `createdAt`: 普通索引
- `isWeakAdmin`: 普通索引

### 2. 智能体表 (exe_agents)

用于存储智能体配置信息和提示词。

```json
{
  "_id": "ObjectId",           // MongoDB自动生成的唯一标识
  "agentName": "String",        // 智能体名称
  "agentPrompt": "String",      // 智能体提示词
  "agentType": "String",        // 智能体类型：八字/紫微/无需携带内容
  "description": "String",      // 智能体描述
  "pricingTierId": "String",    // 关联的算力档次ID
  "isActive": "Boolean",        // 是否启用
  "sortOrder": "Number",        // 排序权重
  "createdAt": "Date",          // 创建时间
  "updatedAt": "Date",          // 更新时间
  "createdBy": "String",        // 创建者
  "updatedBy": "String"         // 最后更新者
}
```

**智能体类型说明：**
- `八字`: 需要八字排盘
- `紫微`: 需要紫微斗数排盘
- `无需携带内容`: 不需要排盘

**索引设计：**
- `agentName`: 唯一索引
- `agentType`: 普通索引
- `pricingTierId`: 普通索引
- `isActive`: 普通索引
- `sortOrder`: 普通索引

### 3. 模型表 (exe_models)

用于存储AI模型配置信息和API密钥。

```json
{
  "_id": "ObjectId",           // MongoDB自动生成的唯一标识
  "modelName": "String",        // 模型名称
  "modelApiKey": "String",      // 模型API密钥（加密存储）
  "modelApiUrl": "String",      // 模型API地址
  "modelDisplayName": "String", // 模型前端显示名称
  "modelLevel": "String",       // 模型等级：初级/高级
  "maxTokens": "Number",        // 最大token数
  "temperature": "Number",      // 温度参数
  "isActive": "Boolean",        // 是否启用
  "sortOrder": "Number",        // 排序权重
  "description": "String",      // 模型前端显示描述
  "createdAt": "Date",          // 创建时间
  "updatedAt": "Date",          // 更新时间
  "createdBy": "String",        // 创建者
  "updatedBy": "String"         // 最后更新者
}
```

**模型等级说明：**
- `初级`: 基础AI模型，计算成本较低
- `高级`: 高性能AI模型，计算成本较高

**索引设计：**
- `modelName`: 唯一索引
- `modelLevel`: 普通索引
- `isActive`: 普通索引
- `sortOrder`: 普通索引

### 4. 管理员表 (exe_admins)

用于存储管理员账户信息和权限配置。

```json
{
  "_id": "ObjectId",           // MongoDB自动生成的唯一标识
  "adminName": "String",        // 管理员姓名
  "adminAccount": "String",     // 管理员账号
  "adminPassword": "String",    // 管理员密码（加密存储）
  "adminRole": "String",        // 管理员角色：超级管理员/管理员/操作员
  "permissions": ["String"],    // 权限列表
  "isActive": "Boolean",        // 是否启用
  "lastLoginAt": "Date",        // 最后登录时间
  "loginCount": "Number",       // 登录次数
  "createdAt": "Date",          // 创建时间
  "updatedAt": "Date",          // 更新时间
  "createdBy": "String",        // 创建者
  "updatedBy": "String"         // 最后更新者
}
```

**管理员角色说明：**
- `超级管理员`: 超级管理员，拥有所有权限
- `管理员`: 普通管理员，拥有大部分权限
- `操作员`: 操作员，拥有基础操作权限

**权限列表：**
- `user_manage`: 用户管理
- `agent_manage`: 智能体管理
- `model_manage`: 模型管理
- `page_manage`: 页面配置管理
- `admin_manage`: 管理员管理
- `log_view`: 日志查看
- `stats_view`: 统计查看

**索引设计：**
- `adminAccount`: 唯一索引
- `adminRole`: 普通索引

### 5. 页面配置表 (exe_pages)

用于存储前端页面的Markdown内容配置。

```json
{
  "_id": "ObjectId",           // MongoDB自动生成的唯一标识
  "pageTitle": "String",        // 页面标题
  "pageContent": "String",      // 页面内容（Markdown格式）
  "pageType": "String",         // 页面类型：帮助/关于/条款/隐私/使用指南
  "isActive": "Boolean",        // 是否启用
  "sortOrder": "Number",        // 排序权重
  "slug": "String",             // URL友好标识
  "metaDescription": "String",  // 页面描述
  "keywords": ["String"],       // 关键词
  "createdAt": "Date",          // 创建时间
  "updatedAt": "Date",          // 更新时间
  "createdBy": "String",        // 创建者
  "updatedBy": "String"         // 最后更新者
}
```

**页面类型说明：**
- `帮助`: 帮助文档
- `关于`: 关于我们
- `条款`: 服务条款
- `隐私`: 隐私政策
- `使用指南`: 使用指南

**索引设计：**
- `slug`: 唯一索引
- `pageType`: 普通索引

### 6. 购买订单表 (exe_purchase_orders)

用于存储用户购买算力的订单信息。

```json
{
  "_id": "String",             // 自定义订单ID，格式：order_001
  "userId": "String",          // 关联的用户ID
  "orderNo": "String",         // 订单号，格式：ORD20241215001
  "packageName": "String",     // 套餐名称，如"试用套餐"、"超值套餐"
  "packageQuota": "Number",    // 套餐包含的算力数量
  "orderAmount": "Number",     // 订单金额（单位：分）
  "status": "String",          // 订单状态: PENDING(待支付), PAID(已支付), COMPLETED(已完成), CANCELLED(已取消), REFUNDED(已退款)
  "createTime": "Date",        // 订单创建时间
  "payTime": "Date",           // 支付时间（可为null）
  "transactionId": "String",   // 第三方支付平台的交易流水号（可为null）
  "updatedAt": "Date"          // 最后更新时间
}
```

**订单状态说明：**
- `PENDING`: 待支付，用户已创建订单但未完成支付
- `PAID`: 已支付，支付成功但算力尚未发放
- `COMPLETED`: 已完成，支付成功且算力已发放到用户账户
- `CANCELLED`: 已取消，用户或系统取消了订单
- `REFUNDED`: 已退款，订单已退款处理

**索引设计：**
- `orderNo`: 唯一索引
- `userId`: 普通索引
- `status`: 普通索引
- `createTime`: 普通索引

### 7. 算力套餐表 (exe_payment_packages)

用于定义可供用户购买的各种算力套餐，替代前端硬编码的价格配置。

```json
{
  "_id": "String",             // 自定义套餐ID，格式：package_001
  "packageName": "String",     // 套餐名称，如"试用套餐"、"超值套餐"
  "packageDescription": "String", // 套餐描述，如"适合新用户体验"
  "quotaCount": "Number",      // 套餐包含的算力数量
  "price": "Number",           // 套餐价格（单位：分）
  "originalPrice": "Number",   // 原价（用于显示折扣，单位：分）
  "discountRate": "Number",    // 折扣率（0-1之间），如0.8表示8折
  "isActive": "Boolean",       // 是否启用
  "isRecommended": "Boolean",  // 是否推荐套餐
  "sortOrder": "Number",       // 排序权重，值越小越靠前
  "tags": ["String"],          // 套餐标签，如["热门", "推荐", "限时优惠"]
  "validDays": "Number",       // 套餐有效期（天数），0表示永久有效
  "minPurchaseCount": "Number", // 最小购买数量，默认1
  "maxPurchaseCount": "Number", // 最大购买数量，0表示无限制
  "createdAt": "Date",         // 创建时间
  "updatedAt": "Date",         // 更新时间
  "createdBy": "String",       // 创建者
  "updatedBy": "String"        // 最后更新者
}
```

**套餐设计说明：**
- `试用套餐`: 小额度低价格，适合新用户体验
- `标准套餐`: 中等额度中等价格，适合普通用户
- `超值套餐`: 大额度高性价比，适合重度用户
- `企业套餐`: 超大额度批发价，适合企业用户

**索引设计：**
- `packageName`: 唯一索引
- `isActive`: 普通索引
- `sortOrder`: 普通索引
- `isRecommended`: 普通索引

### 8. 算力档次表 (exe_pricing_tiers)

用于定义不同智能体的算力消耗档次，支持基于智能体类型和模型等级的差异化计费。

```json
{
  "_id": "ObjectId",           // MongoDB自动生成的唯一标识
  "tierName": "String",        // 档次名称，如"基础档次"、"标准档次"、"高级档次"
  "tierDescription": "String", // 档次描述，说明适用场景
  "basicModelCost": "Number",  // 初级模型每次对话扣除的算力点数
  "advancedModelCost": "Number", // 高级模型每次对话扣除的算力点数
  "isActive": "Boolean",       // 是否启用该档次
  "sortOrder": "Number",       // 排序权重，值越小越靠前
  "createdAt": "Date",         // 创建时间
  "updatedAt": "Date",         // 更新时间
  "createdBy": "String",       // 创建者
  "updatedBy": "String"        // 最后更新者
}
```

**档次设计说明：**
- `基础档次`: 适用于简单对话类智能体（如过三关）
- `标准档次`: 适用于一般专业智能体（如命理、解梦）
- `高级档次`: 适用于复杂分析类智能体（如风水）

**计费逻辑：**
- 每次AI对话完成后，根据智能体的档次和所选模型的等级，扣除对应的算力点数
- 扣费公式：实际扣费 = 智能体档次.basicModelCost（初级模型）或 智能体档次.advancedModelCost（高级模型）

**索引设计：**
- `tierName`: 唯一索引
- `isActive`: 普通索引
- `sortOrder`: 普通索引

### 9. 版本管理表 (exe_app_versions)

用于管理应用版本信息，支持版本检查、强制更新和下载链接管理。

```json
{
  "_id": "ObjectId",           // MongoDB自动生成的唯一标识
  "versionNumber": "String",   // 版本号，如"1.0.0"、"1.1.0"
  "versionName": "String",     // 版本名称，如"正式版"、"测试版"
  "isAvailable": "Boolean",    // 版本是否可用（true=可用，false=必须更新）
  "forceUpdate": "Boolean",    // 是否强制更新（true=强制，false=可选）
  "downloadUrl": "String",     // 下载链接地址
  "updateDescription": "String", // 更新说明，描述新版本特性
  "releaseNotes": "String",    // 版本发布说明（Markdown格式）
  "isActive": "Boolean",       // 是否启用该版本记录
  "publishedAt": "Date",       // 发布时间
  "createdAt": "Date",         // 创建时间
  "updatedAt": "Date",         // 更新时间
  "createdBy": "String",       // 创建者
  "updatedBy": "String"        // 最后更新者
}
```

**版本状态说明：**
- `isAvailable=true, forceUpdate=false`: 版本可用，提示用户可选更新
- `isAvailable=true, forceUpdate=true`: 版本可用，但强制用户更新到最新版
- `isAvailable=false`: 版本不可用，用户无法登录，必须更新

**版本排序逻辑：**
- 使用语义化版本号（Semantic Versioning）进行排序
- 云函数中使用semver库比较版本号大小
- 最新版本 = 版本号最大且isAvailable=true的版本

**索引设计：**
- `versionNumber`: 唯一索引
- `isAvailable`: 普通索引
- `publishedAt`: 普通索引

### 10. 额度消耗历史表 (exe_usage_history)

用于存储用户每次AI对话的算力消耗详细记录，便于用户查看消费明细和系统进行数据分析。

```json
{
  "_id": "ObjectId",           // MongoDB自动生成的唯一标识
  "userId": "String",          // 用户ID，关联exe_users表
  "agentId": "String",         // 智能体ID，关联exe_agents表
  "agentName": "String",       // 智能体名称（冗余字段，便于查询显示）
  "modelId": "String",         // 模型ID，关联exe_models表
  "modelName": "String",       // 模型显示名称（冗余字段，便于查询显示）
  "pricingTierId": "String",   // 算力档次ID，关联exe_pricing_tiers表
  "tierName": "String",        // 档次名称（冗余字段，便于查询显示）
  "modelLevel": "String",      // 模型等级：初级/高级
  "powerCost": "Number",       // 本次消耗的算力数量
  "balanceBefore": "Number",   // 消耗前的算力余额
  "balanceAfter": "Number",    // 消耗后的算力余额
  "consumeTime": "Date",       // 算力消耗时间
  "description": "String",     // 消耗描述，如"标准档次 + 高级模型 = 20算力"
  "createdAt": "Date"          // 记录创建时间
}
```

**设计说明：**
- `agentName`、`modelName`、`tierName`为冗余字段，避免查询时需要关联多个表
- `balanceBefore`和`balanceAfter`记录余额变化，便于用户了解消费前后状态
- `consumeTime`记录实际消费时间，与`createdAt`可能略有差异
- `description`提供人性化的消费说明

**索引设计：**
- `userId`: 普通索引（主要查询条件）
- `consumeTime`: 普通索引（按时间排序）
- `userId + consumeTime`: 复合索引（用户查询优化）
- `createdAt`: 普通索引（数据管理需要）

### 11. 系统配置表 (exe_system_config)

用于存储系统级别的配置信息，采用单条记录存储所有配置的极简设计。

```json
{
  "_id": "ObjectId",           // MongoDB自动生成的唯一标识
  "go_proxy_api_url": "String", // Go代理服务API地址
  "encryption_key": "String"    // 数据加解密密钥
}
```

**设计说明：**
- 该表仅存储一条记录，无需额外的标识字段
- 只包含必要的配置字段，去除所有冗余信息
- 每个配置项都是独立的字符串字段，简单直接
- 云函数地址硬编码在前端，无需存储在数据库中
- Token为固定值，无需过期时间和刷新机制
- 无需元数据字段（创建时间、更新时间等）

**配置字段说明：**
- `go_proxy_api_url`: Go中转程序API地址（动态获取）
- `encryption_key`: 用于数据加解密的密钥

**索引设计：**
- 无需额外索引（只有一条记录，直接查询即可）

### 12. 激活码核销表 (exe_activation_codes)

用于存储激活码核销记录，激活码本身不存储在数据库中，而是通过加密算法实时生成和验证。

```json
{
  "_id": "ObjectId",           // MongoDB自动生成的唯一标识
  "activationCode": "String",   // 核销的激活码（加密字符串）
  "redeemedAt": "Date",        // 核销时间
  "redeemedBy": "String",      // 核销用户的用户名
  "quotaAmount": "Number",     // 激活码携带的算力数量
  "createdBy": "String",       // 创建激活码的管理员用户名
  "createdAt": "Date"          // 记录创建时间
}
```

**设计说明：**
- 激活码采用时间戳+算力数量+管理员账号的组合，使用系统配置中的加密密钥进行加密生成
- 只有成功核销的激活码才会存储在此表中，未使用的激活码不占用数据库空间
- 通过加密算法确保激活码的唯一性和安全性
- 支持追溯激活码的创建者和使用者

**索引设计：**
- `activationCode`: 唯一索引（防止重复核销）
- `redeemedBy`: 普通索引（查询用户核销历史）
- `createdBy`: 普通索引（查询管理员创建的激活码）
- `redeemedAt`: 普通索引（按时间排序）

### 13. 管理员算力操作记录表 (exe_quota_operations)

用于记录管理员对用户算力的增减操作，提供完整的操作审计功能。

```json
{
  "_id": "ObjectId",           // MongoDB自动生成的唯一标识
  "operatorUsername": "String", // 操作者用户名（管理员）
  "targetUsername": "String",   // 被操作用户的用户名
  "operationType": "String",    // 操作类型："increase"增加 / "decrease"减少
  "quotaAmount": "Number",      // 操作的算力数量（正数）
  "quotaBefore": "Number",      // 操作前用户算力余额
  "quotaAfter": "Number",       // 操作后用户算力余额
  "reason": "String",           // 操作原因备注（可选）
  "operatedAt": "Date",         // 操作时间
  "createdAt": "Date"           // 记录创建时间
}
```

**设计说明：**
- 每次算力增减操作都会生成一条记录，确保操作的可追溯性
- 记录操作前后的算力余额，便于核对和审计
- 操作类型明确区分增加和减少，便于统计和分析
- 支持操作原因备注，提高操作的透明度

**索引设计：**
- `operatorUsername`: 普通索引（查询特定管理员的操作记录）
- `targetUsername`: 普通索引（查询特定用户的被操作记录）
- `operatedAt`: 普通索引（按时间排序）
- `operatorUsername + operatedAt`: 复合索引（优化管理员操作历史查询）

## 数据安全设计

### 1. 密码加密
- 用户密码使用bcrypt进行加密存储
- 管理员密码使用bcrypt进行加密存储
- 模型API密钥使用AES加密存储

### 2. 数据访问控制
- 用户只能访问自己的数据
- 管理员根据权限访问相应数据
- API密钥仅在后端Go程序中解密使用

### 3. 数据备份
- 定期备份数据库
- 重要操作记录日志
- 支持数据恢复机制

## 数据库操作规范

### 1. 创建操作
- 所有记录必须包含创建时间
- MongoDB自动生成唯一标识（_id）
- 记录创建者信息

### 2. 更新操作
- 更新时自动更新updatedAt字段
- 记录更新者信息
- 保留历史记录（如次数变更）

### 3. 删除操作
- 重要数据采用软删除
- 记录删除操作日志
- 管理员权限验证

### 4. 查询优化
- 合理使用索引
- 分页查询大数据集
- 缓存常用查询结果

## 扩展性考虑

### 1. 分片策略
- 用户数据按_id分片
- 日志数据按时间分片
- 配置数据集中存储

### 2. 性能优化
- 读写分离
- 缓存热点数据
- 异步处理非关键操作

### 3. 监控告警
- 数据库性能监控
- 存储空间监控
- 异常操作告警
