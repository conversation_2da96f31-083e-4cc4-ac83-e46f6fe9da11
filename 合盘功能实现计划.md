# 合盘功能实现详细计划

## 项目概述

本项目是一个基于Flutter的八字命理应用，包含前端界面、Go中转服务和云函数后端。当前已实现单人八字排盘功能和AI对话功能，现需要添加合盘功能，通过在对话系统中创建专门的合盘智能体，让用户可以选择两个已有的八字记录进行合盘分析。

## 现有系统深度调研分析

### 数据库结构 (腾讯云开发)
通过MCP调研发现的关键数据库集合：
- **exe_users** (30条记录): 用户表，包含用户基本信息、算力余额(availableCount)、会员信息等
- **exe_agents** (8条记录): 智能体表，包含八字命理、通用AI助手、紫微斗数大师、家居风水、八字过三关等智能体
- **exe_models** (3条记录): AI模型表，包含基础模型(deepseek-chat)、高阶模型PLUS版(gemini-2.5-pro)等
- **exe_usage_history** (598条记录): 使用历史记录表，记录用户的算力消耗
- **exe_pricing_tiers** (4条记录): 定价层级表，用于算力计费

### 前端结构 (numerology_ai_chat_new)
- **主要技术栈**: Flutter + Riverpod状态管理
- **核心页面**: 
  - BaziScreen: 八字排盘主页面，包含BaziPanel输入面板和BaziHistoryList历史列表
  - ChatScreen: AI对话页面，包含智能体选择、对话历史和聊天面板
  - HomeScreen: 主导航页面
- **数据模型**:
  - BaziInputModel/BaziResultModel: 八字输入和结果模型，支持真太阳时计算
  - ConversationModel: 对话模型，支持智能体、模型选择和八字数据关联
  - ChatMessage: 聊天消息模型，支持流式输出
- **服务层**:
  - BaziService: 八字计算服务，直接调用云函数API
  - BaziStorageService: 本地八字记录存储服务
  - ChatStorageService: 聊天记录本地存储服务

### 中转层 (go_proxy)
- **功能**: 处理AI聊天请求转发，用户认证，权限管理
- **主要文件**:
  - internal/api/handlers/chat_handler.go: 处理聊天请求，包含用户认证和算力扣费
  - internal/proxy: LLM代理服务，处理AI API调用
- **重要发现**: Go代理主要处理AI聊天请求，八字排盘功能直接调用云函数，不经过Go代理

### 后端 (cloudfunctions/exeFunction)
- **入口文件**: index.js，通过action参数路由到不同处理器
- **八字相关**:
  - handlers/bazi.js: 八字分析处理器，调用baziCalculator进行计算
  - 支持真太阳时计算、公农历转换、完整八字排盘
- **智能体和模型管理**:
  - handlers/agents.js: 智能体管理，包含getList和getFullList接口
  - handlers/models.js: 模型管理
- **用户管理**:
  - handlers/user_info.js: 用户信息管理，包含算力更新接口

## 合盘功能需求分析

### 功能目标
1. **智能体集成**: 在现有对话系统中添加合盘专用智能体
2. **八字选择**: 用户可以从已有的八字记录中选择两个进行合盘
3. **对话式分析**: 通过对话形式展示合盘分析结果
4. **历史保存**: 利用现有聊天历史功能保存合盘对话
5. **简洁体验**: 保持现有对话系统的简洁性和易用性

### 核心特性
- **无缝集成**: 完全融入现有对话系统，无需额外界面
- **智能选择**: 智能推荐和快速选择已有八字记录
- **对话式交互**: 自然的对话方式进行合盘分析
- **结果丰富**: 提供详细的合盘分析内容和解释

### 技术约束分析
基于调研发现的关键约束：
1. **数据库限制**: 现有数据库没有专门的合盘记录表，需要利用现有聊天记录存储
2. **API路由**: 云函数已有完整的action路由系统，需要添加新的合盘action
3. **前端存储**: 八字记录通过BaziStorageService本地存储，合盘需要读取这些记录
4. **智能体管理**: 需要在exe_agents表中添加新的合盘智能体记录
5. **算力计费**: 合盘功能需要集成到现有的算力计费系统中

## 实现方案设计

### 第一阶段：数据库和智能体配置

#### 1.1 创建合盘智能体记录
需要在exe_agents集合中添加新的合盘智能体记录：
```json
{
  "agentName": "八字合盘分析师",
  "agentType": "八字",
  "description": "专业八字合盘分析，提供两人命理匹配度解读",
  "agentPrompt": "你是一位专业的八字合盘分析师...[详细提示词]",
  "isActive": true,
  "needLaymanVersion": false,
  "pricingTierId": "对应的定价层级ID",
  "sortOrder": 6,
  "createdBy": "admin",
  "updatedBy": "admin"
}
```

#### 1.2 智能体提示词设计
基于现有八字智能体的提示词结构，设计专门的合盘分析提示词：
- 包含传统合盘理论（五行相生相克、十神关系等）
- 支持结构化的合盘分析输出
- 能够理解和处理两个八字的输入数据
- 提供专业的合盘建议和指导

### 第二阶段：简化的后端实现方案

#### 2.1 采用智能体对话方案
**核心思路**: 不在云函数中实现复杂的合盘算法，而是：
1. 前端将两个八字数据整理成结构化文档
2. 通过Go代理程序调用合盘智能体
3. 利用AI智能体的专业提示词进行合盘分析

#### 2.2 数据流程简化
```
前端选择两个八字 → 整理成合盘文档 → Go代理 → 合盘智能体 → AI分析结果
```

#### 2.3 无需云函数修改
- **优势**: 完全复用现有的聊天系统架构
- **实现**: 合盘功能本质上就是一个特殊的AI对话
- **数据处理**: 在前端将八字数据格式化为智能体可理解的文本

### 第三阶段：前端界面实现（大幅简化）

#### 3.1 八字选择组件
**新增文件**: `lib/src/widgets/bazi_selector_widget.dart`
```dart
class BaziSelectorWidget extends ConsumerWidget {
  // 显示已有八字记录列表
  // 支持选择2个八字记录
  // 显示基本信息预览
  // 生成合盘文档按钮
}
```

#### 3.2 合盘文档生成器
**新增文件**: `lib/src/utils/hepan_document_generator.dart`
```dart
class HepanDocumentGenerator {
  static String generateHepanDocument(
    BaziResultModel firstBazi,
    BaziResultModel secondBazi,
  ) {
    // 将两个八字数据格式化为AI可理解的文档
    // 包含基本信息、八字详情、分析要求等
  }
}
```

#### 3.3 聊天界面集成（最小改动）
**扩展文件**: `lib/src/widgets/chat_panel.dart`
```dart
// 检测是否选择了合盘智能体
if (selectedAgent?.agentName == "八字合盘分析师") {
  // 显示八字选择器
  // 用户选择完成后，自动生成合盘文档并发送
}
```

#### 3.4 无需额外服务层
- **复用现有**: 直接使用现有的GoProxyService发送聊天请求
- **无需新API**: 合盘本质上就是一个特殊格式的聊天消息
- **数据处理**: 在前端完成八字数据的格式化

### 第四阶段：对话流程设计

#### 4.1 对话启动流程
1. 用户选择"八字合盘分析师"智能体
2. 系统自动在输入区域上方显示八字选择器
3. 用户从本地八字记录中选择两个进行合盘
4. 选择完成后，系统自动发送合盘请求并开始分析

#### 4.2 交互设计优化
- **智能提示**: 当选择合盘智能体时，自动显示引导文字
- **选择验证**: 确保用户选择了两个不同的八字记录
- **预览功能**: 显示选中八字的基本信息（姓名、性别、出生时间）
- **一键重置**: 支持清空选择重新开始

#### 4.3 结果呈现策略
- **流式输出**: 利用现有的流式聊天功能展示分析过程
- **结构化显示**: 合盘结果按维度分段显示（五行匹配、性格互补等）
- **交互式问答**: 用户可以针对特定维度进行深入提问
- **历史保存**: 合盘对话自动保存到聊天历史中

### 第五阶段：状态管理（极简方案）

#### 5.1 复用现有八字数据获取
```dart
// 直接使用现有的BaziService.getAllBaziRecords()
// 无需额外的筛选方法，在UI层处理筛选
```

#### 5.2 最小化状态管理
**仅在ChatPanel中添加局部状态**:
```dart
class _ChatPanelState extends ConsumerState<ChatPanel> {
  List<BaziResultModel> selectedBaziList = [];
  bool showBaziSelector = false;
  
  // 检测合盘智能体时显示选择器
  // 选择完成后自动生成并发送合盘文档
}
```

#### 5.3 无需全局状态扩展
- **优势**: 避免复杂的全局状态管理
- **实现**: 合盘选择状态仅在聊天界面中维护
- **数据流**: 选择 → 生成文档 → 发送消息 → 清空选择状态

## 技术实现细节

### 简化的数据流设计
基于现有聊天系统的合盘数据流：
1. **智能体选择**: 用户在ChatScreen中选择合盘智能体
2. **八字选择**: ChatPanel检测到合盘智能体，显示BaziSelectorWidget
3. **数据准备**: 从BaziStorageService读取本地八字记录
4. **文档生成**: 将两个八字数据格式化为AI可理解的合盘文档
5. **消息发送**: 通过现有的GoProxyService发送聊天请求
6. **AI分析**: 合盘智能体基于提示词进行专业分析
7. **流式返回**: 通过现有的流式聊天机制展示分析结果
8. **历史保存**: 利用ChatStorageService保存合盘对话

### 简化的数据流设计
```javascript
// 前端整理合盘文档
const hepanDocument = `
【八字合盘分析请求】

第一人信息：
姓名：张三
性别：男
出生时间：1990年5月15日 10:30
八字：庚午年 辛巳月 甲子日 己巳时
[详细八字分析数据...]

第二人信息：
姓名：李四
性别：女  
出生时间：1992年8月20日 14:20
八字：壬申年 戊申月 丁卯日 丁未时
[详细八字分析数据...]

请进行专业的八字合盘分析。
`;

// 通过现有聊天API发送
const chatRequest = {
  agentId: "合盘智能体ID",
  modelId: "选择的模型ID", 
  messages: [
    {
      role: "user",
      content: hepanDocument
    }
  ]
};
```

### 状态管理策略
基于现有Riverpod架构：
- **ChatProvider**: 扩展支持合盘模式状态
- **AgentService**: 自动加载合盘智能体
- **BaziStorageService**: 复用现有八字记录读取逻辑
- **新增HepanProvider**: 专门管理合盘选择状态

### 错误处理机制
- **网络错误**: 复用现有的HTTP错误处理
- **数据验证**: 确保选择了两个有效的八字记录
- **计算失败**: 提供友好的错误提示和重试选项
- **存储错误**: 处理本地八字记录读取失败的情况

## 开发时间估算

### 第一阶段：数据库和智能体配置 (0.5天)
- 在exe_agents集合中添加合盘智能体记录
- 设计专业的合盘分析提示词
- 配置定价层级和排序

### 第二阶段：智能体提示词优化 (0.5天)
- 优化合盘智能体的提示词，确保能理解结构化的八字文档
- 测试智能体对合盘文档的理解和分析能力

### 第三阶段：前端文档生成器 (1天)
- 开发八字数据到合盘文档的转换工具
- 确保生成的文档格式符合智能体要求
- 测试文档生成的准确性

### 第四阶段：前端界面组件 (2天)
- 开发八字选择器组件
- 在聊天面板中集成合盘模式
- 实现选择到发送的完整流程

### 第五阶段：集成测试和优化 (1天)
- 端到端功能测试
- 用户体验优化
- 错误处理完善

### 第六阶段：测试和优化 (1-2天)
- 端到端功能测试
- 用户体验优化
- 性能调优和错误处理

**总计**: 5个工作日（大幅缩短！）

### 关键里程碑
- **第1天**: 数据库智能体配置完成，提示词优化完成
- **第2天**: 合盘文档生成器开发完成，可生成标准格式文档
- **第4天**: 前端界面集成完成，可进行完整的合盘流程
- **第5天**: 测试和优化完成，功能上线

### 简化方案的优势
1. **开发周期缩短60%**: 从10天缩短到5天
2. **技术风险降低**: 无需开发复杂的合盘算法
3. **维护成本低**: 复用现有架构，无额外的API接口
4. **扩展性好**: 后续可通过优化智能体提示词来改进分析质量

## 风险评估与应对

### 技术风险
1. **数据库约束**: 现有数据库结构可能不完全适配合盘功能
   - **应对**: 充分利用现有ConversationModel的baziData字段存储合盘相关数据
   - **备选方案**: 如需要，可在exe_agents表中添加合盘专用配置字段

2. **算力计费集成**: 合盘功能需要正确集成到现有计费系统
   - **应对**: 复用现有的算力扣费逻辑，确保合盘也会正确扣费
   - **测试重点**: 验证合盘操作的算力消耗计算

3. **本地存储兼容**: 八字记录的本地存储格式可能需要调整
   - **应对**: 基于现有BaziStorageService，确保向后兼容
   - **版本控制**: 如需修改存储格式，提供数据迁移方案

### 用户体验风险
1. **选择流程复杂**: 从多个八字记录中选择两个可能造成困扰
   - **应对**: 提供清晰的选择界面，显示八字基本信息
   - **优化方案**: 支持搜索和筛选功能，快速定位目标记录

2. **结果理解难度**: 合盘分析结果可能过于专业
   - **应对**: 设计通俗易懂的分析报告格式
   - **智能体优化**: 在提示词中强调要用通俗语言解释专业概念

### 项目风险
1. **现有功能影响**: 新功能可能影响现有聊天和八字功能
   - **应对**: 采用渐进式开发，确保现有功能不受影响
   - **测试策略**: 重点测试现有功能的回归测试

2. **用户接受度**: 用户可能不理解或不使用合盘功能
   - **应对**: 提供清晰的功能介绍和使用指南
   - **推广策略**: 在八字排盘完成后提示用户可以进行合盘分析

## 测试策略

### 单元测试
- 数据模型测试
- 计算逻辑测试
- 服务层功能测试

### 集成测试
- 前后端接口测试
- 数据流完整性测试
- 存储功能测试

### 用户测试
- 界面易用性测试
- 功能完整性测试
- 性能压力测试

## 部署计划

### 开发环境部署
1. 本地开发环境配置
2. 云函数开发环境部署
3. 数据库测试环境准备

### 测试环境部署
1. 完整功能测试环境
2. 性能测试环境
3. 用户验收测试环境

### 生产环境部署
1. 灰度发布策略
2. 数据迁移方案
3. 回滚预案准备

## 后续维护计划

### 功能迭代
- 根据用户反馈优化合盘算法
- 增加更多分析维度
- 提供个性化设置选项

### 性能优化
- 持续监控系统性能
- 优化计算算法效率
- 改进用户界面响应速度

### 用户支持
- 提供详细的使用文档
- 建立用户反馈机制
- 定期更新功能说明

## 实施建议

### 开发优先级
1. **高优先级**: 后端合盘API开发，这是整个功能的核心
2. **中优先级**: 前端八字选择组件，直接影响用户体验
3. **低优先级**: 界面美化和高级功能，可在基础功能完成后迭代

### 测试策略
1. **单元测试**: 重点测试合盘算法的准确性
2. **集成测试**: 验证合盘功能与现有聊天系统的集成
3. **用户测试**: 邀请用户测试合盘功能的易用性

### 上线策略
1. **灰度发布**: 先向部分用户开放合盘功能
2. **数据监控**: 监控合盘功能的使用情况和错误率
3. **用户反馈**: 收集用户反馈，持续优化功能

## 总结

基于对现有系统的深度调研，本合盘功能实现计划充分考虑了系统的技术约束和架构特点。通过复用现有的智能体系统、聊天机制和本地存储，可以以最小的改动实现合盘功能。

**关键成功因素**:
1. **充分复用**: 最大化利用现有的技术架构和组件
2. **渐进开发**: 分阶段实现，确保每个阶段都有可交付的成果
3. **用户体验**: 保持与现有功能一致的操作体验
4. **数据安全**: 确保合盘数据的本地存储和隐私保护

本计划为开发团队提供了基于实际系统调研的详细实施路径，可以确保合盘功能的成功交付和良好的用户体验。
