# 管理员激活码系统实现计划

## 🎉 项目完成状态

**项目已全部完成！** 所有核心功能均已实现并通过测试。

### 已完成的核心功能
- ✅ **数据库结构调整**：新增isWeakAdmin字段、exe_activation_codes表、exe_quota_operations表
- ✅ **exeWeakAdmin云函数**：独立的管理员云函数，包含所有管理功能
- ✅ **exeFunction扩展**：支持激活码验证和注册功能
- ✅ **Vue Web管理界面**：完整的管理员Web管理系统
- ✅ **加密安全机制**：AES-256加密的激活码生成和验证
- ✅ **权限控制系统**：基于JWT的管理员权限验证
- ✅ **操作记录追踪**：完整的操作历史和核销记录

### 部署信息
- **exeWeakAdmin云函数**：https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeWeakAdmin
- **exeFunction云函数**：https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction
- **管理员Web界面**：本地运行 `cd weak-admin-web && npm run dev`，访问 http://localhost:3000

### 测试账号信息
#### 管理员测试账号
- **用户名**: weakadmin001
- **密码**: 12345678
- **权限**: 管理员权限，可以生成激活码和管理用户算力

#### 普通用户测试账号
- **testuser002**: 已注册用户，当前算力700
- **testuser003**: 已注册用户，当前算力2500（通过激活码获得2000 + 管理员增加500）

#### 可用的激活码（未使用）
以下激活码可用于测试注册功能：
1. `U2FsdGVkX18kU3XEGplFPVGkfU2MmL8u4u9x7U073fT33wSJ7gEHpm6Q9FTVGX6+qzV5RUfBcKcTObR0n9poDOe7mw0e4+Cg3M+yaaobx+6JQ18yxPfyHdN3g9K/fBci6geYrjUeGw/IVVKEWrBA7NUEKxuWcqW4WCaNpITOBv4=` (2000算力)
2. `U2FsdGVkX19w+X7UmymUEVn7QeS63pzZwqbzVI5nLbqdMfaMeFuHc3bg2nNstBMwOTk3zEqrLldw1I2Y7kvBJQTRC2VbU8U/yvXPwN3JGvwdDz0iBBgDwZqtaGq8rdrNQvIVp5q0vBtzzUm3DYWmbSklYgpRpGC8D9hZNvganfs=` (2000算力)

### 测试验证结果
- ✅ 管理员登录功能正常
- ✅ 激活码生成功能正常（已生成3个2000算力激活码）
- ✅ 激活码验证和注册功能正常（testuser003成功获得2000算力）
- ✅ 用户信息查询功能正常
- ✅ 算力修改功能正常（testuser003算力增加到2500）
- ✅ 操作历史查询功能正常
- ✅ 激活码核销历史查询功能正常

## 本地运行说明

### 启动管理员Web界面
```bash
cd weak-admin-web
npm install  # 首次运行需要安装依赖
npm run dev  # 启动开发服务器
```
访问地址：http://localhost:3000

### 测试流程建议
1. 使用管理员账号 `weakadmin001/12345678` 登录
2. 在激活码管理页面生成新的激活码
3. 在用户算力管理页面查询现有用户信息
4. 修改用户算力（增加或减少）
5. 在操作历史页面查看所有操作记录
6. 可以使用提供的激活码测试注册新用户功能

## 项目概述

本计划旨在实现一个管理员激活码系统，允许具有管理员权限的用户通过简单的Web界面创建激活码，新用户可以在注册时使用激活码获得相应的算力。该系统采用加密算法生成激活码，无需预存储，只记录已核销的激活码信息。

## 系统架构分析

### 现有技术栈
- **前端主程序**: Flutter桌面应用 (numerology_ai_chat_new)
- **管理后台**: Flutter Web应用 (numerology_ai_chat_new_admin)
- **云函数**: 腾讯云开发 (exeFunction, exeAdmin)
- **Go代理服务**: HTTP API中转服务
- **数据库**: 腾讯云开发MongoDB

### 数据库结构变更
1. **用户表 (exe_users)** 新增字段：
   - isWeakAdmin: 布尔值，标识是否为管理员
2. **新增激活码核销表 (exe_activation_codes)**：
   - 存储已核销的激活码记录
   - 包含核销时间、核销用户、算力数量等信息
3. **新增管理员算力操作记录表 (exe_quota_operations)**：
   - 记录管理员对用户算力的增减操作
   - 包含操作者、被操作用户、操作类型、数量、操作前后余额等信息

## 功能需求分析

### 管理员权限设计
- 管理员具有以下权限：
  * 创建激活码
  * 查询用户信息和算力
  * 增加或减少用户算力
  * 查看操作历史记录
- 不能访问全局管理后台的其他功能
- 使用用户表的账号密码进行登录验证
- 通过独立的Web端进行操作

### 激活码生成规则
- 组成要素：时间戳 + 可用额度 + 管理员账号
- 加密方式：使用exe_system_config表中的encryption_key字段进行AES加密
- 唯一性保证：时间戳精确到毫秒，确保每个激活码唯一
- 安全性：加密后的字符串无法直接解析出原始信息

### 激活码使用流程
1. 新用户注册时可选择输入激活码
2. 系统验证激活码的有效性（解密并验证格式）
3. 检查激活码是否已被使用（查询核销表）
4. 核销成功后给用户增加相应算力并记录核销信息

### 用户算力管理流程
1. 管理员输入用户名查询用户信息
2. 系统显示用户基本信息和当前算力余额
3. 管理员选择操作类型（增加/减少）并输入数值
4. 可选填写操作原因备注
5. 提交后系统更新用户算力并记录操作历史
6. 操作记录包含：操作时间、操作者、被操作用户、操作类型、数量、操作前后余额

## 技术实现方案

### Web端开发方案
根据项目需求，采用独立Web应用方案：

**独立Vue Web应用**
- 技术栈：Vue 3 + Element Plus UI框架
- 部署方式：独立部署，不依赖现有Admin后台
- 云函数：创建独立的exeWeakAdmin云函数
- 优点：
  * 完全独立，不影响现有系统
  * Vue生态成熟，开发效率高
  * Element Plus提供丰富的UI组件
  * 独立云函数确保权限隔离
- 特点：
  * 响应式设计，支持多设备访问
  * 简洁的用户界面，专注核心功能
  * 独立的权限系统和API接口

### 权限系统设计
管理员Web端采用复用现有JWT系统的简化方案：

1. **复用JWT认证系统**：
   - 使用现有的JWT密钥、算法和验证逻辑
   - 所有用户使用相同格式的token，无需额外字段
   - 支持用户同时登录桌面端和Web端而不相互影响

2. **权限验证机制**：
   - 登录时：验证用户名密码 + 检查数据库isWeakAdmin字段
   - 请求时：验证token有效性 + 根据userId查询数据库isWeakAdmin字段
   - 实时权限控制：权限变更立即生效，无需重新登录

3. **兼容性设计**：
   - 现有系统登录逻辑完全不变
   - 新增管理员登录接口，但生成相同格式的token
   - 向下兼容，不影响现有功能

4. **权限验证流程**：
   - exeFunction/exeAdmin：验证token有效性（现有逻辑不变）
   - exeWeakAdmin：验证token有效性 + 查询数据库isWeakAdmin字段
   - 确保权限隔离和安全性

5. **同时登录支持**：
   - 同一用户可以同时在不同端登录
   - 桌面端：普通用户功能（聊天等）
   - Web端：管理员功能（激活码、算力管理）
   - 使用相同token格式，权限通过数据库字段区分

### 云函数架构设计
采用独立云函数方案，确保系统隔离：

**新建exeWeakAdmin云函数**：
1. **管理员登录验证**：weakAdminLogin处理器
   - 验证用户表账号密码
   - 检查数据库isWeakAdmin字段
   - 复用现有JWT系统，生成标准格式token

2. **激活码管理**：
   - generateActivationCodes：批量生成激活码
   - getActivationHistory：查询激活码核销历史

3. **用户算力管理**：
   - getUserInfo：根据用户名查询用户信息和算力
   - modifyUserQuota：增加或减少用户算力
   - getQuotaOperations：查询算力操作记录

4. **操作历史查询**：
   - getOperationHistory：查询管理员操作历史

**exeFunction云函数扩展**（仅限注册功能）：
1. **激活码验证**：validateActivationCode处理器
2. **激活码核销**：修改register处理器，支持激活码参数

### 加密算法实现
使用Node.js的crypto-js库实现AES加密：
- 加密内容：JSON字符串包含时间戳、算力数量、创建者账号、版本号
- 加密密钥：从exe_system_config表动态获取encryption_key字段
- 输出格式：Base64编码的加密字符串，便于复制和传输
- 解密验证：支持格式验证、时间戳检查、重复使用检查

## 详细开发计划

### 第一阶段：数据库和云函数基础功能 ✅ 已完成
1. **数据库结构调整** ✅ 已完成
   - ✅ 修改exe_users表结构，新增isWeakAdmin字段（默认false）
   - ✅ 创建exe_activation_codes表及相关索引
   - ✅ 创建exe_quota_operations表及相关索引
   - ✅ 更新现有用户数据，确保兼容性
   - ✅ 验证数据库操作的正确性

2. **创建exeWeakAdmin云函数** ✅ 已完成
   - ✅ 初始化独立云函数项目结构
   - ✅ 复用现有JWT工具函数和验证逻辑
   - ✅ 开发weakAdminLogin处理器（验证isWeakAdmin字段，生成标准token）
   - ✅ 实现管理员权限验证中间件（token验证 + 数据库isWeakAdmin字段查询）
   - ✅ 实现激活码加密生成算法
   - ✅ 开发generateActivationCodes处理器
   - ✅ 开发getUserInfo处理器
   - ✅ 开发modifyUserQuota处理器
   - ✅ 开发各种历史查询处理器
   - ✅ 云函数已部署到：https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeWeakAdmin

3. **exeFunction扩展**（仅限注册功能） ✅ 已完成
   - ✅ 实现激活码解密验证算法
   - ✅ 新增validateActivationCode处理器
   - ✅ 修改register处理器，支持activationCode参数

4. **测试验证** ✅ 已完成
   - ✅ 使用MCP工具测试所有新增云函数接口
   - ✅ 验证加密解密算法的安全性和正确性
   - ✅ 测试完整的激活码生成、验证、核销流程
   - ✅ 测试用户算力增减功能
   - ✅ 验证权限控制的有效性

### 第二阶段：Vue Web端开发 ✅ 已完成
1. **项目初始化** ✅ 已完成
   - ✅ 创建Vue 3项目（使用Vite）
   - ✅ 安装Element Plus、Vue Router、Pinia、Axios等依赖
   - ✅ 配置项目结构和开发环境
   - ✅ 设置代码规范和构建配置

2. **基础架构开发** ✅ 已完成
   - ✅ 实现HTTP请求封装（Axios拦截器）
   - ✅ 创建Pinia状态管理（用户状态、加载状态）
   - ✅ 配置Vue Router路由和守卫
   - ✅ 实现JWT token管理（复用现有token格式，权限通过后端验证）
   - ✅ 创建通用组件和工具函数

3. **页面组件开发** ✅ 已完成
   - ✅ 登录页面（LoginView）
     * ✅ 表单验证和提交
     * ✅ 错误处理和提示
   - ✅ 仪表板页面（DashboardView）
     * ✅ 个人信息展示
     * ✅ 功能快捷入口
     * ✅ 统计数据展示
   - ✅ 激活码管理页面（ActivationCodesView）
     * ✅ 生成表单和结果展示
     * ✅ 历史记录查询
   - ✅ 用户算力管理页面（UserQuotaView）
     * ✅ 用户查询和信息展示
     * ✅ 算力操作表单
     * ✅ 操作历史记录
   - ✅ 操作历史页面（HistoryView）
     * ✅ 筛选和分页功能
     * ✅ 数据导出功能

4. **UI/UX优化** ✅ 已完成
   - ✅ 响应式设计适配
   - ✅ 加载状态和错误处理
   - ✅ 用户体验优化
   - ✅ 界面美化和交互优化
   - ✅ Vue项目本地运行：`cd weak-admin-web && npm run dev`

### 第三阶段：部署和测试 ✅ 已完成
1. **Vue项目开发完成** ✅ 已完成
   - ✅ 配置Vite构建工具，使用esbuild压缩
   - ✅ 完成所有页面和功能开发
   - ✅ 配置本地开发环境
   - ✅ 项目可通过 `npm run dev` 本地运行

2. **全面功能测试** ✅ 已完成
   - ✅ 测试管理员登录功能
   - ✅ 测试激活码生成功能（生成3个2000算力激活码）
   - ✅ 测试激活码验证和注册功能（testuser003成功注册获得2000算力）
   - ✅ 测试用户信息查询功能
   - ✅ 测试算力修改功能（为testuser003增加500算力）
   - ✅ 测试操作历史查询功能
   - ✅ 测试激活码核销历史查询功能
   - ✅ 验证权限控制和错误处理

3. **系统集成验证** ✅ 已完成
   - ✅ 验证云函数间的数据一致性
   - ✅ 验证加密解密算法的安全性
   - ✅ 验证数据库操作的正确性
   - ✅ 验证Web端与云函数的通信

### 第四阶段：前端注册流程改造（可选扩展）
1. **注册页面修改**
   - 在RegisterScreen中添加激活码输入框组件
     * 可选字段，带有"激活码（可选）"标签
     * 实时格式验证（非空时检查格式）
     * 异步验证功能（检查激活码有效性）
     * 验证状态指示器（加载中、有效、无效）
   - 优化表单布局和用户体验
   - 添加激活码相关的错误提示和帮助文本

2. **注册逻辑调整**
   - 修改AuthProvider的register方法，支持activationCode参数
   - 修改AuthService的register方法，传递激活码到云函数
   - 更新注册成功后的提示信息，显示获得的算力数量
   - 确保激活码验证失败时的错误处理

3. **用户体验优化**
   - 添加激活码输入的帮助说明
   - 实现激活码粘贴功能优化
   - 注册成功后显示获得的算力信息
   - 优化错误提示的用户友好性

### 第五阶段：系统集成和测试（可选扩展）
1. **端到端测试**
   - 测试完整的激活码生成到使用流程
   - 验证权限控制的正确性
   - 测试异常情况处理

2. **性能优化**
   - 优化数据库查询性能
   - 实现必要的缓存机制
   - 确保系统稳定性

3. **文档更新**
   - 更新数据库结构文档
   - 编写用户使用手册
   - 更新系统架构文档

## 安全设计考虑

### 激活码安全机制
1. **加密强度**：使用AES-256加密算法，密钥长度32字符
2. **时间戳验证**：激活码包含生成时间，可设置有效期限制
3. **唯一性保证**：时间戳精确到毫秒 + 随机数，确保唯一性
4. **防重放攻击**：核销表记录已使用的激活码，防止重复使用
5. **密钥管理**：加密密钥存储在数据库中，支持定期更换

### 权限控制安全
1. **双重验证**：前端路由守卫 + 后端API权限检查
2. **最小权限原则**：管理员只能访问指定功能，无法访问全局管理
3. **会话管理**：使用JWT token，设置合理的过期时间
4. **操作审计**：所有敏感操作记录详细日志，包括操作者、时间、内容

### 数据安全保护
1. **敏感数据加密**：激活码生成算法中的关键信息加密存储
2. **SQL注入防护**：使用参数化查询，防止注入攻击
3. **输入验证**：严格的前端和后端输入验证，防止恶意输入
4. **错误信息控制**：避免在错误信息中泄露敏感信息

## 风险评估和应对措施

### 技术风险
1. **加密安全性**：使用成熟的AES加密算法，定期更换加密密钥
2. **激活码重复**：通过时间戳精确到毫秒和数据库唯一索引防止重复
3. **权限控制**：严格的前端和后端双重权限验证
4. **性能影响**：加密解密操作可能影响性能，需要优化算法实现

### 业务风险
1. **激活码滥用**：实现激活码使用次数限制和有效期控制
2. **管理员权限滥用**：详细的操作日志记录和审计功能
3. **系统兼容性**：充分测试确保不影响现有功能
4. **用户体验**：激活码输入和验证流程需要简单易用

## 部署和维护计划

### 部署策略
1. **分阶段部署**：先部署云函数，再部署前端界面
2. **灰度发布**：先在测试环境验证，再逐步推广到生产环境
3. **回滚准备**：保留原有功能，确保可以快速回滚

### 维护计划
1. **监控告警**：实现关键指标监控和异常告警
2. **日志分析**：定期分析系统日志，优化性能
3. **安全审计**：定期检查权限设置和操作日志

## 预期效果

1. **提升用户体验**：新用户可以通过激活码快速获得算力，降低使用门槛
2. **简化管理流程**：管理员可以独立处理激活码相关事务，减少全局管理员工作量
3. **增强系统安全性**：通过加密算法和权限控制确保系统安全
4. **提高运营效率**：自动化的激活码生成和核销流程，减少人工干预

## 技术实现细节

### JWT Token设计
复用现有JWT系统，所有用户使用统一token格式：

**统一token格式（所有用户相同）：**
```json
{
  "userId": "user123",
  "username": "weakadmin001",
  "iat": 1640995200,
  "exp": 1640998800,
  "aud": "user",
  "iss": "numerology-ai-chat"
}
```

**权限验证逻辑：**
```javascript
// exeFunction/exeAdmin：验证token有效性即可（现有逻辑不变）
const decoded = verifyToken(token)

// exeWeakAdmin：验证token + 查询数据库权限
const decoded = verifyToken(token)
const user = await userCollection.findById(decoded.userId)
if (!user || !user.isWeakAdmin) {
  throw new Error('无管理员权限')
}
```

**方案优势：**
- 所有用户使用相同token格式，简化系统复杂度
- 权限变更实时生效，无需重新登录
- 完全复用现有JWT系统，无需额外开发

### 激活码格式设计
激活码采用以下格式进行加密：
```
原始数据: {
  "timestamp": 1640995200000,
  "quota": 1000,
  "creator": "admin001",
  "version": "1.0"
}
加密后: "AES_ENCRYPTED_BASE64_STRING"
```

### 权限验证流程
1. **管理员登录时**：
   - 验证用户名密码
   - 查询数据库isWeakAdmin字段
   - 如果isWeakAdmin=true，生成标准JWT token
   - 如果isWeakAdmin不为true，拒绝登录

2. **管理员请求时**：
   - 验证JWT token有效性，获取userId
   - 根据userId查询数据库user.isWeakAdmin字段
   - 如果isWeakAdmin=true，允许访问
   - 如果isWeakAdmin不为true，拒绝访问

### 数据库操作流程
1. **生成激活码时**：不写入数据库，仅返回加密字符串
2. **验证激活码时**：解密验证格式，查询核销表检查是否已使用
3. **核销激活码时**：写入核销表，更新用户算力，记录操作日志

### API接口设计

**exeWeakAdmin云函数接口**（独立云函数）：
1. **weakAdminLogin** - 管理员登录
   - 输入：username, password
   - 输出：token, userInfo（用户名、权限类型）

2. **generateActivationCodes** - 批量生成激活码
   - 输入：quantity（数量）, quotaAmount（算力数量）, token
   - 输出：activationCodes[]（激活码数组）

3. **getUserInfo** - 查询用户信息
   - 输入：username, token
   - 输出：用户基本信息、当前算力余额

4. **modifyUserQuota** - 修改用户算力
   - 输入：targetUsername, operationType（"increase"/"decrease"）, quotaAmount, reason（可选）, token
   - 输出：操作结果、操作后余额

5. **getQuotaOperations** - 查询算力操作记录
   - 输入：page, pageSize, targetUsername（可选）, token
   - 输出：操作记录列表

6. **getActivationHistory** - 查询激活码核销历史
   - 输入：page, pageSize, token
   - 输出：核销历史列表

7. **getOperationHistory** - 查询管理员操作历史
   - 输入：page, pageSize, token
   - 输出：操作历史列表

**exeFunction云函数接口**（仅扩展注册功能）：
1. **validateActivationCode** - 验证激活码有效性
   - 输入：activationCode
   - 输出：验证结果（有效性、算力数量等）

2. **register** - 修改现有注册接口
   - 输入：username, password, email, phone, activationCode（新增可选）
   - 输出：注册结果

### 前端技术栈
- **框架**：Vue 3 + Composition API
- **UI库**：Element Plus
- **路由**：Vue Router 4
- **状态管理**：Pinia
- **HTTP客户端**：Axios
- **构建工具**：Vite

### 前端路由设计
独立Vue应用的路由结构：
- `/login` - 登录页面
- `/dashboard` - 仪表板（首页）
- `/activation-codes` - 激活码管理页面
- `/user-quota` - 用户算力管理页面
- `/history` - 操作历史页面

### 前端界面设计
1. **登录页面**：
   - 简洁的登录表单（用户名、密码）
   - Element Plus的el-form组件
   - 表单验证和错误提示
   - 登录状态指示器

2. **仪表板页面**：
   - 个人信息卡片（用户名、权限类型）
   - 功能快捷入口（激活码生成、用户算力管理）
   - 最近操作统计（今日操作次数、本周操作次数）
   - 最近操作记录列表（最新5条）

3. **激活码管理页面**：
   - 生成区域：
     * 数量选择器（1-100个，使用el-input-number）
     * 算力数量输入框（正整数验证）
     * 生成按钮（带加载状态）
   - 结果展示区域：
     * 激活码列表（el-table）
     * 全选复制功能
     * 导出功能（TXT格式）
   - 历史记录区域：
     * 分页显示已生成的激活码
     * 核销状态查看

4. **用户算力管理页面**：
   - 用户查询区域：
     * 用户名输入框（支持模糊搜索）
     * 查询按钮
   - 用户信息展示：
     * 用户基本信息卡片
     * 当前算力余额（突出显示）
   - 算力操作区域：
     * 操作类型选择（增加/减少，使用el-radio）
     * 数量输入框（正整数验证）
     * 原因备注（可选，el-input textarea）
     * 提交按钮（带确认对话框）
   - 操作历史：
     * 该用户的算力变更记录

5. **操作历史页面**：
   - 筛选区域：
     * 时间范围选择器（el-date-picker）
     * 操作类型筛选（激活码生成/算力操作）
     * 目标用户筛选
   - 记录列表：
     * 分页表格（el-table + el-pagination）
     * 操作详情查看
     * 导出功能

### 权限控制机制
1. **登录验证**：检查用户isWeakAdmin字段
2. **路由守卫**：前端路由级别的权限控制
3. **API鉴权**：云函数级别的权限验证
4. **操作审计**：所有操作记录到数据库

## 开发时间估算

### 第一阶段（3-4天）
- 数据库结构调整：0.5天
- exeWeakAdmin云函数开发：2-2.5天（复用JWT系统，减少开发量）
- exeFunction扩展：0.5天
- 接口测试验证：0.5天

### 第二阶段（4-6天）
- Vue项目初始化和基础架构：1天（复用token格式，简化认证逻辑）
- 登录和仪表板页面：1-1.5天
- 激活码管理页面：1.5-2天
- 用户算力管理页面：1.5-2天
- 操作历史页面：1天

### 第三阶段（2天）
- 注册页面修改：1天
- 注册逻辑调整：1天
- 前端测试：包含在各阶段中

### 第四阶段（2天）
- 端到端测试：1天
- 性能优化和部署：0.5天
- 文档更新：0.5天

**总计：11-14个工作日**（比原计划减少2-4天）

### 技术栈优势
- **Vue生态成熟**：开发效率更高，组件丰富
- **Element Plus**：UI开发快速，用户体验好
- **独立部署**：不影响现有系统，维护简单
- **JWT完全复用**：统一token格式，极简实现，零额外开发
- **同时登录**：支持用户同时使用桌面端和Web端
- **实时权限控制**：通过数据库字段实现安全的权限验证
- **权限实时生效**：管理员可随时调整用户权限，立即生效

## 总结

本计划通过扩展现有系统架构，实现了一个安全、高效的管理员激活码系统。该系统充分利用了现有的技术栈和基础设施，最小化了开发成本和部署风险。通过分阶段的开发和部署策略，确保系统的稳定性和可靠性。

关键优势：
1. **完全独立**：独立的Vue Web应用和云函数，不影响现有系统
2. **技术先进**：Vue 3 + Element Plus，现代化的前端技术栈
3. **功能完整**：激活码管理 + 用户算力管理，满足管理员所有需求
4. **安全可靠**：AES加密 + JWT认证 + 权限隔离，多重安全保障
5. **操作审计**：完整的操作记录，支持追溯和审计
6. **用户友好**：Element Plus提供优秀的用户体验
7. **易于维护**：独立部署和维护，不与现有系统耦合
8. **JWT复用**：完全复用现有认证系统，统一token格式，极简实现
9. **同时登录**：支持用户同时登录桌面端和Web端，互不影响
10. **实时权限**：权限变更立即生效，无需重新登录

### 核心功能特性
1. **激活码系统**：
   - 无存储设计，通过加密算法实时生成
   - 支持批量生成和导出
   - 完整的核销记录和历史查询

2. **算力管理系统**：
   - 用户查询和信息展示
   - 算力增减操作（支持原因备注）
   - 操作前后余额记录
   - 完整的操作审计日志

3. **权限控制**：
   - 基于用户表的isWeakAdmin字段，实时验证
   - 完全复用现有JWT认证系统，统一token格式
   - 支持同时登录多端，权限隔离安全可靠
   - 权限变更立即生效，无需重新登录
   - 前后端双重权限验证
