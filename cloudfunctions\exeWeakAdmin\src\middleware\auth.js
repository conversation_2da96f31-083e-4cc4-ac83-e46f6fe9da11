const jwt = require('jsonwebtoken')
const { userCollection } = require('../utils/db')
const { createBusinessError, ERROR_CODES } = require('./error_handler')
const logger = require('../utils/logger')

// JWT密钥 - 与exeFunction保持一致
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production'
const JWT_EXPIRES_IN = '1h' // Access Token有效期 - 与exeFunction保持一致
const REFRESH_TOKEN_EXPIRES_IN = '30d' // Refresh Token有效期

/**
 * 生成Access Token
 * @param {object} payload Token载荷
 * @returns {string} JWT Token
 */
function generateAccessToken(payload) {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN,
    issuer: 'numerology-ai-chat',
    audience: 'user'
  })
}

/**
 * 生成Refresh Token
 * @param {object} payload Token载荷
 * @returns {string} JWT Token
 */
function generateRefreshToken(payload) {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: REFRESH_TOKEN_EXPIRES_IN,
    issuer: 'numerology-ai-chat',
    audience: 'refresh'
  })
}

/**
 * 验证Token
 * @param {string} token JWT Token
 * @param {string} audience 预期的audience
 * @returns {object} 解码后的载荷
 */
function verifyToken(token, audience = 'user') {
  try {
    return jwt.verify(token, JWT_SECRET, {
      issuer: 'numerology-ai-chat',
      audience
    })
  } catch (error) {
    throw error // 让上层处理JWT相关错误
  }
}

/**
 * 生成Token对
 * @param {string} userId 用户ID
 * @param {string} username 用户名
 * @returns {object} Token对象
 */
function generateTokenPair(userId, username) {
  const payload = { userId, username }
  
  const accessToken = generateAccessToken(payload)
  const refreshToken = generateRefreshToken(payload)
  
  return {
    accessToken,
    refreshToken,
    expiresIn: JWT_EXPIRES_IN
  }
}

/**
 * 管理员权限验证中间件
 * @param {string} token JWT Token
 * @returns {Promise<object>} 用户信息
 */
async function weakAdminAuthMiddleware(token) {
  if (!token) {
    throw createBusinessError(ERROR_CODES.TOKEN_MISSING, '请求未携带Token', 401)
  }

  try {
    // 验证Token有效性
    const decoded = verifyToken(token, 'user')

    // 获取用户信息
    const user = await userCollection.findById(decoded.userId)
    if (!user) {
      throw createBusinessError(
        ERROR_CODES.USER_NOT_FOUND,
        '用户不存在',
        401
      )
    }

    // 检查用户状态
    if (user.status !== '激活') {
      throw createBusinessError(
        ERROR_CODES.USER_DISABLED,
        '用户账户已被禁用',
        401
      )
    }

    // 检查管理员权限
    if (!user.isWeakAdmin) {
      throw createBusinessError(
        ERROR_CODES.INSUFFICIENT_PERMISSIONS,
        '无管理员权限',
        403
      )
    }

    logger.info('管理员权限验证成功', {
      userId: user._id,
      username: user.username
    })

    // 返回用户信息（不包含敏感信息）
    return {
      userId: user._id,
      username: user.username,
      isWeakAdmin: user.isWeakAdmin
    }

  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw createBusinessError(ERROR_CODES.TOKEN_EXPIRED, 'Token已过期', 401)
    }
    if (error.name === 'JsonWebTokenError') {
      throw createBusinessError(ERROR_CODES.TOKEN_INVALID, 'Token无效', 401)
    }
    
    logger.error('管理员权限验证失败', {
      error: error.message
    })
    throw error
  }
}

module.exports = {
  generateAccessToken,
  generateRefreshToken,
  generateTokenPair,
  verifyToken,
  weakAdminAuthMiddleware,
  JWT_SECRET,
  JWT_EXPIRES_IN,
  REFRESH_TOKEN_EXPIRES_IN
}
