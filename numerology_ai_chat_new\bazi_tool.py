import requests
import json
import tkinter as tk
from tkinter import messagebox, scrolledtext, filedialog
import threading

def create_ui():
    """创建并运行GUI【create and run GUI】"""
    window = tk.Tk()
    window.title("八字排盘工具")
    window.geometry("500x650")

    # --- 数据变量 ---
    name_var = tk.StringVar()
    gender_var = tk.StringVar(value="男")
    calendar_type_var = tk.StringVar(value="公历")
    birth_year_var = tk.StringVar()
    birth_month_var = tk.StringVar()
    birth_day_var = tk.StringVar()
    is_leap_month_var = tk.BooleanVar()
    birth_hour_var = tk.StringVar()
    birth_minute_var = tk.StringVar()
    birth_place_var = tk.StringVar()

    # --- UI 组件 ---
    main_frame = tk.Frame(window, padx=10, pady=10)
    main_frame.pack(fill="both", expand=True)

    # --- 输入表单 ---
    form_frame = tk.Label<PERSON>rame(main_frame, text="输入信息", padx=10, pady=10)
    form_frame.pack(fill="x", expand=True)
    
    fields = [
        ("姓名:", name_var, 0),
        ("出生年 (例如: 1985):", birth_year_var, 3),
        ("出生月 (例如: 9):", birth_month_var, 4),
        ("出生日 (例如: 16):", birth_day_var, 5),
        ("出生时 (0-23):", birth_hour_var, 7),
        ("出生分 (0-59):", birth_minute_var, 8),
        ("出生地:", birth_place_var, 9)
    ]
    
    for text, var, row in fields:
        tk.Label(form_frame, text=text).grid(row=row, column=0, sticky="w", pady=2)
        tk.Entry(form_frame, textvariable=var).grid(row=row, column=1, sticky="ew", pady=2)

    # 性别
    tk.Label(form_frame, text="性别:").grid(row=1, column=0, sticky="w", pady=2)
    gender_frame = tk.Frame(form_frame)
    tk.Radiobutton(gender_frame, text="男", variable=gender_var, value="男").pack(side="left")
    tk.Radiobutton(gender_frame, text="女", variable=gender_var, value="女").pack(side="left")
    gender_frame.grid(row=1, column=1, sticky="w")

    # 历法
    tk.Label(form_frame, text="历法:").grid(row=2, column=0, sticky="w", pady=2)
    calendar_frame = tk.Frame(form_frame)
    tk.Radiobutton(calendar_frame, text="公历", variable=calendar_type_var, value="公历").pack(side="left")
    tk.Radiobutton(calendar_frame, text="农历", variable=calendar_type_var, value="农历").pack(side="left")
    calendar_frame.grid(row=2, column=1, sticky="w")
    
    # 是否闰月
    tk.Checkbutton(form_frame, text="是否闰月", variable=is_leap_month_var).grid(row=6, column=1, sticky="w")

    form_frame.grid_columnconfigure(1, weight=1)

    # --- 结果显示区域 ---
    result_frame = tk.LabelFrame(main_frame, text="分析结果", padx=10, pady=10)
    result_frame.pack(fill="both", expand=True, pady=(10, 0))
    result_text = scrolledtext.ScrolledText(result_frame, wrap=tk.WORD, height=10)
    result_text.pack(fill="both", expand=True)

    def perform_request():
        """在线程中执行API请求【perform API request in a thread】"""
        result_text.delete(1.0, tk.END)
        result_text.insert(tk.END, "正在分析中，请稍候...")
        analyze_button.config(state="disabled")

        try:
            # 校验和格式化输入【validate and format input】
            birth_date = f"{birth_year_var.get()}-{int(birth_month_var.get()):02d}-{int(birth_day_var.get()):02d}"
            birth_time = f"{int(birth_hour_var.get()):02d}:{int(birth_minute_var.get()):02d}"

            params = {
                "name": name_var.get(),
                "gender": gender_var.get(),
                "calendarType": calendar_type_var.get(),
                "birthDate": birth_date,
                "birthTime": birth_time,
                "isLeapMonth": is_leap_month_var.get(),
                "birthPlace": birth_place_var.get()
            }

            if not all([params["name"], params["birthPlace"], birth_year_var.get(), birth_month_var.get(), birth_day_var.get(), birth_hour_var.get(), birth_minute_var.get()]):
                raise ValueError("所有输入框都必须填写。")

            request_body = {
                "action": "baziAnalyze",
                "params": params
            }

            api_url = "https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/main"

            # 根据云函数日志，我们必须直接发送包含action和params的JSON对象作为请求体。
            # 之前的错误在于将这个对象错误地封装在了一个'body'字段中。
            response = requests.post(api_url, json=request_body, timeout=30)
            response.raise_for_status()

            data = response.json()

            if data.get("code") == 0 and data.get("data") and data["data"].get("textResult"):
                result = data["data"]["textResult"]
            else:
                result = f"错误: {data.get('message', '未能获取到有效结果。')}\n原始响应: {response.text}"

        except ValueError as e:
            result = f"输入错误: {e}"
        except requests.exceptions.RequestException as e:
            result = f"请求失败: {e}"
        except Exception as e:
            result = f"发生未知错误: {e}"

        # 在主线程中更新UI
        window.after(0, update_ui_with_result, result)

    def update_ui_with_result(result):
        """在主线程中安全地更新UI【safely update UI in the main thread】"""
        result_text.delete(1.0, tk.END)
        result_text.insert(tk.END, result)
        analyze_button.config(state="normal")
        
    def start_request_thread():
        """启动请求线程【start request thread】"""
        thread = threading.Thread(target=perform_request)
        thread.daemon = True
        thread.start()

    def copy_to_clipboard():
        """将结果复制到剪贴板【copy result to clipboard】"""
        result = result_text.get(1.0, tk.END)
        if result.strip() and "正在分析中" not in result:
            window.clipboard_clear()
            window.clipboard_append(result)
            messagebox.showinfo("成功", "结果已复制到剪贴板！")
        else:
            messagebox.showwarning("提示", "没有可复制的内容。")

    def export_to_txt():
        """将结果导出为TXT文件【export result to TXT file】"""
        result = result_text.get(1.0, tk.END)
        if not (result.strip() and "正在分析中" not in result):
            messagebox.showwarning("提示", "没有可导出的内容。")
            return
            
        try:
            name = name_var.get()
            year = birth_year_var.get()
            month = birth_month_var.get()
            day = birth_day_var.get()

            if not all([name, year, month, day]):
                 raise ValueError("请填写姓名和完整的出生年月日以生成文件名。")

            default_filename = f"{name}_{year}{int(month):02d}{int(day):02d}.txt"
            
            filepath = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                initialfile=default_filename,
                title="选择保存位置"
            )

            if filepath:
                with open(filepath, "w", encoding="utf-8") as f:
                    f.write(result)
                messagebox.showinfo("成功", f"文件已保存至:\n{filepath}")

        except ValueError as e:
            messagebox.showerror("输入错误", str(e))
        except Exception as e:
            messagebox.showerror("保存失败", f"保存文件时发生错误: {e}")

    # --- 按钮 ---
    buttons_frame = tk.Frame(main_frame)
    buttons_frame.pack(pady=10)

    analyze_button = tk.Button(buttons_frame, text="开始分析", command=start_request_thread)
    analyze_button.pack(side="left", padx=5)

    copy_button = tk.Button(buttons_frame, text="一键复制", command=copy_to_clipboard)
    copy_button.pack(side="left", padx=5)

    export_button = tk.Button(buttons_frame, text="一键导出txt", command=export_to_txt)
    export_button.pack(side="left", padx=5)

    window.mainloop()


if __name__ == "__main__":
    # 不再使用命令行输入【no longer use command line input】
    # main()
    create_ui() 