[Unit]
Description=Go Proxy Service for AI Chat
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/your/go_proxy
ExecStart=/path/to/your/go_proxy/go_proxy_linux
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=go_proxy

# 环境变量 (可选，也可以使用 .env 文件)
Environment=SERVER_PORT=8080
Environment=CLOUDFUNC_BASE_URL=https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/path/to/your/go_proxy

[Install]
WantedBy=multi-user.target
