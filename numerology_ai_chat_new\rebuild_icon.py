#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新构建图标 - 简化版本
"""

from PIL import Image
import os

def rebuild_icon():
    """重新构建图标"""
    jpg_path = "assets/images/logo.png"
    ico_path = "windows/runner/resources/app_icon.ico"
    
    print("🔄 重新构建应用图标...")
    
    if not os.path.exists(jpg_path):
        print(f"❌ 源文件不存在: {jpg_path}")
        return False
    
    try:
        # 打开并处理图片
        with Image.open(jpg_path) as img:
            print(f"📏 源图片: {img.size}")
            
            # 转换为RGBA
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            # 裁剪为正方形
            min_size = min(img.size)
            left = (img.size[0] - min_size) // 2
            top = (img.size[1] - min_size) // 2
            right = left + min_size
            bottom = top + min_size
            img_square = img.crop((left, top, right, bottom))
            
            # 生成多种尺寸
            sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
            icon_images = []
            
            for size in sizes:
                resized = img_square.resize(size, Image.Resampling.LANCZOS)
                icon_images.append(resized)
                print(f"✅ {size[0]}x{size[1]}")
            
            # 保存ICO
            icon_images[0].save(
                ico_path,
                format='ICO',
                sizes=sizes,
                append_images=icon_images[1:]
            )
            
            print(f"✅ 图标已保存: {ico_path}")
            return True
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

if __name__ == "__main__":
    rebuild_icon()
