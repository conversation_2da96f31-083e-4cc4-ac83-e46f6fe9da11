import{w as z}from"./weakAdminService-c6c1aace.js";import{_ as O}from"./_plugin-vue_export-helper-c27b6911.js";import{r as y,b as k,o as G,c as d,y as H,d as b,e as q,f as n,g as t,w as o,z as V,A as I,E as l,m,t as _,F as M,B as P,C as X}from"./index-0bf154dd.js";const J={class:"activation-codes"},K={class:"card-header"},Q={class:"header-actions"},W={class:"codes-list"},Y={class:"code-content"},Z={class:"code-text"},ee={class:"code-info"},te={class:"card-header"},oe={class:"code-display"},ae={class:"pagination"},se={__name:"ActivationCodesView",setup(ne){const C=y(),p=k({quantity:1,quotaAmount:1e3}),S={quantity:[{required:!0,message:"请输入数量",trigger:"blur"},{type:"number",min:1,max:100,message:"数量必须在1-100之间",trigger:"blur"}],quotaAmount:[{required:!0,message:"请输入算力数量",trigger:"blur"},{type:"number",min:1,max:1e5,message:"算力数量必须在1-100000之间",trigger:"blur"}]},h=y(!1),g=y([]),w=y(!1),x=y([]),c=k({page:1,pageSize:20,total:0}),L=async()=>{var a;if(C.value)try{if(!await C.value.validate())return;h.value=!0;const r=await z.generateActivationCodes(p.quantity,p.quotaAmount);r.success?(g.value=r.data.activationCodes,l.success(r.message),v()):l.error(((a=r.error)==null?void 0:a.message)||"生成失败")}catch(e){console.error("Generate error:",e),l.error("生成激活码失败")}finally{h.value=!1}},U=async a=>{try{await navigator.clipboard.writeText(a),l.success("复制成功")}catch(e){console.error("Copy error:",e),l.error("复制失败")}},B=async()=>{try{const a=g.value.map(e=>e.code).join(`
`);await navigator.clipboard.writeText(a),l.success("全部复制成功")}catch(a){console.error("Copy all error:",a),l.error("复制失败")}},D=()=>{const a=g.value.map(u=>`${u.code} (${u.quotaAmount}算力)`).join(`
`),e=new Blob([a],{type:"text/plain"}),r=URL.createObjectURL(e),i=document.createElement("a");i.href=r,i.download=`激活码_${new Date().toISOString().slice(0,10)}.txt`,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(r),l.success("导出成功")},v=async()=>{var a;try{w.value=!0;const e=await z.getActivationHistory(c.page,c.pageSize);e.success?(x.value=e.data.data,c.total=e.data.total):l.error(((a=e.error)==null?void 0:a.message)||"加载历史记录失败")}catch(e){console.error("Load history error:",e),l.error("加载历史记录失败")}finally{w.value=!1}},R=a=>a?new Date(a).toLocaleString("zh-CN"):"-";return G(()=>{v()}),(a,e)=>{const r=d("el-input-number"),i=d("el-form-item"),u=d("el-button"),j=d("el-form"),A=d("el-card"),f=d("el-table-column"),F=d("el-tag"),N=d("el-table"),T=d("el-pagination"),E=H("loading");return b(),q("div",J,[e[11]||(e[11]=n("div",{class:"page-header"},[n("h2",null,"激活码管理"),n("p",null,"生成和管理激活码")],-1)),t(A,{class:"generate-card",shadow:"hover"},{header:o(()=>e[4]||(e[4]=[n("div",{class:"card-header"},[n("span",null,"生成激活码")],-1)])),default:o(()=>[t(j,{ref_key:"generateFormRef",ref:C,model:p,rules:S,inline:"",class:"generate-form"},{default:o(()=>[t(i,{label:"数量",prop:"quantity"},{default:o(()=>[t(r,{modelValue:p.quantity,"onUpdate:modelValue":e[0]||(e[0]=s=>p.quantity=s),min:1,max:100,placeholder:"请输入数量"},null,8,["modelValue"])]),_:1}),t(i,{label:"算力数量",prop:"quotaAmount"},{default:o(()=>[t(r,{modelValue:p.quotaAmount,"onUpdate:modelValue":e[1]||(e[1]=s=>p.quotaAmount=s),min:1,max:1e5,placeholder:"请输入算力数量"},null,8,["modelValue"])]),_:1}),t(i,null,{default:o(()=>[t(u,{type:"primary",loading:h.value,onClick:L},{default:o(()=>[m(_(h.value?"生成中...":"生成激活码"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1}),g.value.length>0?(b(),V(A,{key:0,class:"result-card",shadow:"hover"},{header:o(()=>[n("div",K,[e[7]||(e[7]=n("span",null,"生成结果",-1)),n("div",Q,[t(u,{size:"small",onClick:B},{default:o(()=>e[5]||(e[5]=[m("复制全部")])),_:1,__:[5]}),t(u,{size:"small",onClick:D},{default:o(()=>e[6]||(e[6]=[m("导出TXT")])),_:1,__:[6]})])])]),default:o(()=>[n("div",W,[(b(!0),q(M,null,P(g.value,(s,$)=>(b(),q("div",{key:$,class:"code-item"},[n("div",Y,[n("span",Z,_(s.code),1),n("span",ee,_(s.quotaAmount)+"算力",1)]),t(u,{size:"small",onClick:re=>U(s.code)},{default:o(()=>e[8]||(e[8]=[m("复制")])),_:2,__:[8]},1032,["onClick"])]))),128))])]),_:1})):I("",!0),t(A,{class:"history-card",shadow:"hover"},{header:o(()=>[n("div",te,[e[10]||(e[10]=n("span",null,"核销历史",-1)),t(u,{size:"small",onClick:v},{default:o(()=>e[9]||(e[9]=[m("刷新")])),_:1,__:[9]})])]),default:o(()=>[X((b(),V(N,{data:x.value,stripe:"",style:{width:"100%"}},{default:o(()=>[t(f,{prop:"activationCode",label:"激活码",width:"300"},{default:o(({row:s})=>[n("span",oe,_(s.activationCode.substring(0,20))+"...",1)]),_:1}),t(f,{prop:"quotaAmount",label:"算力数量",width:"120"}),t(f,{prop:"usedBy",label:"使用者",width:"150"}),t(f,{prop:"usedAt",label:"使用时间",width:"180"},{default:o(({row:s})=>[m(_(R(s.usedAt)),1)]),_:1}),t(f,{prop:"status",label:"状态",width:"100"},{default:o(({row:s})=>[t(F,{type:"success"},{default:o(()=>[m(_(s.status==="used"?"已使用":"未使用"),1)]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[E,w.value]]),n("div",ae,[t(T,{"current-page":c.page,"onUpdate:currentPage":e[2]||(e[2]=s=>c.page=s),"page-size":c.pageSize,"onUpdate:pageSize":e[3]||(e[3]=s=>c.pageSize=s),total:c.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:v,onCurrentChange:v},null,8,["current-page","page-size","total"])])]),_:1})])}}},ce=O(se,[["__scopeId","data-v-d7cccf08"]]);export{ce as default};
