import 'package:json_annotation/json_annotation.dart';

part 'package_model.g.dart';

@JsonSerializable()
class PaymentPackage {
  @JsonKey(name: '_id')
  final String id;
  final String packageName;
  final String packageDescription;
  final int originalPrice;
  final int price;
  @JsonKey(name: 'quotaCount')
  final int quotaAmount;
  final bool isActive;
  final int sortOrder;
  final List<String>? tags;
  final bool? isRecommended;
  final int? validDays;
  final double? discountRate;
  final int? maxPurchaseCount;
  final int? minPurchaseCount;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? createdBy;
  final String? updatedBy;

  PaymentPackage({
    required this.id,
    required this.packageName,
    required this.packageDescription,
    required this.originalPrice,
    required this.price,
    required this.quotaAmount,
    required this.isActive,
    required this.sortOrder,
    this.tags,
    this.isRecommended,
    this.validDays,
    this.discountRate,
    this.maxPurchaseCount,
    this.minPurchaseCount,
    required this.createdAt,
    required this.updatedAt,
    this.createdBy,
    this.updatedBy,
  });

  factory PaymentPackage.fromJson(Map<String, dynamic> json) => _$PaymentPackageFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentPackageToJson(this);
}

@JsonSerializable()
class CreatePackageRequest {
  final String packageName;
  final String packageDescription;
  final double originalPrice;
  final double currentPrice;
  final int quotaAmount;
  final bool isActive;
  final int sortOrder;
  final String? promotionText;
  final DateTime? promotionStartTime;
  final DateTime? promotionEndTime;

  CreatePackageRequest({
    required this.packageName,
    required this.packageDescription,
    required this.originalPrice,
    required this.currentPrice,
    required this.quotaAmount,
    this.isActive = true,
    this.sortOrder = 0,
    this.promotionText,
    this.promotionStartTime,
    this.promotionEndTime,
  });

  factory CreatePackageRequest.fromJson(Map<String, dynamic> json) => _$CreatePackageRequestFromJson(json);
  Map<String, dynamic> toJson() => _$CreatePackageRequestToJson(this);
}

@JsonSerializable()
class UpdatePackageRequest {
  final String? packageName;
  final String? packageDescription;
  final double? originalPrice;
  final double? currentPrice;
  final int? quotaAmount;
  final bool? isActive;
  final int? sortOrder;
  final String? promotionText;
  final DateTime? promotionStartTime;
  final DateTime? promotionEndTime;

  UpdatePackageRequest({
    this.packageName,
    this.packageDescription,
    this.originalPrice,
    this.currentPrice,
    this.quotaAmount,
    this.isActive,
    this.sortOrder,
    this.promotionText,
    this.promotionStartTime,
    this.promotionEndTime,
  });

  factory UpdatePackageRequest.fromJson(Map<String, dynamic> json) => _$UpdatePackageRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdatePackageRequestToJson(this);
}
