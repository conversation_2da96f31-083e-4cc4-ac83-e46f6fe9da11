const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 支付日志管理处理器
 */
class PaymentLogManagement {
  /**
   * 获取支付日志列表
   */
  static async listPaymentLogs(event) {
    try {
      const { page = 1, limit = 20, search, operationType, status } = event;
      
      // 构建查询条件
      let query = {};
      
      if (search) {
        // 搜索订单ID或用户ID
        query.$or = [
          { orderId: { $regex: search, $options: 'i' } },
          { userId: { $regex: search, $options: 'i' } }
        ];
      }
      
      if (operationType) {
        query.operationType = operationType;
      }
      
      if (status) {
        query.status = status;
      }
      
      // 计算跳过的记录数
      const skip = (page - 1) * limit;
      
      // 获取总数
      const totalResult = await db.collection('exe_payment_logs')
        .where(query)
        .count();
      
      const total = totalResult.total;
      
      // 获取支付日志列表
      const logsResult = await db.collection('exe_payment_logs')
        .where(query)
        .orderBy('timestamp', 'desc')
        .skip(skip)
        .limit(limit)
        .get();
      
      return {
        code: 0,
        message: '获取支付日志列表成功',
        data: {
          logs: logsResult.data,
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      console.error('获取支付日志列表失败:', error);
      return {
        code: -1,
        message: '获取支付日志列表失败: ' + error.message
      };
    }
  }

  /**
   * 获取支付日志详情
   */
  static async getPaymentLogDetail(event) {
    try {
      const { logId } = event;
      
      if (!logId) {
        return {
          code: -1,
          message: '日志ID不能为空'
        };
      }
      
      const logResult = await db.collection('exe_payment_logs')
        .doc(logId)
        .get();
      
      if (!logResult.data) {
        return {
          code: -1,
          message: '支付日志不存在'
        };
      }
      
      return {
        code: 0,
        message: '获取支付日志详情成功',
        data: logResult.data
      };
    } catch (error) {
      console.error('获取支付日志详情失败:', error);
      return {
        code: -1,
        message: '获取支付日志详情失败: ' + error.message
      };
    }
  }

  /**
   * 获取支付日志统计信息
   */
  static async getPaymentLogStats(event) {
    try {
      // 获取总日志数
      const totalLogsResult = await db.collection('exe_payment_logs').count();
      const totalLogs = totalLogsResult.total;
      
      // 获取各状态日志数
      const successResult = await db.collection('exe_payment_logs')
        .where({ status: 'SUCCESS' })
        .count();
      
      const failedResult = await db.collection('exe_payment_logs')
        .where({ status: 'FAILED' })
        .count();
      
      const pendingResult = await db.collection('exe_payment_logs')
        .where({ status: 'PENDING' })
        .count();
      
      // 获取各操作类型统计
      const createOrderResult = await db.collection('exe_payment_logs')
        .where({ operationType: 'CREATE_ORDER' })
        .count();
      
      const fuiouOrderResult = await db.collection('exe_payment_logs')
        .where({ operationType: 'FUIOU_UNIFIED_ORDER' })
        .count();
      
      const callbackResult = await db.collection('exe_payment_logs')
        .where({ operationType: 'PAYMENT_CALLBACK' })
        .count();
      
      // 获取今日日志数
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayLogsResult = await db.collection('exe_payment_logs')
        .where({
          timestamp: db.command.gte(today)
        })
        .count();
      
      return {
        code: 0,
        message: '获取支付日志统计成功',
        data: {
          totalLogs,
          successLogs: successResult.total,
          failedLogs: failedResult.total,
          pendingLogs: pendingResult.total,
          createOrderLogs: createOrderResult.total,
          fuiouOrderLogs: fuiouOrderResult.total,
          callbackLogs: callbackResult.total,
          todayLogs: todayLogsResult.total
        }
      };
    } catch (error) {
      console.error('获取支付日志统计失败:', error);
      return {
        code: -1,
        message: '获取支付日志统计失败: ' + error.message
      };
    }
  }

  /**
   * 删除支付日志（软删除）
   */
  static async deletePaymentLog(event) {
    try {
      const { logId } = event;
      
      if (!logId) {
        return {
          code: -1,
          message: '日志ID不能为空'
        };
      }
      
      // 检查日志是否存在
      const logResult = await db.collection('exe_payment_logs')
        .doc(logId)
        .get();
      
      if (!logResult.data) {
        return {
          code: -1,
          message: '支付日志不存在'
        };
      }
      
      // 软删除：标记为已删除
      await db.collection('exe_payment_logs')
        .doc(logId)
        .update({
          isDeleted: true,
          deletedAt: new Date()
        });
      
      return {
        code: 0,
        message: '删除支付日志成功'
      };
    } catch (error) {
      console.error('删除支付日志失败:', error);
      return {
        code: -1,
        message: '删除支付日志失败: ' + error.message
      };
    }
  }
}

module.exports = PaymentLogManagement;
