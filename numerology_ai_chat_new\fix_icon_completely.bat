@echo off
chcp 65001 >nul
echo ════════════════════════════════════════════
echo    完全修复应用图标问题
echo ════════════════════════════════════════════
echo.

echo 📋 本脚本将执行以下操作:
echo 1. 检查源图片文件
echo 2. 强制生成新的图标文件
echo 3. 运行Flutter图标生成器
echo 4. 清理所有缓存
echo 5. 重新构建应用
echo 6. 提供图标缓存清理指导
echo.

pause

echo 🔍 步骤1: 检查源图片文件...
if not exist "assets\images\logo.png" (
    echo ❌ 源图片文件不存在: assets\images\logo.png
    echo 💡 请确保将您的logo图片放在 assets\images\logo.png
    pause
    exit /b 1
)
echo ✅ 源图片文件存在

echo.
echo 🔧 步骤2: 强制生成新的图标文件...
python force_update_icon.py
if %errorlevel% neq 0 (
    echo ⚠️ Python图标生成失败，继续使用Flutter方式...
)

echo.
echo 🔄 步骤3: 运行Flutter图标生成器...
dart run flutter_launcher_icons
if %errorlevel% neq 0 (
    echo ❌ Flutter图标生成失败！
    echo 💡 尝试手动安装插件: flutter pub add dev:flutter_launcher_icons
    pause
    exit /b 1
)
echo ✅ Flutter图标生成完成

echo.
echo 🧹 步骤4: 清理所有缓存...
echo 正在清理Flutter缓存...
flutter clean
if %errorlevel% neq 0 (
    echo ⚠️ Flutter缓存清理失败，继续...
)

echo 正在清理构建目录...
if exist "build" (
    rmdir /s /q "build" 2>nul
    echo ✅ 构建目录已清理
)

echo.
echo 📦 步骤5: 获取依赖包...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ 获取依赖包失败！
    pause
    exit /b 1
)
echo ✅ 依赖包获取完成

echo.
echo 🔨 步骤6: 重新构建应用...
flutter build windows --release
if %errorlevel% neq 0 (
    echo ❌ 构建失败！
    pause
    exit /b 1
)

echo.
echo 🎉 应用构建完成！
echo ════════════════════════════════════════════
echo 📁 可执行文件位置: build\windows\x64\runner\Release\numerology_ai_chat.exe
echo.

echo 🔍 检查图标是否更新...
if exist "build\windows\x64\runner\Release\numerology_ai_chat.exe" (
    echo ✅ 应用文件已生成
    echo 💡 请检查exe文件图标是否已更新
) else (
    echo ❌ 应用文件未找到
)

echo.
echo 📝 如果图标仍然是默认的，请执行以下步骤:
echo.
echo 🔄 方法1: 重启Windows资源管理器
echo    1. 按 Ctrl+Shift+Esc 打开任务管理器
echo    2. 找到 "Windows资源管理器" 进程
echo    3. 右键选择 "重新启动"
echo    4. 等待桌面重新加载
echo.
echo 🧹 方法2: 清理Windows图标缓存
echo    1. 按 Win+R 打开运行对话框
echo    2. 输入: %%localappdata%%\Microsoft\Windows\Explorer
echo    3. 删除所有 iconcache*.db 文件
echo    4. 重启计算机
echo.
echo 🔧 方法3: 手动验证图标文件
echo    1. 检查文件: windows\runner\resources\app_icon.ico
echo    2. 确保文件大小大于10KB
echo    3. 双击查看图标是否正确显示
echo.
echo 💡 提示: 有时Windows需要一些时间来更新图标缓存
echo    如果以上方法都不行，请重启计算机后再检查
echo.
pause
