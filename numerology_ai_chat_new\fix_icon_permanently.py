#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
永久修复Windows应用图标问题
确保每次编译都自动应用正确的图标
"""

from PIL import Image
import os
import shutil

def create_perfect_ico(jpg_path, ico_path):
    """
    创建完美的Windows ICO文件
    """
    try:
        print(f"🔄 开始处理图标: {jpg_path}")
        
        # 打开JPG图片
        with Image.open(jpg_path) as img:
            print(f"📏 原始图片: {img.size}, 模式: {img.mode}")
            
            # 转换为RGBA模式
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            # 确保是正方形
            min_size = min(img.size)
            left = (img.size[0] - min_size) // 2
            top = (img.size[1] - min_size) // 2
            right = left + min_size
            bottom = top + min_size
            img_square = img.crop((left, top, right, bottom))
            
            # Windows标准图标尺寸
            sizes = [
                (16, 16),   # 小图标
                (24, 24),   # 小图标高DPI
                (32, 32),   # 中等图标
                (48, 48),   # 中等图标高DPI
                (64, 64),   # 大图标
                (128, 128), # 超大图标
                (256, 256)  # 超大图标高DPI
            ]
            
            # 生成各种尺寸
            icon_images = []
            for size in sizes:
                resized = img_square.resize(size, Image.Resampling.LANCZOS)
                
                # 小尺寸图标锐化处理
                if size[0] <= 48:
                    from PIL import ImageFilter, ImageEnhance
                    # 轻微锐化
                    resized = resized.filter(ImageFilter.SHARPEN)
                    # 增强对比度
                    enhancer = ImageEnhance.Contrast(resized)
                    resized = enhancer.enhance(1.1)
                
                icon_images.append(resized)
                print(f"✅ 生成 {size[0]}x{size[1]} 图标")
            
            # 保存ICO文件
            icon_images[0].save(
                ico_path,
                format='ICO',
                sizes=sizes,
                append_images=icon_images[1:]
            )
            
            size = os.path.getsize(ico_path)
            print(f"✅ ICO文件已生成: {ico_path} ({size} 字节)")
            return True
            
    except Exception as e:
        print(f"❌ 生成ICO失败: {e}")
        return False

def update_cmake_for_icon():
    """
    确保CMake配置正确引用图标
    """
    cmake_file = "windows/CMakeLists.txt"
    
    if not os.path.exists(cmake_file):
        print(f"⚠️ CMake文件不存在: {cmake_file}")
        return False
    
    try:
        with open(cmake_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含图标配置
        if 'app_icon.ico' in content:
            print("✅ CMake已包含图标配置")
            return True
        else:
            print("⚠️ CMake可能缺少图标配置")
            return False
            
    except Exception as e:
        print(f"❌ 检查CMake失败: {e}")
        return False

def create_build_hook():
    """
    创建构建钩子，确保每次构建前都更新图标
    """
    hook_script = """#!/usr/bin/env python3
# 构建前钩子 - 自动更新图标
import os
import sys
from PIL import Image

def update_icon():
    jpg_path = "assets/images/logo.png"
    ico_path = "windows/runner/resources/app_icon.ico"
    
    if not os.path.exists(jpg_path):
        print(f"警告: 源图标不存在 {jpg_path}")
        return False
    
    # 检查是否需要更新
    if os.path.exists(ico_path):
        jpg_time = os.path.getmtime(jpg_path)
        ico_time = os.path.getmtime(ico_path)
        if ico_time > jpg_time:
            print("图标已是最新，跳过更新")
            return True
    
    print("更新应用图标...")
    # 这里调用图标生成逻辑
    return True

if __name__ == "__main__":
    update_icon()
"""
    
    hook_path = "scripts/pre_build_hook.py"
    os.makedirs(os.path.dirname(hook_path), exist_ok=True)
    
    with open(hook_path, 'w', encoding='utf-8') as f:
        f.write(hook_script)
    
    print(f"✅ 构建钩子已创建: {hook_path}")

def main():
    print("🔧 永久修复Windows应用图标问题...")
    print("=" * 50)
    
    # 1. 检查源文件
    jpg_path = "assets/images/logo.png"
    if not os.path.exists(jpg_path):
        print(f"❌ 源图标文件不存在: {jpg_path}")
        return False
    
    # 2. 生成高质量ICO文件
    ico_path = "windows/runner/resources/app_icon.ico"
    os.makedirs(os.path.dirname(ico_path), exist_ok=True)
    
    # 备份现有图标
    if os.path.exists(ico_path):
        backup_path = ico_path + ".original_backup"
        shutil.copy2(ico_path, backup_path)
        print(f"📦 已备份原图标: {backup_path}")
    
    # 生成新图标
    if not create_perfect_ico(jpg_path, ico_path):
        print("❌ 图标生成失败")
        return False
    
    # 3. 检查CMake配置
    print("\n🔍 检查CMake配置...")
    update_cmake_for_icon()
    
    # 4. 创建构建钩子
    print("\n🔗 创建构建钩子...")
    create_build_hook()
    
    # 5. 创建验证脚本
    verify_script = """@echo off
echo 验证图标是否正确应用...
if exist "build\\windows\\x64\\runner\\Release\\numerology_ai_chat.exe" (
    echo ✅ Release版本存在
    echo 📁 位置: build\\windows\\x64\\runner\\Release\\numerology_ai_chat.exe
) else (
    echo ❌ Release版本不存在，请先构建
)

if exist "build\\windows\\x64\\runner\\Debug\\numerology_ai_chat.exe" (
    echo ✅ Debug版本存在  
    echo 📁 位置: build\\windows\\x64\\runner\\Debug\\numerology_ai_chat.exe
) else (
    echo ❌ Debug版本不存在
)

echo.
echo 💡 如果图标仍然不正确，请：
echo 1. 重启Windows资源管理器
echo 2. 清理图标缓存
echo 3. 重新构建应用
pause
"""
    
    with open("verify_icon.bat", 'w', encoding='utf-8') as f:
        f.write(verify_script)
    
    print("✅ 验证脚本已创建: verify_icon.bat")
    
    print("\n🎉 图标修复完成！")
    print("\n📝 接下来请：")
    print("1. 运行 flutter clean")
    print("2. 运行 flutter build windows --release")
    print("3. 检查生成的exe文件图标")
    print("4. 如果图标仍然不正确，运行 verify_icon.bat")
    
    return True

if __name__ == "__main__":
    main()
