#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超高清图标生成器 - 创建真正的高清Windows应用图标
使用PNG格式嵌入ICO文件来保持最高质量
"""

import os
import sys
import shutil
from PIL import Image, ImageFilter, ImageEnhance
from datetime import datetime
import io

def print_status(message, status="info"):
    """打印带状态的消息"""
    icons = {
        "info": "🔄",
        "success": "✅", 
        "warning": "⚠️",
        "error": "❌",
        "check": "🔍",
        "hd": "🎯",
        "ultra": "💎"
    }
    print(f"{icons.get(status, '📝')} {message}")

def backup_existing_icon():
    """备份现有图标文件"""
    ico_path = "windows/runner/resources/app_icon.ico"
    if os.path.exists(ico_path):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"{ico_path}.ultra_backup_{timestamp}"
        shutil.copy2(ico_path, backup_path)
        print_status(f"已备份现有图标到: {backup_path}", "success")
        return backup_path
    return None

def enhance_image_quality(img, size):
    """增强图像质量"""
    # 对于超大尺寸图标，保持原始质量
    if size >= 256:
        # 轻微增强锐度
        enhancer = ImageEnhance.Sharpness(img)
        img = enhancer.enhance(1.05)
    
    # 对于中等尺寸图标
    elif size >= 48:
        enhancer = ImageEnhance.Sharpness(img)
        img = enhancer.enhance(1.15)
        
        enhancer = ImageEnhance.Contrast(img)
        img = enhancer.enhance(1.05)
    
    # 对于小尺寸图标，需要更强的增强
    else:
        enhancer = ImageEnhance.Sharpness(img)
        img = enhancer.enhance(1.3)
        
        enhancer = ImageEnhance.Contrast(img)
        img = enhancer.enhance(1.1)
        
        # 应用锐化滤镜
        img = img.filter(ImageFilter.UnsharpMask(radius=0.5, percent=120, threshold=2))
    
    return img

def create_ico_manually(icon_images, ico_path):
    """手动创建ICO文件以确保高质量"""
    
    # ICO文件头结构
    ico_header = bytearray(6)
    ico_header[0:2] = (0).to_bytes(2, 'little')  # Reserved
    ico_header[2:4] = (1).to_bytes(2, 'little')  # Type (1 = ICO)
    ico_header[4:6] = len(icon_images).to_bytes(2, 'little')  # Number of images
    
    # 计算每个图像的目录条目和数据
    directory_entries = bytearray()
    image_data = bytearray()
    current_offset = 6 + (16 * len(icon_images))  # Header + directory entries
    
    for img in icon_images:
        width, height = img.size
        
        # 将图像保存为PNG格式（保持高质量）
        png_buffer = io.BytesIO()
        img.save(png_buffer, format='PNG', optimize=False, compress_level=0)
        png_data = png_buffer.getvalue()
        
        # 创建目录条目
        entry = bytearray(16)
        entry[0] = width if width < 256 else 0  # Width
        entry[1] = height if height < 256 else 0  # Height
        entry[2] = 0  # Color palette (0 for PNG)
        entry[3] = 0  # Reserved
        entry[4:6] = (1).to_bytes(2, 'little')  # Color planes
        entry[6:8] = (32).to_bytes(2, 'little')  # Bits per pixel
        entry[8:12] = len(png_data).to_bytes(4, 'little')  # Image size
        entry[12:16] = current_offset.to_bytes(4, 'little')  # Image offset
        
        directory_entries.extend(entry)
        image_data.extend(png_data)
        current_offset += len(png_data)
    
    # 写入ICO文件
    with open(ico_path, 'wb') as f:
        f.write(ico_header)
        f.write(directory_entries)
        f.write(image_data)

def create_ultra_hd_icon():
    """创建超高清Windows图标文件"""
    print_status("💎 开始创建超高清Windows应用图标", "ultra")
    print("=" * 80)
    
    # 源文件和目标文件
    source_path = "assets/images/logo.png"
    ico_path = "windows/runner/resources/app_icon.ico"
    
    # 检查源文件
    if not os.path.exists(source_path):
        print_status(f"源图片文件不存在: {source_path}", "error")
        return False
    
    print_status(f"找到源图片: {source_path}", "success")
    
    # 备份现有图标
    backup_existing_icon()
    
    try:
        # 打开源图片
        with Image.open(source_path) as source_img:
            print_status(f"源图片尺寸: {source_img.size}", "check")
            print_status(f"源图片模式: {source_img.mode}", "check")
            
            # 转换为RGBA模式以支持透明度
            if source_img.mode != 'RGBA':
                source_img = source_img.convert('RGBA')
                print_status("已转换为RGBA模式", "info")
            
            # 确保图片是正方形（如果不是，则居中裁剪）
            width, height = source_img.size
            if width != height:
                min_size = min(width, height)
                left = (width - min_size) // 2
                top = (height - min_size) // 2
                right = left + min_size
                bottom = top + min_size
                source_img = source_img.crop((left, top, right, bottom))
                print_status(f"已裁剪为正方形: {min_size}x{min_size}", "info")
            
            # 定义超高清图标尺寸集合
            # 重点关注常用尺寸，但包含高清版本
            icon_sizes = [16, 24, 32, 48, 64, 96, 128, 256, 512]
            
            print_status(f"将生成 {len(icon_sizes)} 种尺寸的超高清图标", "ultra")
            
            # 生成不同尺寸的图标
            icon_images = []
            
            for size in icon_sizes:
                print_status(f"生成超高清 {size}x{size} 图标", "info")
                
                # 使用最高质量的缩放算法
                if size > source_img.size[0]:
                    # 如果目标尺寸大于源图片，使用双三次插值
                    resized = source_img.resize((size, size), Image.Resampling.BICUBIC)
                else:
                    # 如果目标尺寸小于等于源图片，使用Lanczos算法
                    resized = source_img.resize((size, size), Image.Resampling.LANCZOS)
                
                # 增强图像质量
                resized = enhance_image_quality(resized, size)
                
                icon_images.append(resized)
            
            # 确保输出目录存在
            output_dir = os.path.dirname(ico_path)
            os.makedirs(output_dir, exist_ok=True)
            
            print_status("正在保存超高清ICO文件（使用PNG嵌入）...", "ultra")
            
            # 使用手动方法创建ICO文件以保持最高质量
            create_ico_manually(icon_images, ico_path)
            
            # 验证生成的文件
            file_size = os.path.getsize(ico_path)
            print_status(f"超高清图标文件已生成: {ico_path}", "success")
            print_status(f"文件大小: {file_size:,} 字节", "success")
            print_status(f"包含尺寸: {len(icon_sizes)} 种", "success")
            
            # 详细验证ICO文件
            try:
                with Image.open(ico_path) as ico_img:
                    print_status(f"验证成功 - 主图标尺寸: {ico_img.size}", "success")
                    
                    # 评估文件质量
                    if file_size > 100000:  # 100KB以上
                        print_status("💎 超高清图标生成成功！文件大小表明质量极佳", "ultra")
                    elif file_size > 50000:  # 50KB以上
                        print_status("✨ 高清图标生成成功！文件大小表明质量良好", "hd")
                    elif file_size > 10000:  # 10KB以上
                        print_status("📊 中等质量图标，质量可接受", "success")
                    else:
                        print_status("⚠️ 图标文件较小，可能质量不够高", "warning")
                        
            except Exception as e:
                print_status(f"ICO文件验证失败: {e}", "warning")
            
            return True
            
    except Exception as e:
        print_status(f"创建图标时发生错误: {e}", "error")
        return False

def main():
    """主函数"""
    print("💎 超高清图标生成器")
    print("=" * 80)
    print("📝 将 assets/images/logo.png 转换为超高清Windows图标")
    print("🎯 使用PNG嵌入技术保持最高质量")
    print("💎 支持高DPI显示器，确保在任何分辨率下都清晰")
    print("=" * 80)
    print()
    
    # 检查PIL库
    try:
        from PIL import Image
        print_status("PIL库检查通过", "success")
    except ImportError:
        print_status("PIL库未安装，请运行: pip install Pillow", "error")
        return False
    
    # 创建超高清图标
    if create_ultra_hd_icon():
        print()
        print_status("🎉 超高清图标创建成功！", "ultra")
        print()
        print("📝 接下来的步骤:")
        print("1. 运行: flutter clean")
        print("2. 运行: flutter build windows --release")
        print("3. 检查生成的exe文件图标")
        print()
        print("💎 超高清图标特点:")
        print("- 使用PNG格式嵌入ICO文件")
        print("- 包含9种关键尺寸，优化文件大小")
        print("- 支持高DPI显示器")
        print("- 在4K/8K显示器上保持完美清晰度")
        print("- 文件大小适中但质量极高")
        print()
        print("🔧 如果图标仍然显示为默认图标:")
        print("- 重启Windows资源管理器 (Ctrl+Shift+Esc -> 重启Windows资源管理器)")
        print("- 清理Windows图标缓存")
        print("- 确保exe文件是新构建的版本")
        
        return True
    else:
        print_status("❌ 超高清图标创建失败", "error")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
