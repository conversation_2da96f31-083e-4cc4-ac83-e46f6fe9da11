import 'package:flutter/material.dart';
import 'package:numerology_ai_chat_admin/src/models/page_model.dart';

class PageEditDialog extends StatefulWidget {
  final PageModel? page;
  final Function(dynamic) onSave;

  const PageEditDialog({
    super.key,
    this.page,
    required this.onSave,
  });

  @override
  State<PageEditDialog> createState() => _PageEditDialogState();
}

class _PageEditDialogState extends State<PageEditDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _slugController = TextEditingController();
  final _contentController = TextEditingController();
  final _metaDescriptionController = TextEditingController();
  final _keywordsController = TextEditingController();
  final _sortOrderController = TextEditingController();

  String _selectedPageType = '帮助';
  bool _isActive = true;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.page != null) {
      _titleController.text = widget.page!.pageTitle;
      _slugController.text = widget.page!.slug;
      _contentController.text = widget.page!.pageContent;
      _metaDescriptionController.text = widget.page!.metaDescription ?? '';
      _keywordsController.text = widget.page!.keywords?.join(', ') ?? '';
      _sortOrderController.text = widget.page!.sortOrder.toString();
      _selectedPageType = widget.page!.pageType;
      _isActive = widget.page!.isActive;
    } else {
      _sortOrderController.text = '0';
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _slugController.dispose();
    _contentController.dispose();
    _metaDescriptionController.dispose();
    _keywordsController.dispose();
    _sortOrderController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Row(
              children: [
                Text(
                  widget.page == null ? '新建页面' : '编辑页面',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // 表单内容
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 基本信息行
                      Row(
                        children: [
                          // 页面标题
                          Expanded(
                            flex: 2,
                            child: TextFormField(
                              controller: _titleController,
                              decoration: const InputDecoration(
                                labelText: '页面标题 *',
                                border: OutlineInputBorder(),
                              ),
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return '请输入页面标题';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          // URL标识
                          Expanded(
                            child: TextFormField(
                              controller: _slugController,
                              decoration: const InputDecoration(
                                labelText: 'URL标识 *',
                                border: OutlineInputBorder(),
                                helperText: '用于URL路径，如: help',
                              ),
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return '请输入URL标识';
                                }
                                if (!RegExp(r'^[a-z0-9_-]+$').hasMatch(value.trim())) {
                                  return '只能包含小写字母、数字、下划线和连字符';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      // 类型和状态行
                      Row(
                        children: [
                          // 页面类型
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              value: _selectedPageType,
                              decoration: const InputDecoration(
                                labelText: '页面类型',
                                border: OutlineInputBorder(),
                              ),
                              items: const [
                                DropdownMenuItem(value: '帮助', child: Text('帮助')),
                                DropdownMenuItem(value: '关于', child: Text('关于')),
                                DropdownMenuItem(value: '条款', child: Text('条款')),
                                DropdownMenuItem(value: '隐私', child: Text('隐私')),
                                DropdownMenuItem(value: '使用指南', child: Text('使用指南')),
                              ],
                              onChanged: (value) {
                                setState(() {
                                  _selectedPageType = value!;
                                });
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          // 排序权重
                          Expanded(
                            child: TextFormField(
                              controller: _sortOrderController,
                              decoration: const InputDecoration(
                                labelText: '排序权重',
                                border: OutlineInputBorder(),
                                helperText: '数字越小越靠前',
                              ),
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value != null && value.isNotEmpty) {
                                  if (int.tryParse(value) == null) {
                                    return '请输入有效数字';
                                  }
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          // 状态开关
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('状态'),
                              const SizedBox(height: 8),
                              Switch(
                                value: _isActive,
                                onChanged: (value) {
                                  setState(() {
                                    _isActive = value;
                                  });
                                },
                              ),
                              Text(
                                _isActive ? '启用' : '禁用',
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      // 页面描述
                      TextFormField(
                        controller: _metaDescriptionController,
                        decoration: const InputDecoration(
                          labelText: '页面描述',
                          border: OutlineInputBorder(),
                          helperText: '用于SEO优化，可选',
                        ),
                        maxLines: 2,
                      ),
                      const SizedBox(height: 16),
                      // 关键词
                      TextFormField(
                        controller: _keywordsController,
                        decoration: const InputDecoration(
                          labelText: '关键词',
                          border: OutlineInputBorder(),
                          helperText: '多个关键词用逗号分隔，可选',
                        ),
                      ),
                      const SizedBox(height: 16),
                      // 页面内容
                      const Text(
                        '页面内容 (Markdown格式) *',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        height: 300,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: TextFormField(
                          controller: _contentController,
                          decoration: const InputDecoration(
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.all(12),
                            hintText: '请输入页面内容，支持Markdown格式...',
                          ),
                          maxLines: null,
                          expands: true,
                          textAlignVertical: TextAlignVertical.top,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return '请输入页面内容';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            // 操作按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _isLoading ? null : _handleSave,
                  child: _isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Text(widget.page == null ? '创建' : '保存'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _handleSave() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final keywords = _keywordsController.text.trim().isEmpty
          ? null
          : _keywordsController.text.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();

      if (widget.page == null) {
        // 创建新页面
        final request = CreatePageRequest(
          pageTitle: _titleController.text.trim(),
          pageContent: _contentController.text.trim(),
          pageType: _selectedPageType,
          slug: _slugController.text.trim(),
          isActive: _isActive,
          sortOrder: int.tryParse(_sortOrderController.text) ?? 0,
          metaDescription: _metaDescriptionController.text.trim().isEmpty ? null : _metaDescriptionController.text.trim(),
          keywords: keywords,
        );
        await widget.onSave(request);
      } else {
        // 更新页面
        final request = UpdatePageRequest(
          pageTitle: _titleController.text.trim(),
          pageContent: _contentController.text.trim(),
          pageType: _selectedPageType,
          slug: _slugController.text.trim(),
          isActive: _isActive,
          sortOrder: int.tryParse(_sortOrderController.text) ?? 0,
          metaDescription: _metaDescriptionController.text.trim().isEmpty ? null : _metaDescriptionController.text.trim(),
          keywords: keywords,
        );
        await widget.onSave(request);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
