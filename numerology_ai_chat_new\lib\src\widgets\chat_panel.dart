import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/chat_provider.dart';
import '../providers/auth_provider.dart';
import '../providers/portrait_mode_provider.dart';
import '../services/image_service.dart';
import '../models/chat_message.dart';
import '../models/chat_state.dart';
import 'chat_bubble.dart';
import 'portrait_mode_button.dart';

/// 聊天面板
class ChatPanel extends ConsumerStatefulWidget {
  const ChatPanel({super.key});

  @override
  ConsumerState<ChatPanel> createState() => _ChatPanelState();
}

class _ChatPanelState extends ConsumerState<ChatPanel> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _inputFocusNode = FocusNode();
  final ImageService _imageService = ImageService();

  // 滚动控制状态
  bool _autoScrollEnabled = true; // 是否启用自动滚动
  bool _userScrolledAway = false; // 用户是否手动滚动离开底部
  bool _isScrolling = false; // 是否正在执行滚动动画
  final bool _allowInterruptScroll = true; // 是否允许用户打断滚动（设为false则类似旧版本）

  // 图片相关状态
  List<ImageAttachment> _selectedImages = [];
  bool _isSelectingImage = false;

  @override
  void initState() {
    super.initState();
    // 初始化聊天
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(chatProvider.notifier).init();
    });

    // 监听滚动事件
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _messageController.dispose();
    _scrollController.dispose();
    _inputFocusNode.dispose();
    super.dispose();
  }

  /// 监听滚动事件
  void _onScroll() {
    // 如果不允许打断滚动，则不处理用户滚动事件
    if (!_allowInterruptScroll) return;

    // 如果正在执行自动滚动，忽略事件
    if (_isScrolling) return;

    final position = _scrollController.position;
    if (!position.hasContentDimensions) return;

    // 检查是否滚动到底部（允许10像素的误差）
    final isAtBottom = position.pixels >= (position.maxScrollExtent - 10);

    if (isAtBottom && _userScrolledAway) {
      // 用户滚动回到底部，重新启用自动滚动
      setState(() {
        _userScrolledAway = false;
        _autoScrollEnabled = true;
      });
    } else if (!isAtBottom && !_userScrolledAway) {
      // 用户手动滚动离开底部，禁用自动滚动
      setState(() {
        _userScrolledAway = true;
        _autoScrollEnabled = false;
      });
    }
  }

  void _sendMessage() {
    final content = _trimSpacesOnly(_messageController.text);
    final hasImages = _selectedImages.isNotEmpty;

    // 检查是否有内容或图片（移除所有空白字符后检查是否为空）
    if (content.trim().isEmpty && !hasImages) return;

    // 发送消息时启用自动滚动
    setState(() {
      _autoScrollEnabled = true;
      _userScrolledAway = false;
    });

    // 发送消息（包含图片）
    _sendMessageWithImages(content, _selectedImages);

    // 清理状态
    _messageController.clear();
    setState(() {
      _selectedImages = [];
    });
    _inputFocusNode.requestFocus();
    _scrollToBottom(force: true); // 强制滚动到底部
  }

  void _sendMessageWithImages(String content, List<ImageAttachment> images) {
    // 创建包含图片的消息
    final message = ChatMessage.user(
      content: content,
      images: images.isNotEmpty ? images : null,
    );

    // 通过provider发送消息
    ref.read(chatProvider.notifier).sendMessageWithImages(message);
  }

  void _scrollToBottom({bool force = false}) {
    // 如果不是强制滚动且自动滚动被禁用，则不执行滚动
    if (!force && !_autoScrollEnabled) {
      return;
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients && _scrollController.position.hasContentDimensions) {
        _isScrolling = true; // 标记正在执行自动滚动

        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 200), // 缩短动画时间，类似旧版本
          curve: Curves.easeOut,
        ).then((_) {
          _isScrolling = false; // 滚动完成
        });
      }
    });
  }

  /// 处理键盘事件
  bool _handleKeyEvent(KeyEvent event) {
    // 只在输入框有焦点时处理键盘事件
    if (!_inputFocusNode.hasFocus) return false;

    // 只处理按键按下事件
    if (event is! KeyDownEvent) return false;

    final chatState = ref.read(chatProvider);
    final canSend = chatState.canSendMessage;

    // 检查是否按下了回车键
    if (event.logicalKey == LogicalKeyboardKey.enter) {
      // 检查是否同时按下了Shift键
      final isShiftPressed = HardwareKeyboard.instance.logicalKeysPressed.contains(LogicalKeyboardKey.shiftLeft) ||
                            HardwareKeyboard.instance.logicalKeysPressed.contains(LogicalKeyboardKey.shiftRight);

      if (isShiftPressed) {
        // Shift+回车：插入换行符
        _insertNewline();
        return true; // 阻止事件继续传播
      } else {
        // 单独回车：发送消息
        if (canSend) {
          _sendMessage();
          return true; // 阻止事件继续传播，防止TextField插入换行符
        }
      }
    }

    return false; // 允许其他事件正常传播
  }

  /// 在光标位置插入换行符
  void _insertNewline() {
    final controller = _messageController;
    final selection = controller.selection;
    final text = controller.text;

    // 在光标位置插入换行符
    final newText = text.replaceRange(
      selection.start,
      selection.end,
      '\n',
    );

    // 更新文本和光标位置
    controller.value = TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(offset: selection.start + 1),
    );
  }

  /// 只移除开头和结尾的空格和制表符，保留换行符
  String _trimSpacesOnly(String text) {
    // 移除开头的空格和制表符
    int start = 0;
    while (start < text.length && (text[start] == ' ' || text[start] == '\t')) {
      start++;
    }

    // 移除结尾的空格和制表符
    int end = text.length;
    while (end > start && (text[end - 1] == ' ' || text[end - 1] == '\t')) {
      end--;
    }

    return text.substring(start, end);
  }

  @override
  Widget build(BuildContext context) {
    final isPortraitMode = ref.watch(portraitModeStateProvider);
    final portraitModeNotifier = ref.watch(portraitModeProvider);

    // 监听整个chatProvider的变化（类似旧版本的实现）
    ref.listen(chatProvider, (previous, current) {
      // 检查消息数量变化
      final previousLength = previous?.currentSession?.messages.length ?? 0;
      final currentLength = current.currentSession?.messages.length ?? 0;

      // 检查是否有正在输入的消息
      final hasTypingMessage = current.currentSession?.messages.isNotEmpty == true &&
          current.currentSession!.messages.last.isTyping;

      // 如果是流式输出开始，启用自动滚动
      if (current.isStreaming && (previous?.isStreaming != true)) {
        setState(() {
          _autoScrollEnabled = true;
          _userScrolledAway = false;
        });
      }

      // 滚动条件：消息数量变化、有正在输入的消息、或者正在流式输出
      if (previousLength != currentLength || hasTypingMessage || current.isStreaming) {
        _scrollToBottom();
      }
    });

    final borderRadius = isPortraitMode ? 0.0 : 12.0;
    final elevation = isPortraitMode ? 0.0 : 2.0;

    return Focus(
      onKeyEvent: (node, event) {
        final handled = _handleKeyEvent(event);
        return handled ? KeyEventResult.handled : KeyEventResult.ignored;
      },
      child: Card(
        margin: EdgeInsets.zero,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        elevation: elevation,
        shadowColor: Colors.black.withValues(alpha: 0.1),
        child: Column(
          children: [
            _buildHeader(Theme.of(context)),
            if (!isPortraitMode) const Divider(height: 1),
            Expanded(child: _buildMessageList()),
            if (!isPortraitMode) const Divider(height: 1),
            _buildInputArea(Theme.of(context)),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    final chatState = ref.watch(chatProvider);
    final selectedAgent = chatState.selectedAgent;
    final isPortraitMode = ref.watch(portraitModeStateProvider);
    final portraitModeNotifier = ref.watch(portraitModeProvider);

    return Container(
      padding: portraitModeNotifier.getPortraitModePadding(
        const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      ),
      child: Row(
        children: [
          if (selectedAgent != null)
            Container(
              padding: portraitModeNotifier.getPortraitModePadding(
                const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              ),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.smart_toy,
                    size: portraitModeNotifier.getPortraitModeTextSize(16),
                    color: theme.colorScheme.primary,
                  ),
                  SizedBox(width: portraitModeNotifier.getPortraitModeSpacing(6)),
                  Text(
                    selectedAgent.agentName,
                    style: theme.textTheme.labelLarge?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.bold,
                      fontSize: portraitModeNotifier.getPortraitModeTextSize(
                        theme.textTheme.labelLarge?.fontSize ?? 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          const Spacer(),

          // 竖屏模式切换按钮
          const PortraitModeButton(),

          SizedBox(width: portraitModeNotifier.getPortraitModeSpacing(8)),

          if (chatState.isStreaming)
            TextButton.icon(
              onPressed: () => ref.read(chatProvider.notifier).stopAIResponse(),
              icon: Icon(
                Icons.stop,
                size: portraitModeNotifier.getPortraitModeTextSize(16),
              ),
              label: Text(
                '停止',
                style: TextStyle(
                  fontSize: portraitModeNotifier.getPortraitModeTextSize(14),
                ),
              ),
              style: TextButton.styleFrom(
                foregroundColor: theme.colorScheme.error,
                padding: portraitModeNotifier.getPortraitModePadding(
                  const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMessageList() {
    final chatState = ref.watch(chatProvider);
    final portraitModeNotifier = ref.watch(portraitModeProvider);
    final allMessages = chatState.currentSession?.messages ?? [];

    // 过滤掉系统消息，只显示用户和AI的对话
    // 这样可以确保用户选择的智能体、八字等信息不会出现在对话框中
    final messages = allMessages.where((message) => !message.isSystem).toList();

    if (chatState.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (messages.isEmpty) {
      return Center(
        child: Text(
          '开始对话吧',
          style: TextStyle(
            fontSize: portraitModeNotifier.getPortraitModeTextSize(16),
          ),
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: portraitModeNotifier.getPortraitModePadding(const EdgeInsets.all(16)),
      itemCount: messages.length,
      itemBuilder: (context, index) {
        final message = messages[index];
        final isRetrying = chatState.isMessageRetrying(message.id);
        return ChatBubble(
          message: message,
          isRetrying: isRetrying,
          onResend: message.isFailed && !isRetrying ? () {
            ref.read(chatProvider.notifier).resendMessage(message.id);
          } : null,
        );
      },
    );
  }

  Widget _buildInputArea(ThemeData theme) {
    final chatState = ref.watch(chatProvider);
    final portraitModeNotifier = ref.watch(portraitModeProvider);
    final canSend = chatState.canSendMessage;
    final blockReason = chatState.sendMessageBlockReason;
    final hasContent = _messageController.text.trim().isNotEmpty || _selectedImages.isNotEmpty;

    return Container(
      padding: portraitModeNotifier.getPortraitModePadding(const EdgeInsets.all(12)),
      color: theme.colorScheme.surface,
      child: Column(
        children: [
          // 图片预览区域
          if (_selectedImages.isNotEmpty) _buildImagePreview(theme),

          // 算力消耗显示
          _buildPowerCostInfo(theme, chatState),

          // 输入区域
          Row(
            children: [
              // 图片选择按钮
              IconButton(
                icon: _isSelectingImage
                    ? SizedBox(
                        width: portraitModeNotifier.getPortraitModeTextSize(20),
                        height: portraitModeNotifier.getPortraitModeTextSize(20),
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: theme.colorScheme.primary,
                        ),
                      )
                    : Icon(
                        Icons.image,
                        size: portraitModeNotifier.getPortraitModeTextSize(24),
                      ),
                onPressed: canSend && !_isSelectingImage ? _selectImages : null,
                style: IconButton.styleFrom(
                  foregroundColor: theme.colorScheme.primary,
                  padding: portraitModeNotifier.getPortraitModePadding(
                    const EdgeInsets.all(12),
                  ),
                ),
              ),
              SizedBox(width: portraitModeNotifier.getPortraitModeSpacing(8)),

              // 文本输入框
              Expanded(
                child: TextField(
                  controller: _messageController,
                  focusNode: _inputFocusNode,
                  enabled: canSend,
                  maxLines: null,
                  keyboardType: TextInputType.multiline,
                  textInputAction: TextInputAction.none,
                  style: TextStyle(
                    fontSize: portraitModeNotifier.getPortraitModeTextSize(16),
                  ),
                  decoration: InputDecoration(
                    hintText: canSend ? '输入消息... (回车发送，Shift+回车换行)' : blockReason,
                    hintStyle: TextStyle(
                      fontSize: portraitModeNotifier.getPortraitModeTextSize(14),
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: theme.scaffoldBackgroundColor,
                    contentPadding: portraitModeNotifier.getPortraitModePadding(
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                  ),
                  onChanged: (value) {
                    setState(() {}); // 触发重建以更新发送按钮状态
                  },
                ),
              ),
              SizedBox(width: portraitModeNotifier.getPortraitModeSpacing(8)),

              // 发送按钮
              IconButton(
                icon: chatState.isStreaming
                    ? SizedBox(
                        width: portraitModeNotifier.getPortraitModeTextSize(20),
                        height: portraitModeNotifier.getPortraitModeTextSize(20),
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: theme.colorScheme.primary,
                        ),
                      )
                    : Icon(
                        Icons.send,
                        size: portraitModeNotifier.getPortraitModeTextSize(24),
                      ),
                onPressed: canSend && hasContent ? _sendMessage : null,
                style: IconButton.styleFrom(
                  backgroundColor: canSend && hasContent
                      ? theme.colorScheme.primary
                      : theme.disabledColor,
                  foregroundColor: Colors.white,
                  padding: portraitModeNotifier.getPortraitModePadding(
                    const EdgeInsets.all(12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 选择图片
  Future<void> _selectImages() async {
    if (_isSelectingImage) return;

    setState(() {
      _isSelectingImage = true;
    });

    try {
      // 显示选择对话框
      final result = await showDialog<String>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('选择图片'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('从相册选择'),
                onTap: () => Navigator.of(context).pop('gallery'),
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('拍照'),
                onTap: () => Navigator.of(context).pop('camera'),
              ),
            ],
          ),
        ),
      );

      if (result == null) return;

      List<ImageAttachment>? images;
      if (result == 'gallery') {
        images = await _imageService.pickImagesFromGallery(
          multiple: true,
          maxImages: 5, // 限制最多选择5张图片
        );
      } else if (result == 'camera') {
        final image = await _imageService.takePhoto();
        images = image != null ? [image] : null;
      }

      if (images != null && images.isNotEmpty) {
        setState(() {
          _selectedImages.addAll(images!);
          // 限制总数不超过5张
          if (_selectedImages.length > 5) {
            _selectedImages = _selectedImages.take(5).toList();
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('选择图片失败: $e')),
        );
      }
    } finally {
      setState(() {
        _isSelectingImage = false;
      });
    }
  }

  /// 构建图片预览
  Widget _buildImagePreview(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      height: 80,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _selectedImages.length,
        itemBuilder: (context, index) {
          final image = _selectedImages[index];
          return Container(
            margin: const EdgeInsets.only(right: 8),
            width: 80,
            height: 80,
            child: Stack(
              children: [
                // 图片预览
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: theme.colorScheme.outline.withValues(alpha: 0.3),
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: _buildImageWidget(image),
                  ),
                ),
                // 删除按钮
                Positioned(
                  top: 4,
                  right: 4,
                  child: GestureDetector(
                    onTap: () => _removeImage(index),
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.error,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.close,
                        size: 14,
                        color: theme.colorScheme.onError,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 构建图片组件
  Widget _buildImageWidget(ImageAttachment image) {
    if (image.localPath != null) {
      final file = _imageService.getImageFile(image.localPath!);
      if (file != null) {
        return Image.file(
          file,
          fit: BoxFit.cover,
          width: 80,
          height: 80,
        );
      }
    }

    if (image.base64Data != null) {
      try {
        final bytes = base64Decode(image.base64Data!);
        return Image.memory(
          bytes,
          fit: BoxFit.cover,
          width: 80,
          height: 80,
        );
      } catch (e) {
        // 忽略解码错误
      }
    }

    // 显示占位符
    return Container(
      width: 80,
      height: 80,
      color: Colors.grey[300],
      child: const Icon(Icons.broken_image, color: Colors.grey),
    );
  }

  /// 移除图片
  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  /// 构建算力消耗信息
  Widget _buildPowerCostInfo(ThemeData theme, ChatState chatState) {
    final authState = ref.watch(authProvider);
    final user = authState.user;

    if (user == null) return const SizedBox.shrink();

    // 检查是否选择了智能体和模型
    final conversation = chatState.currentConversation;
    if (conversation?.selectedAgent == null || conversation?.selectedModel == null) {
      return const SizedBox.shrink();
    }

    return FutureBuilder<Map<String, dynamic>>(
      future: _getPowerCostInfo(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const SizedBox.shrink(); // 加载中不显示
        }

        final data = snapshot.data!;
        final currentPowerCost = data['cost'] as int;
        final powerDetails = data['details'] as Map<String, dynamic>;

        // 如果没有选择智能体和模型，不显示
        if (currentPowerCost == 0) return const SizedBox.shrink();

        final hasEnoughPower = user.availableCount >= currentPowerCost;

        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: hasEnoughPower
                ? theme.colorScheme.primaryContainer.withValues(alpha: 0.3)
                : theme.colorScheme.errorContainer.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: hasEnoughPower
                  ? theme.colorScheme.primary.withValues(alpha: 0.3)
                  : theme.colorScheme.error.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                hasEnoughPower ? Icons.account_balance_wallet : Icons.warning,
                size: 16,
                color: hasEnoughPower
                    ? theme.colorScheme.primary
                    : theme.colorScheme.error,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  hasEnoughPower
                      ? '剩余 ${user.availableCount} 算力，本次消耗 $currentPowerCost 算力'
                      : '算力不足！需要 $currentPowerCost 算力，当前仅有 ${user.availableCount} 算力',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: hasEnoughPower
                        ? theme.colorScheme.onPrimaryContainer
                        : theme.colorScheme.onErrorContainer,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              if (powerDetails['description'] != null)
                Tooltip(
                  message: powerDetails['description'] as String,
                  child: Icon(
                    Icons.info_outline,
                    size: 14,
                    color: hasEnoughPower
                        ? theme.colorScheme.primary
                        : theme.colorScheme.error,
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  /// 获取算力消耗信息
  Future<Map<String, dynamic>> _getPowerCostInfo() async {
    try {
      final currentPowerCost = await ref.read(chatProvider.notifier).getCurrentPowerCost();
      final powerDetails = await ref.read(chatProvider.notifier).getCurrentPowerCostDetails();

      return {
        'cost': currentPowerCost,
        'details': powerDetails,
      };
    } catch (e) {
      return {
        'cost': 0,
        'details': {'description': '计算失败'},
      };
    }
  }
}