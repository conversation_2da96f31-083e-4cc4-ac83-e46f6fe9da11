const Joi = require('joi')
const { createBusinessError, ERROR_CODES } = require('../middleware/error_handler')

/**
 * 验证管理员登录参数
 * @param {object} params 登录参数
 * @returns {object} 验证后的参数
 */
function validateWeakAdminLogin(params) {
  const schema = Joi.object({
    username: Joi.string().min(1).max(50).required().messages({
      'string.empty': '用户名不能为空',
      'string.min': '用户名长度至少1个字符',
      'string.max': '用户名长度不能超过50个字符',
      'any.required': '用户名是必填项'
    }),
    password: Joi.string().min(6).max(100).required().messages({
      'string.empty': '密码不能为空',
      'string.min': '密码长度至少6个字符',
      'string.max': '密码长度不能超过100个字符',
      'any.required': '密码是必填项'
    })
  }).unknown(true) // 允许额外字段

  const { error, value } = schema.validate(params)
  if (error) {
    throw createBusinessError(
      ERROR_CODES.VALIDATION_ERROR,
      error.details[0].message,
      400
    )
  }

  return value
}

/**
 * 验证生成激活码参数
 * @param {object} params 生成参数
 * @returns {object} 验证后的参数
 */
function validateGenerateActivationCodes(params) {
  const schema = Joi.object({
    quantity: Joi.number().integer().min(1).max(100).required().messages({
      'number.base': '数量必须是数字',
      'number.integer': '数量必须是整数',
      'number.min': '数量至少为1',
      'number.max': '数量不能超过100',
      'any.required': '数量是必填项'
    }),
    quotaAmount: Joi.number().integer().min(1).max(100000).required().messages({
      'number.base': '算力数量必须是数字',
      'number.integer': '算力数量必须是整数',
      'number.min': '算力数量至少为1',
      'number.max': '算力数量不能超过100000',
      'any.required': '算力数量是必填项'
    }),
    token: Joi.string().required().messages({
      'string.empty': 'Token不能为空',
      'any.required': 'Token是必填项'
    })
  }).unknown(true)

  const { error, value } = schema.validate(params)
  if (error) {
    throw createBusinessError(
      ERROR_CODES.VALIDATION_ERROR,
      error.details[0].message,
      400
    )
  }

  return value
}

/**
 * 验证查询用户信息参数
 * @param {object} params 查询参数
 * @returns {object} 验证后的参数
 */
function validateGetUserInfo(params) {
  const schema = Joi.object({
    username: Joi.string().min(1).max(50).required().messages({
      'string.empty': '用户名不能为空',
      'string.min': '用户名长度至少1个字符',
      'string.max': '用户名长度不能超过50个字符',
      'any.required': '用户名是必填项'
    }),
    token: Joi.string().required().messages({
      'string.empty': 'Token不能为空',
      'any.required': 'Token是必填项'
    })
  }).unknown(true)

  const { error, value } = schema.validate(params)
  if (error) {
    throw createBusinessError(
      ERROR_CODES.VALIDATION_ERROR,
      error.details[0].message,
      400
    )
  }

  return value
}

/**
 * 验证修改用户算力参数
 * @param {object} params 修改参数
 * @returns {object} 验证后的参数
 */
function validateModifyUserQuota(params) {
  const schema = Joi.object({
    targetUsername: Joi.string().min(1).max(50).required().messages({
      'string.empty': '目标用户名不能为空',
      'string.min': '目标用户名长度至少1个字符',
      'string.max': '目标用户名长度不能超过50个字符',
      'any.required': '目标用户名是必填项'
    }),
    operationType: Joi.string().valid('increase', 'decrease').required().messages({
      'any.only': '操作类型必须是increase或decrease',
      'any.required': '操作类型是必填项'
    }),
    quotaAmount: Joi.number().integer().min(1).max(100000).required().messages({
      'number.base': '算力数量必须是数字',
      'number.integer': '算力数量必须是整数',
      'number.min': '算力数量至少为1',
      'number.max': '算力数量不能超过100000',
      'any.required': '算力数量是必填项'
    }),
    reason: Joi.string().max(200).allow('').optional().messages({
      'string.max': '操作原因不能超过200个字符'
    }),
    token: Joi.string().required().messages({
      'string.empty': 'Token不能为空',
      'any.required': 'Token是必填项'
    })
  }).unknown(true)

  const { error, value } = schema.validate(params)
  if (error) {
    throw createBusinessError(
      ERROR_CODES.VALIDATION_ERROR,
      error.details[0].message,
      400
    )
  }

  return value
}

/**
 * 验证分页参数
 * @param {object} params 分页参数
 * @returns {object} 验证后的参数
 */
function validatePagination(params) {
  const schema = Joi.object({
    page: Joi.number().integer().min(1).default(1).messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数',
      'number.min': '页码至少为1'
    }),
    pageSize: Joi.number().integer().min(1).max(100).default(20).messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量至少为1',
      'number.max': '每页数量不能超过100'
    }),
    token: Joi.string().required().messages({
      'string.empty': 'Token不能为空',
      'any.required': 'Token是必填项'
    })
  }).unknown(true)

  const { error, value } = schema.validate(params)
  if (error) {
    throw createBusinessError(
      ERROR_CODES.VALIDATION_ERROR,
      error.details[0].message,
      400
    )
  }

  return value
}

module.exports = {
  validateWeakAdminLogin,
  validateGenerateActivationCodes,
  validateGetUserInfo,
  validateModifyUserQuota,
  validatePagination
}
