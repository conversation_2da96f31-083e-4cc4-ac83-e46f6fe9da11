const { purchaseOrderCollection, userCollection } = require('../utils/db')
const { successResponse, createBusinessError, ERROR_CODES } = require('../middleware/error_handler')
const logger = require('../utils/logger')

/**
 * 获取用户购买历史
 * @param {object} params 请求参数（包含鉴权后的用户信息）
 * @param {string} params.userId 用户ID（由鉴权中间件设置）
 * @param {number} [params.page=1] 页码
 * @param {number} [params.limit=20] 每页数量
 * @returns {object} 购买历史列表
 */
async function getUserOrders(params) {
  try {
    const { userId, page = 1, limit = 20 } = params

    if (!userId) {
      throw createBusinessError(ERROR_CODES.UNAUTHORIZED, '无效的Token或用户认证失败', 401)
    }

    logger.info('Getting user purchase history', { userId, page, limit })

    // 获取用户的购买订单历史
    const orderResult = await purchaseOrderCollection.getUserOrders(userId, page, limit)

    // 获取用户的充值历史记录（来自用户表的purchaseHistory字段）
    const userResult = await userCollection.findById(userId)
    const purchaseHistory = userResult?.purchaseHistory || []

    // 将purchaseHistory转换为与订单相同的格式
    const historyRecords = purchaseHistory.map((record, index) => ({
      _id: `history_${userId}_${index}`, // 生成唯一ID
      orderNo: `ADMIN_${record.date.getTime()}`, // 生成订单号
      packageName: record.reason || '管理员充值',
      packageQuota: record.details.addedCount,
      orderAmount: 0, // 管理员充值无金额
      status: 'COMPLETED',
      createTime: record.date,
      payTime: record.date,
      transactionId: `ADMIN_${record.operator}_${record.date.getTime()}`,
      isAdminRecharge: true, // 标记为管理员充值
      operator: record.operator,
      reason: record.reason
    }))

    // 合并订单和充值历史，按时间倒序排列
    const allRecords = [...orderResult.orders, ...historyRecords]
    allRecords.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))

    // 重新分页处理合并后的数据
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedRecords = allRecords.slice(startIndex, endIndex)

    const totalRecords = allRecords.length
    const totalPages = Math.ceil(totalRecords / limit)

    logger.info('User purchase history retrieved successfully', {
      userId,
      ordersCount: orderResult.orders.length,
      historyCount: historyRecords.length,
      totalRecords,
      page,
      totalPages
    })

    return successResponse({
      orders: paginatedRecords,
      total: totalRecords,
      page: page,
      limit: limit,
      totalPages: totalPages
    }, '获取购买历史成功')

  } catch (error) {
    logger.error('Failed to get user purchase history', {
      error: error.message,
      userId: params.userId,
      page: params.page,
      limit: params.limit
    })
    throw error
  }
}

/**
 * 获取订单详情
 * @param {object} params 请求参数（包含鉴权后的用户信息）
 * @param {string} params.userId 用户ID（由鉴权中间件设置）
 * @param {string} params.orderId 订单ID
 * @returns {object} 订单详情
 */
async function getOrderDetail(params) {
  try {
    const { userId, orderId } = params

    if (!userId) {
      throw createBusinessError(ERROR_CODES.UNAUTHORIZED, '无效的Token或用户认证失败', 401)
    }

    if (!orderId) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAM, '订单ID不能为空', 400)
    }

    logger.info('Getting order detail', { userId, orderId })

    // 获取订单详情，确保订单属于当前用户
    const order = await purchaseOrderCollection.getOrderDetail(orderId, userId)

    if (!order) {
      throw createBusinessError(ERROR_CODES.ORDER_NOT_FOUND, '订单不存在或无权访问', 404)
    }

    logger.info('Order detail retrieved successfully', {
      userId,
      orderId,
      orderStatus: order.status
    })

    return successResponse(order, '获取订单详情成功')

  } catch (error) {
    logger.error('Failed to get order detail', { 
      error: error.message,
      userId: params.userId,
      orderId: params.orderId
    })
    throw error
  }
}

/**
 * 重新支付订单
 * @param {object} params 请求参数（包含鉴权后的用户信息）
 * @param {string} params.userId 用户ID（由鉴权中间件设置）
 * @param {string} params.orderId 订单ID
 * @returns {object} 支付信息
 */
async function retryPayment(params) {
  try {
    const { userId, orderId } = params

    if (!userId) {
      throw createBusinessError(ERROR_CODES.UNAUTHORIZED, '无效的Token或用户认证失败', 401)
    }

    if (!orderId) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAM, '订单ID不能为空', 400)
    }

    logger.info('Retrying payment for order', { userId, orderId })

    // 获取订单详情，确保订单属于当前用户
    const order = await purchaseOrderCollection.getOrderDetail(orderId, userId)

    if (!order) {
      throw createBusinessError(ERROR_CODES.ORDER_NOT_FOUND, '订单不存在或无权访问', 404)
    }

    // 检查订单状态是否允许重新支付
    if (order.status !== 'PENDING') {
      throw createBusinessError(ERROR_CODES.INVALID_ORDER_STATUS, '订单状态不允许重新支付', 400)
    }

    // 这里应该调用支付接口，目前返回模拟数据
    const paymentInfo = {
      orderId: orderId,
      paymentUrl: `https://pay.example.com/order/${orderId}`,
      qrCode: `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`,
      expireTime: new Date(Date.now() + 30 * 60 * 1000) // 30分钟后过期
    }

    logger.info('Payment retry initiated successfully', {
      userId,
      orderId,
      expireTime: paymentInfo.expireTime
    })

    return successResponse(paymentInfo, '重新支付订单成功')

  } catch (error) {
    logger.error('Failed to retry payment', { 
      error: error.message,
      userId: params.userId,
      orderId: params.orderId
    })
    throw error
  }
}

/**
 * 取消订单
 * @param {object} params 请求参数（包含鉴权后的用户信息）
 * @param {string} params.userId 用户ID（由鉴权中间件设置）
 * @param {string} params.orderId 订单ID
 * @returns {object} 取消结果
 */
async function cancelOrder(params) {
  try {
    const { userId, orderId } = params

    if (!userId) {
      throw createBusinessError(ERROR_CODES.UNAUTHORIZED, '无效的Token或用户认证失败', 401)
    }

    if (!orderId) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAM, '订单ID不能为空', 400)
    }

    logger.info('Cancelling order', { userId, orderId })

    // 获取订单详情，确保订单属于当前用户
    const order = await purchaseOrderCollection.getOrderDetail(orderId, userId)

    if (!order) {
      throw createBusinessError(ERROR_CODES.ORDER_NOT_FOUND, '订单不存在或无权访问', 404)
    }

    // 检查订单状态是否允许取消
    if (!['PENDING', 'PAID'].includes(order.status)) {
      throw createBusinessError(ERROR_CODES.INVALID_ORDER_STATUS, '订单状态不允许取消', 400)
    }

    // 更新订单状态为已取消
    const updateResult = await purchaseOrderCollection.updateOrderStatus(orderId, 'CANCELLED')

    if (updateResult.stats.updated === 0) {
      throw createBusinessError(ERROR_CODES.UPDATE_FAILED, '取消订单失败', 500)
    }

    logger.info('Order cancelled successfully', {
      userId,
      orderId,
      previousStatus: order.status
    })

    return successResponse({
      orderId: orderId,
      status: 'CANCELLED',
      cancelledAt: new Date()
    }, '取消订单成功')

  } catch (error) {
    logger.error('Failed to cancel order', { 
      error: error.message,
      userId: params.userId,
      orderId: params.orderId
    })
    throw error
  }
}

module.exports = {
  getUserOrders,
  getOrderDetail,
  retryPayment,
  cancelOrder
}
