const { createBusinessError, ERROR_CODES, formatSuccessResponse } = require('../utils/error_handler')
const { logCollection, userCollection, modelCollection } = require('../utils/db')
const { checkPermission } = require('../middleware/auth')
const logger = require('../utils/logger')

/**
 * 查看系统日志
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 日志列表
 */
async function viewLogs(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'log_read')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限查看系统日志', 403)
    }

    const {
      page = 1,
      pageSize = 50,
      logType = '',
      userId = '',
      startTime = '',
      endTime = '',
      keyword = ''
    } = event || {}

    logger.info('管理员查看系统日志', {
      adminId: adminAuth.adminId,
      page,
      pageSize,
      logType,
      userId
    })

    // 构建查询条件
    const filter = {}

    if (logType) {
      filter.logType = logType
    }

    if (userId) {
      filter.userId = userId
    }

    if (startTime && endTime) {
      filter.createdAt = {
        $gte: new Date(startTime),
        $lte: new Date(endTime)
      }
    } else if (startTime) {
      filter.createdAt = { $gte: new Date(startTime) }
    } else if (endTime) {
      filter.createdAt = { $lte: new Date(endTime) }
    }

    if (keyword) {
      filter.$or = [
        { action: { $regex: keyword, $options: 'i' } },
        { details: { $regex: keyword, $options: 'i' } },
        { userAgent: { $regex: keyword, $options: 'i' } }
      ]
    }

    // 获取日志数据（模拟实现，实际应该从日志集合获取）
    const mockLogs = [
      {
        _id: 'log001',
        logType: '用户操作',
        userId: '6c2530cc684d918002caa3f7173fc6bf',
        username: 'testuser001',
        action: '用户登录',
        details: '用户成功登录系统',
        ip: '***********00',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        createdAt: new Date('2025-06-14T15:30:00.000Z')
      },
      {
        _id: 'log002',
        logType: '系统操作',
        userId: null,
        username: null,
        action: '系统启动',
        details: '云函数系统启动完成',
        ip: null,
        userAgent: null,
        createdAt: new Date('2025-06-14T15:00:00.000Z')
      },
      {
        _id: 'log003',
        logType: '管理员操作',
        userId: adminAuth.adminId,
        username: 'admin',
        action: '创建用户',
        details: '管理员创建了新用户 testuser002',
        ip: '***********',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        createdAt: new Date('2025-06-14T15:18:00.000Z')
      }
    ]

    // 应用过滤条件（简化实现）
    let filteredLogs = mockLogs
    if (logType) {
      filteredLogs = filteredLogs.filter(log => log.logType === logType)
    }
    if (userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === userId)
    }
    if (keyword) {
      filteredLogs = filteredLogs.filter(log =>
        log.action.toLowerCase().includes(keyword.toLowerCase()) ||
        log.details.toLowerCase().includes(keyword.toLowerCase())
      )
    }

    // 手动分页
    const total = filteredLogs.length
    const totalPages = Math.ceil(total / pageSize)
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedLogs = filteredLogs.slice(startIndex, endIndex)

    const responseData = {
      logs: paginatedLogs,
      pagination: {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        total,
        totalPages
      },
      filters: {
        logTypes: ['用户操作', '管理员操作', '系统操作', '错误日志'],
        dateRange: {
          earliest: '2025-06-14T00:00:00.000Z',
          latest: new Date().toISOString()
        }
      }
    }

    return formatSuccessResponse(responseData, '获取系统日志成功')

  } catch (error) {
    logger.error('管理员查看系统日志异常', {
      adminId: adminAuth.adminId,
      error: error.message
    })
    throw error
  }
}

module.exports = {
  viewLogs
}