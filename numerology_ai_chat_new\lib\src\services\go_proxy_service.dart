import 'dart:convert';
import 'dart:async';
import 'package:http/http.dart' as http;
import '../core/constants/app_constants.dart';
import '../models/chat_message.dart';
import '../services/system_config_service.dart';
import '../services/storage_service.dart';

/// 大白话版本流式数据块
class LaymanVersionChunk {
  final LaymanVersionChunkType type;
  final String content;
  final String? stage; // 用于stage_complete类型

  const LaymanVersionChunk._(this.type, this.content, [this.stage]);

  /// 阶段标识（professional 或 layman）
  factory LaymanVersionChunk.stage(String stage) => LaymanVersionChunk._(LaymanVersionChunkType.stage, stage);

  /// 内容数据
  factory LaymanVersionChunk.content(String content) => LaymanVersionChunk._(LaymanVersionChunkType.content, content);

  /// 阶段转换消息
  factory LaymanVersionChunk.transition(String message) => LaymanVersionChunk._(LaymanVersionChunkType.transition, message);

  /// 阶段完成（包含完整内容）
  factory LaymanVersionChunk.stageComplete(String stage, String content) => LaymanVersionChunk._(LaymanVersionChunkType.stageComplete, content, stage);

  /// 完成标记
  factory LaymanVersionChunk.complete(String professionalVersion) => LaymanVersionChunk._(LaymanVersionChunkType.complete, professionalVersion);

  /// 错误消息
  factory LaymanVersionChunk.error(String error) => LaymanVersionChunk._(LaymanVersionChunkType.error, error);

  bool get isStage => type == LaymanVersionChunkType.stage;
  bool get isContent => type == LaymanVersionChunkType.content;
  bool get isTransition => type == LaymanVersionChunkType.transition;
  bool get isStageComplete => type == LaymanVersionChunkType.stageComplete;
  bool get isComplete => type == LaymanVersionChunkType.complete;
  bool get isError => type == LaymanVersionChunkType.error;
}

enum LaymanVersionChunkType {
  stage,
  content,
  transition,
  stageComplete,
  complete,
  error,
}

/// Go代理服务
class GoProxyService {
  static const String _chatEndpoint = '/chat/completions';
  final SystemConfigService _systemConfigService;

  GoProxyService(this._systemConfigService);

  /// 发送聊天消息（流式响应）
  Stream<String> sendChatMessage({
    required String token,
    required String agentId,
    required String modelId,
    required List<ChatMessage> messages,
  }) async* {
    try {
      // 获取动态API地址
      final baseUrl = await _systemConfigService.getGoProxyApiUrl();

      // 构造请求体 - 转换ChatMessage为Go服务期望的格式
      final requestBody = {
        'agentId': agentId,
        'modelId': modelId,
        'messages': messages.map((msg) => _convertMessageToApiFormat(msg)).toList(),
        'stream': true,
      };

      // 创建HTTP请求
      final request = http.Request(
        'POST',
        Uri.parse('$baseUrl$_chatEndpoint'),
      );

      request.headers.addAll({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache',
      });

      request.body = jsonEncode(requestBody);

      // 创建HTTP客户端，设置超时
      final client = http.Client();

      // 发送请求
      final streamedResponse = await client.send(request).timeout(
        AppConstants.receiveTimeout,
        onTimeout: () {
          client.close();
          throw Exception('请求超时，请稍后重试');
        },
      );

      if (streamedResponse.statusCode != 200) {
        final errorBody = await streamedResponse.stream.bytesToString();
        client.close();
        print('Go代理服务请求失败:');
        print('状态码: ${streamedResponse.statusCode}');
        print('请求体: ${jsonEncode(requestBody)}');
        print('错误响应: $errorBody');
        throw Exception('Go代理服务错误 ${streamedResponse.statusCode}: $errorBody');
      }

      try {
        // 处理流式响应
        await for (final chunk in streamedResponse.stream.transform(utf8.decoder)) {
          final lines = chunk.split('\n');

          for (final line in lines) {
            final trimmedLine = line.trim();
            if (trimmedLine.isEmpty) continue;

            // 处理Server-Sent Events格式
            if (trimmedLine.startsWith('data: ')) {
              final data = trimmedLine.substring(6);

              // 检查是否为结束标记
              if (data == '[DONE]') {
                client.close();
                return;
              }

              try {
                // 解析JSON数据
                final jsonData = jsonDecode(data) as Map<String, dynamic>;
                final choices = jsonData['choices'] as List?;

                if (choices != null && choices.isNotEmpty) {
                  final choice = choices[0] as Map<String, dynamic>;
                  final delta = choice['delta'] as Map<String, dynamic>?;

                  if (delta != null && delta['content'] != null) {
                    yield delta['content'] as String;
                  }
                }
              } catch (e) {
                // 忽略JSON解析错误，继续处理下一行
                continue;
              }
            }
          }
        }
      } finally {
        // 确保客户端被关闭
        client.close();
      }
    } catch (e) {
      throw Exception('发送聊天消息失败: $e');
    }
  }

  /// 发送聊天消息（非流式响应）
  Future<String> sendChatMessageSync({
    required String token,
    required String agentId,
    required String modelId,
    required List<ChatMessage> messages,
  }) async {
    try {
      // 获取动态API地址
      final baseUrl = await _systemConfigService.getGoProxyApiUrl();

      // 构造请求体 - 转换ChatMessage为Go服务期望的格式
      final requestBody = {
        'agentId': agentId,
        'modelId': modelId,
        'messages': messages.map((msg) => _convertMessageToApiFormat(msg)).toList(),
        'stream': false,
      };

      final response = await http.post(
        Uri.parse('$baseUrl$_chatEndpoint'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(requestBody),
      ).timeout(AppConstants.receiveTimeout);

      if (response.statusCode != 200) {
        print('Go代理服务请求失败:');
        print('状态码: ${response.statusCode}');
        print('请求体: ${jsonEncode(requestBody)}');
        print('错误响应: ${response.body}');
        throw Exception('Go代理服务错误 ${response.statusCode}: ${response.body}');
      }

      final jsonData = jsonDecode(response.body) as Map<String, dynamic>;

      // 检查是否为大白话版本响应
      if (jsonData.containsKey('professional_version') && jsonData.containsKey('layman_version')) {
        // 返回大白话版本的内容
        return jsonData['layman_version'] as String;
      }

      // 普通响应格式
      final choices = jsonData['choices'] as List?;

      if (choices != null && choices.isNotEmpty) {
        final choice = choices[0] as Map<String, dynamic>;
        final message = choice['message'] as Map<String, dynamic>?;

        if (message != null && message['content'] != null) {
          return message['content'] as String;
        }
      }

      throw Exception('无效的响应格式');
    } catch (e) {
      throw Exception('发送聊天消息失败: $e');
    }
  }

  /// 发送聊天消息并获取大白话版本（流式）
  Stream<LaymanVersionChunk> sendChatMessageWithLaymanVersionStream({
    required String token,
    required String agentId,
    required String modelId,
    required List<ChatMessage> messages,
  }) async* {
    try {
      // 获取动态API地址
      final baseUrl = await _systemConfigService.getGoProxyApiUrl();

      // 构造请求体 - 转换ChatMessage为Go服务期望的格式
      final requestBody = {
        'agentId': agentId,
        'modelId': modelId,
        'messages': messages.map((msg) => _convertMessageToApiFormat(msg)).toList(),
        'stream': true,
      };

      // 创建HTTP请求
      final request = http.Request(
        'POST',
        Uri.parse('$baseUrl$_chatEndpoint'),
      );

      request.headers.addAll({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache',
      });

      request.body = jsonEncode(requestBody);

      // 创建HTTP客户端，设置超时
      final client = http.Client();

      // 发送请求
      final streamedResponse = await client.send(request).timeout(
        AppConstants.receiveTimeout,
        onTimeout: () {
          client.close();
          throw Exception('请求超时，请稍后重试');
        },
      );

      if (streamedResponse.statusCode != 200) {
        final errorBody = await streamedResponse.stream.bytesToString();
        client.close();
        print('Go代理服务请求失败:');
        print('状态码: ${streamedResponse.statusCode}');
        print('请求体: ${jsonEncode(requestBody)}');
        print('错误响应: $errorBody');
        throw Exception('Go代理服务错误 ${streamedResponse.statusCode}: $errorBody');
      }

      try {
        // 处理流式响应
        await for (final chunk in streamedResponse.stream.transform(utf8.decoder)) {
          final lines = chunk.split('\n');

          for (final line in lines) {
            final trimmedLine = line.trim();
            if (trimmedLine.isEmpty) continue;

            // 处理Server-Sent Events格式
            if (trimmedLine.startsWith('data: ')) {
              final data = trimmedLine.substring(6);

              // 检查是否为结束标记
              if (data == '[DONE]') {
                client.close();
                return;
              }

              try {
                // 解析JSON数据
                final jsonData = jsonDecode(data) as Map<String, dynamic>;

                // 处理不同类型的消息
                if (jsonData.containsKey('stage')) {
                  // 阶段标识
                  yield LaymanVersionChunk.stage(jsonData['stage'] as String);
                } else if (jsonData.containsKey('transition')) {
                  // 阶段转换消息
                  yield LaymanVersionChunk.transition(jsonData['transition'] as String);
                } else if (jsonData.containsKey('stage_complete')) {
                  // 阶段完成（包含完整内容）
                  yield LaymanVersionChunk.stageComplete(
                    jsonData['stage_complete'] as String,
                    jsonData['content'] as String,
                  );
                } else if (jsonData.containsKey('complete')) {
                  // 完成标记
                  yield LaymanVersionChunk.complete(
                    jsonData['professional_version'] as String? ?? '',
                  );
                } else if (jsonData.containsKey('error')) {
                  // 错误消息
                  yield LaymanVersionChunk.error(jsonData['error'] as String);
                } else if (jsonData.containsKey('choices')) {
                  // 内容数据
                  final choices = jsonData['choices'] as List?;
                  if (choices != null && choices.isNotEmpty) {
                    final choice = choices[0] as Map<String, dynamic>;
                    final delta = choice['delta'] as Map<String, dynamic>?;

                    if (delta != null && delta['content'] != null) {
                      yield LaymanVersionChunk.content(delta['content'] as String);
                    }
                  }
                }
              } catch (e) {
                // 忽略JSON解析错误，继续处理下一行
                continue;
              }
            }
          }
        }
      } finally {
        // 确保客户端被关闭
        client.close();
      }
    } catch (e) {
      throw Exception('发送聊天消息失败: $e');
    }
  }

  /// 检查Go代理服务健康状态
  Future<bool> checkHealth() async {
    try {
      // 获取动态API地址，去掉/api后缀
      final baseUrl = await _systemConfigService.getGoProxyApiUrl();
      final healthUrl = baseUrl.replaceAll('/api', '');

      final response = await http.get(
        Uri.parse('$healthUrl/healthz'),
      ).timeout(const Duration(seconds: 5));

      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  /// 获取Go代理服务状态信息
  Future<Map<String, dynamic>?> getStatus() async {
    try {
      // 获取动态API地址，去掉/api后缀
      final baseUrl = await _systemConfigService.getGoProxyApiUrl();
      final statusUrl = baseUrl.replaceAll('/api', '');

      final response = await http.get(
        Uri.parse('$statusUrl/status'),
      ).timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        return jsonDecode(response.body) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// 将ChatMessage转换为API格式
  Map<String, dynamic> _convertMessageToApiFormat(ChatMessage msg) {
    final result = <String, dynamic>{
      'role': _convertSenderToRole(msg.sender),
      'content': msg.content,
    };

    // 添加八字数据
    if (msg.baziData != null && msg.baziData!.isNotEmpty) {
      result['baziData'] = msg.baziData;
    }

    // 添加图片数据
    if (msg.hasImages) {
      result['images'] = msg.images!.map((image) => {
        'id': image.id,
        'base64_data': image.base64Data,
        'mime_type': image.mimeType,
        'width': image.width,
        'height': image.height,
        'file_size': image.fileSize,
      }).toList();
    }

    // 添加消息类型
    if (msg.messageType != MessageType.text) {
      result['message_type'] = msg.messageType.name;
    }

    return result;
  }

  /// 将Flutter的MessageSender转换为Go服务期望的role格式
  String _convertSenderToRole(MessageSender sender) {
    switch (sender) {
      case MessageSender.user:
        return 'user';
      case MessageSender.assistant:
        return 'assistant';
      case MessageSender.system:
        return 'system';
    }
  }
}
