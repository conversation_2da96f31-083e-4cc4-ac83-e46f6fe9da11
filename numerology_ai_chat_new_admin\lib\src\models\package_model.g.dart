// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'package_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PaymentPackage _$PaymentPackageFromJson(Map<String, dynamic> json) =>
    PaymentPackage(
      id: json['_id'] as String,
      packageName: json['packageName'] as String,
      packageDescription: json['packageDescription'] as String,
      originalPrice: (json['originalPrice'] as num).toInt(),
      price: (json['price'] as num).toInt(),
      quotaAmount: (json['quotaCount'] as num).toInt(),
      isActive: json['isActive'] as bool,
      sortOrder: (json['sortOrder'] as num).toInt(),
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      isRecommended: json['isRecommended'] as bool?,
      validDays: (json['validDays'] as num?)?.toInt(),
      discountRate: (json['discountRate'] as num?)?.toDouble(),
      maxPurchaseCount: (json['maxPurchaseCount'] as num?)?.toInt(),
      minPurchaseCount: (json['minPurchaseCount'] as num?)?.toInt(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      createdBy: json['createdBy'] as String?,
      updatedBy: json['updatedBy'] as String?,
    );

Map<String, dynamic> _$PaymentPackageToJson(PaymentPackage instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'packageName': instance.packageName,
      'packageDescription': instance.packageDescription,
      'originalPrice': instance.originalPrice,
      'price': instance.price,
      'quotaCount': instance.quotaAmount,
      'isActive': instance.isActive,
      'sortOrder': instance.sortOrder,
      'tags': instance.tags,
      'isRecommended': instance.isRecommended,
      'validDays': instance.validDays,
      'discountRate': instance.discountRate,
      'maxPurchaseCount': instance.maxPurchaseCount,
      'minPurchaseCount': instance.minPurchaseCount,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'createdBy': instance.createdBy,
      'updatedBy': instance.updatedBy,
    };

CreatePackageRequest _$CreatePackageRequestFromJson(
        Map<String, dynamic> json) =>
    CreatePackageRequest(
      packageName: json['packageName'] as String,
      packageDescription: json['packageDescription'] as String,
      originalPrice: (json['originalPrice'] as num).toDouble(),
      currentPrice: (json['currentPrice'] as num).toDouble(),
      quotaAmount: (json['quotaAmount'] as num).toInt(),
      isActive: json['isActive'] as bool? ?? true,
      sortOrder: (json['sortOrder'] as num?)?.toInt() ?? 0,
      promotionText: json['promotionText'] as String?,
      promotionStartTime: json['promotionStartTime'] == null
          ? null
          : DateTime.parse(json['promotionStartTime'] as String),
      promotionEndTime: json['promotionEndTime'] == null
          ? null
          : DateTime.parse(json['promotionEndTime'] as String),
    );

Map<String, dynamic> _$CreatePackageRequestToJson(
        CreatePackageRequest instance) =>
    <String, dynamic>{
      'packageName': instance.packageName,
      'packageDescription': instance.packageDescription,
      'originalPrice': instance.originalPrice,
      'currentPrice': instance.currentPrice,
      'quotaAmount': instance.quotaAmount,
      'isActive': instance.isActive,
      'sortOrder': instance.sortOrder,
      'promotionText': instance.promotionText,
      'promotionStartTime': instance.promotionStartTime?.toIso8601String(),
      'promotionEndTime': instance.promotionEndTime?.toIso8601String(),
    };

UpdatePackageRequest _$UpdatePackageRequestFromJson(
        Map<String, dynamic> json) =>
    UpdatePackageRequest(
      packageName: json['packageName'] as String?,
      packageDescription: json['packageDescription'] as String?,
      originalPrice: (json['originalPrice'] as num?)?.toDouble(),
      currentPrice: (json['currentPrice'] as num?)?.toDouble(),
      quotaAmount: (json['quotaAmount'] as num?)?.toInt(),
      isActive: json['isActive'] as bool?,
      sortOrder: (json['sortOrder'] as num?)?.toInt(),
      promotionText: json['promotionText'] as String?,
      promotionStartTime: json['promotionStartTime'] == null
          ? null
          : DateTime.parse(json['promotionStartTime'] as String),
      promotionEndTime: json['promotionEndTime'] == null
          ? null
          : DateTime.parse(json['promotionEndTime'] as String),
    );

Map<String, dynamic> _$UpdatePackageRequestToJson(
        UpdatePackageRequest instance) =>
    <String, dynamic>{
      'packageName': instance.packageName,
      'packageDescription': instance.packageDescription,
      'originalPrice': instance.originalPrice,
      'currentPrice': instance.currentPrice,
      'quotaAmount': instance.quotaAmount,
      'isActive': instance.isActive,
      'sortOrder': instance.sortOrder,
      'promotionText': instance.promotionText,
      'promotionStartTime': instance.promotionStartTime?.toIso8601String(),
      'promotionEndTime': instance.promotionEndTime?.toIso8601String(),
    };
