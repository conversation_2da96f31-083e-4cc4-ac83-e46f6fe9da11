import 'dart:convert';
import '../core/storage/storage_service.dart' as core;
import '../core/constants/app_constants.dart';

/// 简化的存储服务适配器
class StorageService {
  final core.StorageService _coreStorageService;

  StorageService() : _coreStorageService = core.HiveStorageService();

  /// 初始化存储服务
  Future<void> init() async {
    await _coreStorageService.init();
  }

  /// 保存字符串
  Future<void> setString(String key, String value) async {
    final result = await _coreStorageService.set(key, value, boxName: HiveBoxes.cache);
    result.when(
      success: (_) {},
      failure: (error) => throw Exception('保存字符串失败: ${error.message}'),
    );
  }

  /// 获取字符串
  Future<String?> getString(String key) async {
    final result = await _coreStorageService.get<String>(key, boxName: HiveBoxes.cache);
    return result.when(
      success: (data) => data,
      failure: (_) => null,
    );
  }

  /// 保存JSON对象
  Future<void> setJson(String key, Map<String, dynamic> value) async {
    final jsonString = jsonEncode(value);
    await setString(key, jsonString);
  }

  /// 获取JSON对象
  Future<Map<String, dynamic>?> getJson(String key) async {
    final jsonString = await getString(key);
    if (jsonString != null) {
      try {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  /// 保存JSON数组
  Future<void> setJsonList(String key, List<Map<String, dynamic>> value) async {
    final jsonString = jsonEncode(value);
    await setString(key, jsonString);
  }

  /// 获取JSON数组
  Future<List<Map<String, dynamic>>?> getJsonList(String key) async {
    final jsonString = await getString(key);
    if (jsonString != null) {
      try {
        final decoded = jsonDecode(jsonString);
        if (decoded is List) {
          return decoded.cast<Map<String, dynamic>>();
        }
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  /// 保存布尔值
  Future<void> setBool(String key, bool value) async {
    final result = await _coreStorageService.set(key, value, boxName: HiveBoxes.cache);
    result.when(
      success: (_) {},
      failure: (error) => throw Exception('保存布尔值失败: ${error.message}'),
    );
  }

  /// 获取布尔值
  Future<bool?> getBool(String key) async {
    final result = await _coreStorageService.get<bool>(key, boxName: HiveBoxes.cache);
    return result.when(
      success: (data) => data,
      failure: (_) => null,
    );
  }

  /// 保存整数
  Future<void> setInt(String key, int value) async {
    final result = await _coreStorageService.set(key, value, boxName: HiveBoxes.cache);
    result.when(
      success: (_) {},
      failure: (error) => throw Exception('保存整数失败: ${error.message}'),
    );
  }

  /// 获取整数
  Future<int?> getInt(String key) async {
    final result = await _coreStorageService.get<int>(key, boxName: HiveBoxes.cache);
    return result.when(
      success: (data) => data,
      failure: (_) => null,
    );
  }

  /// 删除键值
  Future<void> remove(String key) async {
    final result = await _coreStorageService.delete(key, boxName: HiveBoxes.cache);
    result.when(
      success: (_) {},
      failure: (error) => throw Exception('删除键值失败: ${error.message}'),
    );
  }

  /// 清除所有缓存
  Future<void> clear() async {
    final result = await _coreStorageService.clear(boxName: HiveBoxes.cache);
    result.when(
      success: (_) {},
      failure: (error) => throw Exception('清除缓存失败: ${error.message}'),
    );
  }

  /// 检查键是否存在
  Future<bool> containsKey(String key) async {
    final value = await getString(key);
    return value != null;
  }

  /// 获取所有键
  Future<List<String>> getKeys() async {
    final result = await _coreStorageService.getKeys(boxName: HiveBoxes.cache);
    return result.when(
      success: (data) => data,
      failure: (_) => [],
    );
  }
}

/// Hive盒子名称（从常量文件导入）
class HiveBoxes {
  static const String settings = 'settings';
  static const String conversations = 'conversations';
  static const String userProfiles = 'user_profiles';
  static const String cache = 'cache';
}
