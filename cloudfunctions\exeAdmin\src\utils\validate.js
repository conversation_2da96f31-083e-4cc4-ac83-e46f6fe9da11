const Joi = require('joi')

/**
 * 管理员登录参数校验
 */
const adminLoginSchema = Joi.object({
  adminAccount: Joi.string()
    .required()
    .messages({
      'any.required': '管理员账号不能为空'
    }),
  adminPassword: Joi.string()
    .required()
    .messages({
      'any.required': '管理员密码不能为空'
    })
})

/**
 * 管理员鉴权参数校验
 */
const adminAuthSchema = Joi.object({
  authorization: Joi.string()
    .pattern(/^Bearer .+/)
    .required()
    .messages({
      'string.pattern.base': 'Authorization格式不正确，应为Bearer token',
      'any.required': 'Authorization不能为空'
    })
})

/**
 * 用户创建参数校验
 */
const createUserSchema = Joi.object({
  username: Joi.string()
    .alphanum()
    .min(3)
    .max(30)
    .required()
    .messages({
      'string.alphanum': '用户名只能包含字母和数字',
      'string.min': '用户名至少3个字符',
      'string.max': '用户名最多30个字符',
      'any.required': '用户名不能为空'
    }),
  password: Joi.string()
    .min(6)
    .max(50)
    .required()
    .messages({
      'string.min': '密码至少6个字符',
      'string.max': '密码最多50个字符',
      'any.required': '密码不能为空'
    }),
  email: Joi.string()
    .email()
    .optional()
    .allow('')
    .messages({
      'string.email': '邮箱格式不正确'
    }),
  phone: Joi.string()
    .pattern(/^1[3-9]\d{9}$/)
    .optional()
    .allow('')
    .messages({
      'string.pattern.base': '手机号格式不正确'
    }),
  initialQuota: Joi.number()
    .integer()
    .min(0)
    .default(10)
    .messages({
      'number.integer': '初始额度必须是整数',
      'number.min': '初始额度不能小于0'
    }),
  reason: Joi.string()
    .max(200)
    .default('管理员创建用户')
    .messages({
      'string.max': '备注最多200个字符'
    })
})

/**
 * 用户会员更新参数校验
 */
const updateUserMembershipSchema = Joi.object({
  userId: Joi.string()
    .required()
    .messages({
      'any.required': '用户ID不能为空'
    }),
  membershipType: Joi.string()
    .valid('月度会员', '季度会员', '年度会员', '永久会员')
    .required()
    .messages({
      'any.only': '会员类型必须是：月度会员、季度会员、年度会员、永久会员之一',
      'any.required': '会员类型不能为空'
    }),
  durationDays: Joi.number()
    .integer()
    .min(1)
    .when('membershipType', {
      is: '永久会员',
      then: Joi.optional(),
      otherwise: Joi.required()
    })
    .messages({
      'number.integer': '持续天数必须是整数',
      'number.min': '持续天数至少为1天',
      'any.required': '非永久会员必须指定持续天数'
    }),
  reason: Joi.string()
    .max(200)
    .default('管理员操作')
    .messages({
      'string.max': '备注最多200个字符'
    })
})

/**
 * 用户额度更新参数校验
 */
const updateUserQuotaSchema = Joi.object({
  userId: Joi.string()
    .required()
    .messages({
      'any.required': '用户ID不能为空'
    }),
  addedCount: Joi.number()
    .integer()
    .min(1)
    .required()
    .messages({
      'number.integer': '增加次数必须是整数',
      'number.min': '增加次数至少为1',
      'any.required': '增加次数不能为空'
    }),
  reason: Joi.string()
    .max(200)
    .default('管理员充值')
    .messages({
      'string.max': '备注最多200个字符'
    })
})

/**
 * 用户状态更新参数校验
 */
const updateUserStatusSchema = Joi.object({
  userId: Joi.string()
    .required()
    .messages({
      'any.required': '用户ID不能为空'
    }),
  status: Joi.string()
    .valid('激活', '禁用', '暂停')
    .required()
    .messages({
      'any.only': '用户状态必须是：激活、禁用、暂停之一',
      'any.required': '用户状态不能为空'
    }),
  reason: Joi.string()
    .max(200)
    .default('管理员操作')
    .messages({
      'string.max': '备注最多200个字符'
    })
})

/**
 * 智能体创建/更新参数校验
 */
const agentSchema = Joi.object({
  agentName: Joi.string()
    .min(1)
    .max(50)
    .required()
    .messages({
      'string.min': '智能体名称不能为空',
      'string.max': '智能体名称最多50个字符',
      'any.required': '智能体名称不能为空'
    }),
  agentPrompt: Joi.string()
    .min(1)
    .max(5000)
    .required()
    .messages({
      'string.min': '智能体提示词不能为空',
      'string.max': '智能体提示词最多5000个字符',
      'any.required': '智能体提示词不能为空'
    }),
  agentType: Joi.string()
    .valid('八字', '紫微', '无需携带内容')
    .required()
    .messages({
      'any.only': '智能体类型必须是：八字、紫微、无需携带内容之一',
      'any.required': '智能体类型不能为空'
    }),
  description: Joi.string()
    .max(200)
    .default('')
    .messages({
      'string.max': '描述最多200个字符'
    }),
  isActive: Joi.boolean()
    .default(true),
  sortOrder: Joi.number()
    .integer()
    .min(0)
    .default(0)
    .messages({
      'number.integer': '排序权重必须是整数',
      'number.min': '排序权重不能小于0'
    })
})

/**
 * 模型创建/更新参数校验
 */
const modelSchema = Joi.object({
  modelName: Joi.string()
    .min(1)
    .max(50)
    .required()
    .messages({
      'string.min': '模型名称不能为空',
      'string.max': '模型名称最多50个字符',
      'any.required': '模型名称不能为空'
    }),
  modelApiKey: Joi.string()
    .min(1)
    .required()
    .messages({
      'string.min': 'API密钥不能为空',
      'any.required': 'API密钥不能为空'
    }),
  modelApiUrl: Joi.string()
    .uri()
    .required()
    .messages({
      'string.uri': 'API地址格式不正确',
      'any.required': 'API地址不能为空'
    }),
  modelDisplayName: Joi.string()
    .min(1)
    .max(50)
    .required()
    .messages({
      'string.min': '显示名称不能为空',
      'string.max': '显示名称最多50个字符',
      'any.required': '显示名称不能为空'
    }),
  maxTokens: Joi.number()
    .integer()
    .min(1)
    .max(100000)
    .default(4000)
    .messages({
      'number.integer': '最大Token数必须是整数',
      'number.min': '最大Token数至少为1',
      'number.max': '最大Token数不能超过100000'
    }),
  temperature: Joi.number()
    .min(0)
    .max(2)
    .default(0.7)
    .messages({
      'number.min': '温度参数不能小于0',
      'number.max': '温度参数不能大于2'
    }),
  description: Joi.string()
    .max(200)
    .default('')
    .messages({
      'string.max': '描述最多200个字符'
    }),
  isActive: Joi.boolean()
    .default(true),
  sortOrder: Joi.number()
    .integer()
    .min(0)
    .default(0)
    .messages({
      'number.integer': '排序权重必须是整数',
      'number.min': '排序权重不能小于0'
    })
})

/**
 * 页面配置更新参数校验
 */
const pageUpdateSchema = Joi.object({
  pageId: Joi.string()
    .required()
    .messages({
      'any.required': '页面ID不能为空'
    }),
  pageTitle: Joi.string()
    .min(1)
    .max(100)
    .optional()
    .messages({
      'string.min': '页面标题不能为空',
      'string.max': '页面标题最多100个字符'
    }),
  pageContent: Joi.string()
    .min(1)
    .optional()
    .messages({
      'string.min': '页面内容不能为空'
    }),
  isActive: Joi.boolean()
    .optional(),
  sortOrder: Joi.number()
    .integer()
    .min(0)
    .optional()
    .messages({
      'number.integer': '排序权重必须是整数',
      'number.min': '排序权重不能小于0'
    }),
  metaDescription: Joi.string()
    .max(200)
    .optional()
    .messages({
      'string.max': '页面描述最多200个字符'
    })
})

/**
 * 通用校验函数
 * @param {object} schema Joi校验模式
 * @param {object} data 待校验数据
 * @returns {object} 校验结果
 */
function validate(schema, data) {
  const { error, value } = schema.validate(data, {
    abortEarly: false, // 返回所有错误
    stripUnknown: true // 移除未知字段
  })
  
  if (error) {
    const errorMessage = error.details.map(detail => detail.message).join('; ')
    throw new Error(`参数校验失败: ${errorMessage}`)
  }
  
  return value
}

module.exports = {
  validate,
  adminLoginSchema,
  adminAuthSchema,
  createUserSchema,
  updateUserMembershipSchema,
  updateUserQuotaSchema,
  updateUserStatusSchema,
  agentSchema,
  modelSchema,
  pageUpdateSchema
}