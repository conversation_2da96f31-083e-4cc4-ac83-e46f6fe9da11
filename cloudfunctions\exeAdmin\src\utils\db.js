const cloud = require('wx-server-sdk')
const CryptoJS = require('crypto-js')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 获取数据库引用
const db = cloud.database()
const _ = db.command

// 数据库集合名称常量
const COLLECTIONS = {
  USERS: 'exe_users',
  AGENTS: 'exe_agents', 
  MODELS: 'exe_models',
  ADMINS: 'exe_admins',
  PAGES: 'exe_pages'
}

// AES加密密钥 - 生产环境应从环境变量获取
const AES_SECRET_KEY = process.env.AES_SECRET_KEY || 'your-aes-secret-key-32-chars-long'

/**
 * AES加密
 * @param {string} text 待加密文本
 * @returns {string} 加密后的文本
 */
function encryptAES(text) {
  if (!text) return ''
  return CryptoJS.AES.encrypt(text, AES_SECRET_KEY).toString()
}

/**
 * AES解密
 * @param {string} encryptedText 加密的文本
 * @returns {string} 解密后的文本
 */
function decryptAES(encryptedText) {
  if (!encryptedText) return ''
  const bytes = CryptoJS.AES.decrypt(encryptedText, AES_SECRET_KEY)
  return bytes.toString(CryptoJS.enc.Utf8)
}

/**
 * 获取集合引用
 * @param {string} collectionName 集合名称
 * @returns {object} 集合引用
 */
function getCollection(collectionName) {
  return db.collection(collectionName)
}

/**
 * 管理员集合操作
 */
const adminCollection = {
  // 根据账号查找管理员
  async findByAccount(adminAccount) {
    const result = await getCollection(COLLECTIONS.ADMINS)
      .where({ adminAccount })
      .get()
    return result.data[0] || null
  },
  
  // 根据ID查找管理员
  async findById(adminId) {
    const result = await getCollection(COLLECTIONS.ADMINS)
      .doc(adminId)
      .get()
    return result.data || null
  },
  
  // 更新管理员登录信息
  async updateLoginInfo(adminId) {
    return await getCollection(COLLECTIONS.ADMINS)
      .doc(adminId)
      .update({
        data: {
          lastLoginAt: new Date(),
          loginCount: _.inc(1),
          updatedAt: new Date()
        }
      })
  }
}

/**
 * 用户集合操作（管理员视角）
 */
const userCollection = {
  // 分页获取用户列表
  async getList(page = 1, pageSize = 20, filters = {}) {
    let query = getCollection(COLLECTIONS.USERS)
    
    // 应用过滤条件
    if (filters.username) {
      query = query.where({
        username: new RegExp(filters.username, 'i')
      })
    }
    if (filters.status) {
      query = query.where({ status: filters.status })
    }
    if (filters.membershipType) {
      query = query.where({ 'membership.type': filters.membershipType })
    }
    
    // 获取总数
    const countResult = await query.count()
    const total = countResult.total
    
    // 分页查询
    const result = await query
      .orderBy('createdAt', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .field({
        password: false, // 不返回密码
        refreshToken: false // 不返回refreshToken
      })
      .get()
    
    return {
      data: result.data,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }
  },
  
  // 根据ID查找用户
  async findById(userId) {
    const result = await getCollection(COLLECTIONS.USERS)
      .doc(userId)
      .field({
        password: false,
        refreshToken: false
      })
      .get()
    return result.data || null
  },

  // 根据用户名查找用户
  async findByUsername(username) {
    const result = await getCollection(COLLECTIONS.USERS)
      .where({ username })
      .get()
    return result.data[0] || null
  },
  
  // 创建用户
  async create(userData) {
    const result = await getCollection(COLLECTIONS.USERS)
      .add({
        data: {
          ...userData,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      })
    return result._id
  },
  
  // 更新用户
  async update(userId, updateData) {
    return await getCollection(COLLECTIONS.USERS)
      .doc(userId)
      .update({
        data: {
          ...updateData,
          updatedAt: new Date()
        }
      })
  },
  
  // 原子操作：增加用户额度
  async addQuota(userId, addedCount) {
    return await getCollection(COLLECTIONS.USERS)
      .doc(userId)
      .update({
        data: {
          availableCount: _.inc(addedCount),
          updatedAt: new Date()
        }
      })
  },

  // 原子操作：增加用户额度并记录购买历史
  async addQuotaWithHistory(userId, addedCount, reason = '管理员充值', operator = 'admin') {
    const purchaseRecord = {
      date: new Date(),
      type: '额度充值',
      details: {
        addedCount: addedCount
      },
      reason: reason,
      operator: operator
    }

    return await getCollection(COLLECTIONS.USERS)
      .doc(userId)
      .update({
        data: {
          availableCount: _.inc(addedCount),
          purchaseHistory: _.push(purchaseRecord),
          updatedAt: new Date()
        }
      })
  }
}

/**
 * 智能体集合操作（管理员视角）
 */
const agentCollection = {
  // 获取所有智能体列表
  async getList() {
    const result = await getCollection(COLLECTIONS.AGENTS)
      .orderBy('sortOrder', 'asc')
      .get()
    return result.data
  },
  
  // 根据ID查找智能体
  async findById(agentId) {
    const result = await getCollection(COLLECTIONS.AGENTS)
      .doc(agentId)
      .get()
    return result.data || null
  },
  
  // 创建智能体
  async create(agentData) {
    const result = await getCollection(COLLECTIONS.AGENTS)
      .add({
        data: {
          ...agentData,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      })
    return result._id
  },
  
  // 更新智能体
  async update(agentId, updateData) {
    return await getCollection(COLLECTIONS.AGENTS)
      .doc(agentId)
      .update({
        data: {
          ...updateData,
          updatedAt: new Date()
        }
      })
  },
  
  // 删除智能体
  async delete(agentId) {
    return await getCollection(COLLECTIONS.AGENTS)
      .doc(agentId)
      .remove()
  }
}

/**
 * 模型集合操作（管理员视角）
 */
const modelCollection = {
  // 获取所有模型列表
  async getList() {
    const result = await getCollection(COLLECTIONS.MODELS)
      .orderBy('sortOrder', 'asc')
      .get()
    
    // 解密API密钥用于管理员查看
    return result.data.map(model => ({
      ...model,
      modelApiKey: model.modelApiKey ? decryptAES(model.modelApiKey) : ''
    }))
  },
  
  // 根据ID查找模型
  async findById(modelId) {
    const result = await getCollection(COLLECTIONS.MODELS)
      .doc(modelId)
      .get()
    
    if (result.data && result.data.modelApiKey) {
      result.data.modelApiKey = decryptAES(result.data.modelApiKey)
    }
    
    return result.data || null
  },
  
  // 创建模型
  async create(modelData) {
    const dataToCreate = { ...modelData }
    if (dataToCreate.modelApiKey) {
      dataToCreate.modelApiKey = encryptAES(dataToCreate.modelApiKey)
    }
    
    const result = await getCollection(COLLECTIONS.MODELS)
      .add({
        data: {
          ...dataToCreate,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      })
    return result._id
  },
  
  // 更新模型
  async update(modelId, updateData) {
    const dataToUpdate = { ...updateData }
    if (dataToUpdate.modelApiKey) {
      dataToUpdate.modelApiKey = encryptAES(dataToUpdate.modelApiKey)
    }
    
    return await getCollection(COLLECTIONS.MODELS)
      .doc(modelId)
      .update({
        data: {
          ...dataToUpdate,
          updatedAt: new Date()
        }
      })
  },
  
  // 删除模型
  async delete(modelId) {
    return await getCollection(COLLECTIONS.MODELS)
      .doc(modelId)
      .remove()
  }
}

/**
 * 页面配置集合操作
 */
const pageCollection = {
  // 获取页面配置列表（支持分页和筛选）
  async getList(where = {}, skip = 0, limit = 20) {
    const query = getCollection(COLLECTIONS.PAGES).where(where)
    
    const result = await query
      .orderBy('sortOrder', 'asc')
      .skip(skip)
      .limit(limit)
      .get()
    return result.data
  },
  
  // 获取页面配置总数
  async getCount(where = {}) {
    const countResult = await getCollection(COLLECTIONS.PAGES).where(where).count()
    return countResult.total
  },
  
  // 根据ID查找页面配置
  async findById(pageId) {
    const result = await getCollection(COLLECTIONS.PAGES)
      .doc(pageId)
      .get()
    return result.data || null
  },
  
  // 根据slug查找页面配置
  async findBySlug(slug) {
    const result = await getCollection(COLLECTIONS.PAGES)
      .where({ slug })
      .get()
    return result.data[0] || null
  },
  
  // 创建页面配置
  async create(pageData) {
    const result = await getCollection(COLLECTIONS.PAGES)
      .add({
        data: {
          ...pageData,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      })
    return result._id
  },
  
  // 更新页面配置
  async update(pageId, updateData) {
    return await getCollection(COLLECTIONS.PAGES)
      .doc(pageId)
      .update({
        data: {
          ...updateData,
          updatedAt: new Date()
        }
      })
  },
  
  // 删除页面配置
  async delete(pageId) {
    return await getCollection(COLLECTIONS.PAGES)
      .doc(pageId)
      .remove()
  }
}

module.exports = {
  db,
  _,
  COLLECTIONS,
  getCollection,
  encryptAES,
  decryptAES,
  adminCollection,
  userCollection,
  agentCollection,
  modelCollection,
  pageCollection
}