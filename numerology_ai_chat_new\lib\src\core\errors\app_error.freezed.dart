// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_error.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AppError {
  String get message => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String message, String? code, dynamic originalError)
        network,
    required TResult Function(String message, String? code) authentication,
    required TResult Function(String message, String field) validation,
    required TResult Function(String message, int? statusCode, String? code)
        server,
    required TResult Function(String message, String? operation) storage,
    required TResult Function(String message, String? model, String? code) ai,
    required TResult Function(String message, dynamic originalError) unknown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code, dynamic originalError)?
        network,
    TResult? Function(String message, String? code)? authentication,
    TResult? Function(String message, String field)? validation,
    TResult? Function(String message, int? statusCode, String? code)? server,
    TResult? Function(String message, String? operation)? storage,
    TResult? Function(String message, String? model, String? code)? ai,
    TResult? Function(String message, dynamic originalError)? unknown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code, dynamic originalError)?
        network,
    TResult Function(String message, String? code)? authentication,
    TResult Function(String message, String field)? validation,
    TResult Function(String message, int? statusCode, String? code)? server,
    TResult Function(String message, String? operation)? storage,
    TResult Function(String message, String? model, String? code)? ai,
    TResult Function(String message, dynamic originalError)? unknown,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkError value) network,
    required TResult Function(AuthenticationError value) authentication,
    required TResult Function(ValidationError value) validation,
    required TResult Function(ServerError value) server,
    required TResult Function(StorageError value) storage,
    required TResult Function(AIError value) ai,
    required TResult Function(UnknownError value) unknown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkError value)? network,
    TResult? Function(AuthenticationError value)? authentication,
    TResult? Function(ValidationError value)? validation,
    TResult? Function(ServerError value)? server,
    TResult? Function(StorageError value)? storage,
    TResult? Function(AIError value)? ai,
    TResult? Function(UnknownError value)? unknown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkError value)? network,
    TResult Function(AuthenticationError value)? authentication,
    TResult Function(ValidationError value)? validation,
    TResult Function(ServerError value)? server,
    TResult Function(StorageError value)? storage,
    TResult Function(AIError value)? ai,
    TResult Function(UnknownError value)? unknown,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppErrorCopyWith<AppError> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppErrorCopyWith<$Res> {
  factory $AppErrorCopyWith(AppError value, $Res Function(AppError) then) =
      _$AppErrorCopyWithImpl<$Res, AppError>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class _$AppErrorCopyWithImpl<$Res, $Val extends AppError>
    implements $AppErrorCopyWith<$Res> {
  _$AppErrorCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_value.copyWith(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NetworkErrorImplCopyWith<$Res>
    implements $AppErrorCopyWith<$Res> {
  factory _$$NetworkErrorImplCopyWith(
          _$NetworkErrorImpl value, $Res Function(_$NetworkErrorImpl) then) =
      __$$NetworkErrorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? code, dynamic originalError});
}

/// @nodoc
class __$$NetworkErrorImplCopyWithImpl<$Res>
    extends _$AppErrorCopyWithImpl<$Res, _$NetworkErrorImpl>
    implements _$$NetworkErrorImplCopyWith<$Res> {
  __$$NetworkErrorImplCopyWithImpl(
      _$NetworkErrorImpl _value, $Res Function(_$NetworkErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? code = freezed,
    Object? originalError = freezed,
  }) {
    return _then(_$NetworkErrorImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      originalError: freezed == originalError
          ? _value.originalError
          : originalError // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc

class _$NetworkErrorImpl implements NetworkError {
  const _$NetworkErrorImpl(
      {required this.message, this.code, this.originalError});

  @override
  final String message;
  @override
  final String? code;
  @override
  final dynamic originalError;

  @override
  String toString() {
    return 'AppError.network(message: $message, code: $code, originalError: $originalError)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NetworkErrorImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code) &&
            const DeepCollectionEquality()
                .equals(other.originalError, originalError));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code,
      const DeepCollectionEquality().hash(originalError));

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NetworkErrorImplCopyWith<_$NetworkErrorImpl> get copyWith =>
      __$$NetworkErrorImplCopyWithImpl<_$NetworkErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String message, String? code, dynamic originalError)
        network,
    required TResult Function(String message, String? code) authentication,
    required TResult Function(String message, String field) validation,
    required TResult Function(String message, int? statusCode, String? code)
        server,
    required TResult Function(String message, String? operation) storage,
    required TResult Function(String message, String? model, String? code) ai,
    required TResult Function(String message, dynamic originalError) unknown,
  }) {
    return network(message, code, originalError);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code, dynamic originalError)?
        network,
    TResult? Function(String message, String? code)? authentication,
    TResult? Function(String message, String field)? validation,
    TResult? Function(String message, int? statusCode, String? code)? server,
    TResult? Function(String message, String? operation)? storage,
    TResult? Function(String message, String? model, String? code)? ai,
    TResult? Function(String message, dynamic originalError)? unknown,
  }) {
    return network?.call(message, code, originalError);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code, dynamic originalError)?
        network,
    TResult Function(String message, String? code)? authentication,
    TResult Function(String message, String field)? validation,
    TResult Function(String message, int? statusCode, String? code)? server,
    TResult Function(String message, String? operation)? storage,
    TResult Function(String message, String? model, String? code)? ai,
    TResult Function(String message, dynamic originalError)? unknown,
    required TResult orElse(),
  }) {
    if (network != null) {
      return network(message, code, originalError);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkError value) network,
    required TResult Function(AuthenticationError value) authentication,
    required TResult Function(ValidationError value) validation,
    required TResult Function(ServerError value) server,
    required TResult Function(StorageError value) storage,
    required TResult Function(AIError value) ai,
    required TResult Function(UnknownError value) unknown,
  }) {
    return network(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkError value)? network,
    TResult? Function(AuthenticationError value)? authentication,
    TResult? Function(ValidationError value)? validation,
    TResult? Function(ServerError value)? server,
    TResult? Function(StorageError value)? storage,
    TResult? Function(AIError value)? ai,
    TResult? Function(UnknownError value)? unknown,
  }) {
    return network?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkError value)? network,
    TResult Function(AuthenticationError value)? authentication,
    TResult Function(ValidationError value)? validation,
    TResult Function(ServerError value)? server,
    TResult Function(StorageError value)? storage,
    TResult Function(AIError value)? ai,
    TResult Function(UnknownError value)? unknown,
    required TResult orElse(),
  }) {
    if (network != null) {
      return network(this);
    }
    return orElse();
  }
}

abstract class NetworkError implements AppError {
  const factory NetworkError(
      {required final String message,
      final String? code,
      final dynamic originalError}) = _$NetworkErrorImpl;

  @override
  String get message;
  String? get code;
  dynamic get originalError;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NetworkErrorImplCopyWith<_$NetworkErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AuthenticationErrorImplCopyWith<$Res>
    implements $AppErrorCopyWith<$Res> {
  factory _$$AuthenticationErrorImplCopyWith(_$AuthenticationErrorImpl value,
          $Res Function(_$AuthenticationErrorImpl) then) =
      __$$AuthenticationErrorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? code});
}

/// @nodoc
class __$$AuthenticationErrorImplCopyWithImpl<$Res>
    extends _$AppErrorCopyWithImpl<$Res, _$AuthenticationErrorImpl>
    implements _$$AuthenticationErrorImplCopyWith<$Res> {
  __$$AuthenticationErrorImplCopyWithImpl(_$AuthenticationErrorImpl _value,
      $Res Function(_$AuthenticationErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? code = freezed,
  }) {
    return _then(_$AuthenticationErrorImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$AuthenticationErrorImpl implements AuthenticationError {
  const _$AuthenticationErrorImpl({required this.message, this.code});

  @override
  final String message;
  @override
  final String? code;

  @override
  String toString() {
    return 'AppError.authentication(message: $message, code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthenticationErrorImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthenticationErrorImplCopyWith<_$AuthenticationErrorImpl> get copyWith =>
      __$$AuthenticationErrorImplCopyWithImpl<_$AuthenticationErrorImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String message, String? code, dynamic originalError)
        network,
    required TResult Function(String message, String? code) authentication,
    required TResult Function(String message, String field) validation,
    required TResult Function(String message, int? statusCode, String? code)
        server,
    required TResult Function(String message, String? operation) storage,
    required TResult Function(String message, String? model, String? code) ai,
    required TResult Function(String message, dynamic originalError) unknown,
  }) {
    return authentication(message, code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code, dynamic originalError)?
        network,
    TResult? Function(String message, String? code)? authentication,
    TResult? Function(String message, String field)? validation,
    TResult? Function(String message, int? statusCode, String? code)? server,
    TResult? Function(String message, String? operation)? storage,
    TResult? Function(String message, String? model, String? code)? ai,
    TResult? Function(String message, dynamic originalError)? unknown,
  }) {
    return authentication?.call(message, code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code, dynamic originalError)?
        network,
    TResult Function(String message, String? code)? authentication,
    TResult Function(String message, String field)? validation,
    TResult Function(String message, int? statusCode, String? code)? server,
    TResult Function(String message, String? operation)? storage,
    TResult Function(String message, String? model, String? code)? ai,
    TResult Function(String message, dynamic originalError)? unknown,
    required TResult orElse(),
  }) {
    if (authentication != null) {
      return authentication(message, code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkError value) network,
    required TResult Function(AuthenticationError value) authentication,
    required TResult Function(ValidationError value) validation,
    required TResult Function(ServerError value) server,
    required TResult Function(StorageError value) storage,
    required TResult Function(AIError value) ai,
    required TResult Function(UnknownError value) unknown,
  }) {
    return authentication(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkError value)? network,
    TResult? Function(AuthenticationError value)? authentication,
    TResult? Function(ValidationError value)? validation,
    TResult? Function(ServerError value)? server,
    TResult? Function(StorageError value)? storage,
    TResult? Function(AIError value)? ai,
    TResult? Function(UnknownError value)? unknown,
  }) {
    return authentication?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkError value)? network,
    TResult Function(AuthenticationError value)? authentication,
    TResult Function(ValidationError value)? validation,
    TResult Function(ServerError value)? server,
    TResult Function(StorageError value)? storage,
    TResult Function(AIError value)? ai,
    TResult Function(UnknownError value)? unknown,
    required TResult orElse(),
  }) {
    if (authentication != null) {
      return authentication(this);
    }
    return orElse();
  }
}

abstract class AuthenticationError implements AppError {
  const factory AuthenticationError(
      {required final String message,
      final String? code}) = _$AuthenticationErrorImpl;

  @override
  String get message;
  String? get code;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AuthenticationErrorImplCopyWith<_$AuthenticationErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ValidationErrorImplCopyWith<$Res>
    implements $AppErrorCopyWith<$Res> {
  factory _$$ValidationErrorImplCopyWith(_$ValidationErrorImpl value,
          $Res Function(_$ValidationErrorImpl) then) =
      __$$ValidationErrorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String field});
}

/// @nodoc
class __$$ValidationErrorImplCopyWithImpl<$Res>
    extends _$AppErrorCopyWithImpl<$Res, _$ValidationErrorImpl>
    implements _$$ValidationErrorImplCopyWith<$Res> {
  __$$ValidationErrorImplCopyWithImpl(
      _$ValidationErrorImpl _value, $Res Function(_$ValidationErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? field = null,
  }) {
    return _then(_$ValidationErrorImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      field: null == field
          ? _value.field
          : field // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ValidationErrorImpl implements ValidationError {
  const _$ValidationErrorImpl({required this.message, required this.field});

  @override
  final String message;
  @override
  final String field;

  @override
  String toString() {
    return 'AppError.validation(message: $message, field: $field)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ValidationErrorImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.field, field) || other.field == field));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, field);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ValidationErrorImplCopyWith<_$ValidationErrorImpl> get copyWith =>
      __$$ValidationErrorImplCopyWithImpl<_$ValidationErrorImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String message, String? code, dynamic originalError)
        network,
    required TResult Function(String message, String? code) authentication,
    required TResult Function(String message, String field) validation,
    required TResult Function(String message, int? statusCode, String? code)
        server,
    required TResult Function(String message, String? operation) storage,
    required TResult Function(String message, String? model, String? code) ai,
    required TResult Function(String message, dynamic originalError) unknown,
  }) {
    return validation(message, field);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code, dynamic originalError)?
        network,
    TResult? Function(String message, String? code)? authentication,
    TResult? Function(String message, String field)? validation,
    TResult? Function(String message, int? statusCode, String? code)? server,
    TResult? Function(String message, String? operation)? storage,
    TResult? Function(String message, String? model, String? code)? ai,
    TResult? Function(String message, dynamic originalError)? unknown,
  }) {
    return validation?.call(message, field);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code, dynamic originalError)?
        network,
    TResult Function(String message, String? code)? authentication,
    TResult Function(String message, String field)? validation,
    TResult Function(String message, int? statusCode, String? code)? server,
    TResult Function(String message, String? operation)? storage,
    TResult Function(String message, String? model, String? code)? ai,
    TResult Function(String message, dynamic originalError)? unknown,
    required TResult orElse(),
  }) {
    if (validation != null) {
      return validation(message, field);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkError value) network,
    required TResult Function(AuthenticationError value) authentication,
    required TResult Function(ValidationError value) validation,
    required TResult Function(ServerError value) server,
    required TResult Function(StorageError value) storage,
    required TResult Function(AIError value) ai,
    required TResult Function(UnknownError value) unknown,
  }) {
    return validation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkError value)? network,
    TResult? Function(AuthenticationError value)? authentication,
    TResult? Function(ValidationError value)? validation,
    TResult? Function(ServerError value)? server,
    TResult? Function(StorageError value)? storage,
    TResult? Function(AIError value)? ai,
    TResult? Function(UnknownError value)? unknown,
  }) {
    return validation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkError value)? network,
    TResult Function(AuthenticationError value)? authentication,
    TResult Function(ValidationError value)? validation,
    TResult Function(ServerError value)? server,
    TResult Function(StorageError value)? storage,
    TResult Function(AIError value)? ai,
    TResult Function(UnknownError value)? unknown,
    required TResult orElse(),
  }) {
    if (validation != null) {
      return validation(this);
    }
    return orElse();
  }
}

abstract class ValidationError implements AppError {
  const factory ValidationError(
      {required final String message,
      required final String field}) = _$ValidationErrorImpl;

  @override
  String get message;
  String get field;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ValidationErrorImplCopyWith<_$ValidationErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ServerErrorImplCopyWith<$Res>
    implements $AppErrorCopyWith<$Res> {
  factory _$$ServerErrorImplCopyWith(
          _$ServerErrorImpl value, $Res Function(_$ServerErrorImpl) then) =
      __$$ServerErrorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, int? statusCode, String? code});
}

/// @nodoc
class __$$ServerErrorImplCopyWithImpl<$Res>
    extends _$AppErrorCopyWithImpl<$Res, _$ServerErrorImpl>
    implements _$$ServerErrorImplCopyWith<$Res> {
  __$$ServerErrorImplCopyWithImpl(
      _$ServerErrorImpl _value, $Res Function(_$ServerErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? statusCode = freezed,
    Object? code = freezed,
  }) {
    return _then(_$ServerErrorImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      statusCode: freezed == statusCode
          ? _value.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int?,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$ServerErrorImpl implements ServerError {
  const _$ServerErrorImpl({required this.message, this.statusCode, this.code});

  @override
  final String message;
  @override
  final int? statusCode;
  @override
  final String? code;

  @override
  String toString() {
    return 'AppError.server(message: $message, statusCode: $statusCode, code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ServerErrorImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.statusCode, statusCode) ||
                other.statusCode == statusCode) &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, statusCode, code);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ServerErrorImplCopyWith<_$ServerErrorImpl> get copyWith =>
      __$$ServerErrorImplCopyWithImpl<_$ServerErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String message, String? code, dynamic originalError)
        network,
    required TResult Function(String message, String? code) authentication,
    required TResult Function(String message, String field) validation,
    required TResult Function(String message, int? statusCode, String? code)
        server,
    required TResult Function(String message, String? operation) storage,
    required TResult Function(String message, String? model, String? code) ai,
    required TResult Function(String message, dynamic originalError) unknown,
  }) {
    return server(message, statusCode, code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code, dynamic originalError)?
        network,
    TResult? Function(String message, String? code)? authentication,
    TResult? Function(String message, String field)? validation,
    TResult? Function(String message, int? statusCode, String? code)? server,
    TResult? Function(String message, String? operation)? storage,
    TResult? Function(String message, String? model, String? code)? ai,
    TResult? Function(String message, dynamic originalError)? unknown,
  }) {
    return server?.call(message, statusCode, code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code, dynamic originalError)?
        network,
    TResult Function(String message, String? code)? authentication,
    TResult Function(String message, String field)? validation,
    TResult Function(String message, int? statusCode, String? code)? server,
    TResult Function(String message, String? operation)? storage,
    TResult Function(String message, String? model, String? code)? ai,
    TResult Function(String message, dynamic originalError)? unknown,
    required TResult orElse(),
  }) {
    if (server != null) {
      return server(message, statusCode, code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkError value) network,
    required TResult Function(AuthenticationError value) authentication,
    required TResult Function(ValidationError value) validation,
    required TResult Function(ServerError value) server,
    required TResult Function(StorageError value) storage,
    required TResult Function(AIError value) ai,
    required TResult Function(UnknownError value) unknown,
  }) {
    return server(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkError value)? network,
    TResult? Function(AuthenticationError value)? authentication,
    TResult? Function(ValidationError value)? validation,
    TResult? Function(ServerError value)? server,
    TResult? Function(StorageError value)? storage,
    TResult? Function(AIError value)? ai,
    TResult? Function(UnknownError value)? unknown,
  }) {
    return server?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkError value)? network,
    TResult Function(AuthenticationError value)? authentication,
    TResult Function(ValidationError value)? validation,
    TResult Function(ServerError value)? server,
    TResult Function(StorageError value)? storage,
    TResult Function(AIError value)? ai,
    TResult Function(UnknownError value)? unknown,
    required TResult orElse(),
  }) {
    if (server != null) {
      return server(this);
    }
    return orElse();
  }
}

abstract class ServerError implements AppError {
  const factory ServerError(
      {required final String message,
      final int? statusCode,
      final String? code}) = _$ServerErrorImpl;

  @override
  String get message;
  int? get statusCode;
  String? get code;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ServerErrorImplCopyWith<_$ServerErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$StorageErrorImplCopyWith<$Res>
    implements $AppErrorCopyWith<$Res> {
  factory _$$StorageErrorImplCopyWith(
          _$StorageErrorImpl value, $Res Function(_$StorageErrorImpl) then) =
      __$$StorageErrorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? operation});
}

/// @nodoc
class __$$StorageErrorImplCopyWithImpl<$Res>
    extends _$AppErrorCopyWithImpl<$Res, _$StorageErrorImpl>
    implements _$$StorageErrorImplCopyWith<$Res> {
  __$$StorageErrorImplCopyWithImpl(
      _$StorageErrorImpl _value, $Res Function(_$StorageErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? operation = freezed,
  }) {
    return _then(_$StorageErrorImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      operation: freezed == operation
          ? _value.operation
          : operation // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$StorageErrorImpl implements StorageError {
  const _$StorageErrorImpl({required this.message, this.operation});

  @override
  final String message;
  @override
  final String? operation;

  @override
  String toString() {
    return 'AppError.storage(message: $message, operation: $operation)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StorageErrorImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.operation, operation) ||
                other.operation == operation));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, operation);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StorageErrorImplCopyWith<_$StorageErrorImpl> get copyWith =>
      __$$StorageErrorImplCopyWithImpl<_$StorageErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String message, String? code, dynamic originalError)
        network,
    required TResult Function(String message, String? code) authentication,
    required TResult Function(String message, String field) validation,
    required TResult Function(String message, int? statusCode, String? code)
        server,
    required TResult Function(String message, String? operation) storage,
    required TResult Function(String message, String? model, String? code) ai,
    required TResult Function(String message, dynamic originalError) unknown,
  }) {
    return storage(message, operation);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code, dynamic originalError)?
        network,
    TResult? Function(String message, String? code)? authentication,
    TResult? Function(String message, String field)? validation,
    TResult? Function(String message, int? statusCode, String? code)? server,
    TResult? Function(String message, String? operation)? storage,
    TResult? Function(String message, String? model, String? code)? ai,
    TResult? Function(String message, dynamic originalError)? unknown,
  }) {
    return storage?.call(message, operation);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code, dynamic originalError)?
        network,
    TResult Function(String message, String? code)? authentication,
    TResult Function(String message, String field)? validation,
    TResult Function(String message, int? statusCode, String? code)? server,
    TResult Function(String message, String? operation)? storage,
    TResult Function(String message, String? model, String? code)? ai,
    TResult Function(String message, dynamic originalError)? unknown,
    required TResult orElse(),
  }) {
    if (storage != null) {
      return storage(message, operation);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkError value) network,
    required TResult Function(AuthenticationError value) authentication,
    required TResult Function(ValidationError value) validation,
    required TResult Function(ServerError value) server,
    required TResult Function(StorageError value) storage,
    required TResult Function(AIError value) ai,
    required TResult Function(UnknownError value) unknown,
  }) {
    return storage(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkError value)? network,
    TResult? Function(AuthenticationError value)? authentication,
    TResult? Function(ValidationError value)? validation,
    TResult? Function(ServerError value)? server,
    TResult? Function(StorageError value)? storage,
    TResult? Function(AIError value)? ai,
    TResult? Function(UnknownError value)? unknown,
  }) {
    return storage?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkError value)? network,
    TResult Function(AuthenticationError value)? authentication,
    TResult Function(ValidationError value)? validation,
    TResult Function(ServerError value)? server,
    TResult Function(StorageError value)? storage,
    TResult Function(AIError value)? ai,
    TResult Function(UnknownError value)? unknown,
    required TResult orElse(),
  }) {
    if (storage != null) {
      return storage(this);
    }
    return orElse();
  }
}

abstract class StorageError implements AppError {
  const factory StorageError(
      {required final String message,
      final String? operation}) = _$StorageErrorImpl;

  @override
  String get message;
  String? get operation;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StorageErrorImplCopyWith<_$StorageErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AIErrorImplCopyWith<$Res> implements $AppErrorCopyWith<$Res> {
  factory _$$AIErrorImplCopyWith(
          _$AIErrorImpl value, $Res Function(_$AIErrorImpl) then) =
      __$$AIErrorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? model, String? code});
}

/// @nodoc
class __$$AIErrorImplCopyWithImpl<$Res>
    extends _$AppErrorCopyWithImpl<$Res, _$AIErrorImpl>
    implements _$$AIErrorImplCopyWith<$Res> {
  __$$AIErrorImplCopyWithImpl(
      _$AIErrorImpl _value, $Res Function(_$AIErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? model = freezed,
    Object? code = freezed,
  }) {
    return _then(_$AIErrorImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      model: freezed == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as String?,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$AIErrorImpl implements AIError {
  const _$AIErrorImpl({required this.message, this.model, this.code});

  @override
  final String message;
  @override
  final String? model;
  @override
  final String? code;

  @override
  String toString() {
    return 'AppError.ai(message: $message, model: $model, code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AIErrorImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.model, model) || other.model == model) &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, model, code);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AIErrorImplCopyWith<_$AIErrorImpl> get copyWith =>
      __$$AIErrorImplCopyWithImpl<_$AIErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String message, String? code, dynamic originalError)
        network,
    required TResult Function(String message, String? code) authentication,
    required TResult Function(String message, String field) validation,
    required TResult Function(String message, int? statusCode, String? code)
        server,
    required TResult Function(String message, String? operation) storage,
    required TResult Function(String message, String? model, String? code) ai,
    required TResult Function(String message, dynamic originalError) unknown,
  }) {
    return ai(message, model, code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code, dynamic originalError)?
        network,
    TResult? Function(String message, String? code)? authentication,
    TResult? Function(String message, String field)? validation,
    TResult? Function(String message, int? statusCode, String? code)? server,
    TResult? Function(String message, String? operation)? storage,
    TResult? Function(String message, String? model, String? code)? ai,
    TResult? Function(String message, dynamic originalError)? unknown,
  }) {
    return ai?.call(message, model, code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code, dynamic originalError)?
        network,
    TResult Function(String message, String? code)? authentication,
    TResult Function(String message, String field)? validation,
    TResult Function(String message, int? statusCode, String? code)? server,
    TResult Function(String message, String? operation)? storage,
    TResult Function(String message, String? model, String? code)? ai,
    TResult Function(String message, dynamic originalError)? unknown,
    required TResult orElse(),
  }) {
    if (ai != null) {
      return ai(message, model, code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkError value) network,
    required TResult Function(AuthenticationError value) authentication,
    required TResult Function(ValidationError value) validation,
    required TResult Function(ServerError value) server,
    required TResult Function(StorageError value) storage,
    required TResult Function(AIError value) ai,
    required TResult Function(UnknownError value) unknown,
  }) {
    return ai(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkError value)? network,
    TResult? Function(AuthenticationError value)? authentication,
    TResult? Function(ValidationError value)? validation,
    TResult? Function(ServerError value)? server,
    TResult? Function(StorageError value)? storage,
    TResult? Function(AIError value)? ai,
    TResult? Function(UnknownError value)? unknown,
  }) {
    return ai?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkError value)? network,
    TResult Function(AuthenticationError value)? authentication,
    TResult Function(ValidationError value)? validation,
    TResult Function(ServerError value)? server,
    TResult Function(StorageError value)? storage,
    TResult Function(AIError value)? ai,
    TResult Function(UnknownError value)? unknown,
    required TResult orElse(),
  }) {
    if (ai != null) {
      return ai(this);
    }
    return orElse();
  }
}

abstract class AIError implements AppError {
  const factory AIError(
      {required final String message,
      final String? model,
      final String? code}) = _$AIErrorImpl;

  @override
  String get message;
  String? get model;
  String? get code;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AIErrorImplCopyWith<_$AIErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UnknownErrorImplCopyWith<$Res>
    implements $AppErrorCopyWith<$Res> {
  factory _$$UnknownErrorImplCopyWith(
          _$UnknownErrorImpl value, $Res Function(_$UnknownErrorImpl) then) =
      __$$UnknownErrorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, dynamic originalError});
}

/// @nodoc
class __$$UnknownErrorImplCopyWithImpl<$Res>
    extends _$AppErrorCopyWithImpl<$Res, _$UnknownErrorImpl>
    implements _$$UnknownErrorImplCopyWith<$Res> {
  __$$UnknownErrorImplCopyWithImpl(
      _$UnknownErrorImpl _value, $Res Function(_$UnknownErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? originalError = freezed,
  }) {
    return _then(_$UnknownErrorImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      originalError: freezed == originalError
          ? _value.originalError
          : originalError // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc

class _$UnknownErrorImpl implements UnknownError {
  const _$UnknownErrorImpl({required this.message, this.originalError});

  @override
  final String message;
  @override
  final dynamic originalError;

  @override
  String toString() {
    return 'AppError.unknown(message: $message, originalError: $originalError)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnknownErrorImpl &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality()
                .equals(other.originalError, originalError));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, message, const DeepCollectionEquality().hash(originalError));

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UnknownErrorImplCopyWith<_$UnknownErrorImpl> get copyWith =>
      __$$UnknownErrorImplCopyWithImpl<_$UnknownErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String message, String? code, dynamic originalError)
        network,
    required TResult Function(String message, String? code) authentication,
    required TResult Function(String message, String field) validation,
    required TResult Function(String message, int? statusCode, String? code)
        server,
    required TResult Function(String message, String? operation) storage,
    required TResult Function(String message, String? model, String? code) ai,
    required TResult Function(String message, dynamic originalError) unknown,
  }) {
    return unknown(message, originalError);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code, dynamic originalError)?
        network,
    TResult? Function(String message, String? code)? authentication,
    TResult? Function(String message, String field)? validation,
    TResult? Function(String message, int? statusCode, String? code)? server,
    TResult? Function(String message, String? operation)? storage,
    TResult? Function(String message, String? model, String? code)? ai,
    TResult? Function(String message, dynamic originalError)? unknown,
  }) {
    return unknown?.call(message, originalError);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code, dynamic originalError)?
        network,
    TResult Function(String message, String? code)? authentication,
    TResult Function(String message, String field)? validation,
    TResult Function(String message, int? statusCode, String? code)? server,
    TResult Function(String message, String? operation)? storage,
    TResult Function(String message, String? model, String? code)? ai,
    TResult Function(String message, dynamic originalError)? unknown,
    required TResult orElse(),
  }) {
    if (unknown != null) {
      return unknown(message, originalError);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkError value) network,
    required TResult Function(AuthenticationError value) authentication,
    required TResult Function(ValidationError value) validation,
    required TResult Function(ServerError value) server,
    required TResult Function(StorageError value) storage,
    required TResult Function(AIError value) ai,
    required TResult Function(UnknownError value) unknown,
  }) {
    return unknown(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkError value)? network,
    TResult? Function(AuthenticationError value)? authentication,
    TResult? Function(ValidationError value)? validation,
    TResult? Function(ServerError value)? server,
    TResult? Function(StorageError value)? storage,
    TResult? Function(AIError value)? ai,
    TResult? Function(UnknownError value)? unknown,
  }) {
    return unknown?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkError value)? network,
    TResult Function(AuthenticationError value)? authentication,
    TResult Function(ValidationError value)? validation,
    TResult Function(ServerError value)? server,
    TResult Function(StorageError value)? storage,
    TResult Function(AIError value)? ai,
    TResult Function(UnknownError value)? unknown,
    required TResult orElse(),
  }) {
    if (unknown != null) {
      return unknown(this);
    }
    return orElse();
  }
}

abstract class UnknownError implements AppError {
  const factory UnknownError(
      {required final String message,
      final dynamic originalError}) = _$UnknownErrorImpl;

  @override
  String get message;
  dynamic get originalError;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UnknownErrorImplCopyWith<_$UnknownErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ErrorResult<T> {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T data) success,
    required TResult Function(AppError error) failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T data)? success,
    TResult? Function(AppError error)? failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T data)? success,
    TResult Function(AppError error)? failure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Success<T> value) success,
    required TResult Function(Failure<T> value) failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Success<T> value)? success,
    TResult? Function(Failure<T> value)? failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Success<T> value)? success,
    TResult Function(Failure<T> value)? failure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ErrorResultCopyWith<T, $Res> {
  factory $ErrorResultCopyWith(
          ErrorResult<T> value, $Res Function(ErrorResult<T>) then) =
      _$ErrorResultCopyWithImpl<T, $Res, ErrorResult<T>>;
}

/// @nodoc
class _$ErrorResultCopyWithImpl<T, $Res, $Val extends ErrorResult<T>>
    implements $ErrorResultCopyWith<T, $Res> {
  _$ErrorResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ErrorResult
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$SuccessImplCopyWith<T, $Res> {
  factory _$$SuccessImplCopyWith(
          _$SuccessImpl<T> value, $Res Function(_$SuccessImpl<T>) then) =
      __$$SuccessImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({T data});
}

/// @nodoc
class __$$SuccessImplCopyWithImpl<T, $Res>
    extends _$ErrorResultCopyWithImpl<T, $Res, _$SuccessImpl<T>>
    implements _$$SuccessImplCopyWith<T, $Res> {
  __$$SuccessImplCopyWithImpl(
      _$SuccessImpl<T> _value, $Res Function(_$SuccessImpl<T>) _then)
      : super(_value, _then);

  /// Create a copy of ErrorResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = freezed,
  }) {
    return _then(_$SuccessImpl<T>(
      freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as T,
    ));
  }
}

/// @nodoc

class _$SuccessImpl<T> implements Success<T> {
  const _$SuccessImpl(this.data);

  @override
  final T data;

  @override
  String toString() {
    return 'ErrorResult<$T>.success(data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SuccessImpl<T> &&
            const DeepCollectionEquality().equals(other.data, data));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(data));

  /// Create a copy of ErrorResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SuccessImplCopyWith<T, _$SuccessImpl<T>> get copyWith =>
      __$$SuccessImplCopyWithImpl<T, _$SuccessImpl<T>>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T data) success,
    required TResult Function(AppError error) failure,
  }) {
    return success(data);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T data)? success,
    TResult? Function(AppError error)? failure,
  }) {
    return success?.call(data);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T data)? success,
    TResult Function(AppError error)? failure,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(data);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Success<T> value) success,
    required TResult Function(Failure<T> value) failure,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Success<T> value)? success,
    TResult? Function(Failure<T> value)? failure,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Success<T> value)? success,
    TResult Function(Failure<T> value)? failure,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class Success<T> implements ErrorResult<T> {
  const factory Success(final T data) = _$SuccessImpl<T>;

  T get data;

  /// Create a copy of ErrorResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SuccessImplCopyWith<T, _$SuccessImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FailureImplCopyWith<T, $Res> {
  factory _$$FailureImplCopyWith(
          _$FailureImpl<T> value, $Res Function(_$FailureImpl<T>) then) =
      __$$FailureImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({AppError error});

  $AppErrorCopyWith<$Res> get error;
}

/// @nodoc
class __$$FailureImplCopyWithImpl<T, $Res>
    extends _$ErrorResultCopyWithImpl<T, $Res, _$FailureImpl<T>>
    implements _$$FailureImplCopyWith<T, $Res> {
  __$$FailureImplCopyWithImpl(
      _$FailureImpl<T> _value, $Res Function(_$FailureImpl<T>) _then)
      : super(_value, _then);

  /// Create a copy of ErrorResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$FailureImpl<T>(
      null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError,
    ));
  }

  /// Create a copy of ErrorResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res> get error {
    return $AppErrorCopyWith<$Res>(_value.error, (value) {
      return _then(_value.copyWith(error: value));
    });
  }
}

/// @nodoc

class _$FailureImpl<T> implements Failure<T> {
  const _$FailureImpl(this.error);

  @override
  final AppError error;

  @override
  String toString() {
    return 'ErrorResult<$T>.failure(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FailureImpl<T> &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of ErrorResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FailureImplCopyWith<T, _$FailureImpl<T>> get copyWith =>
      __$$FailureImplCopyWithImpl<T, _$FailureImpl<T>>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T data) success,
    required TResult Function(AppError error) failure,
  }) {
    return failure(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T data)? success,
    TResult? Function(AppError error)? failure,
  }) {
    return failure?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T data)? success,
    TResult Function(AppError error)? failure,
    required TResult orElse(),
  }) {
    if (failure != null) {
      return failure(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Success<T> value) success,
    required TResult Function(Failure<T> value) failure,
  }) {
    return failure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Success<T> value)? success,
    TResult? Function(Failure<T> value)? failure,
  }) {
    return failure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Success<T> value)? success,
    TResult Function(Failure<T> value)? failure,
    required TResult orElse(),
  }) {
    if (failure != null) {
      return failure(this);
    }
    return orElse();
  }
}

abstract class Failure<T> implements ErrorResult<T> {
  const factory Failure(final AppError error) = _$FailureImpl<T>;

  AppError get error;

  /// Create a copy of ErrorResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FailureImplCopyWith<T, _$FailureImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}
