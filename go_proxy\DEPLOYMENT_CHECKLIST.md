# Go Proxy 部署检查清单

## ✅ 编译完成
- [x] Linux 可执行文件已生成: `go_proxy_linux` (12.3MB)
- [x] 部署包已创建: `go_proxy_deploy_20250619_005630.tar.gz` (6.7MB)

## 📦 部署包内容
- [x] `go_proxy_linux` - 主程序
- [x] `.env` - 配置文件
- [x] `deploy.sh` - 部署脚本
- [x] `start.sh` - 启动脚本
- [x] `stop.sh` - 停止脚本
- [x] `go_proxy.service` - 系统服务配置
- [x] `README_DEPLOY.md` - 详细部署文档

## 🚀 部署步骤

### 1. 上传到服务器
```bash
# 使用 scp 上传（替换为您的服务器信息）
scp go_proxy_deploy_20250619_005630.tar.gz user@your-server:/opt/

# 或使用其他方式上传到服务器
```

### 2. 服务器端操作
```bash
# 解压部署包
cd /opt
tar -xzf go_proxy_deploy_20250619_005630.tar.gz

# 设置权限
chmod +x go_proxy_linux *.sh

# 检查配置（重要！）
nano .env

# 运行部署脚本
./deploy.sh

# 启动服务
./start.sh
```

### 3. 验证部署
```bash
# 检查服务状态
curl http://localhost:8080/healthz

# 查看日志
tail -f go_proxy.log

# 检查进程
ps aux | grep go_proxy
```

## ⚙️ 配置要点

### 必须检查的配置项
1. **SERVER_PORT**: 确保端口未被占用（默认8080）
2. **CLOUDFUNC_BASE_URL**: 云函数地址必须正确
3. **AES_SECRET_KEY**: 与云函数保持一致

### 防火墙设置
```bash
# Ubuntu/Debian
sudo ufw allow 8080

# CentOS/RHEL  
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --reload
```

## 🔧 服务管理

### 临时运行（测试用）
```bash
./start.sh    # 启动
./stop.sh     # 停止
```

### 系统服务（生产环境推荐）
```bash
# 修改服务文件中的路径
sudo nano go_proxy.service

# 安装系统服务
sudo cp go_proxy.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable go_proxy
sudo systemctl start go_proxy

# 查看状态
sudo systemctl status go_proxy
```

## 🔍 故障排除

### 常见问题
1. **端口被占用**: `sudo netstat -tuln | grep 8080`
2. **权限问题**: `chmod +x go_proxy_linux`
3. **配置错误**: 检查 `.env` 文件
4. **云函数连接**: 测试云函数地址是否可访问

### 日志查看
```bash
# 应用日志
tail -f go_proxy.log

# 系统服务日志
sudo journalctl -u go_proxy -f
```

## 📊 性能监控

### 健康检查
```bash
# 基本健康检查
curl http://localhost:8080/healthz

# 响应时间测试
time curl http://localhost:8080/healthz
```

### 资源监控
```bash
# CPU和内存使用
top -p $(pgrep go_proxy_linux)

# 网络连接
ss -tuln | grep 8080
```

## 🔒 安全建议

1. **运行用户**: 不要使用 root 用户运行服务
2. **文件权限**: 确保配置文件权限适当
3. **网络安全**: 配置防火墙规则
4. **日志管理**: 定期清理日志文件

## 📞 技术支持

如遇到问题，请检查：
1. 服务器系统兼容性（Linux x64）
2. Go 程序依赖是否满足
3. 网络连接是否正常
4. 云函数服务是否可用

---

**部署完成后，您的 Go Proxy 服务将在端口 8080 上运行，作为 AI 聊天系统的中转代理。**
