import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/user_model.dart';
import '../core/constants/app_constants.dart';

/// 认证服务
class AuthService {
  static const String baseUrl = AppConstants.exeFunctionUrl;
  
  /// 用户登录
  Future<Map<String, dynamic>> login(String username, String password) async {
    try {
      final response = await http.post(
        Uri.parse(baseUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'action': 'login',
          'username': username,
          'password': password,
        }),
      ).timeout(AppConstants.connectTimeout);

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        if (responseData['code'] == 0) {
          final outerData = responseData['data'];
          final innerData = outerData['data'];
          final tokens = innerData['tokens'];

          return {
            'success': outerData['success'],
            'token': tokens['accessToken'],
            'refresh_token': tokens['refreshToken'],
            'expires_at': tokens['expiresAt'],
            'user': innerData['user'],
            'message': outerData['message'],
          };
        } else {
          return {
            'success': false,
            'message': responseData['message'] ?? '登录失败',
          };
        }
      } else {
        return {
          'success': false,
          'message': '服务器错误: ${response.statusCode}',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': '网络连接失败: $e',
      };
    }
  }

  /// 用户注册
  Future<Map<String, dynamic>> register(String username, String email, String password, [String? activationCode]) async {
    try {
      // 构建注册请求体
      final requestBody = {
        'action': 'register',
        'username': username,
        'password': password,
      };

      // 如果提供了激活码，添加到请求中
      if (activationCode != null && activationCode.isNotEmpty) {
        requestBody['activationCode'] = activationCode;
      }

      // 第一步：注册用户
      final registerResponse = await http.post(
        Uri.parse(baseUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(requestBody),
      ).timeout(AppConstants.connectTimeout);

      if (registerResponse.statusCode == 200) {
        final registerData = jsonDecode(registerResponse.body);

        if (registerData['code'] == 0) {
          // 注册成功，自动登录
          final loginResult = await login(username, password);
          if (loginResult['success'] == true) {
            return {
              'success': true,
              'token': loginResult['token'],
              'refresh_token': loginResult['refresh_token'],
              'expires_at': loginResult['expires_at'],
              'user': loginResult['user'],
              'message': '注册成功并已自动登录',
            };
          } else {
            return {
              'success': true,
              'message': '注册成功，请手动登录',
              'need_login': true,
            };
          }
        } else {
          return {
            'success': false,
            'message': registerData['message'] ?? '注册失败',
          };
        }
      } else {
        return {
          'success': false,
          'message': '服务器错误: ${registerResponse.statusCode}',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': '网络连接失败: $e',
      };
    }
  }

  /// 验证激活码
  Future<Map<String, dynamic>> validateActivationCode(String activationCode) async {
    try {
      final response = await http.post(
        Uri.parse(baseUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'action': 'validateActivationCode',
          'activationCode': activationCode,
        }),
      ).timeout(AppConstants.connectTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data['code'] == 0 && data['data'] != null) {
          // 云函数返回的数据结构是嵌套的，需要从 data.data.data 获取实际数据
          final responseData = data['data']['data'];
          if (responseData != null) {
            return {
              'valid': responseData['valid'] ?? false,
              'quota': responseData['quota'],
              'creator': responseData['creator'],
              'timestamp': responseData['timestamp'],
              'error': responseData['error'],
            };
          } else {
            return {
              'valid': false,
              'error': data['data']['message'] ?? '验证失败',
            };
          }
        } else {
          return {
            'valid': false,
            'error': data['message'] ?? '验证失败',
          };
        }
      } else {
        return {
          'valid': false,
          'error': '服务器错误: ${response.statusCode}',
        };
      }
    } catch (e) {
      return {
        'valid': false,
        'error': '网络连接失败: $e',
      };
    }
  }

  /// 用户登出
  Future<Map<String, dynamic>> logout() async {
    try {
      // 模拟网络请求延迟
      await Future.delayed(const Duration(milliseconds: 500));
      
      return {
        'success': true,
        'message': '登出成功',
      };
    } catch (e) {
      return {
        'success': false,
        'message': '登出失败: $e',
      };
    }
  }

  /// 获取用户信息
  Future<Map<String, dynamic>> getUserInfo(String token) async {
    try {
      final response = await http.post(
        Uri.parse(baseUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'action': 'getUserInfo',
          'token': token,
        }),
      ).timeout(AppConstants.connectTimeout);

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        print('getUserInfo完整响应: $responseData');

        if (responseData['code'] == 0) {
          final data = responseData['data'];
          print('data层级: $data');
          print('data.data层级: ${data['data']}');

          return {
            'success': data['success'],
            'user': data['data']['user'],
            'message': data['message'],
          };
        } else {
          return {
            'success': false,
            'message': responseData['message'] ?? '获取用户信息失败',
          };
        }
      } else {
        return {
          'success': false,
          'message': '服务器错误: ${response.statusCode}',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': '获取用户信息失败: $e',
      };
    }
  }

  /// 刷新令牌
  Future<Map<String, dynamic>> refreshToken(String refreshToken) async {
    try {
      final response = await http.post(
        Uri.parse(baseUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'action': 'refreshToken',
          'refreshToken': refreshToken,
        }),
      ).timeout(AppConstants.connectTimeout);

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        if (responseData['code'] == 0) {
          final data = responseData['data'];
          final tokens = data['data']['tokens'];
          return {
            'success': data['success'],
            'token': tokens['accessToken'],
            'refresh_token': tokens['refreshToken'],
            'expires_at': tokens['expiresAt'],
            'message': data['message'],
          };
        } else {
          return {
            'success': false,
            'message': responseData['message'] ?? '令牌刷新失败',
          };
        }
      } else {
        return {
          'success': false,
          'message': '服务器错误: ${response.statusCode}',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': '令牌刷新失败: $e',
      };
    }
  }

  /// 修改密码
  Future<Map<String, dynamic>> changePassword(String token, String oldPassword, String newPassword) async {
    try {
      // 模拟网络请求延迟
      await Future.delayed(const Duration(seconds: 1));
      
      // 模拟密码验证
      if (oldPassword == '123456' && newPassword.length >= 6) {
        return {
          'success': true,
          'message': '密码修改成功',
        };
      } else {
        return {
          'success': false,
          'message': '原密码错误或新密码格式不正确',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': '修改密码失败: $e',
      };
    }
  }

  /// 忘记密码
  Future<Map<String, dynamic>> forgotPassword(String email) async {
    try {
      // 模拟网络请求延迟
      await Future.delayed(const Duration(seconds: 1));
      
      if (email.contains('@')) {
        return {
          'success': true,
          'message': '重置密码邮件已发送',
        };
      } else {
        return {
          'success': false,
          'message': '邮箱格式错误',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': '发送重置邮件失败: $e',
      };
    }
  }

  /// 验证令牌有效性
  Future<bool> validateToken(String token) async {
    try {
      final result = await getUserInfo(token);
      return result['success'] == true;
    } catch (e) {
      return false;
    }
  }
}
