/**
 * 测试修复后的八字计算逻辑
 */

const baziCalculator = require('./src/services/bazi/baziCalculator');

console.log('=== 测试修复后的八字计算逻辑 ===\n');

// 测试样例1：年柱测试 - 2024年2月4日16点26分
console.log('样例1：年柱测试 - 2024年2月4日16点26分');
console.log('预期：年柱应为癸卯（兔年），因为尚未交立春\n');

async function testSample1() {
  try {
    const birthInfo1 = baziCalculator.parseBirthInfo('2024-02-04', '16:26:00', '公历', false);
    if (!birthInfo1.success) {
      console.log('解析出生信息失败:', birthInfo1.message);
      return;
    }

    const baziSettings1 = {
      yearGanZhiSet: undefined, // 使用默认设置
      monthGanZhiSet: undefined,
      dayGanZhiSet: undefined,
      hourGanZhiSet: undefined
    };

    const baziInfo1 = baziCalculator.calculateBazi(
      birthInfo1.lunar,
      '测试者',
      '男',
      '北京',
      baziSettings1
    );

    console.log('修复后结果：');
    console.log('八字：', baziInfo1.baziStr);
    console.log('年柱：', baziInfo1.year.ganZhi, baziInfo1.year.ganZhi === '癸卯' ? '✅ 正确' : '❌ 错误');
    console.log('月柱：', baziInfo1.month.ganZhi);
    console.log('日柱：', baziInfo1.day.ganZhi);
    console.log('时柱：', baziInfo1.time.ganZhi);
  } catch (error) {
    console.log('测试样例1出错：', error.message);
  }
}

// 测试样例2：子时测试 - 1990年12月15日23点30分
console.log('\n样例2：子时测试 - 1990年12月15日23点30分');
console.log('测试不同的子时处理设置\n');

async function testSample2() {
  try {
    const birthInfo2 = baziCalculator.parseBirthInfo('1990-12-15', '23:30:00', '公历', false);
    if (!birthInfo2.success) {
      console.log('解析出生信息失败:', birthInfo2.message);
      return;
    }

    // 测试晚子时（子时算当天）
    const baziSettings2a = {
      yearGanZhiSet: undefined,
      monthGanZhiSet: undefined,
      dayGanZhiSet: 0, // 晚子时日干支算当天
      hourGanZhiSet: undefined
    };

    const baziInfo2a = baziCalculator.calculateBazi(
      birthInfo2.lunar,
      '测试者',
      '男',
      '广州',
      baziSettings2a
    );

    console.log('晚子时设置（子时算当天）：');
    console.log('八字：', baziInfo2a.baziStr);
    console.log('日柱：', baziInfo2a.day.ganZhi, '（应为甲寅）');

    // 测试早子时（子时算第二天）
    const baziSettings2b = {
      yearGanZhiSet: undefined,
      monthGanZhiSet: undefined,
      dayGanZhiSet: 1, // 晚子时日干支算明天
      hourGanZhiSet: undefined
    };

    const baziInfo2b = baziCalculator.calculateBazi(
      birthInfo2.lunar,
      '测试者',
      '男',
      '广州',
      baziSettings2b
    );

    console.log('\n早子时设置（子时算第二天）：');
    console.log('八字：', baziInfo2b.baziStr);
    console.log('日柱：', baziInfo2b.day.ganZhi, '（应为乙卯）');

  } catch (error) {
    console.log('测试样例2出错：', error.message);
  }
}

// 测试样例3：月柱节气交接测试 - 1990年11月8日23点30分
console.log('\n样例3：月柱节气交接测试 - 1990年11月8日23点30分');
console.log('立冬时间：1990年11月8日0点23分\n');

async function testSample3() {
  try {
    const birthInfo3 = baziCalculator.parseBirthInfo('1990-11-08', '23:30:00', '公历', false);
    if (!birthInfo3.success) {
      console.log('解析出生信息失败:', birthInfo3.message);
      return;
    }

    const baziSettings3 = {
      yearGanZhiSet: undefined,
      monthGanZhiSet: undefined, // 使用默认的精确节气交接
      dayGanZhiSet: undefined,
      hourGanZhiSet: undefined
    };

    const baziInfo3 = baziCalculator.calculateBazi(
      birthInfo3.lunar,
      '测试者',
      '男',
      '测试地',
      baziSettings3
    );

    console.log('修复后结果：');
    console.log('八字：', baziInfo3.baziStr);
    console.log('年柱：', baziInfo3.year.ganZhi);
    console.log('月柱：', baziInfo3.month.ganZhi, '（应为丁亥，因为已过立冬）');
    console.log('日柱：', baziInfo3.day.ganZhi);
    console.log('时柱：', baziInfo3.time.ganZhi);
  } catch (error) {
    console.log('测试样例3出错：', error.message);
  }
}

// 运行所有测试
async function runAllTests() {
  await testSample1();
  await testSample2();
  await testSample3();
  console.log('\n=== 测试完成 ===');
}

runAllTests();
