import 'dart:convert';
import 'package:http/http.dart' as http;

void main() async {
  print('🧪 开始测试创建订单功能...\n');
  
  // 首先测试登录获取token
  final loginResult = await testLogin();
  if (loginResult == null) {
    print('❌ 登录失败，无法继续测试');
    return;
  }
  
  final token = loginResult['token'];
  if (token != null) {
    print('✅ 登录成功，获取到token: ${token.toString().substring(0, 20)}...\n');
  } else {
    print('❌ 登录成功但未获取到token');
    return;
  }
  
  // 测试获取套餐列表
  final packages = await getPackages(token);
  if (packages == null || packages.isEmpty) {
    print('❌ 获取套餐列表失败');
    return;
  }
  
  print('✅ 获取到 ${packages.length} 个套餐');
  for (var package in packages) {
    print('  - ${package['packageName']}: ${package['id']}');
  }
  print('');

  // 测试创建订单
  final packageId = packages[0]['id'];
  print('🛒 尝试创建订单，套餐ID: $packageId');
  
  final orderResult = await createOrder(token, packageId);
  if (orderResult != null) {
    print('✅ 创建订单成功！');
    print('订单ID: ${orderResult['order']['_id']}');
    print('订单号: ${orderResult['order']['orderNo']}');
    print('支付URL: ${orderResult['payment']['paymentUrl']}');
    print('富友订单ID: ${orderResult['payment']['fuiouOrderId']}');
    print('支付方式: ${orderResult['payment']['paymentMethod']}');
  } else {
    print('❌ 创建订单失败');
  }
}

Future<Map<String, dynamic>?> testLogin() async {
  try {
    final response = await http.post(
      Uri.parse('https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'action': 'register',
        'username': 'testuser12345',
        'password': 'password123',
      }),
    );

    if (response.statusCode == 200) {
      final responseData = jsonDecode(response.body);
      
      if (responseData['code'] == 0) {
        print('✅ 注册成功，尝试登录...');
        
        // 登录
        final loginResponse = await http.post(
          Uri.parse('https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction'),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode({
            'action': 'login',
            'username': 'testuser12345',
            'password': 'password123',
          }),
        );
        
        if (loginResponse.statusCode == 200) {
          final loginData = jsonDecode(loginResponse.body);
          if (loginData['code'] == 0) {
            return {
              'token': loginData['data']['data']['tokens']['accessToken'],
              'user': loginData['data']['data']['user']
            };
          }
        }
      } else if (responseData['message'].contains('用户名已存在')) {
        print('用户已存在，直接登录...');
        
        // 直接登录
        final loginResponse = await http.post(
          Uri.parse('https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction'),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode({
            'action': 'login',
            'username': 'testuser12345',
            'password': 'password123',
          }),
        );
        
        if (loginResponse.statusCode == 200) {
          final loginData = jsonDecode(loginResponse.body);
          print('登录响应: ${loginResponse.body}');
          if (loginData['code'] == 0) {
            return {
              'token': loginData['data']['data']['tokens']['accessToken'],
              'user': loginData['data']['data']['user']
            };
          } else {
            print('登录失败: ${loginData['message']}');
          }
        }
      }
    }
  } catch (e) {
    print('❌ 登录测试异常: $e');
  }
  return null;
}

Future<List<dynamic>?> getPackages(String token) async {
  try {
    final response = await http.post(
      Uri.parse('https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'getActivePackages',
      }),
    );

    print('获取套餐响应状态码: ${response.statusCode}');
    print('获取套餐响应内容: ${response.body}');

    if (response.statusCode == 200) {
      final responseData = jsonDecode(response.body);

      if (responseData['code'] == 0) {
        // 根据云函数的返回结构，正确的路径是 data.data
        return responseData['data']['data'] as List<dynamic>;
      } else {
        print('❌ 获取套餐失败: ${responseData['message']}');
      }
    } else {
      print('❌ 获取套餐请求失败: ${response.statusCode}');
    }
  } catch (e) {
    print('❌ 获取套餐异常: $e');
  }
  return null;
}

Future<Map<String, dynamic>?> createOrder(String token, String packageId) async {
  try {
    final response = await http.post(
      Uri.parse('https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'createPurchaseOrder',
        'packageId': packageId,
        'quantity': 1,
        'paymentMethod': 'WECHAT',
      }),
    );

    print('创建订单响应状态码: ${response.statusCode}');
    print('创建订单响应内容: ${response.body}');

    if (response.statusCode == 200) {
      final responseData = jsonDecode(response.body);
      
      if (responseData['code'] == 0) {
        return responseData['data']['data'];
      } else {
        print('❌ 创建订单失败: ${responseData['message']}');
      }
    } else {
      print('❌ 创建订单请求失败: ${response.statusCode}');
    }
  } catch (e) {
    print('❌ 创建订单异常: $e');
  }
  return null;
}
