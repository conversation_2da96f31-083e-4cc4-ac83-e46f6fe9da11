package database

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"go_proxy/internal/config"
)

// Client 数据库客户端
type Client struct {
	cloudFuncURL string
	httpClient   *http.Client
}

// Agent 智能体结构
type Agent struct {
	ID                 string  `json:"id"`
	AgentName          string  `json:"agentName"`
	AgentPrompt        string  `json:"agentPrompt"`
	AgentType          string  `json:"agentType"`
	Description        string  `json:"description"`
	IsActive           bool    `json:"isActive"`
	SortOrder          int     `json:"sortOrder"`
	NeedLaymanVersion  bool    `json:"needLaymanVersion"`  // 是否需要大白话版本
	LaymanPrompt       string  `json:"laymanPrompt"`       // 大白话润色提示词
	LaymanApiKey       string  `json:"laymanApiKey"`       // 大白话专用API密钥
	LaymanApiUrl       string  `json:"laymanApiUrl"`       // 大白话专用API地址
	LaymanModelName    string  `json:"laymanModelName"`    // 大白话专用模型名称
	LaymanMaxTokens    int     `json:"laymanMaxTokens"`    // 大白话专用最大token数
	LaymanTemperature  float64 `json:"laymanTemperature"`  // 大白话专用温度参数
}

// Model 模型结构
type Model struct {
	ID               string  `json:"id"`
	ModelName        string  `json:"modelName"`
	ModelApiKey      string  `json:"modelApiKey"`
	ModelApiUrl      string  `json:"modelApiUrl"`
	ModelDisplayName string  `json:"modelDisplayName"`
	MaxTokens        int     `json:"maxTokens"`
	Temperature      float64 `json:"temperature"`
	IsActive         bool    `json:"isActive"`
	SortOrder        int     `json:"sortOrder"`
	Description      string  `json:"description"`
}

// CloudFuncRequest 云函数请求结构
type CloudFuncRequest struct {
	Action string `json:"action"`
	Token  string `json:"token"`
}

// CloudFuncResponse 云函数响应结构
type CloudFuncResponse struct {
	Code    interface{} `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// NewClient 创建新的数据库客户端
func NewClient(cfg *config.Config) (*Client, error) {
	return &Client{
		cloudFuncURL: cfg.CloudFuncBaseURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}, nil
}

// GetAgent 根据ID获取智能体信息
func (c *Client) GetAgent(agentID string, token string) (*Agent, error) {
	// 先获取所有智能体列表
	agents, err := c.getAgents(token)
	if err != nil {
		return nil, fmt.Errorf("failed to get agents: %w", err)
	}

	// 查找指定ID的智能体
	for _, agent := range agents {
		if agent.ID == agentID && agent.IsActive {
			return &agent, nil
		}
	}

	return nil, fmt.Errorf("agent not found or inactive")
}

// GetModel 根据ID获取模型信息
func (c *Client) GetModel(modelID string, token string) (*Model, error) {
	// 先获取所有模型列表
	models, err := c.getModels(token)
	if err != nil {
		return nil, fmt.Errorf("failed to get models: %w", err)
	}

	// 查找指定ID的模型
	for _, model := range models {
		if model.ID == modelID && model.IsActive {
			return &model, nil
		}
	}

	return nil, fmt.Errorf("model not found or inactive")
}

// getAgents 通过云函数获取智能体列表（包含提示词）
func (c *Client) getAgents(token string) ([]Agent, error) {
	reqData := CloudFuncRequest{
		Action: "getFullAgents",
		Token:  token,
	}

	respData, err := c.callCloudFunction(reqData)
	if err != nil {
		return nil, err
	}

	// 解析响应数据 - 云函数返回的嵌套结构
	outerDataMap, ok := respData.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid response data format")
	}

	innerDataMap, ok := outerDataMap["data"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid inner data format")
	}

	agentsData, ok := innerDataMap["agents"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid agents data format")
	}

	var agents []Agent
	for _, agentData := range agentsData {
		agentBytes, err := json.Marshal(agentData)
		if err != nil {
			continue
		}

		var agent Agent
		if err := json.Unmarshal(agentBytes, &agent); err != nil {
			continue
		}

		agents = append(agents, agent)
	}

	return agents, nil
}

// getModels 通过云函数获取模型列表（包含API密钥）
func (c *Client) getModels(token string) ([]Model, error) {
	reqData := CloudFuncRequest{
		Action: "getFullModels",
		Token:  token,
	}

	respData, err := c.callCloudFunction(reqData)
	if err != nil {
		return nil, err
	}

	// 解析响应数据 - 云函数返回的嵌套结构
	outerDataMap, ok := respData.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid response data format")
	}

	innerDataMap, ok := outerDataMap["data"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid inner data format")
	}

	modelsData, ok := innerDataMap["models"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid models data format")
	}

	var models []Model
	for _, modelData := range modelsData {
		modelBytes, err := json.Marshal(modelData)
		if err != nil {
			continue
		}

		var model Model
		if err := json.Unmarshal(modelBytes, &model); err != nil {
			continue
		}

		models = append(models, model)
	}

	return models, nil
}

// callCloudFunction 调用云函数
func (c *Client) callCloudFunction(reqData CloudFuncRequest) (*CloudFuncResponse, error) {
	jsonData, err := json.Marshal(reqData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	resp, err := c.httpClient.Post(c.cloudFuncURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to call cloud function: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	var respData CloudFuncResponse
	if err := json.Unmarshal(body, &respData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	// 检查响应状态 - 云函数返回code=0表示成功
	codeFloat, ok := respData.Code.(float64)
	if !ok {
		codeStr, ok := respData.Code.(string)
		if !ok || (codeStr != "SUCCESS" && codeStr != "0") {
			return nil, fmt.Errorf("cloud function error: %s", respData.Message)
		}
	} else if codeFloat != 0 {
		return nil, fmt.Errorf("cloud function error: %s", respData.Message)
	}

	return &respData, nil
}