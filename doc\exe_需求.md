# 目录介绍
- numerology_ai_chat 目录是当前项目的旧版本，仅用作参考
- numerology_ai_chat_new 目录是当前项目的最新版本，正在开发中
- numerology_ai_chat_new_admin 目录是当前项目的后台管理页面，正在开发中
- cloudfunctions 目录是当前项目的后端云函数，正在开发中

# 项目说明
- 本项目是一个命理AI聊天桌面应用，基于Flutter开发
- 项目目标是提供专业的命理分析与AI对话功能

# 重构说明
- 由于旧版本项目是完全本地运行的，并没有联网，新版本需要做一些联网功能。
- 新版本需要实现的功能包括：
  - 用户注册与登录
  - 在线获取智能体列表
  - 在线获取模型列表
  - 聊天记录本地存储（云端不存储任何聊天记录）
  - ai功能实现完全的中转调用，而不在本地存储任何提示词以及api秘钥等等
  - ai中转通过go程序来实现
    - go程序仅负责接收前端用户账号密码以及调用的内容，然后访问对应的云函数进行鉴权，鉴权通过后，开始中转大语言模型内容输出给前端。
  - 剩余功能使用腾讯云开发的云函数实现，版本是Nodejs16.13
    - 前端云函数需要实现的功能包括：
      - 用户注册与登录
      - 在线获取智能体列表（仅获取智能体列表，不获取智能体提示词）
      - 在线获取模型列表（仅获取模型列表，不获取模型api秘钥）
      - 获取用户信息
 
    - 后端云函数需要实现的功能包括：
      - 管理员登录
      - 获取管理员信息（鉴权以及当前账号权限等等）
      - 管理员设置
      - 用户信息管理（创建用户，修改或增加额度，设置会员状态，等等）
      - 智能体管理（增加智能体，修改智能体，删除智能体）
      - 模型管理（增加模型，修改模型，删除模型）
      - 页面配置管理（修改前端md文档页面）
      - 用户管理（增加用户，修改用户，删除用户）
      - 用户调用日志（查看用户调用日志）
      - 用户调用统计（查看用户调用统计）
  - 前端需要新增一个可以在后台设置内容的页面（此页面渲染md格式内容）
  - 实现算力扣费系统：
    - 充值套餐：1000元获得1000算力，10000元获得20000算力
    - 差异化扣费：根据智能体类型和模型等级进行不同的算力扣除
    - 档次管理：创建算力档次表，智能体关联档次，模型设置等级
    - 实时扣费：AI对话完成后根据智能体档次和模型等级自动扣除算力
- 新版本除了新增一个页面外，无需修改界面ui，直接使用旧版本的ui
- 新版本需要实现一个flutter-windows的后台管理页面，用于管理用户、智能体、模型、页面配置、算力档次等

# 数据库
- 数据库使用腾讯云开发文档型数据库
- 数据库结构参考 @numerology_ai_chat_new/doc/数据库结构.md

# 需要注意的事项
  - 前端仅需支持windows平台即可
  - 后端仅需支持windows以及android平台即可