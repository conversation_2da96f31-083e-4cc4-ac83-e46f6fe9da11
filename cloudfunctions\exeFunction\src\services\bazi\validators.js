/**
 * 云函数端验证工具模块
 * 提供各种输入验证函数，确保用户输入安全性和有效性
 */

/**
 * 验证姓名
 * @param {string} name - 姓名字符串
 * @returns {Object} - 验证结果，包含是否有效和错误消息
 */
function validateName(name) {
  if (!name || typeof name !== 'string') {
    return { valid: false, message: '姓名不能为空' };
  }
  
  // 去除首尾空格
  const cleanedName = name.trim();
  
  // 验证长度
  if (cleanedName.length > 20) {
    return { valid: false, message: '姓名不能超过20个字符' };
  }
  
  // 验证字符合法性 (中文、英文、数字、点、中间点)
  const nameRegex = /^[\u4e00-\u9fa5a-zA-Z0-9·.]+$/;
  if (!nameRegex.test(cleanedName)) {
    return { valid: false, message: '姓名包含无效字符' };
  }
  
  return { valid: true, message: '', cleanValue: cleanedName };
}

/**
 * 验证性别
 * @param {string} gender - 性别字符串
 * @returns {Object} - 验证结果
 */
function validateGender(gender) {
  if (!gender || typeof gender !== 'string') {
    return { valid: false, message: '性别不能为空' };
  }
  
  if (gender !== '男' && gender !== '女') {
    return { valid: false, message: '性别值无效' };
  }
  
  return { valid: true, message: '', cleanValue: gender };
}

/**
 * 验证出生日期
 * @param {string} birthDate - 出生日期字符串
 * @returns {Object} - 验证结果
 */
function validateBirthDate(birthDate) {
  if (!birthDate || typeof birthDate !== 'string') {
    return { valid: false, message: '出生日期不能为空' };
  }
  
  // 验证日期格式
  let isValidFormat = false;
  let birthYear, birthMonth, birthDay;
  
  // 检查常见日期格式 (YYYY-MM-DD)
  if (birthDate.match(/^\d{4}-\d{1,2}-\d{1,2}$/)) {
    const parts = birthDate.split('-');
    birthYear = parseInt(parts[0], 10);
    birthMonth = parseInt(parts[1], 10);
    birthDay = parseInt(parts[2], 10);
    isValidFormat = true;
  }
  // 检查中文日期格式
  else if (birthDate.match(/\d+年/) && (birthDate.match(/\d+月/) || birthDate.match(/正月|二月|三月|四月|五月|六月|七月|八月|九月|十月|十一月|十二月/)) && (birthDate.match(/\d+[日|号]/) || birthDate.match(/初一|初二|初三|初四|初五|初六|初七|初八|初九|初十|十一|十二|十三|十四|十五|十六|十七|十八|十九|二十|二十一|二十二|二十三|二十四|二十五|二十六|二十七|二十八|二十九|三十/))) {
    const yearMatch = birthDate.match(/(\d+)年/);
    if (yearMatch) {
      birthYear = parseInt(yearMatch[1], 10);
      isValidFormat = true;
    }
  }
  
  if (!isValidFormat) {
    return { valid: false, message: '出生日期格式无效' };
  }
  
  // 验证年份合理性
  if (birthYear) {
    const currentYear = new Date().getFullYear();
    if (birthYear < 1900 || birthYear > currentYear) {
      return { valid: false, message: '出生年份不合理' };
    }
  }
  
  // 验证公历日期合法性
  if (birthYear && birthMonth && birthDay) {
    if (birthMonth < 1 || birthMonth > 12) {
      return { valid: false, message: '出生月份无效' };
    }
    
    // 检查日期是否有效
    const daysInMonth = new Date(birthYear, birthMonth, 0).getDate();
    if (birthDay < 1 || birthDay > daysInMonth) {
      return { valid: false, message: '出生日期无效' };
    }
  }
  
  return { valid: true, message: '', cleanValue: birthDate };
}

/**
 * 验证出生时间
 * @param {string} birthTime - 出生时间字符串
 * @returns {Object} - 验证结果
 */
function validateBirthTime(birthTime) {
  if (!birthTime || typeof birthTime !== 'string') {
    return { valid: false, message: '出生时间不能为空' };
  }
  
  // 验证24小时制时间格式 (HH:MM 或 HH:MM:SS)
  const timeRegex = /^([01]\d|2[0-3]):([0-5]\d)(?::([0-5]\d))?$/;
  
  // 验证时辰格式
  const timePeriodRegex = /^(子|丑|寅|卯|辰|巳|午|未|申|酉|戌|亥)时$/;
  
  if (!timeRegex.test(birthTime) && !timePeriodRegex.test(birthTime)) {
    return { valid: false, message: '出生时间格式无效' };
  }
  
  return { valid: true, message: '', cleanValue: birthTime };
}

/**
 * 验证历法类型
 * @param {string} calendarType - 历法类型
 * @returns {Object} - 验证结果
 */
function validateCalendarType(calendarType) {
  if (!calendarType || typeof calendarType !== 'string') {
    return { valid: false, message: '历法类型不能为空' };
  }
  
  const validTypes = ['阳历', '阴历', '公历', '农历'];
  if (!validTypes.includes(calendarType)) {
    return { valid: false, message: '历法类型无效' };
  }
  
  return { valid: true, message: '', cleanValue: calendarType };
}

/**
 * 验证出生地点
 * @param {string} birthPlace - 出生地点
 * @returns {Object} - 验证结果
 */
function validateBirthPlace(birthPlace) {
  if (!birthPlace || typeof birthPlace !== 'string') {
    return { valid: true, message: '', cleanValue: '未指定' }; // 允许为空，使用默认值
  }

  // 去除首尾空格
  const cleanedPlace = birthPlace.trim();

  if (cleanedPlace === '') {
    return { valid: true, message: '', cleanValue: '未指定' }; // 允许为空，使用默认值
  }

  // 过滤掉特殊字符
  const filteredPlace = cleanedPlace.replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, '');

  return { valid: true, message: '', cleanValue: filteredPlace };
}

/**
 * 验证经纬度坐标
 * @param {number} latitude - 纬度
 * @param {number} longitude - 经度
 * @returns {Object} - 验证结果
 */
function validateCoordinates(latitude, longitude) {
  // 如果都没有提供，则认为是有效的（不使用真太阳时）
  if (latitude === undefined && longitude === undefined) {
    return { valid: true, message: '', cleanValue: { latitude: null, longitude: null } };
  }

  // 如果只提供了一个，则无效
  if ((latitude === undefined) !== (longitude === undefined)) {
    return { valid: false, message: '经纬度必须同时提供或同时不提供' };
  }

  // 验证数据类型
  if (typeof latitude !== 'number' || typeof longitude !== 'number') {
    return { valid: false, message: '经纬度必须是数字' };
  }

  // 验证数值有效性
  if (isNaN(latitude) || isNaN(longitude)) {
    return { valid: false, message: '经纬度数值无效' };
  }

  // 中国境内经纬度范围验证
  const MIN_LAT = 18.0;  // 最南端（海南）
  const MAX_LAT = 54.0;  // 最北端（黑龙江）
  const MIN_LNG = 73.0;  // 最西端（新疆）
  const MAX_LNG = 135.0; // 最东端（黑龙江）

  if (latitude < MIN_LAT || latitude > MAX_LAT) {
    return { valid: false, message: `纬度超出中国境内范围（${MIN_LAT}°-${MAX_LAT}°）` };
  }

  if (longitude < MIN_LNG || longitude > MAX_LNG) {
    return { valid: false, message: `经度超出中国境内范围（${MIN_LNG}°-${MAX_LNG}°）` };
  }

  return {
    valid: true,
    message: '',
    cleanValue: {
      latitude: parseFloat(latitude.toFixed(6)),
      longitude: parseFloat(longitude.toFixed(6))
    }
  };
}

/**
 * 验证地区参数
 * @param {string} region - 地区
 * @returns {Object} - 验证结果
 */
function validateRegion(region) {
  if (!region) {
    return { valid: true, message: '', cleanValue: '大陆' }; // 默认值
  }

  if (typeof region !== 'string') {
    return { valid: false, message: '地区参数必须是字符串' };
  }

  const validRegions = ['大陆', '台湾', '香港', '澳门'];
  if (!validRegions.includes(region)) {
    return { valid: false, message: '地区参数无效，支持：大陆、台湾、香港、澳门' };
  }

  return { valid: true, message: '', cleanValue: region };
}

/**
 * 验证夏令时考虑选项
 * @param {boolean} considerDaylightSaving - 是否考虑夏令时
 * @returns {Object} - 验证结果
 */
function validateDaylightSavingOption(considerDaylightSaving) {
  if (considerDaylightSaving === undefined || considerDaylightSaving === null) {
    return { valid: true, message: '', cleanValue: true }; // 默认值
  }

  if (typeof considerDaylightSaving !== 'boolean') {
    return { valid: false, message: '夏令时选项必须是布尔值' };
  }

  return { valid: true, message: '', cleanValue: considerDaylightSaving };
}

/**
 * 验证真太阳时计算选项
 * @param {boolean} enableSolarTimeCalculation - 是否启用真太阳时计算
 * @returns {Object} - 验证结果
 */
function validateSolarTimeOption(enableSolarTimeCalculation) {
  if (enableSolarTimeCalculation === undefined || enableSolarTimeCalculation === null) {
    return { valid: true, message: '', cleanValue: true }; // 默认值
  }

  if (typeof enableSolarTimeCalculation !== 'boolean') {
    return { valid: false, message: '真太阳时计算选项必须是布尔值' };
  }

  return { valid: true, message: '', cleanValue: enableSolarTimeCalculation };
}

/**
 * 验证子时处理选项
 * @param {number} ziTimeHandling - 子时处理方式（0: 晚子时，1: 早子时）
 * @returns {Object} - 验证结果
 */
function validateZiTimeHandling(ziTimeHandling) {
  if (ziTimeHandling === undefined || ziTimeHandling === null) {
    return { valid: true, message: '', cleanValue: 0 }; // 默认值：晚子时
  }

  if (typeof ziTimeHandling !== 'number') {
    return { valid: false, message: '子时处理选项必须是数字' };
  }

  if (ziTimeHandling !== 0 && ziTimeHandling !== 1) {
    return { valid: false, message: '子时处理选项无效，支持：0（晚子时）、1（早子时）' };
  }

  return { valid: true, message: '', cleanValue: ziTimeHandling };
}

/**
 * 从输入中移除潜在的危险字符
 * @param {string} input - 输入字符串
 * @returns {string} - 清理后的字符串
 */
function sanitizeInput(input) {
  if (typeof input !== 'string') {
    return input;
  }

  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
    .replace(/`/g, '&#96;')
    .replace(/\(/g, '&#40;')
    .replace(/\)/g, '&#41;');
}

/**
 * 验证请求参数的完整性和合法性
 * @param {Object} params - 请求参数对象
 * @returns {Object} - 验证结果
 */
function validateRequestParams(params) {
  // 验证姓名
  const nameResult = validateName(params.name);
  if (!nameResult.valid) {
    return { valid: false, code: 400, message: nameResult.message };
  }

  // 验证性别
  const genderResult = validateGender(params.gender);
  if (!genderResult.valid) {
    return { valid: false, code: 400, message: genderResult.message };
  }
  
  // 验证出生日期
  const birthDateResult = validateBirthDate(params.birthDate);
  if (!birthDateResult.valid) {
    return { valid: false, code: 400, message: birthDateResult.message };
  }
  
  // 验证出生时间
  const birthTimeResult = validateBirthTime(params.birthTime);
  if (!birthTimeResult.valid) {
    return { valid: false, code: 400, message: birthTimeResult.message };
  }
  
  // 验证历法类型
  const calendarTypeResult = validateCalendarType(params.calendarType);
  if (!calendarTypeResult.valid) {
    return { valid: false, code: 400, message: calendarTypeResult.message };
  }
  
  // 验证出生地点
  const birthPlaceResult = validateBirthPlace(params.birthPlace);
  if (!birthPlaceResult.valid) {
    return { valid: false, code: 400, message: birthPlaceResult.message };
  }

  // 验证经纬度（可选）
  const coordinatesResult = validateCoordinates(params.latitude, params.longitude);
  if (!coordinatesResult.valid) {
    return { valid: false, code: 400, message: coordinatesResult.message };
  }

  // 验证地区（可选）
  const regionResult = validateRegion(params.region);
  if (!regionResult.valid) {
    return { valid: false, code: 400, message: regionResult.message };
  }

  // 验证夏令时选项（可选）
  const daylightSavingResult = validateDaylightSavingOption(params.considerDaylightSaving);
  if (!daylightSavingResult.valid) {
    return { valid: false, code: 400, message: daylightSavingResult.message };
  }

  // 验证真太阳时计算选项（可选）
  const solarTimeResult = validateSolarTimeOption(params.enableSolarTimeCalculation);
  if (!solarTimeResult.valid) {
    return { valid: false, code: 400, message: solarTimeResult.message };
  }

  // 验证子时处理选项（可选）
  const ziTimeHandlingResult = validateZiTimeHandling(params.ziTimeHandling);
  if (!ziTimeHandlingResult.valid) {
    return { valid: false, code: 400, message: ziTimeHandlingResult.message };
  }

  // 所有验证通过，返回清理后的数据
  return {
    valid: true,
    code: 0,
    message: '验证通过',
    cleanData: {
      name: nameResult.cleanValue, // 使用用户输入的真实姓名
      gender: genderResult.cleanValue,
      birthDate: birthDateResult.cleanValue,
      birthTime: birthTimeResult.cleanValue,
      birthPlace: birthPlaceResult.cleanValue,
      calendarType: calendarTypeResult.cleanValue,
      // 保留其他可能的参数
      isLeapMonth: !!params.isLeapMonth,
      yearGanZhiSet: parseInt(params.yearGanZhiSet || 2), // 默认使用精确立春时刻
      monthGanZhiSet: parseInt(params.monthGanZhiSet || 1), // 默认使用精确节气交接时刻
      dayGanZhiSet: parseInt(params.dayGanZhiSet || 0),
      hourGanZhiSet: parseInt(params.hourGanZhiSet || 0),
      // 真太阳时相关参数
      latitude: coordinatesResult.cleanValue.latitude,
      longitude: coordinatesResult.cleanValue.longitude,
      region: regionResult.cleanValue,
      // 新增的夏令时和真太阳时计算选项
      considerDaylightSaving: daylightSavingResult.cleanValue,
      enableSolarTimeCalculation: solarTimeResult.cleanValue,
      // 子时处理选项
      ziTimeHandling: ziTimeHandlingResult.cleanValue
    }
  };
}

module.exports = {
  validateName,
  validateGender,
  validateBirthDate,
  validateBirthTime,
  validateCalendarType,
  validateBirthPlace,
  validateCoordinates,
  validateRegion,
  validateDaylightSavingOption,
  validateSolarTimeOption,
  validateZiTimeHandling,
  sanitizeInput,
  validateRequestParams
};