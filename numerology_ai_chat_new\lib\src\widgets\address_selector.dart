import 'package:flutter/material.dart';
import '../services/address_service.dart';

/// 地址选择组件
/// 提供省市区三级联动选择功能
class AddressSelector extends StatefulWidget {
  /// 地址选择回调
  final Function(AddressSelectionResult?) onAddressSelected;
  
  /// 初始选中的地址
  final AddressSelectionResult? initialAddress;
  
  /// 是否启用真太阳时计算
  final bool enableSolarTime;
  
  /// 是否显示详细地址输入框
  final bool showDetailInput;

  const AddressSelector({
    super.key,
    required this.onAddressSelected,
    this.initialAddress,
    this.enableSolarTime = true,
    this.showDetailInput = false,
  });

  @override
  State<AddressSelector> createState() => _AddressSelectorState();
}

class _AddressSelectorState extends State<AddressSelector> {
  // 数据列表
  List<Map<String, dynamic>> _provinces = [];
  List<Map<String, dynamic>> _cities = [];
  List<Map<String, dynamic>> _districts = [];
  
  // 选中的值
  String? _selectedProvinceId;
  String? _selectedCityId;
  String? _selectedDistrictId;
  
  // 选中的显示名称
  String? _selectedProvinceName;
  String? _selectedCityName;
  String? _selectedDistrictName;
  
  // 加载状态
  bool _isLoadingProvinces = true;
  bool _isLoadingCities = false;
  bool _isLoadingDistricts = false;
  
  // 详细地址控制器
  final TextEditingController _detailController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadProvinces();
    _initializeFromInitialAddress();
  }

  @override
  void dispose() {
    _detailController.dispose();
    super.dispose();
  }

  /// 从初始地址初始化选择状态
  void _initializeFromInitialAddress() {
    if (widget.initialAddress != null) {
      final address = widget.initialAddress!;
      _selectedProvinceId = address.provinceId;
      _selectedProvinceName = address.provinceName;
      _selectedCityId = address.cityId;
      _selectedCityName = address.cityName;
      _selectedDistrictId = address.districtId;
      _selectedDistrictName = address.districtName;
      
      // 异步加载对应的城市和区县数据
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_selectedProvinceId != null) {
          _loadCities(_selectedProvinceId!);
        }
        if (_selectedCityId != null) {
          _loadDistricts(_selectedCityId!);
        }
      });
    }
  }

  /// 加载省份数据
  Future<void> _loadProvinces() async {
    try {
      setState(() {
        _isLoadingProvinces = true;
      });
      
      final provinces = await AddressService.getProvinces();
      
      setState(() {
        _provinces = provinces;
        _isLoadingProvinces = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingProvinces = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载省份数据失败: $e')),
        );
      }
    }
  }

  /// 加载城市数据
  Future<void> _loadCities(String provinceId) async {
    try {
      setState(() {
        _isLoadingCities = true;
        _cities = [];
        _districts = [];
        _selectedCityId = null;
        _selectedCityName = null;
        _selectedDistrictId = null;
        _selectedDistrictName = null;
      });
      
      final cities = await AddressService.getCitiesByProvinceId(provinceId);
      
      setState(() {
        _cities = cities;
        _isLoadingCities = false;
      });
      
      _notifyAddressChange();
    } catch (e) {
      setState(() {
        _isLoadingCities = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载城市数据失败: $e')),
        );
      }
    }
  }

  /// 加载区县数据
  Future<void> _loadDistricts(String cityId) async {
    try {
      setState(() {
        _isLoadingDistricts = true;
        _districts = [];
        _selectedDistrictId = null;
        _selectedDistrictName = null;
      });
      
      final districts = await AddressService.getDistrictsByCityId(cityId);
      
      setState(() {
        _districts = districts;
        _isLoadingDistricts = false;
      });
      
      _notifyAddressChange();
    } catch (e) {
      setState(() {
        _isLoadingDistricts = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载区县数据失败: $e')),
        );
      }
    }
  }

  /// 通知地址变化
  void _notifyAddressChange() {
    if (_selectedProvinceId != null && 
        _selectedCityId != null && 
        _selectedDistrictId != null &&
        _selectedProvinceName != null &&
        _selectedCityName != null &&
        _selectedDistrictName != null) {
      
      // 获取选中区县的经纬度
      final selectedDistrict = _districts.firstWhere(
        (district) => district['id'] == _selectedDistrictId,
        orElse: () => <String, dynamic>{},
      );
      
      if (selectedDistrict.isNotEmpty) {
        final result = AddressSelectionResult(
          provinceId: _selectedProvinceId!,
          provinceName: _selectedProvinceName!,
          cityId: _selectedCityId!,
          cityName: _selectedCityName!,
          districtId: _selectedDistrictId!,
          districtName: _selectedDistrictName!,
          latitude: (selectedDistrict['latitude'] ?? 0.0).toDouble(),
          longitude: (selectedDistrict['longitude'] ?? 0.0).toDouble(),
        );
        
        widget.onAddressSelected(result);
      }
    } else {
      widget.onAddressSelected(null);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // 省份选择
        _buildProvinceDropdown(),
        
        const SizedBox(height: 12),
        
        // 城市选择
        _buildCityDropdown(),
        
        const SizedBox(height: 12),
        
        // 区县选择
        _buildDistrictDropdown(),
        
        // 真太阳时说明
        if (widget.enableSolarTime && _selectedDistrictId != null)
          _buildSolarTimeInfo(),
        
        // 详细地址输入（可选）
        if (widget.showDetailInput) ...[
          const SizedBox(height: 12),
          _buildDetailAddressInput(),
        ],
      ],
    );
  }

  /// 构建省份下拉框
  Widget _buildProvinceDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedProvinceId,
      decoration: const InputDecoration(
        labelText: '省份',
        prefixIcon: Icon(Icons.location_city_outlined),
      ),
      hint: _isLoadingProvinces 
          ? const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 8),
                Text('加载中...'),
              ],
            )
          : const Text('请选择省份'),
      items: _provinces.map((province) {
        return DropdownMenuItem<String>(
          value: province['id'],
          child: Text(province['text']),
        );
      }).toList(),
      onChanged: _isLoadingProvinces ? null : (value) {
        if (value != null) {
          final selectedProvince = _provinces.firstWhere(
            (province) => province['id'] == value,
          );
          
          setState(() {
            _selectedProvinceId = value;
            _selectedProvinceName = selectedProvince['text'];
          });
          
          _loadCities(value);
        }
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return '请选择省份';
        }
        return null;
      },
    );
  }

  /// 构建城市下拉框
  Widget _buildCityDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedCityId,
      decoration: const InputDecoration(
        labelText: '城市',
        prefixIcon: Icon(Icons.location_on_outlined),
      ),
      hint: _isLoadingCities 
          ? const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 8),
                Text('加载中...'),
              ],
            )
          : const Text('请先选择省份'),
      items: _cities.map((city) {
        return DropdownMenuItem<String>(
          value: city['id'],
          child: Text(city['text']),
        );
      }).toList(),
      onChanged: (_isLoadingCities || _cities.isEmpty) ? null : (value) {
        if (value != null) {
          final selectedCity = _cities.firstWhere(
            (city) => city['id'] == value,
          );
          
          setState(() {
            _selectedCityId = value;
            _selectedCityName = selectedCity['text'];
          });
          
          _loadDistricts(value);
        }
      },
      validator: (value) {
        if (_selectedProvinceId != null && (value == null || value.isEmpty)) {
          return '请选择城市';
        }
        return null;
      },
    );
  }

  /// 构建区县下拉框
  Widget _buildDistrictDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedDistrictId,
      decoration: const InputDecoration(
        labelText: '区县',
        prefixIcon: Icon(Icons.place_outlined),
      ),
      hint: _isLoadingDistricts 
          ? const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 8),
                Text('加载中...'),
              ],
            )
          : const Text('请先选择城市'),
      items: _districts.map((district) {
        return DropdownMenuItem<String>(
          value: district['id'],
          child: Text(district['text']),
        );
      }).toList(),
      onChanged: (_isLoadingDistricts || _districts.isEmpty) ? null : (value) {
        if (value != null) {
          final selectedDistrict = _districts.firstWhere(
            (district) => district['id'] == value,
          );
          
          setState(() {
            _selectedDistrictId = value;
            _selectedDistrictName = selectedDistrict['text'];
          });
          
          _notifyAddressChange();
        }
      },
      validator: (value) {
        if (_selectedCityId != null && (value == null || value.isEmpty)) {
          return '请选择区县';
        }
        return null;
      },
    );
  }

  /// 构建真太阳时信息显示
  Widget _buildSolarTimeInfo() {
    final selectedDistrict = _districts.firstWhere(
      (district) => district['id'] == _selectedDistrictId,
      orElse: () => <String, dynamic>{},
    );
    
    if (selectedDistrict.isEmpty) return const SizedBox.shrink();
    
    final longitude = (selectedDistrict['longitude'] ?? 0.0).toDouble();
    final latitude = (selectedDistrict['latitude'] ?? 0.0).toDouble();
    
    // 计算时间差
    final longitudeDiff = longitude - 120;
    final timeOffsetMinutes = (longitudeDiff * 4).round();
    
    String timeInfo;
    if (timeOffsetMinutes > 0) {
      timeInfo = '真太阳时比北京时间快${timeOffsetMinutes}分钟';
    } else if (timeOffsetMinutes < 0) {
      timeInfo = '真太阳时比北京时间慢${timeOffsetMinutes.abs()}分钟';
    } else {
      timeInfo = '真太阳时与北京时间相同';
    }
    
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.access_time,
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 4),
              Text(
                '真太阳时计算',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            timeInfo,
            style: Theme.of(context).textTheme.bodySmall,
          ),
          Text(
            '经度: ${longitude.toStringAsFixed(4)}°, 纬度: ${latitude.toStringAsFixed(4)}°',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建详细地址输入框
  Widget _buildDetailAddressInput() {
    return TextFormField(
      controller: _detailController,
      decoration: const InputDecoration(
        labelText: '详细地址（可选）',
        prefixIcon: Icon(Icons.home_outlined),
        hintText: '如：XX街道XX号',
      ),
      maxLines: 2,
      textInputAction: TextInputAction.done,
    );
  }
}
