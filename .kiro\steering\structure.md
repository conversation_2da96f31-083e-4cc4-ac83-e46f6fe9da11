# 项目组织结构与文件夹结构

## 仓库概述
这是一个多组件命理AI应用程序，在不同服务和平台之间有清晰的关注点分离。

## 根目录结构
```
├── numerology_ai_chat_new/          # 主要的Flutter桌面应用程序
├── numerology_ai_chat_new_admin/    # Flutter管理面板
├── go_proxy/                        # Go中间件服务
├── cloudfunctions/                  # 腾讯云函数
├── weak-admin-web/                  # Vue.js网页管理界面
├── doc/                            # 项目文档
├── tools/                          # 实用工具脚本
└── [测试文件和配置]                  # 各种测试和配置文件
```

## Flutter桌面应用 (numerology_ai_chat_new/)
```
lib/
├── main.dart                       # 应用程序入口点
└── src/
    ├── core/                       # 核心基础设施
    │   ├── config/                 # 应用配置
    │   ├── constants/              # 应用常量
    │   ├── router/                 # 导航路由
    │   └── storage/                # 本地存储工具
    ├── models/                     # 数据模型（带JSON序列化）
    │   ├── user.dart              # 用户模型 (↔ exe_users集合)
    │   ├── agent.dart             # AI智能体模型 (↔ exe_agents)
    │   ├── model.dart             # AI模型配置 (↔ exe_models)
    │   └── chat_*.dart            # 聊天相关模型
    ├── providers/                  # Riverpod状态管理
    │   ├── auth_provider.dart     # 认证状态
    │   ├── chat_provider.dart     # 聊天功能
    │   └── app_provider.dart      # 全局应用状态
    ├── services/                   # 业务逻辑层
    │   ├── api_service.dart       # HTTP客户端包装器
    │   ├── auth_service.dart      # 认证服务
    │   ├── chat_service.dart      # 聊天功能
    │   └── storage_service.dart   # 本地存储服务
    ├── screens/                    # UI界面
    │   ├── auth/                  # 登录/注册界面
    │   ├── chat/                  # 聊天界面
    │   ├── settings/              # 设置界面
    │   └── home/                  # 主仪表板
    ├── widgets/                    # 可复用UI组件
    │   ├── common/                # 通用组件
    │   └── chat/                  # 聊天专用组件
    ├── theme/                      # UI主题
    │   ├── app_theme.dart         # Material 3主题配置
    │   └── color_schemes.dart     # 颜色定义
    └── utils/                      # 实用函数
        ├── validators.dart        # 表单验证
        └── extensions.dart        # Dart扩展
```

## Go代理服务 (go_proxy/)
```
├── cmd/
│   └── server/                     # 主应用程序入口
├── internal/
│   ├── config/                     # 配置管理
│   ├── handlers/                   # HTTP请求处理器
│   ├── middleware/                 # HTTP中间件
│   ├── models/                     # 数据结构
│   └── services/                   # 业务逻辑
├── go.mod                          # Go模块定义
├── .env.example                    # 环境变量模板
├── deploy.sh                       # 部署脚本
└── go_proxy.service               # Systemd服务文件
```

## 云函数 (cloudfunctions/)
```
├── exeFunction/                    # 用户端API
│   ├── index.js                   # 带动作路由的入口点
│   ├── src/
│   │   ├── handlers/              # 业务逻辑处理器
│   │   │   ├── auth.js           # 用户认证
│   │   │   ├── agents.js         # 智能体管理
│   │   │   ├── models.js         # 模型管理
│   │   │   └── user_info.js      # 用户信息
│   │   ├── middleware/            # 请求中间件
│   │   └── utils/                 # 实用函数
│   └── package.json
├── exeAdmin/                       # 管理端API
│   ├── index.js                   # 带动作路由的入口点
│   ├── src/
│   │   ├── handlers/              # 管理专用处理器
│   │   │   ├── admin_auth.js     # 管理员认证
│   │   │   ├── user_manage.js    # 用户管理
│   │   │   ├── agent_manage.js   # 智能体CRUD操作
│   │   │   └── model_manage.js   # 模型CRUD操作
│   │   └── utils/                 # 共享实用工具
│   └── package.json
└── shared/                         # 公共实用工具（可选）
```

## 网页管理界面 (weak-admin-web/)
```
├── src/
│   ├── components/                 # Vue组件
│   ├── views/                      # 页面视图
│   ├── router/                     # Vue Router配置
│   ├── stores/                     # Pinia状态存储
│   └── utils/                      # 实用函数
├── public/                         # 静态资源
├── package.json                    # NPM依赖
└── vite.config.js                 # Vite构建配置
```

## 文档 (doc/)
- `exe_需求.md` - 项目需求规范
- `exe_项目开发日志.md` - 开发日志和进度跟踪
- `exe_前端项目结构.md` - 前端架构文档
- `exe_后端项目结构.md` - 后端架构文档
- `exe_数据库结构.md` - 数据库模式文档

## 关键架构模式

### 数据流
```
前端 → Go代理 → 云函数 → 数据库
前端 → 本地存储（仅聊天历史）
```

### 安全边界
- **前端**: 无API密钥，无敏感提示词
- **Go代理**: 处理AI API调用，加密/解密数据
- **云函数**: 用户认证，业务逻辑
- **数据库**: 加密的敏感数据（API密钥，提示词）

### 文件命名约定
- **Dart文件**: snake_case（例如：`chat_provider.dart`）
- **Go文件**: snake_case（例如：`auth_handler.go`）
- **JavaScript文件**: snake_case（例如：`user_manage.js`）
- **Vue文件**: 组件使用PascalCase（例如：`UserList.vue`）

### 导入组织
- **Flutter**: lib/内使用相对导入，包使用绝对导入
- **Go**: 标准库优先，然后第三方，最后本地
- **Node.js**: 内置模块优先，然后npm包，最后本地模块

### 配置管理
- **Flutter**: `core/config/`中的环境特定配置
- **Go**: 使用godotenv的`.env`文件
- **云函数**: 云控制台中的环境变量
- **Vue**: Vite环境变量

## 开发工作流程
1. **前端更改**: 使用`flutter run -d windows`本地测试
2. **Go服务更改**: 使用`go run ./cmd/server`测试
3. **云函数更改**: 通过腾讯云CLI部署
4. **数据库更改**: 通过管理界面或直接控制台更新