#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试案例，验证云函数是否正常工作
"""

import requests
import json

# 云函数API地址
API_URL = "https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction"

def test_simple_case():
    """测试一个简单的案例"""
    print("测试简单案例：2024年2月4日16:26")
    
    request_data = {
        "action": "baziAnalyze",
        "name": "测试者",
        "gender": "男",
        "calendarType": "公历",
        "birthDate": "2024-02-04",
        "birthTime": "16:26",
        "isLeapMonth": False,
        "birthPlace": "北京",
        "ziTimeHandling": 0
    }
    
    try:
        response = requests.post(
            API_URL,
            headers={'Content-Type': 'application/json'},
            json=request_data,
            timeout=30
        )
        
        print(f"HTTP状态码：{response.status_code}")
        print(f"响应内容：{response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"API返回码：{result.get('code')}")
            print(f"API消息：{result.get('message')}")
            
            if result.get('code') == 0:
                data = result.get('data', {})
                print(f"八字结果：{data.get('baziStr', '')}")
                return True
        
        return False
        
    except Exception as e:
        print(f"请求异常：{str(e)}")
        return False

if __name__ == "__main__":
    test_simple_case()
