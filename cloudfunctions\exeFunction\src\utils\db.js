const cloud = require('wx-server-sdk')
const CryptoJS = require('crypto-js')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 获取数据库引用
const db = cloud.database()
const _ = db.command

// 数据库集合名称常量
const COLLECTIONS = {
  USERS: 'exe_users',
  AGENTS: 'exe_agents',
  MODELS: 'exe_models',
  ADMINS: 'exe_admins',
  PAGES: 'exe_pages',
  PURCHASE_ORDERS: 'exe_purchase_orders',
  PAYMENT_PACKAGES: 'exe_payment_packages',
  PRICING_TIERS: 'exe_pricing_tiers',
  USAGE_HISTORY: 'exe_usage_history',
  ACTIVATION_CODES: 'exe_activation_codes',
  SYSTEM_CONFIG: 'exe_system_config'
}

// AES加密密钥 - 生产环境应从环境变量获取
const AES_SECRET_KEY = process.env.AES_SECRET_KEY || 'your-aes-secret-key-32-chars-long'

/**
 * AES加密
 * @param {string} text 待加密文本
 * @returns {string} 加密后的文本
 */
function encryptAES(text) {
  if (!text) return ''
  return CryptoJS.AES.encrypt(text, AES_SECRET_KEY).toString()
}

/**
 * AES解密
 * @param {string} encryptedText 加密的文本
 * @returns {string} 解密后的文本
 */
function decryptAES(encryptedText) {
  if (!encryptedText) return ''

  // 如果不是加密格式（不以U2FsdGVkX1+开头），直接返回原文
  if (!encryptedText.startsWith('U2FsdGVkX1+')) {
    return encryptedText
  }

  try {
    const bytes = CryptoJS.AES.decrypt(encryptedText, AES_SECRET_KEY)
    const decrypted = bytes.toString(CryptoJS.enc.Utf8)
    return decrypted || encryptedText // 如果解密失败，返回原文
  } catch (error) {
    // 解密失败时返回原文
    return encryptedText
  }
}

/**
 * 获取集合引用
 * @param {string} collectionName 集合名称
 * @returns {object} 集合引用
 */
function getCollection(collectionName) {
  return db.collection(collectionName)
}

/**
 * 管理员集合操作
 */
const adminCollection = {
  // 根据账号查找管理员
  async findByAccount(adminAccount) {
    const result = await getCollection(COLLECTIONS.ADMINS)
      .where({ adminAccount })
      .get()
    return result.data[0] || null
  },
  
  // 根据ID查找管理员
  async findById(adminId) {
    const result = await getCollection(COLLECTIONS.ADMINS)
      .doc(adminId)
      .get()
    return result.data || null
  },
  
  // 更新管理员登录信息
  async updateLoginInfo(adminId) {
    return await getCollection(COLLECTIONS.ADMINS)
      .doc(adminId)
      .update({
        data: {
          lastLoginAt: new Date(),
          loginCount: _.inc(1),
          updatedAt: new Date()
        }
      })
  }
}

/**
 * 用户集合操作（管理员视角）
 */
const userCollection = {
  // 分页获取用户列表
  async getList(page = 1, pageSize = 20, filters = {}) {
    let query = getCollection(COLLECTIONS.USERS)
    
    // 应用过滤条件
    if (filters.username) {
      query = query.where({
        username: new RegExp(filters.username, 'i')
      })
    }
    if (filters.status) {
      query = query.where({ status: filters.status })
    }
    if (filters.membershipType) {
      query = query.where({ 'membership.type': filters.membershipType })
    }
    
    // 获取总数
    const countResult = await query.count()
    const total = countResult.total
    
    // 分页查询
    const result = await query
      .orderBy('createdAt', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .field({
        password: false, // 不返回密码
        refreshToken: false // 不返回refreshToken
      })
      .get()
    
    return {
      data: result.data,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }
  },
  
  // 根据ID查找用户
  async findById(userId) {
    const result = await getCollection(COLLECTIONS.USERS)
      .doc(userId)
      .field({
        password: false,
        refreshToken: false
      })
      .get()
    return result.data || null
  },

  // 根据用户名查找用户（包含密码，用于登录验证）
  async findByUsername(username) {
    const result = await getCollection(COLLECTIONS.USERS)
      .where({ username })
      .get()
    return result.data[0] || null
  },

  // 根据refreshToken查找用户
  async findByRefreshToken(refreshToken) {
    const result = await getCollection(COLLECTIONS.USERS)
      .where({ refreshToken })
      .get()
    return result.data[0] || null
  },
  
  // 创建用户
  async create(userData) {
    const result = await getCollection(COLLECTIONS.USERS)
      .add({
        data: {
          ...userData,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      })
    return result._id
  },
  
  // 更新用户
  async update(userId, updateData) {
    return await getCollection(COLLECTIONS.USERS)
      .doc(userId)
      .update({
        data: {
          ...updateData,
          updatedAt: new Date()
        }
      })
  },
  
  // 原子操作：增加用户额度
  async addQuota(userId, addedCount) {
    return await getCollection(COLLECTIONS.USERS)
      .doc(userId)
      .update({
        data: {
          availableCount: _.inc(addedCount),
          updatedAt: new Date()
        }
      })
  },

  // 原子操作：扣减用户使用次数
  async decrementUsage(userId, usageCount = 1) {
    return await getCollection(COLLECTIONS.USERS)
      .doc(userId)
      .update({
        data: {
          availableCount: _.inc(-usageCount),
          totalUsageCount: _.inc(usageCount),
          updatedAt: new Date()
        }
      })
  }
}

/**
 * 智能体集合操作（管理员视角）
 */
const agentCollection = {
  // 获取所有智能体列表
  async getList() {
    const result = await getCollection(COLLECTIONS.AGENTS)
      .orderBy('sortOrder', 'asc')
      .get()
    return result.data
  },

  // 获取启用的智能体列表（用户视角，不包含提示词）
  async getActiveList() {
    const result = await getCollection(COLLECTIONS.AGENTS)
      .where({ isActive: true })
      .orderBy('sortOrder', 'asc')
      .field({
        agentPrompt: false // 不返回提示词
      })
      .get()
    return result.data
  },

  // 获取启用的智能体完整列表（包含提示词，供Go程序使用）
  async getActiveFullList() {
    const result = await getCollection(COLLECTIONS.AGENTS)
      .where({ isActive: true })
      .orderBy('sortOrder', 'asc')
      .get()
    return result.data
  },
  
  // 根据ID查找智能体
  async findById(agentId) {
    const result = await getCollection(COLLECTIONS.AGENTS)
      .doc(agentId)
      .get()
    return result.data || null
  },
  
  // 创建智能体
  async create(agentData) {
    const result = await getCollection(COLLECTIONS.AGENTS)
      .add({
        data: {
          ...agentData,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      })
    return result._id
  },
  
  // 更新智能体
  async update(agentId, updateData) {
    return await getCollection(COLLECTIONS.AGENTS)
      .doc(agentId)
      .update({
        data: {
          ...updateData,
          updatedAt: new Date()
        }
      })
  },
  
  // 删除智能体
  async delete(agentId) {
    return await getCollection(COLLECTIONS.AGENTS)
      .doc(agentId)
      .remove()
  }
}

/**
 * 模型集合操作（管理员视角）
 */
const modelCollection = {
  // 获取所有模型列表
  async getList() {
    const result = await getCollection(COLLECTIONS.MODELS)
      .orderBy('sortOrder', 'asc')
      .get()

    // 解密API密钥用于管理员查看
    return result.data.map(model => ({
      ...model,
      modelApiKey: model.modelApiKey ? decryptAES(model.modelApiKey) : ''
    }))
  },

  // 获取启用的模型列表（用户视角，包含API URL但不包含API密钥）
  async getActiveList() {
    const result = await getCollection(COLLECTIONS.MODELS)
      .where({ isActive: true })
      .orderBy('sortOrder', 'asc')
      .field({
        modelApiKey: false // 不返回API密钥，但保留API URL
      })
      .get()
    return result.data
  },

  // 获取启用的模型完整列表（包含API密钥和URL，供Go程序使用）
  async getActiveFullList() {
    const result = await getCollection(COLLECTIONS.MODELS)
      .where({ isActive: true })
      .orderBy('sortOrder', 'asc')
      .get()

    // 解密API密钥
    return result.data.map(model => {
      const decryptedKey = model.modelApiKey ? decryptAES(model.modelApiKey) : ''
      console.log(`Model ${model.modelName}: original key length=${model.modelApiKey?.length || 0}, decrypted key length=${decryptedKey.length}`)
      return {
        ...model,
        modelApiKey: decryptedKey
      }
    })
  },
  
  // 根据ID查找模型
  async findById(modelId) {
    const result = await getCollection(COLLECTIONS.MODELS)
      .doc(modelId)
      .get()
    
    if (result.data && result.data.modelApiKey) {
      result.data.modelApiKey = decryptAES(result.data.modelApiKey)
    }
    
    return result.data || null
  },
  
  // 创建模型
  async create(modelData) {
    const dataToCreate = { ...modelData }
    if (dataToCreate.modelApiKey) {
      dataToCreate.modelApiKey = encryptAES(dataToCreate.modelApiKey)
    }
    
    const result = await getCollection(COLLECTIONS.MODELS)
      .add({
        data: {
          ...dataToCreate,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      })
    return result._id
  },
  
  // 更新模型
  async update(modelId, updateData) {
    const dataToUpdate = { ...updateData }
    if (dataToUpdate.modelApiKey) {
      dataToUpdate.modelApiKey = encryptAES(dataToUpdate.modelApiKey)
    }
    
    return await getCollection(COLLECTIONS.MODELS)
      .doc(modelId)
      .update({
        data: {
          ...dataToUpdate,
          updatedAt: new Date()
        }
      })
  },
  
  // 删除模型
  async delete(modelId) {
    return await getCollection(COLLECTIONS.MODELS)
      .doc(modelId)
      .remove()
  }
}

/**
 * 页面配置集合操作
 */
const pageCollection = {
  // 获取页面配置列表（支持分页和筛选）
  async getList(where = {}, skip = 0, limit = 20) {
    const query = getCollection(COLLECTIONS.PAGES).where(where)
    
    const result = await query
      .orderBy('sortOrder', 'asc')
      .skip(skip)
      .limit(limit)
      .get()
    return result.data
  },
  
  // 获取页面配置总数
  async getCount(where = {}) {
    const countResult = await getCollection(COLLECTIONS.PAGES).where(where).count()
    return countResult.total
  },
  
  // 根据ID查找页面配置
  async findById(pageId) {
    const result = await getCollection(COLLECTIONS.PAGES)
      .doc(pageId)
      .get()
    return result.data || null
  },
  
  // 根据slug查找页面配置
  async findBySlug(slug) {
    const result = await getCollection(COLLECTIONS.PAGES)
      .where({ slug })
      .get()
    return result.data[0] || null
  },
  
  // 创建页面配置
  async create(pageData) {
    const result = await getCollection(COLLECTIONS.PAGES)
      .add({
        data: {
          ...pageData,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      })
    return result._id
  },
  
  // 更新页面配置
  async update(pageId, updateData) {
    return await getCollection(COLLECTIONS.PAGES)
      .doc(pageId)
      .update({
        data: {
          ...updateData,
          updatedAt: new Date()
        }
      })
  },
  
  // 删除页面配置
  async delete(pageId) {
    return await getCollection(COLLECTIONS.PAGES)
      .doc(pageId)
      .remove()
  }
}

/**
 * 购买订单集合操作
 */
const purchaseOrderCollection = {
  // 获取用户购买历史（分页）
  async getUserOrders(userId, page = 1, limit = 20) {
    const query = getCollection(COLLECTIONS.PURCHASE_ORDERS)
      .where({ userId })

    // 获取总数
    const countResult = await query.count()
    const total = countResult.total

    // 分页查询
    const result = await query
      .orderBy('createTime', 'desc')
      .skip((page - 1) * limit)
      .limit(limit)
      .get()

    return {
      orders: result.data,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    }
  },

  // 获取订单详情（确保订单属于指定用户）
  async getOrderDetail(orderId, userId) {
    const result = await getCollection(COLLECTIONS.PURCHASE_ORDERS)
      .doc(orderId)
      .get()

    const order = result.data
    if (!order || order.userId !== userId) {
      return null
    }

    return order
  },

  // 更新订单状态
  async updateOrderStatus(orderId, status, extraData = {}) {
    return await getCollection(COLLECTIONS.PURCHASE_ORDERS)
      .doc(orderId)
      .update({
        data: {
          status,
          ...extraData,
          updatedAt: new Date()
        }
      })
  },

  // 创建订单
  async create(orderData) {
    const result = await getCollection(COLLECTIONS.PURCHASE_ORDERS)
      .add({
        data: {
          ...orderData,
          createTime: new Date(),
          updatedAt: new Date()
        }
      })
    return result._id
  },

  // 更新订单支付信息
  async updateOrderPaymentInfo(orderId, paymentInfo) {
    return await getCollection(COLLECTIONS.PURCHASE_ORDERS)
      .doc(orderId)
      .update({
        data: {
          ...paymentInfo,
          updatedAt: new Date()
        }
      })
  }
}

/**
 * 算力套餐集合操作
 */
const paymentPackageCollection = {
  // 获取所有启用的套餐列表
  async getActiveList() {
    const result = await getCollection(COLLECTIONS.PAYMENT_PACKAGES)
      .where({ isActive: true })
      .orderBy('sortOrder', 'asc')
      .get()
    return result.data
  },

  // 根据ID查找套餐
  async findById(packageId) {
    const result = await getCollection(COLLECTIONS.PAYMENT_PACKAGES)
      .doc(packageId)
      .get()
    return result.data || null
  },

  // 获取所有套餐列表（管理员用）
  async getList() {
    const result = await getCollection(COLLECTIONS.PAYMENT_PACKAGES)
      .orderBy('sortOrder', 'asc')
      .get()
    return result.data
  },

  // 创建套餐
  async create(packageData) {
    const result = await getCollection(COLLECTIONS.PAYMENT_PACKAGES)
      .add({
        data: {
          ...packageData,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      })
    return result._id
  },

  // 更新套餐
  async update(packageId, updateData) {
    return await getCollection(COLLECTIONS.PAYMENT_PACKAGES)
      .doc(packageId)
      .update({
        data: {
          ...updateData,
          updatedAt: new Date()
        }
      })
  }
}

/**
 * 档次配置集合操作
 */
const pricingTierCollection = {
  // 获取启用的档次配置列表
  async getActiveList() {
    const result = await getCollection(COLLECTIONS.PRICING_TIERS)
      .where({ isActive: true })
      .orderBy('sortOrder', 'asc')
      .get()
    return result.data
  },

  // 获取所有档次配置列表
  async getList() {
    const result = await getCollection(COLLECTIONS.PRICING_TIERS)
      .orderBy('sortOrder', 'asc')
      .get()
    return result.data
  },

  // 根据ID查找档次配置
  async findById(tierId) {
    const result = await getCollection(COLLECTIONS.PRICING_TIERS)
      .doc(tierId)
      .get()
    return result.data || null
  },

  // 创建档次配置
  async create(tierData) {
    const result = await getCollection(COLLECTIONS.PRICING_TIERS)
      .add({
        data: {
          ...tierData,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      })
    return result._id
  },

  // 更新档次配置
  async update(tierId, updateData) {
    return await getCollection(COLLECTIONS.PRICING_TIERS)
      .doc(tierId)
      .update({
        data: {
          ...updateData,
          updatedAt: new Date()
        }
      })
  },

  // 删除档次配置
  async delete(tierId) {
    return await getCollection(COLLECTIONS.PRICING_TIERS)
      .doc(tierId)
      .remove()
  }
}

/**
 * 额度消耗历史集合操作
 */
const usageHistoryCollection = {
  // 创建消耗记录
  async create(historyData) {
    return await getCollection(COLLECTIONS.USAGE_HISTORY).add({
      data: {
        ...historyData,
        createdAt: new Date()
      }
    })
  },

  // 获取用户消耗历史（分页）
  async getUserHistory(userId, page = 1, limit = 20) {
    const offset = (page - 1) * limit

    // 获取总数
    const countResult = await getCollection(COLLECTIONS.USAGE_HISTORY)
      .where({ userId })
      .count()
    const total = countResult.total

    // 分页查询
    const result = await getCollection(COLLECTIONS.USAGE_HISTORY)
      .where({ userId })
      .orderBy('consumeTime', 'desc')
      .skip(offset)
      .limit(limit)
      .get()

    return {
      data: result.data,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    }
  },

  // 根据用户ID和时间范围查询消耗历史
  async getUserHistoryByDateRange(userId, startDate, endDate, page = 1, limit = 20) {
    const offset = (page - 1) * limit

    const query = getCollection(COLLECTIONS.USAGE_HISTORY)
      .where({
        userId,
        consumeTime: _.gte(startDate).and(_.lte(endDate))
      })

    // 获取总数
    const countResult = await query.count()
    const total = countResult.total

    // 分页查询
    const result = await query
      .orderBy('consumeTime', 'desc')
      .skip(offset)
      .limit(limit)
      .get()

    return {
      data: result.data,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    }
  }
}

/**
 * 激活码核销记录集合操作
 */
const activationCodeCollection = {
  // 检查激活码是否已被使用
  async isCodeUsed(activationCode) {
    const result = await getCollection(COLLECTIONS.ACTIVATION_CODES)
      .where({ activationCode })
      .get()
    return result.data.length > 0
  },

  // 记录激活码核销
  async recordUsage(activationCode, quotaAmount, createdBy, usedBy) {
    return await getCollection(COLLECTIONS.ACTIVATION_CODES)
      .add({
        data: {
          activationCode,
          quotaAmount,
          createdBy,
          usedBy,
          usedAt: new Date(),
          status: 'used',
          createdAt: new Date()
        }
      })
  }
}

/**
 * 系统配置集合操作
 */
const systemConfigCollection = {
  // 获取系统配置
  async getConfig() {
    const result = await getCollection(COLLECTIONS.SYSTEM_CONFIG)
      .limit(1)
      .get()
    return result.data[0] || null
  }
}

module.exports = {
  db,
  _,
  encryptAES,
  decryptAES,
  adminCollection,
  userCollection,
  agentCollection,
  modelCollection,
  pageCollection,
  purchaseOrderCollection,
  paymentPackageCollection,
  pricingTierCollection,
  usageHistoryCollection,
  activationCodeCollection,
  systemConfigCollection,
  COLLECTIONS
}