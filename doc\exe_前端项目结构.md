# 前端项目结构

## 一、概述

本文件描述 **numerology_ai_chat_new**（重构版）前端项目的整体目录结构、主要模块功能及其与后端、数据库的对应关系，旨在帮助开发者快速了解并参与项目。

- 开发框架：Flutter 3.x（Windows 平台）
- 状态管理：Riverpod
- 路由：go_router
- 网络通信：dio
- 本地安全存储：flutter_secure_storage + Hive
- Markdown 渲染：flutter_markdown

> 与旧版 **numerology_ai_chat** 相比，重构版将所有 AI 逻辑、提示词及 API 密钥移至云端/Go 中转服务，前端仅负责 UI 与业务流程，保证安全性与易维护性。

---

## 二、顶层目录结构

```text
numerology_ai_chat_new/
├─ lib/
│  ├─ main.dart
│  ├─ src/
│  │  ├─ core/              # 核心基础设施（配置、常量、路由、存储）
│  │  │  ├─ config/
│  │  │  │  ├─ app_config.dart
│  │  │  │  └─ env.dart
│  │  │  ├─ constants/
│  │  │  │  ├─ api_endpoints.dart
│  │  │  │  └─ keys.dart
│  │  │  ├─ errors/
│  │  │  │  └─ exceptions.dart
│  │  │  ├─ responsive/
│  │  │  │  └─ responsive_layout.dart
│  │  │  ├─ router/
│  │  │  │  ├─ app_router.dart
│  │  │  │  └─ routes.dart
│  │  │  └─ storage/
│  │  │      ├─ secure_storage.dart
│  │  │      └─ local_db.dart
│  │  ├─ models/            # 数据模型（与数据库集合一一对应）
│  │  │  ├─ user.dart
│  │  │  ├─ agent.dart
│  │  │  ├─ model.dart
│  │  │  └─ page_config.dart
│  │  ├─ providers/         # Riverpod 状态提供者
│  │  │  ├─ auth_provider.dart
│  │  │  ├─ agent_provider.dart
│  │  │  ├─ model_provider.dart
│  │  │  └─ settings_provider.dart
│  │  ├─ services/          # 与后端/云函数交互的服务层
│  │  │  ├─ api_service.dart
│  │  │  ├─ auth_service.dart
│  │  │  ├─ agent_service.dart
│  │  │  ├─ model_service.dart
│  │  │  └─ page_service.dart
│  │  ├─ screens/           # UI 页面
│  │  │  ├─ auth/
│  │  │  │  ├─ login_screen.dart
│  │  │  │  └─ register_screen.dart
│  │  │  ├─ home/
│  │  │  │  └─ home_screen.dart
│  │  │  ├─ chat/
│  │  │  │  └─ chat_screen.dart
│  │  │  ├─ settings/
│  │  │  │  └─ settings_screen.dart
│  │  │  └─ markdown_page/
│  │  │      └─ markdown_page_screen.dart
│  │  ├─ widgets/           # 可复用组件
│  │  │  ├─ common/
│  │  │  │  ├─ primary_button.dart
│  │  │  │  └─ ...
│  │  │  └─ chat/
│  │  │      ├─ chat_bubble.dart
│  │  │      └─ ...
│  │  ├─ theme/             # 主题与配色
│  │  │  ├─ app_theme.dart
│  │  │  └─ color_schemes.dart
│  │  └─ utils/             # 工具方法
│  │      ├─ validators.dart
│  │      ├─ date_utils.dart
│  │      └─ logger.dart
│  └─ generated/            # g.dart/intl 等生成文件
├─ assets/
│  ├─ fonts/
│  ├─ images/
│  └─ markdown/
└─ test/                    # 单元与集成测试
```

---

## 三、各目录详细说明

### 1. lib/main.dart
应用入口：初始化依赖、读取配置、启动 Router。

### 2. src/core
- **config/**：全局配置与环境变量加载；`app_config.dart` 按运行环境自动切换 API 根路径。
- **constants/**：应用级静态常量，例如 API 端点、SharedPreferences Key 等。
- **errors/**：统一异常封装与错误码解析。
- **responsive/**：响应式布局适配。
- **router/**：路由表与导航守卫，未登录时自动跳转登录页。
- **storage/**：本地安全存储封装，包含用户 Token、聊天记录加密保存。

### 3. models
与数据库集合保持字段对齐，方便 JSON 序列化：
- `user.dart` ↔ `exe_users`
- `agent.dart` ↔ `exe_agents`（包含pricingTierId字段）
- `model.dart` ↔ `exe_models`（包含modelLevel字段）
- `page_config.dart` ↔ `exe_pages`
- `pricing_tier.dart` ↔ `exe_pricing_tiers`（算力档次配置）

### 4. providers
使用 Riverpod 管理状态，依赖服务层发起异步请求并缓存结果。

### 5. services
封装所有网络请求，统一通过 `api_service.dart` 调用 Go 中转服务或云函数：
- `auth_service.dart`：登录/注册，鉴权与 Token 刷新
- `agent_service.dart`：获取智能体列表（不包含 Prompt）
- `model_service.dart`：获取可用模型列表（不包含 API Key）
- `page_service.dart`：获取 Markdown 页面内容

> 所有服务均不存储敏感信息，仅携带用户 Token。

### 6. screens
按页面/功能拆分子目录，每个目录下包含 `*_screen.dart`、`*_view_model.dart`（可选）等文件。

### 7. widgets
通用 UI 组件与业务组件分层，确保 UI 复用。

### 8. theme
统一的颜色、文字样式与暗黑模式适配。

### 9. utils
工具函数库，如日期转换、表单校验、日志封装等。

---

## 四、数据流 & 本地存储

1. 用户登录后，`auth_service` 获取 Token → `secure_storage` 持久化。
2. 业务请求自动在 Header 带上 Token，经 Go 中转服务鉴权 → 云函数 → 数据库。
3. 聊天记录仅保存至本地 `Hive`，遵循"云端不存储聊天内容"原则。

---

## 五、重构关键差异

| 模块 | 旧版实现 | 重构版实现 |
|------|----------|-----------|
| AI 调用 | Flutter 本地直接调用 OpenAI Key | Go 中转 + 云函数，前端无 Key |
| 数据库存储 | 无远端数据库 | 腾讯云开发文档型 DB，结构详见 `doc/数据库结构.md` |
| 登录鉴权 | 本地假 token | 后端统一 JWT，存于 `secure_storage` |
| Chat 记录 | 全部保存在本地文件 | 加密后写入 Hive，支持搜索与导出 |
| 远程配置 | 无 | 管理员可在后台修改智能体/页面配置，前端动态拉取 |
| 扣费系统 | 无 | 基于智能体档次和模型等级的差异化算力扣费 |

---

## 六、与数据库集合对应关系

| 服务层 | 云函数/集合 | 说明 |
|--------|-------------|------|
| auth_service | `exe_users` | 注册、登录、算力查询 |
| agent_service | `exe_agents` | 智能体列表（包含档次关联） |
| model_service | `exe_models` | 模型列表（包含等级信息） |
| page_service | `exe_pages` | Markdown 页面内容 |
| pricing_tier_service | `exe_pricing_tiers` | 算力档次配置（管理端） |

> 管理侧功能由独立的 **numerology_ai_chat_new_admin** 提供，前端用户侧仅读权限。

---

## 七、环境与构建

```bash
# 安装依赖
flutter pub get

# 运行调试（Windows）
flutter run -d windows

# 构建发布版
flutter build windows --release
```

- 环境变量通过 `--dart-define` 注入，如：
  ```bash
  flutter run -d windows --dart-define=ENV=prod
  ```

---

## 八、后续扩展

1. 多语言支持：整合 `flutter_localizations` 与 `intl`。
2. 插件化智能体：支持热更新智能体提示词。
3. 日志上报：可选 Sentry/Firebase Crashlytics。

> 至此，重构版前端项目结构介绍完毕。如有疑问或建议，请在文档评论区或工单系统提出。 