# 管理员激活码系统测试指南

## 快速开始

### 1. 启动Web管理界面
```bash
cd weak-admin-web
npm install  # 首次运行
npm run dev   # 启动开发服务器
```
访问：http://localhost:3000

### 2. 登录测试
- **用户名**: `weakadmin001`
- **密码**: `12345678`

## 详细测试账号信息

### 管理员账号
| 用户名 | 密码 | 权限 | 说明 |
|--------|------|------|------|
| weakadmin001 | 12345678 | 管理员 | 可生成激活码、管理用户算力 |

### 普通用户账号
| 用户名 | 密码 | 当前算力 | 说明 |
|--------|------|----------|------|
| testuser002 | 12345678 | 700 | 已注册用户，可用于测试算力修改 |
| testuser003 | 12345678 | 2500 | 通过激活码注册的用户 |

### 可用激活码（未使用）
以下激活码可用于测试新用户注册：

**激活码1** (2000算力):
```
U2FsdGVkX18kU3XEGplFPVGkfU2MmL8u4u9x7U073fT33wSJ7gEHpm6Q9FTVGX6+qzV5RUfBcKcTObR0n9poDOe7mw0e4+Cg3M+yaaobx+6JQ18yxPfyHdN3g9K/fBci6geYrjUeGw/IVVKEWrBA7NUEKxuWcqW4WCaNpITOBv4=
```

**激活码2** (2000算力):
```
U2FsdGVkX19w+X7UmymUEVn7QeS63pzZwqbzVI5nLbqdMfaMeFuHc3bg2nNstBMwOTk3zEqrLldw1I2Y7kvBJQTRC2VbU8U/yvXPwN3JGvwdDz0iBBgDwZqtaGq8rdrNQvIVp5q0vBtzzUm3DYWmbSklYgpRpGC8D9hZNvganfs=
```

## 功能测试流程

### 1. 管理员登录测试
1. 访问 http://localhost:3000
2. 输入用户名：`weakadmin001`
3. 输入密码：`12345678`
4. 点击登录
5. 验证是否成功进入仪表板

### 2. 激活码管理测试
1. 点击左侧菜单"激活码管理"
2. 在生成表单中设置：
   - 数量：2
   - 算力数量：1000
3. 点击"生成激活码"
4. 验证是否成功生成激活码
5. 测试复制功能和导出功能
6. 查看核销历史记录

### 3. 用户算力管理测试
1. 点击左侧菜单"用户算力管理"
2. 在用户查询中输入：`testuser002`
3. 点击"查询用户"
4. 验证用户信息显示
5. 在算力操作中：
   - 选择"增加算力"
   - 输入算力数量：200
   - 输入操作原因：测试增加算力
6. 点击"确认操作"
7. 验证算力是否成功修改

### 4. 操作历史测试
1. 点击左侧菜单"操作历史"
2. 查看"算力操作历史"标签页
3. 验证刚才的操作记录是否显示
4. 切换到"激活码核销历史"标签页
5. 查看激活码使用记录

### 5. 新用户注册测试（使用激活码）
1. 使用curl或Postman测试注册接口：
```bash
curl -X POST "https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction" \
-H "Content-Type: application/json" \
-d '{
  "action": "register",
  "username": "testuser004",
  "password": "12345678",
  "activationCode": "刚生成的激活码"
}'
```
2. 验证注册是否成功并获得相应算力

## 云函数端点

### exeWeakAdmin云函数
- **地址**: https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeWeakAdmin
- **功能**: 管理员相关操作

### exeFunction云函数  
- **地址**: https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction
- **功能**: 用户注册、激活码验证等

## 常见问题

### Q: 登录失败怎么办？
A: 确认用户名密码正确，检查网络连接和云函数状态

### Q: 激活码生成失败？
A: 检查token是否有效，确认管理员权限

### Q: 用户查询不到？
A: 确认用户名拼写正确，该用户确实存在

### Q: 算力修改失败？
A: 检查操作权限，确认目标用户存在

## 技术支持

如遇到问题，可以：
1. 检查浏览器控制台错误信息
2. 查看云函数日志
3. 验证网络连接状态
4. 确认测试数据的准确性
