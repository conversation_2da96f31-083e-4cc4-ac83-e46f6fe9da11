
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:numerology_ai_chat_admin/src/models/user_model.dart';
import 'package:numerology_ai_chat_admin/src/providers/auth_provider.dart';
import 'package:numerology_ai_chat_admin/src/services/admin_api_service.dart';

// 用户列表状态
class UserListState {
  final List<User>? users;
  final bool isLoading;
  final String? error;

  UserListState({this.users, this.isLoading = false, this.error});
}

// UserProvider
class UserNotifier extends StateNotifier<UserListState> {
  final AdminApiService _apiService;
  final String? _token;

  UserNotifier(this._apiService, this._token) : super(UserListState()) {
    if (_token != null) {
      fetchUsers();
    }
  }

  Future<void> fetchUsers() async {
    state = UserListState(isLoading: true);
    try {
      final response = await _apiService.getUserList(_token!);
      if (response['code'] == 0 && response['data'] != null) {
        final data = response['data']['data'];
        if (data != null && data['users'] != null) {
          final userList = (data['users'] as List)
              .map((userJson) => User.fromJson(userJson))
              .toList();
          state = UserListState(users: userList);
        } else {
          state = UserListState(users: []);
        }
      } else {
        state = UserListState(error: response['message'] ?? '获取用户列表失败');
      }
    } catch (e) {
      state = UserListState(error: e.toString());
    }
  }
Future<void> updateUserQuota(String userId, int quota) async {
    try {
      final response = await _apiService.updateUserQuota(_token!, userId, quota);
      if (response['code'] == 0) {
        fetchUsers(); // Refresh the list
      } else {
        // Handle error
      }
    } catch (e) {
      // Handle error
    }
  }

  Future<void> updateUserStatus(String userId, String status) async {
    try {
      final response = await _apiService.updateUserStatus(_token!, userId, status);
      if (response['code'] == 0) {
        fetchUsers(); // Refresh the list
      } else {
        // Handle error
      }
    } catch (e) {
      // Handle error
    }
  }
}

// User状态提供者
final userProvider = StateNotifierProvider<UserNotifier, UserListState>((ref) {
  final apiService = ref.watch(adminApiServiceProvider);
  final token = ref.watch(authProvider).token;
  return UserNotifier(apiService, token);
});
