import 'package:json_annotation/json_annotation.dart';

part 'config_model.g.dart';

@JsonSerializable()
class SystemConfig {
  @JsonKey(name: '_id')
  final String id;
  final String configKey;
  final String configValue;
  final String configType;
  final String description;
  final bool isActive;
  final String category;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? createdBy;
  final String? updatedBy;

  SystemConfig({
    required this.id,
    required this.configKey,
    required this.configValue,
    required this.configType,
    required this.description,
    required this.isActive,
    required this.category,
    required this.createdAt,
    required this.updatedAt,
    this.createdBy,
    this.updatedBy,
  });

  factory SystemConfig.fromJson(Map<String, dynamic> json) => _$SystemConfigFromJson(json);
  Map<String, dynamic> toJson() => _$SystemConfigToJson(this);
}

@JsonSerializable()
class CreateConfigRequest {
  final String configKey;
  final String configValue;
  final String configType;
  final String description;
  final bool isActive;
  final String category;

  CreateConfigRequest({
    required this.configKey,
    required this.configValue,
    required this.configType,
    required this.description,
    this.isActive = true,
    required this.category,
  });

  factory CreateConfigRequest.fromJson(Map<String, dynamic> json) => _$CreateConfigRequestFromJson(json);
  Map<String, dynamic> toJson() => _$CreateConfigRequestToJson(this);
}

@JsonSerializable()
class UpdateConfigRequest {
  final String? configValue;
  final String? configType;
  final String? description;
  final bool? isActive;
  final String? category;

  UpdateConfigRequest({
    this.configValue,
    this.configType,
    this.description,
    this.isActive,
    this.category,
  });

  factory UpdateConfigRequest.fromJson(Map<String, dynamic> json) => _$UpdateConfigRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdateConfigRequestToJson(this);
}
