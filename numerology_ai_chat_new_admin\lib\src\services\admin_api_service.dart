
import 'dart:convert';
import 'package:http/http.dart' as http;

class AdminApiService {
  final String _baseUrl = 'https://cloud1-8g3yh69faf07fd82-**********.ap-shanghai.app.tcloudbase.com/exeAdmin';

  Future<Map<String, dynamic>> adminLogin(String adminAccount, String adminPassword) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'action': 'adminLogin',
        'adminAccount': adminAccount,
        'adminPassword': adminPassword,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to login');
    }
  }

  Future<Map<String, dynamic>> getUserList(String token) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'listUsers',
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to get user list');
    }
  }

  
  Future<Map<String, dynamic>> updateUserQuota(String token, String userId, int quota) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'updateUserQuota',
        'userId': userId,
        'quota': quota,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to update user quota');
    }
  }

  Future<Map<String, dynamic>> updateUserStatus(String token, String userId, String status) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'updateUserStatus',
        'data': {
          'userId': userId,
          'status': status,
        }
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to update user status');
    }
  }

  
  Future<Map<String, dynamic>> getAgents(String token) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'listAgents',
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to get agent list');
    }
  }

  Future<Map<String, dynamic>> createAgent(String token, Map<String, dynamic> agentData) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'createAgent',
        'data': agentData,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to create agent');
    }
  }

  Future<Map<String, dynamic>> updateAgent(String token, String agentId, Map<String, dynamic> agentData) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'updateAgent',
        'agentId': agentId,
        'data': agentData,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to update agent');
    }
  }

  Future<Map<String, dynamic>> deleteAgent(String token, String agentId) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'deleteAgent',
        'data': {
          'agentId': agentId,
        }
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to delete agent');
    }
  }

  
  Future<Map<String, dynamic>> getModels(String token) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'listModels',
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to get model list');
    }
  }

  Future<Map<String, dynamic>> createModel(String token, Map<String, dynamic> modelData) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'createModel',
        'data': modelData,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to create model');
    }
  }

  Future<Map<String, dynamic>> updateModel(String token, String modelId, Map<String, dynamic> modelData) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'updateModel',
        'modelId': modelId,
        'data': modelData,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to update model');
    }
  }

  Future<Map<String, dynamic>> deleteModel(String token, String modelId) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'deleteModel',
        'data': {
          'modelId': modelId,
        }
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to delete model');
    }
  }

  // 页面管理相关API
  Future<Map<String, dynamic>> getPageList(String token, {
    String? pageType,
    bool? isActive,
    int page = 1,
    int pageSize = 20,
  }) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'listPages',
        'pageType': pageType,
        'isActive': isActive,
        'page': page,
        'pageSize': pageSize,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to get page list');
    }
  }

  Future<Map<String, dynamic>> createPage(String token, Map<String, dynamic> pageData) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'createPage',
        ...pageData,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to create page');
    }
  }

  Future<Map<String, dynamic>> updatePage(String token, String pageId, Map<String, dynamic> pageData) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'updatePage',
        'pageId': pageId,
        ...pageData,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to update page');
    }
  }

  Future<Map<String, dynamic>> deletePage(String token, String pageId) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'deletePage',
        'pageId': pageId,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to delete page');
    }
  }

  // 统计相关API
  Future<Map<String, dynamic>> getSystemStats(String token) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'stats',
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to get system stats');
    }
  }

  // 系统配置管理相关API
  Future<Map<String, dynamic>> getSystemConfigs(String token, {
    String? category,
    bool? isActive,
    int page = 1,
    int pageSize = 20,
  }) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'listSystemConfigs',
        'category': category,
        'isActive': isActive,
        'page': page,
        'pageSize': pageSize,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to get system configs');
    }
  }

  Future<Map<String, dynamic>> createSystemConfig(String token, Map<String, dynamic> configData) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'createSystemConfig',
        ...configData,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to create system config');
    }
  }

  Future<Map<String, dynamic>> updateSystemConfig(String token, String configId, Map<String, dynamic> configData) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'updateSystemConfig',
        'configId': configId,
        ...configData,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to update system config');
    }
  }

  Future<Map<String, dynamic>> deleteSystemConfig(String token, String configId) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'deleteSystemConfig',
        'configId': configId,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to delete system config');
    }
  }

  // 套餐管理相关API
  Future<Map<String, dynamic>> getPackageList(String token, {
    bool? isActive,
    int page = 1,
    int pageSize = 20,
  }) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'listPackages',
        'isActive': isActive,
        'page': page,
        'pageSize': pageSize,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to get package list');
    }
  }

  Future<Map<String, dynamic>> createPackage(String token, Map<String, dynamic> packageData) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'createPackage',
        ...packageData,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to create package');
    }
  }

  Future<Map<String, dynamic>> updatePackage(String token, String packageId, Map<String, dynamic> packageData) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'updatePackage',
        'packageId': packageId,
        ...packageData,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to update package');
    }
  }

  Future<Map<String, dynamic>> deletePackage(String token, String packageId) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'deletePackage',
        'packageId': packageId,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to delete package');
    }
  }

  // 订单管理相关API
  Future<Map<String, dynamic>> getOrderList(String token, {
    int page = 1,
    int limit = 20,
    String? search,
    String? status,
    String? paymentMethod,
  }) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'listOrders',
        'page': page,
        'limit': limit,
        if (search != null) 'search': search,
        if (status != null) 'status': status,
        if (paymentMethod != null) 'paymentMethod': paymentMethod,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to get order list');
    }
  }

  Future<Map<String, dynamic>> getOrderDetail(String token, String orderId) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'getOrderDetail',
        'orderId': orderId,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to get order detail');
    }
  }

  Future<Map<String, dynamic>> updateOrderStatus(String token, String orderId, String status) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'updateOrderStatus',
        'orderId': orderId,
        'status': status,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to update order status');
    }
  }

  // 支付日志管理相关API
  Future<Map<String, dynamic>> getPaymentLogList(String token, {
    int page = 1,
    int limit = 20,
    String? search,
    String? operationType,
    String? status,
  }) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'listPaymentLogs',
        'page': page,
        'limit': limit,
        if (search != null) 'search': search,
        if (operationType != null) 'operationType': operationType,
        if (status != null) 'status': status,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to get payment log list');
    }
  }

  Future<Map<String, dynamic>> getPaymentLogDetail(String token, String logId) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'getPaymentLogDetail',
        'logId': logId,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to get payment log detail');
    }
  }

  // 使用历史管理相关API
  Future<Map<String, dynamic>> getUsageHistoryList(String token, {
    int page = 1,
    int limit = 20,
    String? search,
    String? agentId,
    String? modelLevel,
    String? tierId,
  }) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'listUsageHistories',
        'page': page,
        'limit': limit,
        if (search != null) 'search': search,
        if (agentId != null) 'agentId': agentId,
        if (modelLevel != null) 'modelLevel': modelLevel,
        if (tierId != null) 'tierId': tierId,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to get usage history list');
    }
  }

  Future<Map<String, dynamic>> getUsageHistoryDetail(String token, String historyId) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'getUsageHistoryDetail',
        'historyId': historyId,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to get usage history detail');
    }
  }

  Future<Map<String, dynamic>> getUsageHistoryStats(String token) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'getUsageHistoryStats',
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to get usage history stats');
    }
  }

  // 分析统计相关API
  Future<Map<String, dynamic>> getOverallStats(String token) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'getOverallStats',
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to get overall stats');
    }
  }

  Future<Map<String, dynamic>> getTrendData(String token, {int days = 7}) async {
    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'action': 'getTrendData',
        'days': days,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to get trend data');
    }
  }

}
