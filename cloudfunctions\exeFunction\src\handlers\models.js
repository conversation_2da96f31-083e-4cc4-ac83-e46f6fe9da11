const { modelCollection } = require('../utils/db')
const { successResponse } = require('../middleware/error_handler')
const logger = require('../utils/logger')

/**
 * 获取模型列表
 * @param {object} params 请求参数
 * @returns {object} 模型列表
 */
async function getList(params) {
  try {
    logger.info('Getting models list')

    // 获取启用的模型列表（不包含API密钥和URL）
    const models = await modelCollection.getActiveList()

    logger.info('Models list retrieved successfully', {
      count: models.length
    })

    // 返回模型列表（前端需要基本信息，包含等级信息用于算力计算）
    return successResponse({
      models: models.map(model => ({
        id: model._id,
        modelName: model.modelName,
        modelDisplayName: model.modelDisplayName,
        modelApiUrl: model.modelApiUrl || '', // 添加API URL字段
        description: model.description,
        modelLevel: model.modelLevel || '初级', // 添加模型等级字段
        maxTokens: model.maxTokens,
        temperature: model.temperature,
        isActive: model.isActive, // 添加isActive字段
        sortOrder: model.sortOrder,
        createdAt: model.createdAt || new Date().toISOString(), // 添加创建时间
        updatedAt: model.updatedAt || new Date().toISOString()  // 添加更新时间
      }))
    }, '获取模型列表成功')

  } catch (error) {
    logger.error('Failed to get models list', {
      error: error.message
    })
    throw error
  }
}

/**
 * 获取模型完整信息（包含API密钥，供Go程序使用）
 * @param {object} params 请求参数
 * @returns {object} 模型完整信息
 */
async function getFullList(params) {
  try {
    logger.info('Getting full models list for Go proxy')

    // 获取启用的模型列表（包含API密钥和URL）
    const models = await modelCollection.getActiveFullList()

    logger.info('Full models list retrieved successfully', {
      count: models.length,
      firstModel: models[0] ? {
        id: models[0]._id,
        name: models[0].modelName,
        hasApiKey: !!models[0].modelApiKey,
        hasApiUrl: !!models[0].modelApiUrl
      } : null
    })

    // 返回完整的模型信息（包含API密钥）
    return successResponse({
      models: models.map(model => ({
        id: model._id,  // 使用id字段保持与前端一致
        modelName: model.modelName,
        modelApiKey: model.modelApiKey,
        modelApiUrl: model.modelApiUrl,
        modelDisplayName: model.modelDisplayName,
        maxTokens: model.maxTokens,
        temperature: model.temperature,
        isActive: model.isActive,
        sortOrder: model.sortOrder,
        description: model.description
      }))
    }, '获取模型完整信息成功')

  } catch (error) {
    logger.error('Failed to get full models list', {
      error: error.message
    })
    throw error
  }
}

module.exports = {
  getList,
  getFullList
}