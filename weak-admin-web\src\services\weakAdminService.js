import api from '@/utils/api'

class WeakAdminService {
  // 生成激活码
  async generateActivationCodes(quantity, quotaAmount) {
    const response = await api.post('/exeWeakAdmin', {
      action: 'generateActivationCodes',
      quantity,
      quotaAmount
    })
    return response.data
  }

  // 获取激活码历史
  async getActivationHistory(page = 1, pageSize = 20) {
    const response = await api.post('/exeWeakAdmin', {
      action: 'getActivationHistory',
      page,
      pageSize
    })
    return response.data
  }

  // 查询用户信息
  async getUserInfo(username) {
    const response = await api.post('/exeWeakAdmin', {
      action: 'getUserInfo',
      username
    })
    return response.data
  }

  // 修改用户算力
  async modifyUserQuota(targetUsername, operationType, quotaAmount, reason = '') {
    const response = await api.post('/exeWeakAdmin', {
      action: 'modifyUserQuota',
      targetUsername,
      operationType,
      quotaAmount,
      reason
    })
    return response.data
  }

  // 获取算力操作记录
  async getQuotaOperations(page = 1, pageSize = 20, targetUsername = '') {
    const response = await api.post('/exeWeakAdmin', {
      action: 'getQuotaOperations',
      page,
      pageSize,
      targetUsername
    })
    return response.data
  }
}

export default new WeakAdminService()
