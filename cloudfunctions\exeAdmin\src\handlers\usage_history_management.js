const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 使用历史管理处理器
 */
class UsageHistoryManagement {
  /**
   * 获取使用历史列表
   */
  static async listUsageHistories(event) {
    try {
      const { page = 1, limit = 20, search, agentId, modelLevel, tierId } = event;
      
      // 构建查询条件
      let query = {};
      
      if (search) {
        // 搜索用户ID或智能体名称
        query.$or = [
          { userId: { $regex: search, $options: 'i' } },
          { agentName: { $regex: search, $options: 'i' } }
        ];
      }
      
      if (agentId) {
        query.agentId = agentId;
      }
      
      if (modelLevel) {
        query.modelLevel = modelLevel;
      }
      
      if (tierId) {
        query.pricingTierId = tierId;
      }
      
      // 计算跳过的记录数
      const skip = (page - 1) * limit;
      
      // 获取总数
      const totalResult = await db.collection('exe_usage_history')
        .where(query)
        .count();
      
      const total = totalResult.total;
      
      // 获取使用历史列表
      const historiesResult = await db.collection('exe_usage_history')
        .where(query)
        .orderBy('consumeTime', 'desc')
        .skip(skip)
        .limit(limit)
        .get();
      
      return {
        code: 0,
        message: '获取使用历史列表成功',
        data: {
          histories: historiesResult.data,
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      console.error('获取使用历史列表失败:', error);
      return {
        code: -1,
        message: '获取使用历史列表失败: ' + error.message
      };
    }
  }

  /**
   * 获取使用历史详情
   */
  static async getUsageHistoryDetail(event) {
    try {
      const { historyId } = event;
      
      if (!historyId) {
        return {
          code: -1,
          message: '历史记录ID不能为空'
        };
      }
      
      const historyResult = await db.collection('exe_usage_history')
        .doc(historyId)
        .get();
      
      if (!historyResult.data) {
        return {
          code: -1,
          message: '使用历史记录不存在'
        };
      }
      
      return {
        code: 0,
        message: '获取使用历史详情成功',
        data: historyResult.data
      };
    } catch (error) {
      console.error('获取使用历史详情失败:', error);
      return {
        code: -1,
        message: '获取使用历史详情失败: ' + error.message
      };
    }
  }

  /**
   * 获取使用历史统计信息
   */
  static async getUsageHistoryStats(event) {
    try {
      // 获取总记录数
      const totalHistoriesResult = await db.collection('exe_usage_history').count();
      const totalHistories = totalHistoriesResult.total;
      
      // 获取今日使用记录数
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayHistoriesResult = await db.collection('exe_usage_history')
        .where({
          consumeTime: db.command.gte(today)
        })
        .count();
      
      // 获取各模型等级使用统计
      const basicLevelResult = await db.collection('exe_usage_history')
        .where({ modelLevel: '初级' })
        .count();
      
      const intermediateLevelResult = await db.collection('exe_usage_history')
        .where({ modelLevel: '中级' })
        .count();
      
      const advancedLevelResult = await db.collection('exe_usage_history')
        .where({ modelLevel: '高级' })
        .count();
      
      const topLevelResult = await db.collection('exe_usage_history')
        .where({ modelLevel: '顶级' })
        .count();
      
      // 获取总算力消耗
      const allHistoriesResult = await db.collection('exe_usage_history')
        .get();
      
      const totalPowerConsumed = allHistoriesResult.data.reduce((sum, record) => {
        return sum + (record.powerCost || 0);
      }, 0);
      
      // 获取今日算力消耗
      const todayHistoriesData = await db.collection('exe_usage_history')
        .where({
          consumeTime: db.command.gte(today)
        })
        .get();
      
      const todayPowerConsumed = todayHistoriesData.data.reduce((sum, record) => {
        return sum + (record.powerCost || 0);
      }, 0);
      
      // 获取各档次使用统计
      const basicTierResult = await db.collection('exe_usage_history')
        .where({ tierName: '基础档次' })
        .count();
      
      const standardTierResult = await db.collection('exe_usage_history')
        .where({ tierName: '标准档次' })
        .count();
      
      const advancedTierResult = await db.collection('exe_usage_history')
        .where({ tierName: '高级档次' })
        .count();
      
      const professionalTierResult = await db.collection('exe_usage_history')
        .where({ tierName: '专业档次' })
        .count();
      
      return {
        code: 0,
        message: '获取使用历史统计成功',
        data: {
          totalHistories,
          todayHistories: todayHistoriesResult.total,
          totalPowerConsumed,
          todayPowerConsumed,
          modelLevelStats: {
            basic: basicLevelResult.total,
            intermediate: intermediateLevelResult.total,
            advanced: advancedLevelResult.total,
            top: topLevelResult.total
          },
          tierStats: {
            basic: basicTierResult.total,
            standard: standardTierResult.total,
            advanced: advancedTierResult.total,
            professional: professionalTierResult.total
          }
        }
      };
    } catch (error) {
      console.error('获取使用历史统计失败:', error);
      return {
        code: -1,
        message: '获取使用历史统计失败: ' + error.message
      };
    }
  }

  /**
   * 删除使用历史记录（软删除）
   */
  static async deleteUsageHistory(event) {
    try {
      const { historyId } = event;
      
      if (!historyId) {
        return {
          code: -1,
          message: '历史记录ID不能为空'
        };
      }
      
      // 检查记录是否存在
      const historyResult = await db.collection('exe_usage_history')
        .doc(historyId)
        .get();
      
      if (!historyResult.data) {
        return {
          code: -1,
          message: '使用历史记录不存在'
        };
      }
      
      // 软删除：标记为已删除
      await db.collection('exe_usage_history')
        .doc(historyId)
        .update({
          isDeleted: true,
          deletedAt: new Date()
        });
      
      return {
        code: 0,
        message: '删除使用历史记录成功'
      };
    } catch (error) {
      console.error('删除使用历史记录失败:', error);
      return {
        code: -1,
        message: '删除使用历史记录失败: ' + error.message
      };
    }
  }
}

module.exports = UsageHistoryManagement;
