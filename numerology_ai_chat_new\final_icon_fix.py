#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终图标修复工具 - 将logo.png转换为高质量的Windows应用图标
确保生成的exe文件在文件管理器中显示正确的自定义图标
"""

import os
import sys
import shutil
from PIL import Image, ImageFilter, ImageEnhance
from datetime import datetime

def print_status(message, status="info"):
    """打印带状态的消息"""
    icons = {
        "info": "🔄",
        "success": "✅",
        "warning": "⚠️",
        "error": "❌",
        "check": "🔍"
    }
    print(f"{icons.get(status, '📝')} {message}")

def backup_existing_icon():
    """备份现有图标文件"""
    ico_path = "windows/runner/resources/app_icon.ico"
    if os.path.exists(ico_path):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"{ico_path}.backup_{timestamp}"
        shutil.copy2(ico_path, backup_path)
        print_status(f"已备份现有图标到: {backup_path}", "success")
        return backup_path
    return None

def enhance_small_icon(img, size):
    """为小尺寸图标进行特殊优化"""
    if size <= 32:
        # 增强锐度
        enhancer = ImageEnhance.Sharpness(img)
        img = enhancer.enhance(1.3)

        # 增强对比度
        enhancer = ImageEnhance.Contrast(img)
        img = enhancer.enhance(1.1)

        # 轻微锐化
        img = img.filter(ImageFilter.UnsharpMask(radius=0.5, percent=120, threshold=2))

    return img

def create_high_quality_icon():
    """创建高质量的Windows图标文件"""
    print_status("🎯 开始创建高质量Windows应用图标", "info")
    print("=" * 60)

    # 源文件和目标文件
    source_path = "assets/images/logo.png"
    ico_path = "windows/runner/resources/app_icon.ico"

    # 检查源文件
    if not os.path.exists(source_path):
        print_status(f"源图片文件不存在: {source_path}", "error")
        return False

    print_status(f"找到源图片: {source_path}", "success")

    # 备份现有图标
    backup_existing_icon()

    try:
        # 打开源图片
        with Image.open(source_path) as source_img:
            print_status(f"源图片尺寸: {source_img.size}", "check")
            print_status(f"源图片模式: {source_img.mode}", "check")

            # 转换为RGBA模式以支持透明度
            if source_img.mode != 'RGBA':
                source_img = source_img.convert('RGBA')
                print_status("已转换为RGBA模式", "info")

            # 确保图片是正方形（如果不是，则居中裁剪）
            width, height = source_img.size
            if width != height:
                min_size = min(width, height)
                left = (width - min_size) // 2
                top = (height - min_size) // 2
                right = left + min_size
                bottom = top + min_size
                source_img = source_img.crop((left, top, right, bottom))
                print_status(f"已裁剪为正方形: {min_size}x{min_size}", "info")

            # 定义Windows图标的完整尺寸集合（包含高清尺寸）
            icon_sizes = [16, 20, 24, 32, 40, 48, 64, 72, 96, 128, 256, 512]

            # 生成不同尺寸的图标
            icon_images = []

            for size in icon_sizes:
                print_status(f"生成 {size}x{size} 图标", "info")

                # 高质量缩放
                resized = source_img.resize((size, size), Image.Resampling.LANCZOS)

                # 为小尺寸图标进行特殊优化
                resized = enhance_small_icon(resized, size)

                icon_images.append(resized)

            # 确保输出目录存在
            output_dir = os.path.dirname(ico_path)
            os.makedirs(output_dir, exist_ok=True)

            # 保存为高质量ICO文件
            icon_images[0].save(
                ico_path,
                format='ICO',
                sizes=[(img.width, img.height) for img in icon_images],
                append_images=icon_images[1:],
                optimize=False,  # 不压缩以保持高质量
                quality=100      # 最高质量
            )

            # 验证生成的文件
            file_size = os.path.getsize(ico_path)
            print_status(f"图标文件已生成: {ico_path}", "success")
            print_status(f"文件大小: {file_size:,} 字节", "success")
            print_status(f"包含尺寸: {len(icon_sizes)} 种", "success")

            # 验证ICO文件
            try:
                with Image.open(ico_path) as ico_img:
                    print_status(f"验证成功 - 主图标尺寸: {ico_img.size}", "success")
            except Exception as e:
                print_status(f"ICO文件验证失败: {e}", "warning")

            return True

    except Exception as e:
        print_status(f"创建图标时发生错误: {e}", "error")
        return False

def main():
    """主函数"""
    print("🎨 最终图标修复工具")
    print("=" * 60)
    print("📝 将 assets/images/logo.png 转换为高质量Windows图标")
    print("🎯 解决exe文件在文件管理器中显示默认图标的问题")
    print("=" * 60)
    print()

    # 检查PIL库
    try:
        from PIL import Image
        print_status("PIL库检查通过", "success")
    except ImportError:
        print_status("PIL库未安装，请运行: pip install Pillow", "error")
        return False

    # 创建图标
    if create_high_quality_icon():
        print()
        print_status("🎉 图标创建成功！", "success")
        print()
        print("📝 接下来的步骤:")
        print("1. 运行: flutter clean")
        print("2. 运行: flutter build windows --release")
        print("3. 检查生成的exe文件图标")
        print()
        print("💡 如果图标仍然显示为默认图标:")
        print("- 重启Windows资源管理器 (Ctrl+Shift+Esc -> 重启Windows资源管理器)")
        print("- 清理Windows图标缓存")
        print("- 确保exe文件是新构建的版本")

        return True
    else:
        print_status("❌ 图标创建失败", "error")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
