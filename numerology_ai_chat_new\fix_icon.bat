@echo off
chcp 65001 >nul
echo ════════════════════════════════════════════
echo    修复应用图标问题
echo ════════════════════════════════════════════
echo.

echo 🔄 步骤1: 重新生成图标文件...
python rebuild_icon.py
if %errorlevel% neq 0 (
    echo ❌ 图标生成失败！
    pause
    exit /b 1
)

echo.
echo 🔄 步骤2: 清理构建缓存...
flutter clean
if %errorlevel% neq 0 (
    echo ⚠️ 清理失败，继续...
)

echo.
echo 🔄 步骤3: 重新构建应用...
flutter build windows --release
if %errorlevel% neq 0 (
    echo ❌ 构建失败！
    pause
    exit /b 1
)

echo.
echo 🎉 修复完成！
echo 📁 检查文件: build\windows\x64\runner\Release\numerology_ai_chat.exe
echo 💡 如果图标仍然不正确，请重启Windows资源管理器
echo.
pause
