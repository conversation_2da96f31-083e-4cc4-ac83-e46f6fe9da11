import 'dart:convert';
import 'package:flutter/services.dart';

/// 地址服务类
/// 直接使用现有的国内地址.txt数据格式，无需复杂转换
class AddressService {
  static List<dynamic>? _addressData;
  
  /// 加载地址数据
  static Future<void> loadAddressData() async {
    if (_addressData != null) return;
    
    try {
      final String jsonString = await rootBundle.loadString('assets/data/china_address.json');
      _addressData = json.decode(jsonString);
    } catch (e) {
      print('加载地址数据失败: $e');
      _addressData = [];
    }
  }
  
  /// 获取所有省份
  static Future<List<Map<String, dynamic>>> getProvinces() async {
    await loadAddressData();
    
    return _addressData?.map<Map<String, dynamic>>((item) => {
      'id': item['id'],
      'text': item['text'],
    }).toList() ?? [];
  }
  
  /// 根据省份ID获取城市列表
  static Future<List<Map<String, dynamic>>> getCitiesByProvinceId(String provinceId) async {
    await loadAddressData();
    
    try {
      final province = _addressData?.firstWhere(
        (item) => item['id'] == provinceId,
        orElse: () => null,
      );
      
      return province?['children']?.map<Map<String, dynamic>>((item) => {
        'id': item['id'],
        'text': item['text'],
      }).toList() ?? [];
    } catch (e) {
      print('获取城市列表失败: $e');
      return [];
    }
  }
  
  /// 根据城市ID获取区县列表
  static Future<List<Map<String, dynamic>>> getDistrictsByCityId(String cityId) async {
    await loadAddressData();
    
    try {
      for (final province in _addressData ?? []) {
        for (final city in province['children'] ?? []) {
          if (city['id'] == cityId) {
            return city['children']?.map<Map<String, dynamic>>((item) => {
              'id': item['id'],
              'text': item['text'],
              'latitude': item['gisGcj02Lat'],
              'longitude': item['gisGcj02Lng'],
            }).toList() ?? [];
          }
        }
      }
    } catch (e) {
      print('获取区县列表失败: $e');
    }
    
    return [];
  }
  
  /// 根据区县ID获取经纬度
  static Future<Map<String, double>?> getCoordinatesByDistrictId(String districtId) async {
    await loadAddressData();
    
    try {
      for (final province in _addressData ?? []) {
        for (final city in province['children'] ?? []) {
          for (final district in city['children'] ?? []) {
            if (district['id'] == districtId) {
              return {
                'latitude': (district['gisGcj02Lat'] ?? 0.0).toDouble(),
                'longitude': (district['gisGcj02Lng'] ?? 0.0).toDouble(),
              };
            }
          }
        }
      }
    } catch (e) {
      print('获取经纬度失败: $e');
    }
    
    return null;
  }
  
  /// 根据区县ID获取完整地址信息
  static Future<Map<String, dynamic>?> getFullAddressByDistrictId(String districtId) async {
    await loadAddressData();
    
    try {
      for (final province in _addressData ?? []) {
        for (final city in province['children'] ?? []) {
          for (final district in city['children'] ?? []) {
            if (district['id'] == districtId) {
              return {
                'province': {
                  'id': province['id'],
                  'text': province['text'],
                },
                'city': {
                  'id': city['id'],
                  'text': city['text'],
                },
                'district': {
                  'id': district['id'],
                  'text': district['text'],
                  'latitude': (district['gisGcj02Lat'] ?? 0.0).toDouble(),
                  'longitude': (district['gisGcj02Lng'] ?? 0.0).toDouble(),
                },
              };
            }
          }
        }
      }
    } catch (e) {
      print('获取完整地址信息失败: $e');
    }
    
    return null;
  }
  
  /// 搜索地址（支持模糊搜索）
  static Future<List<Map<String, dynamic>>> searchAddress(String keyword) async {
    await loadAddressData();
    
    if (keyword.isEmpty) return [];
    
    final List<Map<String, dynamic>> results = [];
    
    try {
      for (final province in _addressData ?? []) {
        for (final city in province['children'] ?? []) {
          for (final district in city['children'] ?? []) {
            final fullAddress = '${province['text']}${city['text']}${district['text']}';
            if (fullAddress.contains(keyword)) {
              results.add({
                'province': province['text'],
                'city': city['text'],
                'district': district['text'],
                'districtId': district['id'],
                'fullAddress': fullAddress,
                'latitude': (district['gisGcj02Lat'] ?? 0.0).toDouble(),
                'longitude': (district['gisGcj02Lng'] ?? 0.0).toDouble(),
              });
            }
          }
        }
      }
    } catch (e) {
      print('搜索地址失败: $e');
    }
    
    return results;
  }
  
  /// 验证区县ID是否有效
  static Future<bool> isValidDistrictId(String districtId) async {
    final coordinates = await getCoordinatesByDistrictId(districtId);
    return coordinates != null;
  }
  
  /// 获取数据统计信息
  static Future<Map<String, int>> getDataStatistics() async {
    await loadAddressData();
    
    int provinceCount = 0;
    int cityCount = 0;
    int districtCount = 0;
    
    try {
      for (final province in _addressData ?? []) {
        provinceCount++;
        for (final city in province['children'] ?? []) {
          cityCount++;
          districtCount += (city['children'] as List? ?? []).length;
        }
      }
    } catch (e) {
      print('获取统计信息失败: $e');
    }
    
    return {
      'provinces': provinceCount,
      'cities': cityCount,
      'districts': districtCount,
    };
  }
}

/// 地址选择结果类
class AddressSelectionResult {
  final String provinceId;
  final String provinceName;
  final String cityId;
  final String cityName;
  final String districtId;
  final String districtName;
  final double latitude;
  final double longitude;
  
  const AddressSelectionResult({
    required this.provinceId,
    required this.provinceName,
    required this.cityId,
    required this.cityName,
    required this.districtId,
    required this.districtName,
    required this.latitude,
    required this.longitude,
  });
  
  /// 获取完整地址字符串
  String get fullAddress => '$provinceName$cityName$districtName';
  
  /// 获取简化地址字符串（处理直辖市等特殊情况）
  String get shortAddress {
    if (provinceName == cityName) {
      return '$provinceName$districtName';
    }
    return fullAddress;
  }
  
  /// 转换为JSON格式
  Map<String, dynamic> toJson() {
    return {
      'provinceId': provinceId,
      'provinceName': provinceName,
      'cityId': cityId,
      'cityName': cityName,
      'districtId': districtId,
      'districtName': districtName,
      'latitude': latitude,
      'longitude': longitude,
    };
  }
  
  /// 从JSON格式创建对象
  factory AddressSelectionResult.fromJson(Map<String, dynamic> json) {
    return AddressSelectionResult(
      provinceId: json['provinceId'],
      provinceName: json['provinceName'],
      cityId: json['cityId'],
      cityName: json['cityName'],
      districtId: json['districtId'],
      districtName: json['districtName'],
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
    );
  }
  
  @override
  String toString() => 'AddressSelectionResult($shortAddress, lat: $latitude, lng: $longitude)';
}
