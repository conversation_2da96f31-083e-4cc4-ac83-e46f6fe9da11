const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 获取数据库引用
const db = cloud.database()
const _ = db.command

// 数据库集合名称常量
const COLLECTIONS = {
  USERS: 'exe_users',
  ACTIVATION_CODES: 'exe_activation_codes',
  QUOTA_OPERATIONS: 'exe_quota_operations',
  SYSTEM_CONFIG: 'exe_system_config'
}

/**
 * 获取集合引用
 * @param {string} collectionName 集合名称
 * @returns {object} 集合引用
 */
function getCollection(collectionName) {
  return db.collection(collectionName)
}

/**
 * 用户集合操作
 */
const userCollection = {
  // 根据用户名查找用户（包含密码，用于登录验证）
  async findByUsername(username) {
    const result = await getCollection(COLLECTIONS.USERS)
      .where({ username })
      .get()
    return result.data[0] || null
  },

  // 根据ID查找用户
  async findById(userId) {
    const result = await getCollection(COLLECTIONS.USERS)
      .doc(userId)
      .field({
        password: false,
        refreshToken: false
      })
      .get()
    return result.data || null
  },

  // 更新用户算力
  async updateQuota(userId, newQuota) {
    return await getCollection(COLLECTIONS.USERS)
      .doc(userId)
      .update({
        data: {
          availableCount: newQuota,
          updatedAt: new Date()
        }
      })
  },

  // 原子操作：增加用户算力
  async addQuota(userId, addedCount) {
    return await getCollection(COLLECTIONS.USERS)
      .doc(userId)
      .update({
        data: {
          availableCount: _.inc(addedCount),
          updatedAt: new Date()
        }
      })
  }
}

/**
 * 激活码核销记录集合操作
 */
const activationCodeCollection = {
  // 检查激活码是否已被使用
  async isCodeUsed(activationCode) {
    const result = await getCollection(COLLECTIONS.ACTIVATION_CODES)
      .where({ activationCode })
      .get()
    return result.data.length > 0
  },

  // 记录激活码核销
  async recordUsage(activationCode, quotaAmount, createdBy, usedBy) {
    return await getCollection(COLLECTIONS.ACTIVATION_CODES)
      .add({
        data: {
          activationCode,
          quotaAmount,
          createdBy,
          usedBy,
          usedAt: new Date(),
          status: 'used',
          createdAt: new Date()
        }
      })
  },

  // 获取激活码核销历史（分页）
  async getHistory(page = 1, pageSize = 20, createdBy = null) {
    let query = getCollection(COLLECTIONS.ACTIVATION_CODES)
    
    if (createdBy) {
      query = query.where({ createdBy })
    }

    // 获取总数
    const countResult = await query.count()
    const total = countResult.total

    // 分页查询
    const result = await query
      .orderBy('usedAt', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get()

    return {
      data: result.data,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }
  }
}

/**
 * 算力操作记录集合操作
 */
const quotaOperationCollection = {
  // 记录算力操作
  async recordOperation(operatorId, operatorUsername, targetUserId, targetUsername, operationType, quotaAmount, quotaBefore, quotaAfter, reason = '') {
    return await getCollection(COLLECTIONS.QUOTA_OPERATIONS)
      .add({
        data: {
          operatorId,
          operatorUsername,
          targetUserId,
          targetUsername,
          operationType,
          quotaAmount,
          quotaBefore,
          quotaAfter,
          reason,
          createdAt: new Date()
        }
      })
  },

  // 获取算力操作记录（分页）
  async getOperations(page = 1, pageSize = 20, filters = {}) {
    let query = getCollection(COLLECTIONS.QUOTA_OPERATIONS)
    
    // 应用过滤条件
    if (filters.operatorId) {
      query = query.where({ operatorId: filters.operatorId })
    }
    if (filters.targetUsername) {
      query = query.where({ targetUsername: new RegExp(filters.targetUsername, 'i') })
    }

    // 获取总数
    const countResult = await query.count()
    const total = countResult.total

    // 分页查询
    const result = await query
      .orderBy('createdAt', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get()

    return {
      data: result.data,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }
  }
}

/**
 * 系统配置集合操作
 */
const systemConfigCollection = {
  // 获取系统配置
  async getConfig() {
    const result = await getCollection(COLLECTIONS.SYSTEM_CONFIG)
      .limit(1)
      .get()
    return result.data[0] || null
  }
}

module.exports = {
  db,
  _,
  userCollection,
  activationCodeCollection,
  quotaOperationCollection,
  systemConfigCollection,
  COLLECTIONS
}
