import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/agent_model.dart';
import '../models/ai_model.dart';
import '../providers/auth_provider.dart';
import '../providers/chat_provider.dart';
import '../providers/agent_provider.dart';
import '../providers/model_provider.dart';
import 'bazi_selector.dart';

/// 智能体面板
class AgentPanel extends ConsumerStatefulWidget {
  const AgentPanel({super.key});

  @override
  ConsumerState<AgentPanel> createState() => _AgentPanelState();
}

class _AgentPanelState extends ConsumerState<AgentPanel> {
  @override
  void initState() {
    super.initState();
    // 不再在这里加载数据，改为使用Provider统一管理
  }



  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final chatState = ref.watch(chatProvider);
    final selectedAgent = chatState.selectedAgent;

    // 使用Provider获取智能体和模型数据
    final agentState = ref.watch(agentProvider);
    final modelState = ref.watch(modelProvider);

    return Column(
      children: [
        // 智能体选择区域 - 固定高度
        Card(
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildAgentTitleRow(theme, agentState),
                const SizedBox(height: 8),
                _buildAgentDropdown(theme, agentState),
              ],
            ),
          ),
        ),

        const SizedBox(height: 8),

        // AI模型选择区域 - 固定高度
        Card(
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildModelTitleRow(theme, modelState),
                const SizedBox(height: 8),
                _buildModelDropdown(theme, modelState),
              ],
            ),
          ),
        ),

        const SizedBox(height: 8),

        // 八字选择区域 - 条件显示，固定高度
        if (selectedAgent != null && selectedAgent.requiresBazi)
          const BaziSelector(),
      ],
    );
  }

  Widget _buildModelTitleRow(ThemeData theme, ModelState modelState) {
    final chatState = ref.watch(chatProvider);
    final canSwitch = chatState.canSwitchModel;
    final hasStartedConversation = chatState.currentConversation != null && !chatState.currentConversation!.isEmpty;

    return Row(
      children: [
        Icon(
          Icons.psychology,
          color: theme.colorScheme.primary,
          size: 20,
        ),
        const SizedBox(width: 8),
        Text(
          'AI模型选择',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        // 对话进行中的提示
        if (hasStartedConversation && !canSwitch) ...[
          const SizedBox(width: 8),
          Icon(
            Icons.lock_outline,
            size: 14,
            color: theme.colorScheme.onSurface.withOpacity(0.4),
          ),
          const SizedBox(width: 4),
          Text(
            '对话进行中',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.5),
              fontSize: 11,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
        const Spacer(),
        if (modelState.isLoading)
          const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
      ],
    );
  }

  Widget _buildAgentTitleRow(ThemeData theme, AgentState agentState) {
    final chatState = ref.watch(chatProvider);
    final canSwitch = chatState.canSwitchAgent;
    final hasStartedConversation = chatState.currentConversation != null && !chatState.currentConversation!.isEmpty;

    return Row(
      children: [
        Icon(
          Icons.smart_toy,
          color: theme.colorScheme.primary,
          size: 20,
        ),
        const SizedBox(width: 8),
        Text(
          '智能体选择',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        // 对话进行中的提示
        if (hasStartedConversation && !canSwitch) ...[
          const SizedBox(width: 8),
          Icon(
            Icons.lock_outline,
            size: 14,
            color: theme.colorScheme.onSurface.withOpacity(0.4),
          ),
          const SizedBox(width: 4),
          Text(
            '对话进行中',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.5),
              fontSize: 11,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
        const Spacer(),
        if (agentState.isLoading)
          const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
      ],
    );
  }

  Widget _buildAgentDropdown(ThemeData theme, AgentState agentState) {
    final chatState = ref.watch(chatProvider);
    final selectedAgent = chatState.selectedAgent;
    final canSwitch = chatState.canSwitchAgent;
    final isTyping = chatState.isTyping;
    final hasStartedConversation = chatState.currentConversation != null && !chatState.currentConversation!.isEmpty;
    final agents = agentState.agents;

    // 获取提示文本
    String getHintText() {
      if (agentState.isLoading) return '正在加载智能体...';
      if (agentState.hasError) return '加载失败，请重试';
      if (!canSwitch) {
        if (selectedAgent != null) return selectedAgent.agentName;
        if (isTyping) return 'AI正在回复中...';
        return '对话已开始';
      }
      if (agents.isEmpty) return '暂无可用智能体';
      return '请选择智能体';
    }

    return _buildAgentDropdownCore(theme, canSwitch, isTyping, selectedAgent, getHintText, hasStartedConversation, agentState);
  }

  Widget _buildAgentDropdownCore(ThemeData theme, bool canSwitch, bool isTyping, AgentModel? selectedAgent, String Function() getHintText, bool hasStartedConversation, AgentState agentState) {
    final agents = agentState.agents;

    return Container(
      height: 56, // 固定高度
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(
          color: canSwitch && !agentState.isLoading
              ? theme.colorScheme.outline.withOpacity(0.3)
              : theme.colorScheme.outline.withOpacity(0.1),
        ),
        borderRadius: BorderRadius.circular(8),
        color: canSwitch && !agentState.isLoading ? null : theme.colorScheme.surface.withOpacity(0.5),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: selectedAgent?.id,
          hint: Row(
            children: [
              Icon(
                Icons.smart_toy_outlined,
                size: 16,
                color: canSwitch && !agentState.isLoading
                    ? theme.colorScheme.onSurface.withOpacity(0.6)
                    : theme.colorScheme.onSurface.withOpacity(0.3),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  getHintText(),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: canSwitch && !agentState.isLoading
                        ? theme.colorScheme.onSurface.withOpacity(0.6)
                        : theme.colorScheme.onSurface.withOpacity(0.3),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          isExpanded: true,
          menuMaxHeight: null, // 不限制下拉菜单高度
          items: canSwitch && !agentState.isLoading && agents.isNotEmpty
              ? agents.map<DropdownMenuItem<String>>((agent) {
                  return DropdownMenuItem<String>(
                    value: agent.id,
                    child: _buildAgentDropdownItem(agent, theme),
                  );
                }).toList()
              : [],
          onChanged: canSwitch && !agentState.isLoading && agents.isNotEmpty
              ? (agentId) {
                  if (agentId != null) {
                    final agent = agents.firstWhere((a) => a.id == agentId);
                    ref.read(chatProvider.notifier).selectAgent(agent);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('已选择智能体：${agent.agentName}'),
                        duration: const Duration(seconds: 1),
                        behavior: SnackBarBehavior.floating,
                        margin: const EdgeInsets.all(16),
                      ),
                    );
                  }
                }
              : (value) {
                  // 显示不可操作的提示
                  String message = '无法切换智能体';
                  if (agentState.isLoading) {
                    message = '正在加载中，请稍候';
                  } else if (agentState.hasError) {
                    message = '加载失败，请重试';
                  } else if (isTyping) {
                    message = 'AI正在回复中，无法切换智能体';
                  } else if (!canSwitch) {
                    message = '对话已开始，无法切换智能体';
                  } else if (agents.isEmpty) {
                    message = '暂无可用智能体';
                  }

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(message),
                      duration: const Duration(seconds: 2),
                      behavior: SnackBarBehavior.floating,
                      margin: const EdgeInsets.all(16),
                    ),
                  );
                },
        ),
      ),
    );
  }

  Widget _buildAgentDropdownItem(AgentModel agent, ThemeData theme) {
    return Text(
      agent.agentName,
      style: theme.textTheme.bodyMedium,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildAgentList(ThemeData theme) {
    final agentState = ref.watch(agentProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // 标题
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(
                Icons.psychology_outlined,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                '选择智能体',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              if (agentState.isLoading)
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              else
                IconButton(
                  icon: const Icon(Icons.refresh),
                  tooltip: '刷新',
                  onPressed: () {
                    final authState = ref.read(authProvider);
                    if (authState.isAuthenticated && authState.token != null) {
                      ref.read(agentProvider.notifier).refreshAgents(authState.token!);
                    }
                  },
                ),
            ],
          ),
        ),

        // 智能体列表
        Expanded(
          child: _buildAgentListContent(theme),
        ),
      ],
    );
  }

  Widget _buildAgentListContent(ThemeData theme) {
    final agentState = ref.watch(agentProvider);
    final agents = agentState.agents;

    if (agentState.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (agentState.hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: theme.colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: theme.textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              agentState.error ?? '未知错误',
              style: theme.textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                final authState = ref.read(authProvider);
                if (authState.isAuthenticated && authState.token != null) {
                  ref.read(agentProvider.notifier).refreshAgents(authState.token!);
                }
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (agents.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.psychology_outlined,
              size: 48,
              color: theme.colorScheme.onSurface.withOpacity(0.3),
            ),
            const SizedBox(height: 16),
            Text(
              '暂无智能体',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
      );
    }

    final chatState = ref.watch(chatProvider);

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: agents.length,
      itemBuilder: (context, index) {
        final agent = agents[index];
        final isSelected = chatState.selectedAgent?.id == agent.id;

        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: _buildAgentCard(agent, isSelected, theme),
        );
      },
    );
  }

  Widget _buildAgentCard(
    AgentModel agent,
    bool isSelected,
    ThemeData theme,
  ) {
    final chatState = ref.watch(chatProvider);
    final canSwitch = chatState.canSwitchAgent;
    final isTyping = chatState.isTyping;

    return InkWell(
      onTap: canSwitch ? () {
        ref.read(chatProvider.notifier).selectAgent(agent);
        // 显示选择成功的反馈
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已选择智能体：${agent.agentName}'),
            duration: const Duration(seconds: 1),
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
          ),
        );
      } : () {
        // 显示不可操作的提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isTyping ? 'AI正在回复中，无法切换智能体' : '对话已开始，无法切换智能体'),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
          ),
        );
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: !canSwitch
              ? theme.colorScheme.surface.withOpacity(0.5)
              : (isSelected
                  ? theme.colorScheme.primaryContainer
                  : theme.colorScheme.surface),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: !canSwitch
                ? theme.colorScheme.outline.withOpacity(0.1)
                : (isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.outline.withOpacity(0.3)),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 智能体名称和类型
            Row(
              children: [
                Expanded(
                  child: Text(
                    agent.agentName,
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isSelected 
                          ? theme.colorScheme.onPrimaryContainer
                          : theme.colorScheme.onSurface,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Color(int.parse(agent.typeColor.substring(1, 7),
                            radix: 16) +
                        0xFF000000),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    agent.agentType.displayName,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.white,
                      fontSize: 10,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            // 智能体描述
            Text(
              agent.description,
              style: theme.textTheme.bodySmall?.copyWith(
                color: isSelected 
                    ? theme.colorScheme.onPrimaryContainer.withOpacity(0.8)
                    : theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            
            // 是否需要八字信息
            if (agent.requiresBazi) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(
                    Icons.calculate_outlined,
                    size: 12,
                    color: theme.colorScheme.secondary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '需要八字信息',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.secondary,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildModelDropdown(ThemeData theme, ModelState modelState) {
    final chatState = ref.watch(chatProvider);
    final selectedModel = chatState.selectedModel;
    final availableModels = modelState.models;
    final canSwitch = chatState.canSwitchModel;
    final isTyping = chatState.isTyping;
    final hasStartedConversation = chatState.currentConversation != null && !chatState.currentConversation!.isEmpty;

    // 获取提示文本
    String getHintText() {
      if (modelState.isLoading) return '正在加载模型...';
      if (modelState.hasError) return '加载失败，请重试';
      if (!canSwitch) {
        if (selectedModel != null) return selectedModel.modelDisplayName;
        if (isTyping) return 'AI正在回复中...';
        return '对话已开始';
      }
      if (availableModels.isEmpty) return '暂无可用模型';
      return '请选择AI模型';
    }

    return _buildModelDropdownCore(theme, canSwitch, isTyping, selectedModel, availableModels, getHintText, modelState);
  }

  Widget _buildModelDropdownCore(ThemeData theme, bool canSwitch, bool isTyping, AIModel? selectedModel, List<AIModel> availableModels, String Function() getHintText, ModelState modelState) {

    return Container(
      height: 56, // 固定高度
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(
          color: canSwitch && !modelState.isLoading
              ? theme.colorScheme.outline.withOpacity(0.3)
              : theme.colorScheme.outline.withOpacity(0.1),
        ),
        borderRadius: BorderRadius.circular(8),
        color: canSwitch && !modelState.isLoading ? null : theme.colorScheme.surface.withOpacity(0.5),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: selectedModel?.id,
          hint: Row(
            children: [
              Icon(
                Icons.psychology_outlined,
                size: 16,
                color: canSwitch && !modelState.isLoading
                    ? theme.colorScheme.onSurface.withOpacity(0.6)
                    : theme.colorScheme.onSurface.withOpacity(0.3),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  getHintText(),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: canSwitch && !modelState.isLoading
                        ? theme.colorScheme.onSurface.withOpacity(0.6)
                        : theme.colorScheme.onSurface.withOpacity(0.3),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          isExpanded: true,
          menuMaxHeight: null, // 不限制下拉菜单高度
          items: canSwitch && !modelState.isLoading && availableModels.isNotEmpty
              ? availableModels.map<DropdownMenuItem<String>>((model) {
                  return DropdownMenuItem<String>(
                    value: model.id,
                    child: _buildModelDropdownItem(model, theme),
                  );
                }).toList()
              : [],
          onChanged: canSwitch && !modelState.isLoading && availableModels.isNotEmpty
              ? (modelId) {
                  if (modelId != null) {
                    final model = availableModels.firstWhere((m) => m.id == modelId);
                    ref.read(chatProvider.notifier).selectModel(model);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('已选择模型：${model.modelDisplayName}'),
                        duration: const Duration(seconds: 1),
                        behavior: SnackBarBehavior.floating,
                        margin: const EdgeInsets.all(16),
                      ),
                    );
                  }
                }
              : (value) {
                  // 显示不可操作的提示
                  String message = '无法切换模型';
                  if (modelState.isLoading) {
                    message = '正在加载中，请稍候';
                  } else if (modelState.hasError) {
                    message = '加载失败，请重试';
                  } else if (isTyping) {
                    message = 'AI正在回复中，无法切换模型';
                  } else if (!canSwitch) {
                    message = '对话已开始，无法切换模型';
                  } else if (availableModels.isEmpty) {
                    message = '暂无可用模型';
                  }

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(message),
                      duration: const Duration(seconds: 2),
                      behavior: SnackBarBehavior.floating,
                      margin: const EdgeInsets.all(16),
                    ),
                  );
                },
        ),
      ),
    );
  }

  Widget _buildModelDropdownItem(AIModel model, ThemeData theme) {
    return Text(
      model.modelDisplayName,
      style: theme.textTheme.bodyMedium,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildModelList(ThemeData theme) {
    final chatState = ref.watch(chatProvider);
    final modelState = ref.watch(modelProvider);
    final availableModels = modelState.models;

    if (modelState.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (modelState.hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: theme.colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              '模型加载失败',
              style: theme.textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              modelState.error ?? '未知错误',
              style: theme.textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                final authState = ref.read(authProvider);
                if (authState.isAuthenticated && authState.token != null) {
                  ref.read(modelProvider.notifier).refreshModels(authState.token!);
                }
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (availableModels.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.psychology_outlined,
              size: 48,
              color: theme.colorScheme.onSurface.withOpacity(0.3),
            ),
            const SizedBox(height: 16),
            Text(
              '暂无可用模型',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: availableModels.length,
      itemBuilder: (context, index) {
        final model = availableModels[index];
        final isSelected = chatState.selectedModel?.id == model.id;

        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: _buildModelCard(model, isSelected, theme),
        );
      },
    );
  }

  Widget _buildModelCard(
    AIModel model,
    bool isSelected,
    ThemeData theme,
  ) {
    final chatState = ref.watch(chatProvider);
    final canSwitch = chatState.canSwitchModel;
    final isTyping = chatState.isTyping;

    return InkWell(
      onTap: canSwitch ? () {
        ref.read(chatProvider.notifier).selectModel(model);
        // 显示选择成功的反馈
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已选择模型：${model.modelDisplayName}'),
            duration: const Duration(seconds: 1),
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
          ),
        );
      } : () {
        // 显示不可操作的提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isTyping ? 'AI正在回复中，无法切换模型' : '对话已开始，无法切换模型'),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
          ),
        );
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: !canSwitch
              ? theme.colorScheme.surface.withOpacity(0.5)
              : (isSelected
                  ? theme.colorScheme.secondaryContainer.withOpacity(0.5)
                  : theme.colorScheme.surface),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: !canSwitch
                ? theme.colorScheme.outline.withOpacity(0.1)
                : (isSelected
                    ? theme.colorScheme.secondary
                    : theme.colorScheme.outline.withOpacity(0.3)),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 模型名称和类型
            Row(
              children: [
                Expanded(
                  child: Text(
                    model.modelDisplayName,
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isSelected
                          ? theme.colorScheme.onSecondaryContainer
                          : theme.colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // 模型描述
            Text(
              model.description,
              style: theme.textTheme.bodySmall?.copyWith(
                color: isSelected
                    ? theme.colorScheme.onSecondaryContainer.withOpacity(0.8)
                    : theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
