import '../models/ai_model.dart';
import '../models/api_response.dart';
import '../core/constants/app_constants.dart';
import 'cloud_function_service.dart';
import 'storage_service.dart';

/// AI模型服务
class ModelService {
  final CloudFunctionService _cloudFunctionService = CloudFunctionService();
  final StorageService _storageService = StorageService();

  // 缓存
  List<AIModel>? _cachedModels;
  DateTime? _lastCacheTime;

  /// 获取模型列表
  Future<List<AIModel>> getModels({
    required String token,
    bool forceRefresh = false,
  }) async {
    // 检查缓存
    if (!forceRefresh && _cachedModels != null && _lastCacheTime != null) {
      final cacheAge = DateTime.now().difference(_lastCacheTime!);
      if (cacheAge < AppConstants.cacheExpiry) {
        return _cachedModels!;
      }
    }

    try {
      final response = await _cloudFunctionService.getModels(token: token);
      
      if (response.isSuccess && response.data?.success == true) {
        final modelsData = response.data?.data?.models ?? [];
        final models = modelsData
            .map((modelJson) => AIModel.fromJson(modelJson as Map<String, dynamic>))
            .where((model) => model.isActive) // 只返回激活的模型
            .toList();

        // 按排序权重排序
        models.sort((a, b) => a.sortOrder.compareTo(b.sortOrder));

        // 更新缓存
        _cachedModels = models;
        _lastCacheTime = DateTime.now();

        // 保存到本地存储
        await _saveModelsToStorage(models);

        return models;
      } else {
        // 如果云函数调用失败，尝试从本地存储加载
        return await _loadModelsFromStorage();
      }
    } catch (e) {
      // 网络错误时从本地存储加载，如果本地也没有则抛出异常
      final localModels = await _loadModelsFromStorage();
      if (localModels.isEmpty) {
        throw Exception('无法加载模型列表，请检查网络连接');
      }
      return localModels;
    }
  }

  /// 根据ID获取模型
  Future<AIModel?> getModelById(String modelId, String token) async {
    final models = await getModels(token: token);
    try {
      return models.firstWhere((model) => model.id == modelId);
    } catch (e) {
      return null;
    }
  }

  /// 根据模型名称获取模型
  Future<AIModel?> getModelByName(String modelName, String token) async {
    final models = await getModels(token: token);
    try {
      return models.firstWhere((model) => model.modelName == modelName);
    } catch (e) {
      return null;
    }
  }

  /// 搜索模型
  Future<List<AIModel>> searchModels(String query, String token) async {
    final models = await getModels(token: token);
    final lowerQuery = query.toLowerCase();
    
    return models.where((model) {
      return model.modelName.toLowerCase().contains(lowerQuery) ||
             model.modelDisplayName.toLowerCase().contains(lowerQuery) ||
             model.description.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// 获取推荐模型
  Future<List<AIModel>> getRecommendedModels(String token) async {
    final models = await getModels(token: token);
    
    // 按排序权重返回前几个推荐模型
    return models.take(3).toList();
  }

  /// 按提供商分组模型
  Future<Map<String, List<AIModel>>> getModelsByProvider(String token) async {
    final models = await getModels(token: token);
    final Map<String, List<AIModel>> groupedModels = {};
    
    for (final model in models) {
      String provider = 'Unknown';
      
      if (model.modelName.toLowerCase().contains('gpt')) {
        provider = 'OpenAI';
      } else if (model.modelName.toLowerCase().contains('claude')) {
        provider = 'Anthropic';
      } else if (model.modelName.toLowerCase().contains('gemini')) {
        provider = 'Google';
      } else if (model.modelName.toLowerCase().contains('deepseek')) {
        provider = 'DeepSeek';
      }
      
      if (!groupedModels.containsKey(provider)) {
        groupedModels[provider] = [];
      }
      groupedModels[provider]!.add(model);
    }
    
    return groupedModels;
  }

  /// 清除缓存
  void clearCache() {
    _cachedModels = null;
    _lastCacheTime = null;
  }

  /// 清除所有缓存（包括本地存储）
  Future<void> clearAllCache() async {
    clearCache();
    await _storageService.remove(AppConstants.modelsCacheKey);
    await _storageService.remove('${AppConstants.lastUpdateKey}_models');
  }

  /// 保存模型列表到本地存储
  Future<void> _saveModelsToStorage(List<AIModel> models) async {
    try {
      final modelsJson = models.map((model) => model.toJson()).toList();
      await _storageService.setJsonList(AppConstants.modelsCacheKey, modelsJson);
      await _storageService.setString(
        '${AppConstants.lastUpdateKey}_models',
        DateTime.now().toIso8601String(),
      );
    } catch (e) {
      // 忽略存储错误
    }
  }

  /// 从本地存储加载模型列表
  Future<List<AIModel>> _loadModelsFromStorage() async {
    try {
      final modelsJsonList = await _storageService.getJsonList(AppConstants.modelsCacheKey);
      if (modelsJsonList != null) {
        return modelsJsonList
            .map((modelJson) => AIModel.fromJson(modelJson))
            .where((model) => model.isActive)
            .toList();
      }
    } catch (e) {
      // 忽略加载错误
    }

    // 不再返回硬编码的默认模型
    return [];
  }

  /// 获取默认模型列表（后备方案）
  List<AIModel> _getDefaultModels() {
    // 不再返回硬编码的模型，强制从云函数加载
    return [];
  }
}
