#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
真太阳时计算功能测试脚本
测试八字排盘API的真太阳时计算功能
"""

import requests
import json
from datetime import datetime

# API配置
API_URL = "https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction"

def test_bazi_with_solar_time():
    """测试带真太阳时计算的八字排盘"""
    
    print("🧪 测试真太阳时计算功能")
    print("=" * 50)
    
    # 测试用例
    test_cases = [
        {
            "name": "张三",
            "description": "北京地区测试（经度接近120°）",
            "gender": "男",
            "calendarType": "公历",
            "birthDate": "1990-06-15",
            "birthTime": "14:30",
            "isLeapMonth": False,
            "birthPlace": "北京市东城区",
            "latitude": 39.9316,
            "longitude": 116.41,
            "region": "大陆"
        },
        {
            "name": "李四",
            "description": "新疆地区测试（经度偏西较多）",
            "gender": "女", 
            "calendarType": "公历",
            "birthDate": "1985-03-20",
            "birthTime": "10:15",
            "isLeapMonth": False,
            "birthPlace": "新疆乌鲁木齐",
            "latitude": 43.8256,
            "longitude": 87.6168,
            "region": "大陆"
        },
        {
            "name": "王五",
            "description": "黑龙江地区测试（经度偏东）",
            "gender": "男",
            "calendarType": "公历", 
            "birthDate": "1992-12-08",
            "birthTime": "16:45",
            "isLeapMonth": False,
            "birthPlace": "黑龙江哈尔滨",
            "latitude": 45.8038,
            "longitude": 126.5349,
            "region": "大陆"
        },
        {
            "name": "赵六",
            "description": "不使用真太阳时（无经纬度）",
            "gender": "女",
            "calendarType": "公历",
            "birthDate": "1988-09-12",
            "birthTime": "12:00",
            "isLeapMonth": False,
            "birthPlace": "上海市"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {test_case['description']}")
        print(f"姓名: {test_case['name']}")
        print(f"出生时间: {test_case['birthDate']} {test_case['birthTime']}")
        print(f"出生地: {test_case['birthPlace']}")
        
        if 'latitude' in test_case and 'longitude' in test_case:
            print(f"经纬度: {test_case['latitude']:.4f}°N, {test_case['longitude']:.4f}°E")
            
            # 计算预期的时间差
            longitude_diff = test_case['longitude'] - 120
            time_offset_minutes = longitude_diff * 4
            print(f"预期时间差: {time_offset_minutes:.1f}分钟")
        else:
            print("经纬度: 未提供（不使用真太阳时）")
        
        # 构造请求
        request_data = {
            "action": "baziAnalyze",
            **test_case
        }
        
        try:
            # 发送请求
            print("🔄 发送请求...")
            response = requests.post(
                API_URL,
                headers={'Content-Type': 'application/json'},
                json=request_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('code') == 0:
                    data = result.get('data', {})
                    print("✅ 请求成功")
                    
                    # 显示基本信息
                    print(f"姓名: {data.get('name', 'N/A')}")
                    print(f"性别: {data.get('gender', 'N/A')}")
                    print(f"出生地: {data.get('birthPlace', 'N/A')}")
                    print(f"公历日期: {data.get('solarDate', 'N/A')}")
                    print(f"公历时间: {data.get('solarTime', 'N/A')}")
                    print(f"农历日期: {data.get('lunarDate', 'N/A')}")
                    
                    # 显示八字信息
                    print(f"八字: {data.get('baziStr', 'N/A')}")
                    
                    # 显示真太阳时信息
                    solar_time_info = data.get('solarTimeInfo')
                    if solar_time_info:
                        print("\n🌞 真太阳时计算信息:")
                        print(f"原始时间: {solar_time_info.get('originalTime', 'N/A')}")
                        print(f"真太阳时: {solar_time_info.get('solarTime', 'N/A')}")
                        print(f"经度: {solar_time_info.get('longitude', 'N/A')}°")
                        print(f"经度差: {solar_time_info.get('longitudeDiff', 'N/A')}°")
                        print(f"时间差: {solar_time_info.get('timeDiffMinutes', 'N/A')}分钟")
                        print(f"时间差说明: {solar_time_info.get('timeDiffText', 'N/A')}")
                        
                        # 显示夏令时信息
                        dst_info = solar_time_info.get('daylightSaving', {})
                        if dst_info:
                            print(f"夏令时状态: {'是' if dst_info.get('isDaylightSaving') else '否'}")
                            if dst_info.get('isDaylightSaving'):
                                print(f"夏令时地区: {dst_info.get('region', 'N/A')}")
                    else:
                        print("\n🌞 真太阳时计算信息: 未使用真太阳时计算")
                    
                else:
                    print(f"❌ API返回错误: {result.get('message', '未知错误')}")
                    
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                
        except requests.exceptions.Timeout:
            print("❌ 请求超时")
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
        except Exception as e:
            print(f"❌ 未知错误: {e}")
        
        print("-" * 50)

def test_solar_time_calculation():
    """测试真太阳时计算逻辑"""
    
    print("\n🧮 测试真太阳时计算逻辑")
    print("=" * 50)
    
    # 测试不同经度的时间差计算
    test_longitudes = [
        (87.6168, "乌鲁木齐"),  # 新疆
        (116.41, "北京"),      # 北京
        (120.0, "基准经度"),    # 基准
        (126.5349, "哈尔滨"),  # 黑龙江
        (104.0665, "成都"),    # 四川
    ]
    
    for longitude, city in test_longitudes:
        longitude_diff = longitude - 120
        time_offset_minutes = longitude_diff * 4
        
        print(f"{city:8} | 经度: {longitude:8.4f}° | 差值: {longitude_diff:6.2f}° | 时间差: {time_offset_minutes:6.1f}分钟")

if __name__ == "__main__":
    print("🚀 开始测试真太阳时计算功能")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"API地址: {API_URL}")
    
    # 测试真太阳时计算逻辑
    test_solar_time_calculation()
    
    # 测试API功能
    test_bazi_with_solar_time()
    
    print("\n✨ 测试完成")
