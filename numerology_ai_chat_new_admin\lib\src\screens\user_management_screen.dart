
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:numerology_ai_chat_admin/src/models/user_model.dart';
import 'package:numerology_ai_chat_admin/src/providers/user_provider.dart';
import 'package:numerology_ai_chat_admin/src/widgets/edit_user_dialog.dart';

class UserManagementScreen extends ConsumerWidget {
  const UserManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userState = ref.watch(userProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('用户管理'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.read(userProvider.notifier).fetchUsers(),
          ),
        ],
      ),
      body: userState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : userState.error != null
              ? Center(child: Text('错误: ${userState.error}'))
              : userState.users == null || userState.users!.isEmpty
                  ? const Center(child: Text('没有用户'))
                  : SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: DataTable(
                        columns: const [
                          DataColumn(label: Text('用户名')),
                          DataColumn(label: Text('状态')),
                          DataColumn(label: Text('可用算力')),
                          DataColumn(label: Text('总消耗')),
                          DataColumn(label: Text('注册时间')),
                          DataColumn(label: Text('操作')),
                        ],
                        rows: userState.users!.map((user) => _buildUserRow(context, ref, user)).toList(),
                      ),
                    ),
    );
  }

  DataRow _buildUserRow(BuildContext context, WidgetRef ref, User user) {
    return DataRow(
      cells: [
        DataCell(Text(user.username)),
        DataCell(Switch(
          value: user.status == '激活',
          onChanged: (isActive) {
            final newStatus = isActive ? '激活' : '禁用';
            ref.read(userProvider.notifier).updateUserStatus(user.id, newStatus);
          },
        )),
        DataCell(Text(user.availableCount.toString())),
        DataCell(Text(user.totalUsageCount.toString())),
        DataCell(Text(user.createdAt.toLocal().toString().substring(0, 16))),
        DataCell(
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              showDialog(
                context: context,
                builder: (_) => EditUserDialog(user: user),
              );
            },
          ),
        ),
      ],
    );
  }
}
