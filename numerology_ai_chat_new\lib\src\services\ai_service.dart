﻿import 'dart:async';
import '../models/agent_model.dart';
import '../models/ai_model.dart';
import '../models/chat_message.dart';
import 'go_proxy_service.dart';
import 'agent_service.dart';
import 'model_service.dart';

/// AI服务（重构为使用云函数和Go代理服务）
class AIService {
  final GoProxyService _goProxyService;
  final AgentService _agentService;
  final ModelService _modelService;

  AIService({
    required GoProxyService goProxyService,
    required AgentService agentService,
    required ModelService modelService,
  }) : _goProxyService = goProxyService,
       _agentService = agentService,
       _modelService = modelService;
  
  /// 鑾峰彇鑱婂ぉ瀹屾垚娴侊紙閫氳繃Go浠ｇ悊鏈嶅姟锛?
  Stream<String> getChatCompletionStream({
    required String token,
    required String agentId,
    required String modelId,
    required List<ChatMessage> messages,
  }) async* {
    try {
      yield* _goProxyService.sendChatMessage(
        token: token,
        agentId: agentId,
        modelId: modelId,
        messages: messages,
      );
    } catch (e) {
      throw Exception('AI鏈嶅姟璇锋眰澶辫触: ');
    }
  }

  /// 鑾峰彇鑱婂ぉ瀹屾垚锛堥潪娴佸紡锛岄€氳繃Go浠ｇ悊鏈嶅姟锛?
  Future<String> getChatCompletion({
    required String token,
    required String agentId,
    required String modelId,
    required List<ChatMessage> messages,
  }) async {
    try {
      return await _goProxyService.sendChatMessageSync(
        token: token,
        agentId: agentId,
        modelId: modelId,
        messages: messages,
      );
    } catch (e) {
      throw Exception('AI鏈嶅姟璇锋眰澶辫触: ');
    }
  }

  /// 获取大白话版本聊天完成（流式）
  Stream<LaymanVersionChunk> getChatCompletionWithLaymanVersionStream({
    required String token,
    required String agentId,
    required String modelId,
    required List<ChatMessage> messages,
  }) async* {
    try {
      yield* _goProxyService.sendChatMessageWithLaymanVersionStream(
        token: token,
        agentId: agentId,
        modelId: modelId,
        messages: messages,
      );
    } catch (e) {
      throw Exception('AI服务请求失败: $e');
    }
  }

  /// 鑾峰彇鏅鸿兘浣撳垪琛?
  Future<List<AgentModel>> getAgents({
    required String token,
    bool forceRefresh = false,
  }) async {
    return await _agentService.getAgents(
      token: token,
      forceRefresh: forceRefresh,
    );
  }

  /// 鑾峰彇妯″瀷鍒楄〃
  Future<List<AIModel>> getModels({
    required String token,
    bool forceRefresh = false,
  }) async {
    return await _modelService.getModels(
      token: token,
      forceRefresh: forceRefresh,
    );
  }

  /// 鏍规嵁ID鑾峰彇鏅鸿兘浣?
  Future<AgentModel?> getAgentById(String agentId, String token) async {
    return await _agentService.getAgentById(agentId, token);
  }

  /// 鏍规嵁ID鑾峰彇妯″瀷
  Future<AIModel?> getModelById(String modelId, String token) async {
    return await _modelService.getModelById(modelId, token);
  }

  /// 鎼滅储鏅鸿兘浣?
  Future<List<AgentModel>> searchAgents(String query, String token) async {
    return await _agentService.searchAgents(query, token);
  }

  /// 鎼滅储妯″瀷
  Future<List<AIModel>> searchModels(String query, String token) async {
    return await _modelService.searchModels(query, token);
  }

  /// 妫€鏌o浠ｇ悊鏈嶅姟鍋ュ悍鐘舵€?
  Future<bool> checkGoProxyHealth() async {
    return await _goProxyService.checkHealth();
  }

  /// 娓呴櫎缂撳瓨
  void clearCache() {
    _agentService.clearCache();
    _modelService.clearCache();
  }
}
