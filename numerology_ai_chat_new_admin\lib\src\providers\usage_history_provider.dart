import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/usage_history_model.dart';
import '../services/admin_api_service.dart';
import 'auth_provider.dart';

class UsageHistoryState {
  final List<UsageHistoryModel> histories;
  final bool isLoading;
  final String? error;
  final int currentPage;
  final int totalPages;
  final int totalCount;
  final String searchQuery;
  final String agentFilter;
  final String modelLevelFilter;
  final String tierFilter;

  const UsageHistoryState({
    this.histories = const [],
    this.isLoading = false,
    this.error,
    this.currentPage = 1,
    this.totalPages = 1,
    this.totalCount = 0,
    this.searchQuery = '',
    this.agentFilter = '',
    this.modelLevelFilter = '',
    this.tierFilter = '',
  });

  UsageHistoryState copyWith({
    List<UsageHistoryModel>? histories,
    bool? isLoading,
    String? error,
    int? currentPage,
    int? totalPages,
    int? totalCount,
    String? searchQuery,
    String? agentFilter,
    String? modelLevelFilter,
    String? tierFilter,
  }) {
    return UsageHistoryState(
      histories: histories ?? this.histories,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      totalCount: totalCount ?? this.totalCount,
      searchQuery: searchQuery ?? this.searchQuery,
      agentFilter: agentFilter ?? this.agentFilter,
      modelLevelFilter: modelLevelFilter ?? this.modelLevelFilter,
      tierFilter: tierFilter ?? this.tierFilter,
    );
  }
}

class UsageHistoryNotifier extends StateNotifier<UsageHistoryState> {
  final AdminApiService _apiService;
  final Ref _ref;

  UsageHistoryNotifier(this._apiService, this._ref) : super(const UsageHistoryState());

  Future<void> loadUsageHistories({
    int page = 1,
    int limit = 20,
    String? search,
    String? agentId,
    String? modelLevel,
    String? tierId,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final authState = _ref.read(authProvider);
      if (authState.token == null) {
        state = state.copyWith(isLoading: false, error: '未登录');
        return;
      }

      final response = await _apiService.getUsageHistoryList(
        authState.token!,
        page: page,
        limit: limit,
        search: search,
        agentId: agentId,
        modelLevel: modelLevel,
        tierId: tierId,
      );

      if (response['code'] == 0) {
        final outerData = response['data'] as Map<String, dynamic>;
        final innerData = outerData['data'] as Map<String, dynamic>;
        final historiesData = innerData['histories'] as List;
        final histories = historiesData.map((json) => UsageHistoryModel.fromJson(json)).toList();
        
        state = state.copyWith(
          histories: histories,
          isLoading: false,
          currentPage: page,
          totalCount: innerData['total'] ?? 0,
          totalPages: ((innerData['total'] ?? 0) / limit).ceil(),
          searchQuery: search ?? '',
          agentFilter: agentId ?? '',
          modelLevelFilter: modelLevel ?? '',
          tierFilter: tierId ?? '',
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response['message'] ?? '加载使用历史失败',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: '网络错误: $e',
      );
    }
  }

  Future<void> refreshHistories() async {
    await loadUsageHistories(
      page: state.currentPage,
      search: state.searchQuery.isNotEmpty ? state.searchQuery : null,
      agentId: state.agentFilter.isNotEmpty ? state.agentFilter : null,
      modelLevel: state.modelLevelFilter.isNotEmpty ? state.modelLevelFilter : null,
      tierId: state.tierFilter.isNotEmpty ? state.tierFilter : null,
    );
  }

  Future<void> searchHistories(String query) async {
    await loadUsageHistories(
      page: 1,
      search: query.isNotEmpty ? query : null,
      agentId: state.agentFilter.isNotEmpty ? state.agentFilter : null,
      modelLevel: state.modelLevelFilter.isNotEmpty ? state.modelLevelFilter : null,
      tierId: state.tierFilter.isNotEmpty ? state.tierFilter : null,
    );
  }

  Future<void> filterByAgent(String agentId) async {
    await loadUsageHistories(
      page: 1,
      search: state.searchQuery.isNotEmpty ? state.searchQuery : null,
      agentId: agentId.isNotEmpty ? agentId : null,
      modelLevel: state.modelLevelFilter.isNotEmpty ? state.modelLevelFilter : null,
      tierId: state.tierFilter.isNotEmpty ? state.tierFilter : null,
    );
  }

  Future<void> filterByModelLevel(String modelLevel) async {
    await loadUsageHistories(
      page: 1,
      search: state.searchQuery.isNotEmpty ? state.searchQuery : null,
      agentId: state.agentFilter.isNotEmpty ? state.agentFilter : null,
      modelLevel: modelLevel.isNotEmpty ? modelLevel : null,
      tierId: state.tierFilter.isNotEmpty ? state.tierFilter : null,
    );
  }

  Future<void> filterByTier(String tierId) async {
    await loadUsageHistories(
      page: 1,
      search: state.searchQuery.isNotEmpty ? state.searchQuery : null,
      agentId: state.agentFilter.isNotEmpty ? state.agentFilter : null,
      modelLevel: state.modelLevelFilter.isNotEmpty ? state.modelLevelFilter : null,
      tierId: tierId.isNotEmpty ? tierId : null,
    );
  }

  Future<void> loadPage(int page) async {
    await loadUsageHistories(
      page: page,
      search: state.searchQuery.isNotEmpty ? state.searchQuery : null,
      agentId: state.agentFilter.isNotEmpty ? state.agentFilter : null,
      modelLevel: state.modelLevelFilter.isNotEmpty ? state.modelLevelFilter : null,
      tierId: state.tierFilter.isNotEmpty ? state.tierFilter : null,
    );
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

final usageHistoryProvider = StateNotifierProvider<UsageHistoryNotifier, UsageHistoryState>((ref) {
  final apiService = ref.read(adminApiServiceProvider);
  return UsageHistoryNotifier(apiService, ref);
});
