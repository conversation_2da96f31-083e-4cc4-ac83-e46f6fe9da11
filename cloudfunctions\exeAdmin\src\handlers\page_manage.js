const { createBusinessError, ERROR_CODES, formatSuccessResponse } = require('../utils/error_handler')
const { pageCollection } = require('../utils/db')
const { checkPermission } = require('../middleware/auth')
const logger = require('../utils/logger')

/**
 * 获取页面配置列表
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 页面配置列表
 */
async function getPageList(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'page_read')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限查看页面配置列表', 403)
    }

    const { pageType, isActive, page = 1, pageSize = 20 } = event || {}

    logger.info('管理员获取页面配置列表', {
      adminId: adminAuth.adminId,
      pageType,
      isActive,
      page,
      pageSize
    })

    // 构建查询条件
    const where = {}
    if (pageType) {
      where.pageType = pageType
    }
    if (typeof isActive === 'boolean') {
      where.isActive = isActive
    }

    // 分页查询
    const skip = (page - 1) * pageSize
    const pages = await pageCollection.getList(where, skip, pageSize)
    const total = await pageCollection.getCount(where)

    logger.info('管理员获取页面配置列表成功', {
      adminId: adminAuth.adminId,
      total,
      currentPage: page,
      pageSize
    })

    const responseData = {
      pages,
      pagination: {
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages: Math.ceil(total / pageSize)
      }
    }

    return formatSuccessResponse(responseData, '获取页面配置列表成功')

  } catch (error) {
    logger.error('管理员获取页面配置列表异常', {
      adminId: adminAuth.adminId,
      error: error.message
    })
    throw error
  }
}

/**
 * 更新页面配置
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 更新结果
 */
async function updatePageByAdmin(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'page_update')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限更新页面配置', 403)
    }

    const {
      pageId,
      pageTitle,
      pageContent,
      pageType,
      isActive,
      sortOrder,
      slug,
      metaDescription,
      keywords
    } = event

    // 参数校验
    if (!pageId) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '页面ID不能为空', 400)
    }

    logger.info('管理员更新页面配置', {
      adminId: adminAuth.adminId,
      pageId,
      pageTitle
    })

    // 检查页面是否存在
    const existingPage = await pageCollection.findById(pageId)
    if (!existingPage) {
      throw createBusinessError(ERROR_CODES.PAGE_NOT_FOUND, '页面配置不存在', 404)
    }

    // 如果更新了slug，检查是否重复
    if (slug && slug !== existingPage.slug) {
      const duplicatePage = await pageCollection.findBySlug(slug)
      if (duplicatePage && duplicatePage._id !== pageId) {
        throw createBusinessError(ERROR_CODES.SLUG_ALREADY_EXISTS, 'URL标识已存在', 400)
      }
    }

    // 构建更新数据
    const updateData = {
      updatedBy: adminAuth.adminId
    }

    if (pageTitle !== undefined) updateData.pageTitle = pageTitle
    if (pageContent !== undefined) updateData.pageContent = pageContent
    if (pageType !== undefined) updateData.pageType = pageType
    if (typeof isActive === 'boolean') updateData.isActive = isActive
    if (sortOrder !== undefined) updateData.sortOrder = sortOrder
    if (slug !== undefined) updateData.slug = slug
    if (metaDescription !== undefined) updateData.metaDescription = metaDescription
    if (keywords !== undefined) updateData.keywords = keywords

    // 执行更新
    await pageCollection.update(pageId, updateData)

    logger.info('管理员更新页面配置成功', {
      adminId: adminAuth.adminId,
      pageId,
      pageTitle
    })

    return formatSuccessResponse(null, '页面配置更新成功')

  } catch (error) {
    logger.error('管理员更新页面配置异常', {
      adminId: adminAuth.adminId,
      pageId: event.pageId,
      error: error.message
    })
    throw error
  }
}

/**
 * 创建页面配置
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 创建结果
 */
async function createPageByAdmin(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'page_create')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限创建页面配置', 403)
    }

    const {
      pageTitle,
      pageContent,
      pageType,
      isActive = true,
      sortOrder = 0,
      slug,
      metaDescription = '',
      keywords = []
    } = event

    // 参数校验
    if (!pageTitle || !pageContent || !pageType || !slug) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '页面标题、内容、类型和URL标识不能为空', 400)
    }

    logger.info('管理员创建页面配置', {
      adminId: adminAuth.adminId,
      pageTitle,
      pageType
    })

    // 检查slug是否重复
    const existingPage = await pageCollection.findBySlug(slug)
    if (existingPage) {
      throw createBusinessError(ERROR_CODES.SLUG_ALREADY_EXISTS, 'URL标识已存在', 400)
    }

    // 构建页面数据
    const pageData = {
      pageTitle,
      pageContent,
      pageType,
      isActive,
      sortOrder,
      slug,
      metaDescription,
      keywords,
      createdBy: adminAuth.adminId,
      updatedBy: adminAuth.adminId
    }

    // 创建页面
    const pageId = await pageCollection.create(pageData)

    // 获取创建的页面信息
    const newPage = await pageCollection.findById(pageId)

    logger.info('管理员创建页面配置成功', {
      adminId: adminAuth.adminId,
      pageId,
      pageTitle
    })

    const responseData = {
      pageId: newPage._id,
      pageTitle: newPage.pageTitle,
      pageType: newPage.pageType,
      slug: newPage.slug,
      isActive: newPage.isActive,
      sortOrder: newPage.sortOrder,
      createdAt: newPage.createdAt
    }

    return formatSuccessResponse(responseData, '页面配置创建成功')

  } catch (error) {
    logger.error('管理员创建页面配置异常', {
      adminId: adminAuth.adminId,
      pageTitle: event.pageTitle,
      error: error.message
    })
    throw error
  }
}

/**
 * 删除页面配置
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 删除结果
 */
async function deletePageByAdmin(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'page_delete')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限删除页面配置', 403)
    }

    const { pageId } = event

    // 参数校验
    if (!pageId) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '页面ID不能为空', 400)
    }

    logger.info('管理员删除页面配置', {
      adminId: adminAuth.adminId,
      pageId
    })

    // 检查页面是否存在
    const existingPage = await pageCollection.findById(pageId)
    if (!existingPage) {
      throw createBusinessError(ERROR_CODES.PAGE_NOT_FOUND, '页面配置不存在', 404)
    }

    // 执行删除
    await pageCollection.delete(pageId)

    logger.info('管理员删除页面配置成功', {
      adminId: adminAuth.adminId,
      pageId,
      pageTitle: existingPage.pageTitle
    })

    return formatSuccessResponse(null, '页面配置删除成功')

  } catch (error) {
    logger.error('管理员删除页面配置异常', {
      adminId: adminAuth.adminId,
      pageId: event.pageId,
      error: error.message
    })
    throw error
  }
}

module.exports = {
  getPageList,
  updatePageByAdmin,
  createPageByAdmin,
  deletePageByAdmin
}