#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置永久图标 - 确保每次编译都自动携带正确图标
"""

from PIL import Image
import os
import shutil

def create_high_quality_ico():
    """创建高质量的ico文件"""
    jpg_path = "assets/images/logo.png"
    ico_path = "windows/runner/resources/app_icon.ico"
    
    print("🔄 创建高质量图标文件...")
    
    if not os.path.exists(jpg_path):
        print(f"❌ 源文件不存在: {jpg_path}")
        return False
    
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(ico_path), exist_ok=True)
        
        # 打开并处理图片
        with Image.open(jpg_path) as img:
            print(f"📏 源图片尺寸: {img.size}")
            
            # 转换为RGBA模式
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            # 裁剪为正方形
            min_size = min(img.size)
            left = (img.size[0] - min_size) // 2
            top = (img.size[1] - min_size) // 2
            right = left + min_size
            bottom = top + min_size
            img_square = img.crop((left, top, right, bottom))
            
            # Windows标准图标尺寸
            sizes = [
                (16, 16),   # 小图标
                (20, 20),   # 小图标 125% DPI
                (24, 24),   # 小图标 150% DPI
                (32, 32),   # 中等图标
                (40, 40),   # 中等图标 125% DPI
                (48, 48),   # 中等图标 150% DPI
                (64, 64),   # 大图标
                (96, 96),   # 大图标 150% DPI
                (128, 128), # 超大图标
                (256, 256)  # 超大图标
            ]
            
            # 生成各种尺寸的图标
            icon_images = []
            for size in sizes:
                resized = img_square.resize(size, Image.Resampling.LANCZOS)
                
                # 对小尺寸图标进行锐化处理
                if size[0] <= 48:
                    from PIL import ImageFilter, ImageEnhance
                    resized = resized.filter(ImageFilter.SHARPEN)
                    enhancer = ImageEnhance.Contrast(resized)
                    resized = enhancer.enhance(1.05)
                
                icon_images.append(resized)
                print(f"✅ 生成 {size[0]}x{size[1]} 图标")
            
            # 保存为ICO文件
            icon_images[0].save(
                ico_path,
                format='ICO',
                sizes=sizes,
                append_images=icon_images[1:]
            )
            
            file_size = os.path.getsize(ico_path)
            print(f"✅ 图标文件已保存: {ico_path} ({file_size} 字节)")
            return True
            
    except Exception as e:
        print(f"❌ 创建图标失败: {e}")
        return False

def setup_build_script():
    """创建简化的构建脚本"""
    build_script = '''@echo off
chcp 65001 >nul
echo ════════════════════════════════════════════
echo    命理AI聊天应用 - 构建脚本
echo ════════════════════════════════════════════
echo.

echo 🔄 开始构建Windows应用...
flutter build windows --release

if %errorlevel% equ 0 (
    echo.
    echo 🎉 构建成功！
    echo 📁 可执行文件: build\\windows\\x64\\runner\\Release\\numerology_ai_chat.exe
    echo 💡 图标已自动应用
) else (
    echo.
    echo ❌ 构建失败！
    echo 💡 请检查错误信息
)

echo.
pause
'''
    
    with open("build_app.bat", 'w', encoding='utf-8') as f:
        f.write(build_script)
    
    print("✅ 构建脚本已创建: build_app.bat")

def setup_run_script():
    """创建运行脚本"""
    run_script = '''@echo off
chcp 65001 >nul
echo ════════════════════════════════════════════
echo    命理AI聊天应用 - 开发运行
echo ════════════════════════════════════════════
echo.

echo 🔄 启动应用（开发模式）...
echo 💡 支持热重载，按 Ctrl+C 停止
echo.
flutter run -d windows
'''
    
    with open("run_app.bat", 'w', encoding='utf-8') as f:
        f.write(run_script)
    
    print("✅ 运行脚本已创建: run_app.bat")

def create_readme():
    """创建使用说明"""
    readme_content = '''# 应用图标配置说明

## 📋 图标配置

本项目已配置为自动使用 `assets/images/logo.png` 作为应用图标。

### 🔧 图标文件位置
- **源图标**: `assets/images/logo.png`
- **Windows图标**: `windows/runner/resources/app_icon.ico`

### 🚀 构建应用

#### 方法1: 使用脚本（推荐）
```bash
# 构建发布版本
build_app.bat

# 运行开发版本
run_app.bat
```

#### 方法2: 手动命令
```bash
# 构建发布版本
flutter build windows --release

# 运行开发版本
flutter run -d windows
```

### 📁 输出文件
构建完成后，可执行文件位于：
`build/windows/x64/runner/Release/numerology_ai_chat.exe`

### 🔄 更换图标
如需更换图标：
1. 替换 `assets/images/logo.png` 文件
2. 运行 `python setup_permanent_icon.py`
3. 重新构建应用

### 💡 注意事项
- 图标文件已集成到项目中，每次构建都会自动应用
- 建议使用正方形、高分辨率的图片作为源图标
- 支持JPG、PNG等常见格式

---
配置时间: 2025年1月2日
'''
    
    with open("图标使用说明.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 使用说明已创建: 图标使用说明.md")

def main():
    print("🔧 设置永久图标配置...")
    print("=" * 50)
    
    # 1. 创建高质量图标
    if not create_high_quality_ico():
        print("❌ 图标创建失败")
        return False
    
    # 2. 创建构建脚本
    print("\n📝 创建构建脚本...")
    setup_build_script()
    setup_run_script()
    
    # 3. 创建说明文档
    print("\n📚 创建使用说明...")
    create_readme()
    
    print("\n🎉 永久图标配置完成！")
    print("\n📝 接下来您可以：")
    print("1. 双击 build_app.bat 构建发布版本")
    print("2. 双击 run_app.bat 运行开发版本")
    print("3. 查看 图标使用说明.md 了解详细信息")
    
    print("\n💡 重要提示：")
    print("- 图标文件已永久集成到项目中")
    print("- 每次构建都会自动应用正确的图标")
    print("- 无需手动设置或重复配置")
    
    return True

if __name__ == "__main__":
    main()
