/// 消息发送者枚举
enum MessageSender {
  user('用户'),
  assistant('AI助手'),
  system('系统');

  const MessageSender(this.displayName);
  final String displayName;
}

/// 系统消息类型枚举
enum SystemMessageType {
  welcome('欢迎'),
  general('一般');

  const SystemMessageType(this.displayName);
  final String displayName;
}

/// 消息状态枚举
enum MessageStatus {
  sending('发送中'),
  sent('已发送'),
  delivered('已送达'),
  failed('发送失败'),
  typing('正在输入');

  const MessageStatus(this.displayName);
  final String displayName;
}

/// 消息类型枚举
enum MessageType {
  text('文本'),
  image('图片'),
  mixed('图文混合');

  const MessageType(this.displayName);
  final String displayName;
}

/// 图片附件模型
class ImageAttachment {
  final String id;
  final String? localPath;
  final String? base64Data;
  final String? mimeType;
  final int? fileSize;
  final int? width;
  final int? height;

  const ImageAttachment({
    required this.id,
    this.localPath,
    this.base64Data,
    this.mimeType,
    this.fileSize,
    this.width,
    this.height,
  });

  /// 从JSON创建图片附件
  factory ImageAttachment.fromJson(Map<String, dynamic> json) {
    return ImageAttachment(
      id: json['id'] as String,
      localPath: json['local_path'] as String?,
      base64Data: json['base64_data'] as String?,
      mimeType: json['mime_type'] as String?,
      fileSize: json['file_size'] as int?,
      width: json['width'] as int?,
      height: json['height'] as int?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'local_path': localPath,
      'base64_data': base64Data,
      'mime_type': mimeType,
      'file_size': fileSize,
      'width': width,
      'height': height,
    };
  }

  /// 复制并修改部分属性
  ImageAttachment copyWith({
    String? id,
    String? localPath,
    String? base64Data,
    String? mimeType,
    int? fileSize,
    int? width,
    int? height,
  }) {
    return ImageAttachment(
      id: id ?? this.id,
      localPath: localPath ?? this.localPath,
      base64Data: base64Data ?? this.base64Data,
      mimeType: mimeType ?? this.mimeType,
      fileSize: fileSize ?? this.fileSize,
      width: width ?? this.width,
      height: height ?? this.height,
    );
  }

  /// 是否有有效的图片数据
  bool get hasValidData => localPath != null || base64Data != null;

  /// 获取文件大小的可读格式
  String get fileSizeFormatted {
    if (fileSize == null) return '未知大小';
    final size = fileSize!;
    if (size < 1024) return '${size}B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)}KB';
    return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
  }
}

/// 聊天消息模型
class ChatMessage {
  final String id;
  final String content;
  final MessageSender sender;
  final MessageStatus status;
  final DateTime timestamp;
  final String? agentId;
  final String? baziData;
  final SystemMessageType? systemMessageType;
  final Map<String, dynamic>? metadata;
  final MessageType messageType;
  final List<ImageAttachment>? images;
  /// 大白话版本内容（仅用于显示，不进入历史对话）
  final String? laymanVersion;
  /// 是否为大白话显示消息（用于区分显示样式）
  final bool isLaymanDisplay;

  const ChatMessage({
    required this.id,
    required this.content,
    required this.sender,
    required this.status,
    required this.timestamp,
    this.agentId,
    this.baziData,
    this.systemMessageType,
    this.metadata,
    this.messageType = MessageType.text,
    this.images,
    this.laymanVersion,
    this.isLaymanDisplay = false,
  });

  /// 从JSON创建消息模型
  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'] as String,
      content: json['content'] as String,
      sender: MessageSender.values.firstWhere(
        (e) => e.name == json['sender'],
        orElse: () => MessageSender.user,
      ),
      status: MessageStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => MessageStatus.sent,
      ),
      timestamp: DateTime.parse(json['timestamp'] as String),
      agentId: json['agent_id'] as String?,
      baziData: json['bazi_data'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      messageType: MessageType.values.firstWhere(
        (e) => e.name == json['message_type'],
        orElse: () => MessageType.text,
      ),
      images: (json['images'] as List<dynamic>?)
          ?.map((e) => ImageAttachment.fromJson(e as Map<String, dynamic>))
          .toList(),
      laymanVersion: json['layman_version'] as String?,
      isLaymanDisplay: json['is_layman_display'] as bool? ?? false,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'sender': sender.name,
      'status': status.name,
      'timestamp': timestamp.toIso8601String(),
      'agent_id': agentId,
      'bazi_data': baziData,
      'metadata': metadata,
      'message_type': messageType.name,
      'images': images?.map((e) => e.toJson()).toList(),
      'layman_version': laymanVersion,
      'is_layman_display': isLaymanDisplay,
    };
  }

  /// 复制并修改部分属性
  ChatMessage copyWith({
    String? id,
    String? content,
    MessageSender? sender,
    MessageStatus? status,
    DateTime? timestamp,
    String? agentId,
    String? baziData,
    Map<String, dynamic>? metadata,
    MessageType? messageType,
    List<ImageAttachment>? images,
    String? laymanVersion,
    bool? isLaymanDisplay,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      content: content ?? this.content,
      sender: sender ?? this.sender,
      status: status ?? this.status,
      timestamp: timestamp ?? this.timestamp,
      agentId: agentId ?? this.agentId,
      baziData: baziData ?? this.baziData,
      metadata: metadata ?? this.metadata,
      messageType: messageType ?? this.messageType,
      images: images ?? this.images,
      laymanVersion: laymanVersion ?? this.laymanVersion,
      isLaymanDisplay: isLaymanDisplay ?? this.isLaymanDisplay,
    );
  }

  /// 是否为用户消息
  bool get isUser => sender == MessageSender.user;

  /// 是否为AI消息
  bool get isAssistant => sender == MessageSender.assistant;

  /// 是否为系统消息
  bool get isSystem => sender == MessageSender.system;

  /// 是否正在发送
  bool get isSending => status == MessageStatus.sending;

  /// 是否发送失败
  bool get isFailed => status == MessageStatus.failed;

  /// 是否正在输入
  bool get isTyping => status == MessageStatus.typing;

  /// 是否包含图片
  bool get hasImages => images != null && images!.isNotEmpty;

  /// 是否为纯文本消息
  bool get isTextOnly => messageType == MessageType.text && !hasImages;

  /// 是否为纯图片消息
  bool get isImageOnly => messageType == MessageType.image && content.trim().isEmpty;

  /// 是否为图文混合消息
  bool get isMixed => messageType == MessageType.mixed || (hasImages && content.trim().isNotEmpty);

  /// 是否有大白话版本
  bool get hasLaymanVersion => laymanVersion != null && laymanVersion!.isNotEmpty;

  /// 获取角色字符串（用于API调用）
  String get role {
    switch (sender) {
      case MessageSender.user:
        return 'user';
      case MessageSender.assistant:
        return 'assistant';
      case MessageSender.system:
        return 'system';
    }
  }

  /// 创建用户消息
  factory ChatMessage.user({
    required String content,
    String? agentId,
    String? baziData,
    Map<String, dynamic>? metadata,
    List<ImageAttachment>? images,
  }) {
    // 根据内容和图片自动确定消息类型
    MessageType messageType;
    if (images != null && images.isNotEmpty) {
      if (content.trim().isEmpty) {
        messageType = MessageType.image;
      } else {
        messageType = MessageType.mixed;
      }
    } else {
      messageType = MessageType.text;
    }

    return ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      sender: MessageSender.user,
      status: MessageStatus.sending,
      timestamp: DateTime.now(),
      agentId: agentId,
      baziData: baziData,
      metadata: metadata,
      messageType: messageType,
      images: images,
    );
  }

  /// 创建AI消息
  factory ChatMessage.assistant({
    required String content,
    String? agentId,
    MessageStatus status = MessageStatus.sent,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      sender: MessageSender.assistant,
      status: status,
      timestamp: DateTime.now(),
      agentId: agentId,
      metadata: metadata,
    );
  }

  /// 创建系统消息
  factory ChatMessage.system({
    required String content,
    SystemMessageType? systemMessageType,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      sender: MessageSender.system,
      status: MessageStatus.sent,
      timestamp: DateTime.now(),
      systemMessageType: systemMessageType,
      metadata: metadata,
    );
  }

  /// 创建正在输入的占位消息
  factory ChatMessage.typing({
    String? agentId,
  }) {
    return ChatMessage(
      id: 'typing_${DateTime.now().millisecondsSinceEpoch}',
      content: '',
      sender: MessageSender.assistant,
      status: MessageStatus.typing,
      timestamp: DateTime.now(),
      agentId: agentId,
    );
  }

  /// 创建用户图片消息
  factory ChatMessage.userWithImages({
    required List<ImageAttachment> images,
    String content = '',
    String? agentId,
    String? baziData,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessage.user(
      content: content,
      agentId: agentId,
      baziData: baziData,
      metadata: metadata,
      images: images,
    );
  }

  /// 创建大白话显示消息
  factory ChatMessage.laymanDisplay({
    required String content,
    String? agentId,
    MessageStatus status = MessageStatus.sent,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessage(
      id: 'layman_${DateTime.now().millisecondsSinceEpoch}',
      content: content,
      sender: MessageSender.assistant,
      status: status,
      timestamp: DateTime.now(),
      agentId: agentId,
      metadata: metadata,
      isLaymanDisplay: true,
    );
  }

  @override
  String toString() {
    return 'ChatMessage(id: $id, sender: $sender, status: $status, '
        'content: ${content.length > 50 ? '${content.substring(0, 50)}...' : content})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatMessage && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
