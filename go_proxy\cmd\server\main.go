package main

import (
	"log"
	"net/http"
	"time"

	"go_proxy/internal/api"
	"go_proxy/internal/config"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 设置Gin模式
	if cfg.ServerPort != "" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建路由
	router := api.NewRouter(cfg)

	// 配置服务器
	server := &http.Server{
		Addr:           ":" + cfg.ServerPort,
		Handler:        router,
		ReadTimeout:    60 * time.Second,  // 增加到60秒
		WriteTimeout:   600 * time.Second, // 增加到10分钟，支持长时间流式响应
		MaxHeaderBytes: 1 << 20, // 1MB
	}

	log.Printf("Go Proxy Server starting on port %s", cfg.ServerPort)
	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		log.Fatalf("Failed to start server: %v", err)
	}
}