/// API响应基础模型
class ApiResponse<T> {
  final int code;
  final String message;
  final T? data;

  const ApiResponse({
    required this.code,
    required this.message,
    this.data,
  });

  /// 从JSON创建API响应
  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic)? fromJsonT,
  ) {
    return ApiResponse<T>(
      code: json['code'] as int,
      message: json['message'] as String,
      data: json['data'] != null && fromJsonT != null
          ? fromJsonT(json['data'])
          : json['data'] as T?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson([Object? Function(T)? toJsonT]) {
    return {
      'code': code,
      'message': message,
      'data': data != null && toJsonT != null ? toJsonT(data as T) : data,
    };
  }

  /// 是否成功
  bool get isSuccess => code == 0;

  /// 是否失败
  bool get isFailure => !isSuccess;

  @override
  String toString() {
    return 'ApiResponse(code: $code, message: $message, data: $data)';
  }
}

/// 云函数响应数据模型
class CloudFunctionData<T> {
  final bool success;
  final String? code;
  final String message;
  final T? data;
  final String? requestId;
  final String? timestamp;

  const CloudFunctionData({
    required this.success,
    this.code,
    required this.message,
    this.data,
    this.requestId,
    this.timestamp,
  });

  /// 从JSON创建云函数响应数据
  factory CloudFunctionData.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic)? fromJsonT,
  ) {
    return CloudFunctionData<T>(
      success: json['success'] as bool,
      code: json['code'] as String?,
      message: json['message'] as String,
      data: json['data'] != null && fromJsonT != null
          ? fromJsonT(json['data'])
          : json['data'] as T?,
      requestId: json['requestId'] as String?,
      timestamp: json['timestamp'] as String?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson([Object? Function(T)? toJsonT]) {
    return {
      'success': success,
      if (code != null) 'code': code,
      'message': message,
      if (data != null) 'data': data != null && toJsonT != null ? toJsonT(data as T) : data,
      if (requestId != null) 'requestId': requestId,
      if (timestamp != null) 'timestamp': timestamp,
    };
  }

  @override
  String toString() {
    return 'CloudFunctionData(success: $success, code: $code, message: $message, data: $data)';
  }
}

/// 智能体列表响应
class AgentListResponse {
  final List<dynamic> agents;

  const AgentListResponse({
    required this.agents,
  });

  factory AgentListResponse.fromJson(Map<String, dynamic> json) {
    return AgentListResponse(
      agents: json['agents'] as List<dynamic>? ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'agents': agents,
    };
  }
}

/// 模型列表响应
class ModelListResponse {
  final List<dynamic> models;

  const ModelListResponse({
    required this.models,
  });

  factory ModelListResponse.fromJson(Map<String, dynamic> json) {
    return ModelListResponse(
      models: json['models'] as List<dynamic>? ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'models': models,
    };
  }
}

/// 登录响应数据
class LoginResponseData {
  final dynamic user;
  final Map<String, dynamic>? tokens;

  const LoginResponseData({
    required this.user,
    this.tokens,
  });

  factory LoginResponseData.fromJson(Map<String, dynamic> json) {
    return LoginResponseData(
      user: json['user'],
      tokens: json['tokens'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user': user,
      if (tokens != null) 'tokens': tokens,
    };
  }
}

/// Token信息
class TokenInfo {
  final String accessToken;
  final String? refreshToken;
  final String? expiresIn;

  const TokenInfo({
    required this.accessToken,
    this.refreshToken,
    this.expiresIn,
  });

  factory TokenInfo.fromJson(Map<String, dynamic> json) {
    return TokenInfo(
      accessToken: json['accessToken'] as String,
      refreshToken: json['refreshToken'] as String?,
      expiresIn: json['expiresIn'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'accessToken': accessToken,
      if (refreshToken != null) 'refreshToken': refreshToken,
      if (expiresIn != null) 'expiresIn': expiresIn,
    };
  }
}

/// 版本检查响应
class VersionCheckApiResponse {
  final Map<String, dynamic> data;

  const VersionCheckApiResponse({
    required this.data,
  });

  factory VersionCheckApiResponse.fromJson(Map<String, dynamic> json) {
    return VersionCheckApiResponse(
      data: json,
    );
  }

  Map<String, dynamic> toJson() {
    return data;
  }
}
