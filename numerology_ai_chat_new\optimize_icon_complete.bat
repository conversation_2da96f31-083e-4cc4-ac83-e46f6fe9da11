@echo off
chcp 65001 >nul
echo ════════════════════════════════════════════
echo    应用图标完整优化脚本
echo ════════════════════════════════════════════
echo.

echo 📋 本脚本将执行以下操作:
echo 1. 验证当前图标质量
echo 2. 生成超高质量图标 (15种尺寸)
echo 3. 运行Flutter图标生成器
echo 4. 验证优化后的图标质量
echo 5. 提供后续构建建议
echo.

pause

echo 🔍 步骤1: 验证当前图标质量...
python verify_icon_quality.py
echo.

echo 🚀 步骤2: 生成超高质量图标...
python generate_high_quality_icon.py
if %errorlevel% neq 0 (
    echo ❌ 超高质量图标生成失败！
    echo 💡 可能原因: 缺少PIL库或源图片问题
    echo 📝 解决方案: pip install Pillow
    pause
    exit /b 1
)
echo ✅ 超高质量图标生成完成
echo.

echo 🔄 步骤3: 运行Flutter图标生成器...
dart run flutter_launcher_icons
if %errorlevel% neq 0 (
    echo ❌ Flutter图标生成失败！
    pause
    exit /b 1
)
echo ✅ Flutter图标生成完成
echo.

echo 🔍 步骤4: 验证优化后的图标质量...
python verify_icon_quality.py
echo.

echo 🎉 图标优化完成！
echo ════════════════════════════════════════════
echo 📝 接下来的步骤:
echo.
echo 🔨 构建应用:
echo    build_with_icon.bat
echo.
echo 🏃 运行应用:
echo    run_with_icon.bat
echo.
echo 💡 提示:
echo - 图标已优化为超高质量，支持4K和高DPI显示
echo - 包含15种不同尺寸，适应所有显示场景
echo - 如果图标仍然模糊，请重启Windows资源管理器
echo.
pause
