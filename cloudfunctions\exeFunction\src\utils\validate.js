const Joi = require('joi')

/**
 * 用户注册参数校验
 */
const registerSchema = Joi.object({
  username: Joi.string()
    .alphanum()
    .min(8)
    .max(30)
    .required()
    .messages({
      'string.alphanum': '用户名只能包含数字和大小写英文字母',
      'string.min': '用户名至少8个字符',
      'string.max': '用户名最多30个字符',
      'any.required': '用户名不能为空'
    }),
  password: Joi.string()
    .min(8)
    .max(50)
    .pattern(/^[a-zA-Z0-9!@#$%^&*()_+\-=\[\]{};:"\\|,.<>\/?`~]+$/)
    .required()
    .messages({
      'string.min': '密码至少8个字符',
      'string.max': '密码最多50个字符',
      'string.pattern.base': '密码只能包含字母、数字和常规符号，不能包含中文字符',
      'any.required': '密码不能为空'
    }),
  email: Joi.string()
    .email()
    .optional()
    .messages({
      'string.email': '邮箱格式不正确'
    }),
  phone: Joi.string()
    .pattern(/^1[3-9]\d{9}$/)
    .optional()
    .messages({
      'string.pattern.base': '手机号格式不正确'
    }),
  activationCode: Joi.string()
    .optional()
    .allow('')
    .messages({
      'string.base': '激活码必须是字符串'
    })
})

/**
 * 用户登录参数校验
 */
const loginSchema = Joi.object({
  username: Joi.string()
    .min(1)
    .max(50)
    .required()
    .messages({
      'string.min': '用户名不能为空',
      'string.max': '用户名长度不能超过50个字符',
      'any.required': '用户名不能为空'
    }),
  password: Joi.string()
    .min(1)
    .max(100)
    .required()
    .messages({
      'string.min': '密码不能为空',
      'string.max': '密码长度不能超过100个字符',
      'any.required': '密码不能为空'
    })
})

/**
 * 刷新Token参数校验
 */
const refreshTokenSchema = Joi.object({
  refreshToken: Joi.string()
    .required()
    .messages({
      'any.required': 'refreshToken不能为空'
    })
})

/**
 * 鉴权参数校验
 */
const authSchema = Joi.object({
  authorization: Joi.string()
    .pattern(/^Bearer .+/)
    .required()
    .messages({
      'string.pattern.base': 'Authorization格式不正确，应为Bearer token',
      'any.required': 'Authorization不能为空'
    })
})

/**
 * 通用校验函数
 * @param {object} schema Joi校验模式
 * @param {object} data 待校验数据
 * @returns {object} 校验结果
 */
function validate(schema, data) {
  const { error, value } = schema.validate(data, {
    abortEarly: false, // 返回所有错误
    stripUnknown: true // 移除未知字段
  })
  
  if (error) {
    const errorMessage = error.details.map(detail => detail.message).join('; ')
    throw new Error(`参数校验失败: ${errorMessage}`)
  }
  
  return value
}

/**
 * 校验用户注册参数
 * @param {object} data 注册数据
 * @returns {object} 校验后的数据
 */
function validateRegister(data) {
  return validate(registerSchema, data)
}

/**
 * 校验用户登录参数
 * @param {object} data 登录数据
 * @returns {object} 校验后的数据
 */
function validateLogin(data) {
  return validate(loginSchema, data)
}

/**
 * 校验刷新Token参数
 * @param {object} data 刷新Token数据
 * @returns {object} 校验后的数据
 */
function validateRefreshToken(data) {
  return validate(refreshTokenSchema, data)
}

/**
 * 校验鉴权参数
 * @param {object} data 鉴权数据
 * @returns {object} 校验后的数据
 */
function validateAuth(data) {
  return validate(authSchema, data)
}

/**
 * 激活码验证参数校验
 */
const activationCodeSchema = Joi.object({
  activationCode: Joi.string()
    .required()
    .messages({
      'string.empty': '激活码不能为空',
      'any.required': '激活码是必填项'
    })
})

/**
 * 校验激活码参数
 * @param {object} data 激活码数据
 * @returns {object} 校验后的数据
 */
function validateActivationCode(data) {
  return validate(activationCodeSchema, data)
}

module.exports = {
  validate,
  validateRegister,
  validateLogin,
  validateRefreshToken,
  validateAuth,
  validateActivationCode,
  registerSchema,
  loginSchema,
  refreshTokenSchema,
  authSchema,
  activationCodeSchema
}