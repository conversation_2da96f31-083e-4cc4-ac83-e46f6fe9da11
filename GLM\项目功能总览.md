# 命理AI聊天系统项目功能总览

## 项目概述

命理AI聊天系统是一个综合性的命理分析与AI对话平台，由三个主要项目组成：
1. **numerology_ai_chat_new** - 前端Flutter应用
2. **go_proxy** - Go语言编写的后端代理服务
3. **cloudfunctions** - 云函数集合，提供核心业务逻辑

## 1. numerology_ai_chat_new项目功能

### 基本功能
- 基于Flutter开发的跨平台桌面应用
- 支持Windows、macOS和Linux系统
- 提供美观的用户界面和交互体验
- 支持命理分析与AI对话功能

### 核心模块
- **用户认证模块**：支持用户注册、登录和token管理
- **命理分析模块**：提供八字、合盘等命理分析功能
- **AI对话模块**：与多种AI模型进行智能对话
- **聊天记录管理**：保存和管理用户的聊天历史
- **系统设置模块**：个性化配置和偏好设置

### 技术特点
- 使用Flutter 3.7.2+开发
- 采用Provider和Riverpod进行状态管理
- 使用Hive进行本地数据存储
- 支持Markdown渲染
- 响应式UI设计，适应不同屏幕尺寸
- 自定义霞鹜文楷字体，提供优美的中文显示效果

### 特色功能
- **真太阳时计算**：精确的命理时间计算
- **大白话版命理解析**：将专业命理术语转化为通俗易懂的解释
- **多智能体支持**：提供不同专业领域的AI助手
- **图片上传与分析**：支持上传图片进行命理分析
- **直播模式**：适配直播场景的界面模式
- **版本自动更新**：支持应用自动检测和更新

## 2. go_proxy项目功能

### 基本功能
- 使用Go语言开发的后端代理服务
- 作为前端应用和云函数之间的中间层
- 提供API请求转发和响应处理
- 支持负载均衡和请求缓存

### 核心模块
- **API代理模块**：转发前端请求到相应的云函数
- **请求处理模块**：处理请求参数和响应数据
- **安全认证模块**：验证请求的合法性和安全性
- **日志记录模块**：记录请求和响应日志
- **健康检查模块**：提供服务状态监控

### 技术特点
- 高性能的Go语言实现
- 支持高并发请求处理
- 提供RESTful API接口
- 支持跨域请求处理
- 配置化管理，支持环境变量
- 完善的错误处理和日志记录

### 部署特性
- 支持Linux系统部署
- 提供系统服务配置
- 支持Docker容器化部署
- 提供自动化部署脚本
- 支持Nginx反向代理配置

## 3. cloudfunctions项目功能

### 基本功能
- 云函数集合，提供核心业务逻辑
- 分为不同功能的云函数模块
- 支持用户管理、内容管理、支付处理等功能

### 核心云函数模块

#### exeFunction云函数
- **用户认证功能**：
  - 用户注册：创建新用户账户
  - 用户登录：验证用户身份并返回token
  - Token刷新：更新过期的访问令牌

- **数据获取功能**：
  - 获取智能体列表：返回可用的AI助手
  - 获取模型列表：返回可用的AI模型
  - 获取用户信息：返回用户详细资料

- **使用次数管理**：
  - 更新使用次数：记录用户功能使用情况
  - 检查使用权限：验证用户是否有足够的使用次数

#### exeAdmin云函数
- **管理员功能**：
  - 管理员登录：验证管理员身份
  - 用户管理：查看、创建、更新用户信息
  - 会员管理：管理用户会员状态和权限

- **智能体管理**：
  - 智能体列表：查看所有AI助手
  - 创建智能体：添加新的AI助手
  - 更新智能体：修改AI助手配置
  - 删除智能体：移除不需要的AI助手

- **模型管理**：
  - 模型列表：查看所有AI模型
  - 创建模型：添加新的AI模型
  - 更新模型：修改模型配置
  - 删除模型：移除不需要的模型

- **页面配置管理**：
  - 页面列表：查看所有内容页面
  - 创建页面：添加新的内容页面
  - 更新页面：修改页面内容
  - 删除页面：移除不需要的页面

- **日志和统计**：
  - 查看日志：监控系统运行情况
  - 统计概览：提供系统使用数据统计

#### exeWeakAdmin云函数
- **弱管理员功能**：
  - 弱管理员登录：验证弱管理员身份
  - 有限权限的管理操作
  - 激活码系统管理

#### paymentCallback云函数
- **支付处理功能**：
  - 支付回调处理：处理支付平台的通知
  - 订单状态更新：更新订单支付状态
  - 会员权限更新：根据支付结果更新用户权限

### 技术特点
- 云函数架构，无需管理服务器
- 支持多种触发方式（HTTP调用、定时触发等）
- 数据库集成，支持数据持久化
- 安全认证和授权机制
- 完善的错误处理和日志记录
- 支持多种第三方服务集成

## 系统整体架构

### 数据流
1. 用户通过Flutter前端应用进行交互
2. 前端应用将请求发送到Go代理服务
3. Go代理服务验证请求并转发到相应的云函数
4. 云函数处理业务逻辑并访问数据库
5. 处理结果通过Go代理服务返回给前端应用

### 安全机制
- JWT Token认证
- API请求加密
- 敏感数据加密存储
- 访问权限控制
- 操作日志记录

### 扩展性
- 模块化设计，易于添加新功能
- 支持多种AI模型和智能体
- 可扩展的云函数架构
- 灵活的配置管理系统

## 未来发展方向

### 功能扩展
- 增加更多命理分析方法
- 引入更多专业领域的AI助手
- 开发移动端应用
- 增加社交功能

### 技术优化
- 提升系统性能和响应速度
- 优化用户体验
- 增强系统安全性
- 改进数据分析和统计功能

### 商业模式
- 会员订阅服务
- 高级功能付费
- 企业级解决方案
- API服务开放平台
