const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 订单管理处理器
 */
class OrderManagement {
  /**
   * 获取订单列表
   */
  static async listOrders(event) {
    try {
      const { page = 1, limit = 20, search, status, paymentMethod } = event;
      
      // 构建查询条件
      let query = {};
      
      if (search) {
        // 搜索订单号或用户ID
        query.$or = [
          { orderNo: { $regex: search, $options: 'i' } },
          { userId: { $regex: search, $options: 'i' } }
        ];
      }
      
      if (status) {
        query.status = status;
      }
      
      if (paymentMethod) {
        query.paymentMethod = paymentMethod;
      }
      
      // 计算跳过的记录数
      const skip = (page - 1) * limit;
      
      // 获取总数
      const totalResult = await db.collection('exe_purchase_orders')
        .where(query)
        .count();
      
      const total = totalResult.total;
      
      // 获取订单列表
      const ordersResult = await db.collection('exe_purchase_orders')
        .where(query)
        .orderBy('createTime', 'desc')
        .skip(skip)
        .limit(limit)
        .get();
      
      return {
        code: 0,
        message: '获取订单列表成功',
        data: {
          orders: ordersResult.data,
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      console.error('获取订单列表失败:', error);
      return {
        code: -1,
        message: '获取订单列表失败: ' + error.message
      };
    }
  }

  /**
   * 获取订单详情
   */
  static async getOrderDetail(event) {
    try {
      const { orderId } = event;
      
      if (!orderId) {
        return {
          code: -1,
          message: '订单ID不能为空'
        };
      }
      
      const orderResult = await db.collection('exe_purchase_orders')
        .doc(orderId)
        .get();
      
      if (!orderResult.data) {
        return {
          code: -1,
          message: '订单不存在'
        };
      }
      
      return {
        code: 0,
        message: '获取订单详情成功',
        data: orderResult.data
      };
    } catch (error) {
      console.error('获取订单详情失败:', error);
      return {
        code: -1,
        message: '获取订单详情失败: ' + error.message
      };
    }
  }

  /**
   * 更新订单状态
   */
  static async updateOrderStatus(event) {
    try {
      const { orderId, status } = event;
      
      if (!orderId || !status) {
        return {
          code: -1,
          message: '订单ID和状态不能为空'
        };
      }
      
      // 验证状态值
      const validStatuses = ['PENDING', 'COMPLETED', 'CANCELLED', 'EXPIRED', 'REFUNDED'];
      if (!validStatuses.includes(status)) {
        return {
          code: -1,
          message: '无效的订单状态'
        };
      }
      
      // 检查订单是否存在
      const orderResult = await db.collection('exe_purchase_orders')
        .doc(orderId)
        .get();
      
      if (!orderResult.data) {
        return {
          code: -1,
          message: '订单不存在'
        };
      }
      
      const currentOrder = orderResult.data;
      
      // 检查状态转换是否合法
      if (currentOrder.status === 'COMPLETED' && status !== 'REFUNDED') {
        return {
          code: -1,
          message: '已完成的订单只能转为退款状态'
        };
      }
      
      if (currentOrder.status === 'REFUNDED') {
        return {
          code: -1,
          message: '已退款的订单不能修改状态'
        };
      }
      
      // 更新订单状态
      const updateData = {
        status,
        updatedAt: new Date()
      };
      
      // 如果是取消或过期状态，清除支付相关信息
      if (status === 'CANCELLED' || status === 'EXPIRED') {
        updateData.payTime = null;
        updateData.fuiouPayTime = null;
      }
      
      await db.collection('exe_purchase_orders')
        .doc(orderId)
        .update(updateData);
      
      return {
        code: 0,
        message: '订单状态更新成功'
      };
    } catch (error) {
      console.error('更新订单状态失败:', error);
      return {
        code: -1,
        message: '更新订单状态失败: ' + error.message
      };
    }
  }

  /**
   * 获取订单统计信息
   */
  static async getOrderStats(event) {
    try {
      // 获取总订单数
      const totalOrdersResult = await db.collection('exe_purchase_orders').count();
      const totalOrders = totalOrdersResult.total;
      
      // 获取各状态订单数
      const pendingResult = await db.collection('exe_purchase_orders')
        .where({ status: 'PENDING' })
        .count();
      
      const completedResult = await db.collection('exe_purchase_orders')
        .where({ status: 'COMPLETED' })
        .count();
      
      const cancelledResult = await db.collection('exe_purchase_orders')
        .where({ status: 'CANCELLED' })
        .count();
      
      const expiredResult = await db.collection('exe_purchase_orders')
        .where({ status: 'EXPIRED' })
        .count();
      
      // 获取今日订单数
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayOrdersResult = await db.collection('exe_purchase_orders')
        .where({
          createTime: db.command.gte(today)
        })
        .count();
      
      // 获取总交易金额（已完成订单）
      const completedOrdersResult = await db.collection('exe_purchase_orders')
        .where({ status: 'COMPLETED' })
        .get();
      
      const totalAmount = completedOrdersResult.data.reduce((sum, order) => {
        return sum + (order.orderAmount || 0);
      }, 0);
      
      return {
        code: 0,
        message: '获取订单统计成功',
        data: {
          totalOrders,
          pendingOrders: pendingResult.total,
          completedOrders: completedResult.total,
          cancelledOrders: cancelledResult.total,
          expiredOrders: expiredResult.total,
          todayOrders: todayOrdersResult.total,
          totalAmount: totalAmount / 100 // 转换为元
        }
      };
    } catch (error) {
      console.error('获取订单统计失败:', error);
      return {
        code: -1,
        message: '获取订单统计失败: ' + error.message
      };
    }
  }
}

module.exports = OrderManagement;
