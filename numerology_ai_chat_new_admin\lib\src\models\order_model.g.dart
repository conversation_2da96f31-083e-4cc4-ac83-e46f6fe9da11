// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderModel _$OrderModelFromJson(Map<String, dynamic> json) => OrderModel(
      id: json['_id'] as String,
      orderNo: json['orderNo'] as String,
      userId: json['userId'] as String,
      packageId: json['packageId'] as String,
      packageName: json['packageName'] as String,
      packageQuota: (json['packageQuota'] as num).toInt(),
      quantity: (json['quantity'] as num).toInt(),
      unitPrice: (json['unitPrice'] as num).toInt(),
      unitQuota: (json['unitQuota'] as num).toInt(),
      orderAmount: (json['orderAmount'] as num).toInt(),
      paymentMethod: json['paymentMethod'] as String,
      status: json['status'] as String,
      transactionId: json['transactionId'] as String?,
      fuiouOrderId: json['fuiouOrderId'] as String?,
      fuiouPayAmount: json['fuiouPayAmount'] as String?,
      fuiouPayTime: json['fuiouPayTime'] as String?,
      fuiouPayType: json['fuiouPayType'] as String?,
      paymentUrl: json['paymentUrl'] as String,
      qrCodeData: json['qrCodeData'] as String,
      createTime: DateTime.parse(json['createTime'] as String),
      expireTime: DateTime.parse(json['expireTime'] as String),
      payTime: json['payTime'] == null
          ? null
          : DateTime.parse(json['payTime'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$OrderModelToJson(OrderModel instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'orderNo': instance.orderNo,
      'userId': instance.userId,
      'packageId': instance.packageId,
      'packageName': instance.packageName,
      'packageQuota': instance.packageQuota,
      'quantity': instance.quantity,
      'unitPrice': instance.unitPrice,
      'unitQuota': instance.unitQuota,
      'orderAmount': instance.orderAmount,
      'paymentMethod': instance.paymentMethod,
      'status': instance.status,
      'transactionId': instance.transactionId,
      'fuiouOrderId': instance.fuiouOrderId,
      'fuiouPayAmount': instance.fuiouPayAmount,
      'fuiouPayTime': instance.fuiouPayTime,
      'fuiouPayType': instance.fuiouPayType,
      'paymentUrl': instance.paymentUrl,
      'qrCodeData': instance.qrCodeData,
      'createTime': instance.createTime.toIso8601String(),
      'expireTime': instance.expireTime.toIso8601String(),
      'payTime': instance.payTime?.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };
