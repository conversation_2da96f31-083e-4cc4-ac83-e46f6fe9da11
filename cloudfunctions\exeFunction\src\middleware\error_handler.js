const logger = require('../utils/logger')

/**
 * 错误码定义
 */
const ERROR_CODES = {
  // 通用错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  INVALID_PARAMS: 'INVALID_PARAMS',
  INVALID_PARAM: 'INVALID_PARAM',

  // 认证错误
  UNAUTHORIZED: 'UNAUTHORIZED',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',

  // 用户错误
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  USER_ALREADY_EXISTS: 'USER_ALREADY_EXISTS',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  USER_DISABLED: 'USER_DISABLED',

  // 会员错误
  MEMBERSHIP_EXPIRED: 'MEMBERSHIP_EXPIRED',
  INSUFFICIENT_QUOTA: 'INSUFFICIENT_QUOTA',

  // 订单错误
  ORDER_NOT_FOUND: 'ORDER_NOT_FOUND',
  INVALID_ORDER_STATUS: 'INVALID_ORDER_STATUS',

  // 套餐错误
  PACKAGE_NOT_FOUND: 'PACKAGE_NOT_FOUND',
  PACKAGE_NOT_AVAILABLE: 'PACKAGE_NOT_AVAILABLE',
  INVALID_QUANTITY: 'INVALID_QUANTITY',

  // 数据库错误
  DATABASE_ERROR: 'DATABASE_ERROR',
  UPDATE_FAILED: 'UPDATE_FAILED',

  // 激活码错误
  ACTIVATION_CODE_INVALID: 'ACTIVATION_CODE_INVALID',
  ACTIVATION_CODE_USED: 'ACTIVATION_CODE_USED',

  // 验证错误
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  TOKEN_MISSING: 'TOKEN_MISSING',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS'
}

/**
 * 自定义业务异常类
 */
class BusinessError extends Error {
  constructor(code, message, statusCode = 400) {
    super(message)
    this.name = 'BusinessError'
    this.code = code
    this.statusCode = statusCode
  }
}

/**
 * 统一错误处理函数
 * @param {Error} error 错误对象
 * @returns {object} 标准化的错误响应
 */
function errorHandler(error) {
  logger.error('Error occurred', {
    name: error.name,
    message: error.message,
    stack: error.stack,
    code: error.code
  })
  
  // 业务异常
  if (error instanceof BusinessError) {
    return {
      success: false,
      code: error.code,
      message: error.message,
      statusCode: error.statusCode
    }
  }
  
  // 参数校验错误
  if (error.message && error.message.includes('参数校验失败')) {
    return {
      success: false,
      code: ERROR_CODES.INVALID_PARAMS,
      message: error.message,
      statusCode: 400
    }
  }
  
  // JWT相关错误
  if (error.name === 'JsonWebTokenError') {
    return {
      success: false,
      code: ERROR_CODES.TOKEN_INVALID,
      message: 'Token无效',
      statusCode: 401
    }
  }
  
  if (error.name === 'TokenExpiredError') {
    return {
      success: false,
      code: ERROR_CODES.TOKEN_EXPIRED,
      message: 'Token已过期',
      statusCode: 401
    }
  }
  
  // 数据库错误
  if (error.message && error.message.includes('database')) {
    return {
      success: false,
      code: ERROR_CODES.DATABASE_ERROR,
      message: '数据库操作失败',
      statusCode: 500
    }
  }
  
  // 未知错误
  return {
    success: false,
    code: ERROR_CODES.UNKNOWN_ERROR,
    message: '服务器内部错误',
    statusCode: 500
  }
}

/**
 * 创建业务异常
 * @param {string} code 错误码
 * @param {string} message 错误消息
 * @param {number} statusCode HTTP状态码
 * @returns {BusinessError} 业务异常实例
 */
function createBusinessError(code, message, statusCode = 400) {
  return new BusinessError(code, message, statusCode)
}

/**
 * 成功响应格式化
 * @param {any} data 响应数据
 * @param {string} message 响应消息
 * @returns {object} 标准化的成功响应
 */
function successResponse(data = null, message = '操作成功') {
  return {
    success: true,
    message,
    data,
    timestamp: new Date().toISOString()
  }
}

module.exports = {
  errorHandler,
  BusinessError,
  createBusinessError,
  successResponse,
  ERROR_CODES
}