#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试系统配置功能
验证从数据库动态获取Go代理API地址的功能
"""

import requests
import json

def test_get_go_proxy_api_url():
    """测试获取Go代理API地址"""
    print("=== 测试获取Go代理API地址 ===")
    
    url = "https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction"
    
    payload = {
        "action": "getGoProxyApiUrl"
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, json=payload, headers=headers, timeout=10)
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {response.headers}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('code') == 0:
                config_value = result.get('data', {}).get('configValue')
                print(f"✅ 成功获取Go代理API地址: {config_value}")
                return config_value
            else:
                print(f"❌ 云函数返回错误: {result.get('message')}")
                return None
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"错误内容: {response.text}")
            return None
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return None
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误")
        return None
    except Exception as e:
        print(f"❌ 发生错误: {str(e)}")
        return None

def test_get_all_system_config():
    """测试获取所有系统配置"""
    print("\n=== 测试获取所有系统配置 ===")
    
    url = "https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction"
    
    payload = {
        "action": "getSystemConfig"
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, json=payload, headers=headers, timeout=10)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('code') == 0:
                configs = result.get('data', {}).get('configs', [])
                print(f"✅ 成功获取 {len(configs)} 个系统配置")
                for config in configs:
                    print(f"  - {config.get('configKey')}: {config.get('configValue')} ({config.get('description')})")
                return configs
            else:
                print(f"❌ 云函数返回错误: {result.get('message')}")
                return None
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"错误内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 发生错误: {str(e)}")
        return None

def test_go_proxy_health_with_dynamic_url(api_url):
    """使用动态获取的URL测试Go代理服务健康状态"""
    print(f"\n=== 测试Go代理服务健康状态 (使用动态URL: {api_url}) ===")
    
    if not api_url:
        print("❌ 没有有效的API地址")
        return False
    
    # 去掉/api后缀，添加/healthz
    health_url = api_url.replace('/api', '') + '/healthz'
    
    try:
        response = requests.get(health_url, timeout=5)
        
        print(f"健康检查URL: {health_url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Go代理服务健康状态正常")
            return True
        else:
            print(f"❌ Go代理服务健康检查失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 健康检查失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("开始测试系统配置功能...")
    
    # 测试获取Go代理API地址
    api_url = test_get_go_proxy_api_url()
    
    # 测试获取所有系统配置
    test_get_all_system_config()
    
    # 使用动态获取的URL测试Go代理服务
    test_go_proxy_health_with_dynamic_url(api_url)
    
    print("\n=== 测试完成 ===")
    
    if api_url:
        print(f"✅ 系统配置功能正常，Go代理API地址: {api_url}")
    else:
        print("❌ 系统配置功能异常")

if __name__ == "__main__":
    main()
