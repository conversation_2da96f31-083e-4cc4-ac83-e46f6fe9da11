#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

# API基础URL
BASE_URL = "https://cloud1-8g3yh69faf07fd82-**********.ap-shanghai.app.tcloudbase.com/exeAdmin"

def test_admin_login():
    """测试管理员登录"""
    print("=== 测试管理员登录 ===")
    
    payload = {
        "action": "adminLogin",
        "adminAccount": "admin",
        "adminPassword": "123456"
    }
    
    response = requests.post(BASE_URL, json=payload)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.text}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('code') == 0:
            token = data['data']['data']['tokens']['accessToken']
            print(f"登录成功，Token: {token[:50]}...")
            return token
        else:
            print(f"登录失败: {data.get('message')}")
            return None
    else:
        print("请求失败")
        return None

def test_get_page_list(token):
    """测试获取页面列表"""
    print("\n=== 测试获取页面列表 ===")
    
    payload = {
        "action": "listPages"
    }
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.post(BASE_URL, json=payload, headers=headers)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.text}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('code') == 0:
            pages = data['data']['data']['pages']
            print(f"获取到 {len(pages)} 个页面")
            for page in pages:
                print(f"- {page['pageTitle']} ({page['pageType']}) - {page['slug']}")
            return True
        else:
            print(f"获取页面列表失败: {data.get('message')}")
            return False
    else:
        print("请求失败")
        return False

def test_create_page(token):
    """测试创建页面"""
    print("\n=== 测试创建页面 ===")
    
    payload = {
        "action": "createPage",
        "pageTitle": "测试页面",
        "pageContent": "# 测试页面\n\n这是一个测试页面的内容。",
        "pageType": "帮助",
        "slug": "test-page",
        "isActive": True,
        "sortOrder": 99,
        "metaDescription": "这是一个测试页面",
        "keywords": ["测试", "页面"]
    }
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.post(BASE_URL, json=payload, headers=headers)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.text}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('code') == 0:
            print("页面创建成功")
            return True
        else:
            print(f"页面创建失败: {data.get('message')}")
            return False
    else:
        print("请求失败")
        return False

def test_get_system_stats(token):
    """测试获取系统统计"""
    print("\n=== 测试获取系统统计 ===")

    payload = {
        "action": "stats"
    }

    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    response = requests.post(BASE_URL, json=payload, headers=headers)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.text}")

    if response.status_code == 200:
        data = response.json()
        if data.get('code') == 0:
            stats = data['data']['data']['overview']
            print("获取系统统计成功:")
            print(f"- 用户统计: 总数 {stats['users']['total']}, 活跃 {stats['users']['active']}")
            print(f"- 智能体统计: 总数 {stats['agents']['total']}, 活跃 {stats['agents']['active']}")
            print(f"- 模型统计: 总数 {stats['models']['total']}, 活跃 {stats['models']['active']}")
            print(f"- 页面统计: 总数 {stats['pages']['total']}, 活跃 {stats['pages']['active']}")
            return True
        else:
            print(f"获取系统统计失败: {data.get('message')}")
            return False
    else:
        print("请求失败")
        return False

def test_get_system_configs(token):
    """测试获取系统配置列表"""
    print("\n=== 测试获取系统配置列表 ===")

    payload = {
        "action": "listSystemConfigs"
    }

    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    response = requests.post(BASE_URL, json=payload, headers=headers)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.text}")

    if response.status_code == 200:
        data = response.json()
        if data.get('code') == 0:
            configs = data['data']['data']['configs']
            print(f"获取到 {len(configs)} 个配置")
            for config in configs:
                config_key = config.get('configKey', '未知')
                config_value = config.get('configValue', '未知')
                category = config.get('category', '未知')
                print(f"- {config_key}: {config_value} ({category})")
            return True
        else:
            print(f"获取系统配置列表失败: {data.get('message')}")
            return False
    else:
        print("请求失败")
        return False

def test_create_system_config(token):
    """测试创建系统配置"""
    print("\n=== 测试创建系统配置 ===")

    payload = {
        "action": "createSystemConfig",
        "configKey": "test_config",
        "configValue": "test_value",
        "configType": "string",
        "description": "这是一个测试配置",
        "isActive": True,
        "category": "系统参数"
    }

    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    response = requests.post(BASE_URL, json=payload, headers=headers)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.text}")

    if response.status_code == 200:
        data = response.json()
        if data.get('code') == 0:
            print("系统配置创建成功")
            return True
        else:
            print(f"系统配置创建失败: {data.get('message')}")
            return False
    else:
        print("请求失败")
        return False

def test_get_user_list(token):
    """测试获取用户列表"""
    print("\n=== 测试获取用户列表 ===")

    payload = {
        "action": "listUsers"
    }

    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    response = requests.post(BASE_URL, json=payload, headers=headers)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.text}")

    if response.status_code == 200:
        data = response.json()
        if data.get('code') == 0:
            print("获取用户列表成功")
            return True
        else:
            print(f"获取用户列表失败: {data.get('message')}")
            return False
    else:
        print("请求失败")
        return False

def test_get_agent_list(token):
    """测试获取智能体列表"""
    print("\n=== 测试获取智能体列表 ===")

    payload = {
        "action": "listAgents"
    }

    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    response = requests.post(BASE_URL, json=payload, headers=headers)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.text}")

    if response.status_code == 200:
        data = response.json()
        if data.get('code') == 0:
            print("获取智能体列表成功")
            return True
        else:
            print(f"获取智能体列表失败: {data.get('message')}")
            return False
    else:
        print("请求失败")
        return False

def test_get_model_list(token):
    """测试获取模型列表"""
    print("\n=== 测试获取模型列表 ===")

    payload = {
        "action": "listModels"
    }

    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    response = requests.post(BASE_URL, json=payload, headers=headers)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.text}")

    if response.status_code == 200:
        data = response.json()
        if data.get('code') == 0:
            print("获取模型列表成功")
            return True
        else:
            print(f"获取模型列表失败: {data.get('message')}")
            return False
    else:
        print("请求失败")
        return False

def test_get_package_list(token):
    """测试获取套餐列表"""
    print("\n=== 测试获取套餐列表 ===")

    payload = {
        "action": "listPackages"
    }

    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    response = requests.post(BASE_URL, json=payload, headers=headers)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.text}")

    if response.status_code == 200:
        data = response.json()
        if data.get('code') == 0:
            print("获取套餐列表成功")
            return True
        else:
            print(f"获取套餐列表失败: {data.get('message')}")
            return False
    else:
        print("请求失败")
        return False

def test_create_package(token):
    """测试创建套餐"""
    print("\n=== 测试创建套餐 ===")

    payload = {
        "action": "createPackage",
        "packageName": "测试套餐",
        "packageDescription": "这是一个测试套餐，包含基础算力",
        "originalPrice": 100.0,
        "currentPrice": 88.0,
        "quotaAmount": 1000,
        "isActive": True,
        "sortOrder": 1,
        "promotionText": "限时优惠"
    }

    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    response = requests.post(BASE_URL, json=payload, headers=headers)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.text}")

    if response.status_code == 200:
        data = response.json()
        if data.get('code') == 0:
            print("创建套餐成功")
            return data.get('data', {}).get('packageId')
        else:
            print(f"创建套餐失败: {data.get('message')}")
            return None
    else:
        print("请求失败")
        return None

def main():
    print("开始测试管理后台API...")

    # 1. 测试登录
    token = test_admin_login()
    if not token:
        print("登录失败，无法继续测试")
        return

    # 重点测试套餐管理和系统配置
    print("\n=== 重点测试套餐管理和系统配置 ===")

    # 测试套餐管理
    print("\n--- 套餐管理测试 ---")
    test_get_package_list(token)

    # 测试系统配置
    print("\n--- 系统配置测试 ---")
    test_get_system_configs(token)

    print("\n测试完成！")

if __name__ == "__main__":
    main()
