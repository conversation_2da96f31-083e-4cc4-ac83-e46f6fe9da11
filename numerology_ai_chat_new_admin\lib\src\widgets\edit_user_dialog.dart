
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:numerology_ai_chat_admin/src/models/user_model.dart';
import 'package:numerology_ai_chat_admin/src/providers/user_provider.dart';

class EditUserDialog extends ConsumerWidget {
  final User user;
  const EditUserDialog({super.key, required this.user});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final TextEditingController quotaController = TextEditingController(text: user.availableCount.toString());

    return AlertDialog(
      title: Text('修改用户算力'),
      content: TextField(
        controller: quotaController,
        keyboardType: TextInputType.number,
        decoration: const InputDecoration(labelText: '可用算力'),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: () {
            final newQuota = int.tryParse(quotaController.text);
            if (newQuota != null) {
              ref.read(userProvider.notifier).updateUserQuota(user.id, newQuota);
              Navigator.of(context).pop();
            }
          },
          child: const Text('保存'),
        ),
      ],
    );
  }
}
