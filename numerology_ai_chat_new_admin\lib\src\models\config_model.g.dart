// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'config_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SystemConfig _$SystemConfigFromJson(Map<String, dynamic> json) => SystemConfig(
      id: json['_id'] as String,
      configKey: json['configKey'] as String,
      configValue: json['configValue'] as String,
      configType: json['configType'] as String,
      description: json['description'] as String,
      isActive: json['isActive'] as bool,
      category: json['category'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      createdBy: json['createdBy'] as String?,
      updatedBy: json['updatedBy'] as String?,
    );

Map<String, dynamic> _$SystemConfigToJson(SystemConfig instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'configKey': instance.configKey,
      'configValue': instance.configValue,
      'configType': instance.configType,
      'description': instance.description,
      'isActive': instance.isActive,
      'category': instance.category,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'createdBy': instance.createdBy,
      'updatedBy': instance.updatedBy,
    };

CreateConfigRequest _$CreateConfigRequestFromJson(Map<String, dynamic> json) =>
    CreateConfigRequest(
      configKey: json['configKey'] as String,
      configValue: json['configValue'] as String,
      configType: json['configType'] as String,
      description: json['description'] as String,
      isActive: json['isActive'] as bool? ?? true,
      category: json['category'] as String,
    );

Map<String, dynamic> _$CreateConfigRequestToJson(
        CreateConfigRequest instance) =>
    <String, dynamic>{
      'configKey': instance.configKey,
      'configValue': instance.configValue,
      'configType': instance.configType,
      'description': instance.description,
      'isActive': instance.isActive,
      'category': instance.category,
    };

UpdateConfigRequest _$UpdateConfigRequestFromJson(Map<String, dynamic> json) =>
    UpdateConfigRequest(
      configValue: json['configValue'] as String?,
      configType: json['configType'] as String?,
      description: json['description'] as String?,
      isActive: json['isActive'] as bool?,
      category: json['category'] as String?,
    );

Map<String, dynamic> _$UpdateConfigRequestToJson(
        UpdateConfigRequest instance) =>
    <String, dynamic>{
      'configValue': instance.configValue,
      'configType': instance.configType,
      'description': instance.description,
      'isActive': instance.isActive,
      'category': instance.category,
    };
