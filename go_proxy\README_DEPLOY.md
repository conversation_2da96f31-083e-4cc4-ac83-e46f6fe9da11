# Go Proxy 部署指南

## 快速部署

### 1. 上传文件到服务器
将以下文件上传到服务器目录（如 `/opt/go_proxy/`）：
- `go_proxy_linux` - 编译好的可执行文件
- `.env` - 配置文件
- `deploy.sh` - 部署脚本
- `go_proxy.service` - 系统服务配置（可选）

### 2. 设置权限和运行
```bash
# 进入部署目录
cd /opt/go_proxy/

# 设置执行权限
chmod +x go_proxy_linux deploy.sh

# 运行部署脚本
./deploy.sh
```

### 3. 启动服务

#### 方式一：直接运行
```bash
# 前台运行（测试用）
./go_proxy_linux

# 后台运行
nohup ./go_proxy_linux > go_proxy.log 2>&1 &
```

#### 方式二：系统服务（推荐）
```bash
# 修改 go_proxy.service 中的路径
sudo nano go_proxy.service

# 复制服务文件
sudo cp go_proxy.service /etc/systemd/system/

# 重载系统服务
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start go_proxy

# 设置开机自启
sudo systemctl enable go_proxy

# 查看服务状态
sudo systemctl status go_proxy
```

## 配置说明

### 环境变量配置 (.env)
```bash
# 服务端口
SERVER_PORT=8080

# 云函数地址
CLOUDFUNC_BASE_URL=https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction

# AES密钥（与云函数保持一致）
AES_SECRET_KEY=your-aes-secret-key-32-chars-long
```

### 防火墙配置
```bash
# Ubuntu/Debian
sudo ufw allow 8080

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --reload
```

## 服务管理

### 查看日志
```bash
# 查看实时日志
tail -f go_proxy.log

# 查看系统服务日志
sudo journalctl -u go_proxy -f
```

### 重启服务
```bash
# 直接运行方式
pkill -f go_proxy_linux
nohup ./go_proxy_linux > go_proxy.log 2>&1 &

# 系统服务方式
sudo systemctl restart go_proxy
```

### 停止服务
```bash
# 直接运行方式
pkill -f go_proxy_linux

# 系统服务方式
sudo systemctl stop go_proxy
```

## 健康检查

访问健康检查端点：
```bash
curl http://your-server-ip:8080/healthz
```

应该返回：
```json
{
  "status": "ok",
  "timestamp": "2024-06-18T15:30:00Z"
}
```

## 故障排除

### 1. 端口被占用
```bash
# 查看端口占用
sudo netstat -tuln | grep 8080
sudo lsof -i :8080

# 杀死占用进程
sudo kill -9 <PID>
```

### 2. 权限问题
```bash
# 确保文件有执行权限
chmod +x go_proxy_linux

# 确保用户有读写权限
chown -R $USER:$USER /opt/go_proxy/
```

### 3. 配置问题
检查 `.env` 文件是否存在且配置正确，特别是云函数地址。

## 性能优化

### 1. 系统限制
```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf
```

### 2. 反向代理（可选）
可以使用 Nginx 作为反向代理：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```
