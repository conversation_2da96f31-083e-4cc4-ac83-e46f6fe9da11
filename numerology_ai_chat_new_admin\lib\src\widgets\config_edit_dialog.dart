import 'package:flutter/material.dart';
import 'package:numerology_ai_chat_admin/src/models/config_model.dart';

class ConfigEditDialog extends StatefulWidget {
  final SystemConfig? config;
  final Function(dynamic) onSave;

  const ConfigEditDialog({
    super.key,
    this.config,
    required this.onSave,
  });

  @override
  State<ConfigEditDialog> createState() => _ConfigEditDialogState();
}

class _ConfigEditDialogState extends State<ConfigEditDialog> {
  final _formKey = GlobalKey<FormState>();
  final _configKeyController = TextEditingController();
  final _configValueController = TextEditingController();
  final _descriptionController = TextEditingController();

  String _selectedConfigType = 'string';
  String _selectedCategory = 'API配置';
  bool _isActive = true;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.config != null) {
      _configKeyController.text = widget.config!.configKey;
      _configValueController.text = widget.config!.configValue;
      _descriptionController.text = widget.config!.description;
      _selectedConfigType = widget.config!.configType;
      _selectedCategory = widget.config!.category;
      _isActive = widget.config!.isActive;
    }
  }

  @override
  void dispose() {
    _configKeyController.dispose();
    _configValueController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.6,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Row(
              children: [
                Text(
                  widget.config == null ? '新建配置' : '编辑配置',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // 表单内容
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 配置键
                      TextFormField(
                        controller: _configKeyController,
                        decoration: const InputDecoration(
                          labelText: '配置键 *',
                          border: OutlineInputBorder(),
                          helperText: '唯一标识符，如: api_base_url',
                        ),
                        enabled: widget.config == null, // 编辑时不允许修改配置键
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return '请输入配置键';
                          }
                          if (!RegExp(r'^[a-z0-9_]+$').hasMatch(value.trim())) {
                            return '只能包含小写字母、数字和下划线';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      // 配置类型和分类行
                      Row(
                        children: [
                          // 配置类型
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              value: _selectedConfigType,
                              decoration: const InputDecoration(
                                labelText: '配置类型',
                                border: OutlineInputBorder(),
                              ),
                              items: const [
                                DropdownMenuItem(value: 'string', child: Text('字符串')),
                                DropdownMenuItem(value: 'number', child: Text('数字')),
                                DropdownMenuItem(value: 'boolean', child: Text('布尔值')),
                                DropdownMenuItem(value: 'json', child: Text('JSON')),
                                DropdownMenuItem(value: 'url', child: Text('URL')),
                              ],
                              onChanged: (value) {
                                setState(() {
                                  _selectedConfigType = value!;
                                });
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          // 配置分类
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              value: _selectedCategory,
                              decoration: const InputDecoration(
                                labelText: '配置分类',
                                border: OutlineInputBorder(),
                              ),
                              items: const [
                                DropdownMenuItem(value: 'API配置', child: Text('API配置')),
                                DropdownMenuItem(value: '系统参数', child: Text('系统参数')),
                                DropdownMenuItem(value: '业务配置', child: Text('业务配置')),
                                DropdownMenuItem(value: '界面配置', child: Text('界面配置')),
                              ],
                              onChanged: (value) {
                                setState(() {
                                  _selectedCategory = value!;
                                });
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          // 状态开关
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('状态'),
                              const SizedBox(height: 8),
                              Switch(
                                value: _isActive,
                                onChanged: (value) {
                                  setState(() {
                                    _isActive = value;
                                  });
                                },
                              ),
                              Text(
                                _isActive ? '启用' : '禁用',
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      // 配置描述
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: '配置描述 *',
                          border: OutlineInputBorder(),
                          helperText: '描述此配置的用途',
                        ),
                        maxLines: 2,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return '请输入配置描述';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      // 配置值
                      const Text(
                        '配置值 *',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                      ),
                      const SizedBox(height: 8),
                      _buildConfigValueField(),
                      const SizedBox(height: 16),
                      // 配置说明
                      _buildConfigTypeHelp(),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            // 操作按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _isLoading ? null : _handleSave,
                  child: _isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Text(widget.config == null ? '创建' : '保存'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigValueField() {
    switch (_selectedConfigType) {
      case 'boolean':
        return DropdownButtonFormField<String>(
          value: _configValueController.text.isEmpty ? 'true' : _configValueController.text,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
          ),
          items: const [
            DropdownMenuItem(value: 'true', child: Text('true')),
            DropdownMenuItem(value: 'false', child: Text('false')),
          ],
          onChanged: (value) {
            _configValueController.text = value!;
          },
        );
      case 'json':
        return Container(
          height: 150,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(4),
          ),
          child: TextFormField(
            controller: _configValueController,
            decoration: const InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(12),
              hintText: '请输入JSON格式的配置值...',
            ),
            maxLines: null,
            expands: true,
            textAlignVertical: TextAlignVertical.top,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return '请输入配置值';
              }
              return null;
            },
          ),
        );
      default:
        return TextFormField(
          controller: _configValueController,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            hintText: '请输入配置值...',
          ),
          keyboardType: _selectedConfigType == 'number' ? TextInputType.number : TextInputType.text,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入配置值';
            }
            if (_selectedConfigType == 'number' && double.tryParse(value) == null) {
              return '请输入有效的数字';
            }
            if (_selectedConfigType == 'url') {
              final uri = Uri.tryParse(value);
              if (uri == null || !uri.hasAbsolutePath) {
                return '请输入有效的URL';
              }
            }
            return null;
          },
        );
    }
  }

  Widget _buildConfigTypeHelp() {
    String helpText;
    switch (_selectedConfigType) {
      case 'string':
        helpText = '字符串类型：普通文本值';
        break;
      case 'number':
        helpText = '数字类型：整数或小数';
        break;
      case 'boolean':
        helpText = '布尔类型：true 或 false';
        break;
      case 'json':
        helpText = 'JSON类型：JSON格式的对象或数组';
        break;
      case 'url':
        helpText = 'URL类型：完整的URL地址';
        break;
      default:
        helpText = '';
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.blue.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: Colors.blue, size: 16),
          const SizedBox(width: 8),
          Text(
            helpText,
            style: TextStyle(color: Colors.blue[700], fontSize: 12),
          ),
        ],
      ),
    );
  }

  void _handleSave() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      if (widget.config == null) {
        // 创建新配置
        final request = CreateConfigRequest(
          configKey: _configKeyController.text.trim(),
          configValue: _configValueController.text.trim(),
          configType: _selectedConfigType,
          description: _descriptionController.text.trim(),
          isActive: _isActive,
          category: _selectedCategory,
        );
        await widget.onSave(request);
      } else {
        // 更新配置
        final request = UpdateConfigRequest(
          configValue: _configValueController.text.trim(),
          configType: _selectedConfigType,
          description: _descriptionController.text.trim(),
          isActive: _isActive,
          category: _selectedCategory,
        );
        await widget.onSave(request);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
