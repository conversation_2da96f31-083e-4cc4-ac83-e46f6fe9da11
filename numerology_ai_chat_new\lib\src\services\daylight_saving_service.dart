/// 夏令时期间信息
class DaylightSavingPeriod {
  final int startYear;
  final int endYear;
  final int startMonth;
  final int startDay;
  final int endMonth;
  final int endDay;
  final String region;
  final String periodStr;

  const DaylightSavingPeriod({
    required this.startYear,
    required this.endYear,
    required this.startMonth,
    required this.startDay,
    required this.endMonth,
    required this.endDay,
    required this.region,
    required this.periodStr,
  });
}

/// 夏令时检查结果
class DaylightSavingInfo {
  final bool isDaylightSaving;
  final String region;
  final int year;
  final DaylightSavingPeriod? period;
  final String? description;

  const DaylightSavingInfo({
    required this.isDaylightSaving,
    required this.region,
    required this.year,
    this.period,
    this.description,
  });
}

/// 夏令时检查服务
/// 用于判断指定日期是否在夏令时期间
class DaylightSavingService {
  /// 夏令时历史数据
  static const Map<String, List<String>> _daylightSavingData = {
    "大陆": [
      "1986-1986 5/4-9/14",
      "1987-1987 4/12-9/13",
      "1988-1988 4/10-9/11",
      "1989-1989 4/16-9/17",
      "1990-1990 4/15-9/16",
      "1991-1991 4/14-9/15"
    ],
    "台湾": [
      "1945-1951 5/1-9/30",
      "1952-1952 3/1-10/31",
      "1953-1954 4/1-10/31",
      "1955-1959 4/1-9/30",
      "1960-1961 6/1-9/30",
      "1974-1975 4/1-9/30",
      "1979-1979 7/1-9/30"
    ],
    "澳门": [
      "1946-1946 4/30-12/1",
      "1947-1947 4/19-12/30",
      "1948-1948 5/2-10/29",
      "1951-1951 3/31-10/29",
      "1952-1952 4/5-11/2",
      "1953-1953 4/4-10/31",
      "1954-1954 3/20-10/30",
      "1955-1955 3/19-11/5",
      "1956-1956 3/18-11/4",
      "1957-1957 3/24-11/3",
      "1958-1958 3/23-11/4",
      "1959-1959 3/22-11/1",
      "1960-1960 3/20-11/6",
      "1961-1961 3/19-11/5",
      "1962-1962 3/18-11/3",
      "1963-1963 3/24-11/3",
      "1964-1964 3/22-11/1",
      "1965-1965 4/18-10/17",
      "1966-1966 4/17-10/22",
      "1967-1967 4/16-10/22",
      "1968-1968 4/21-10/20",
      "1969-1969 4/20-10/19",
      "1970-1970 4/19-10/18",
      "1971-1971 4/18-10/17",
      "1972-1972 4/16-10/22",
      "1973-1973 4/22-10/21",
      "1974-1974 3/24-10/20",
      "1975-1975 4/20-10/10",
      "1976-1976 4/18-10/17",
      "1979-1979 5/13-10/21"
    ],
    "香港": [
      "1941-1941 6/15-9/30",
      "1946-1946 4/20-12/1",
      "1947-1947 4/13-11/30",
      "1948-1948 5/2-10/31",
      "1949-1949 4/3-10/30",
      "1950-1950 4/2-10/29",
      "1951-1951 4/1-10/28",
      "1952-1952 4/6-11/2",
      "1953-1953 4/5-11/1",
      "1954-1954 3/21-10/31",
      "1955-1955 3/20-11/6",
      "1956-1956 3/18-11/4",
      "1957-1957 3/24-11/3",
      "1958-1958 3/23-11/2",
      "1959-1959 3/22-11/1",
      "1960-1960 3/20-11/6",
      "1961-1961 3/19-11/5",
      "1962-1962 3/18-11/4",
      "1963-1963 3/24-11/3",
      "1964-1964 3/22-11/1",
      "1965-1965 4/18-10/17",
      "1966-1966 4/17-10/16",
      "1967-1967 4/16-10/22",
      "1968-1968 4/21-10/20",
      "1969-1969 4/20-10/19",
      "1970-1970 4/19-10/18",
      "1971-1971 4/18-10/17",
      "1972-1972 4/16-10/22",
      "1973-1973 4/22-10/21",
      "1974-1974 1/1-10/20",
      "1975-1975 4/20-10/19",
      "1976-1976 4/18-10/17",
      "1979-1979 5/13-10/21"
    ]
  };



  /// 解析夏令时数据字符串
  /// 格式如 "1986-1986 5/4-9/14"
  static DaylightSavingPeriod? _parseDaylightSavingPeriod(String periodStr, String region) {
    try {
      // 跳过包含 * 的记录
      if (periodStr.contains('*')) {
        return null;
      }

      final parts = periodStr.split(' ');
      if (parts.length != 2) return null;

      final yearRange = parts[0].split('-');
      if (yearRange.length != 2) return null;

      final startYear = int.parse(yearRange[0]);
      final endYear = int.parse(yearRange[1]);

      // 处理复杂的日期范围（如 "4/22-10/21,12/30-*"）
      final dateRanges = parts[1].split(',');
      final mainRange = dateRanges[0];
      final dateRange = mainRange.split('-');
      if (dateRange.length != 2) return null;

      final startDate = dateRange[0].split('/');
      final endDate = dateRange[1].split('/');
      if (startDate.length != 2 || endDate.length != 2) return null;

      return DaylightSavingPeriod(
        startYear: startYear,
        endYear: endYear,
        startMonth: int.parse(startDate[0]),
        startDay: int.parse(startDate[1]),
        endMonth: int.parse(endDate[0]),
        endDay: int.parse(endDate[1]),
        region: region,
        periodStr: periodStr,
      );
    } catch (e) {
      return null;
    }
  }

  /// 检查指定日期是否在夏令时期间
  static DaylightSavingInfo checkDaylightSaving(DateTime date, {String region = '大陆'}) {
    final year = date.year;
    final dstPeriods = _daylightSavingData[region] ?? [];

    for (final periodStr in dstPeriods) {
      final period = _parseDaylightSavingPeriod(periodStr, region);
      if (period == null) continue;

      // 检查年份是否在范围内
      if (year >= period.startYear && year <= period.endYear) {
        final startDate = DateTime(year, period.startMonth, period.startDay);
        final endDate = DateTime(year, period.endMonth, period.endDay);

        // 检查日期是否在夏令时期间
        if (date.isAfter(startDate.subtract(const Duration(days: 1))) && 
            date.isBefore(endDate.add(const Duration(days: 1)))) {
          return DaylightSavingInfo(
            isDaylightSaving: true,
            region: region,
            year: year,
            period: period,
            description: '${period.startMonth}月${period.startDay}日 - ${period.endMonth}月${period.endDay}日',
          );
        }
      }
    }

    return DaylightSavingInfo(
      isDaylightSaving: false,
      region: region,
      year: year,
    );
  }

  /// 获取指定年份的夏令时期间列表
  static List<DaylightSavingPeriod> getDaylightSavingPeriodsForYear(int year, {String region = '大陆'}) {
    final periods = <DaylightSavingPeriod>[];
    final dstPeriods = _daylightSavingData[region] ?? [];

    for (final periodStr in dstPeriods) {
      final period = _parseDaylightSavingPeriod(periodStr, region);
      if (period == null) continue;

      if (year >= period.startYear && year <= period.endYear) {
        periods.add(period);
      }
    }

    return periods;
  }

  /// 检查指定年份是否有夏令时
  static bool hasAnyDaylightSavingInYear(int year, {String region = '大陆'}) {
    return getDaylightSavingPeriodsForYear(year, region: region).isNotEmpty;
  }
}
