import '../models/agent_model.dart';
import '../models/ai_model.dart';
import '../models/pricing_tier.dart';
import '../services/pricing_tier_service.dart';

/// 算力计算服务
/// 根据智能体档次和模型等级计算对话消耗的算力
/// 使用从云函数获取的真实数据，完全移除硬编码
class PowerCalculationService {
  static final PricingTierService _pricingTierService = PricingTierService();

  /// 缓存的档次配置映射 (tierId -> PricingTier)
  static Map<String, PricingTier>? _cachedPricingTiers;

  /// 缓存过期时间（5分钟）
  static DateTime? _cacheExpireTime;

  /// 从云函数获取档次配置
  static Future<void> _loadPricingConfiguration(String token) async {
    try {
      // 检查缓存是否有效
      if (_cacheExpireTime != null &&
          DateTime.now().isBefore(_cacheExpireTime!) &&
          _cachedPricingTiers != null) {
        return; // 缓存仍然有效
      }

      print('PowerCalculationService: 从云函数加载档次配置...');

      // 获取档次配置列表
      final pricingTiers = await _pricingTierService.getPricingTiers(token: token);

      // 构建档次配置映射
      final tiersMap = <String, PricingTier>{};
      for (final tier in pricingTiers) {
        if (tier.isActive) {
          tiersMap[tier.id] = tier;
        }
      }

      // 更新缓存
      _cachedPricingTiers = tiersMap;
      _cacheExpireTime = DateTime.now().add(const Duration(minutes: 5));

      print('PowerCalculationService: 档次配置加载完成');
      print('档次数量: ${tiersMap.length}');

    } catch (e) {
      print('PowerCalculationService: 加载档次配置失败: $e');
      // 如果加载失败，使用默认配置
      _useDefaultConfiguration();
    }
  }

  /// 使用默认配置（当云函数不可用时的后备方案）
  static void _useDefaultConfiguration() {
    // 创建默认档次配置
    final defaultTiers = <String, PricingTier>{
      '2ed3518f684fc84402ecb3472b0e960d': PricingTier(
        id: '2ed3518f684fc84402ecb3472b0e960d',
        tierName: '基础档次',
        tierDescription: '适用于简单对话类智能体',
        basicModelCost: 5,
        advancedModelCost: 10,
        isActive: true,
        sortOrder: 1,
      ),
      '2ed3518f684fc84402ecb348303ee4b9': PricingTier(
        id: '2ed3518f684fc84402ecb348303ee4b9',
        tierName: '标准档次',
        tierDescription: '适用于一般专业智能体',
        basicModelCost: 10,
        advancedModelCost: 20,
        isActive: true,
        sortOrder: 2,
      ),
      '2ed3518f684fc84402ecb34925b02dec': PricingTier(
        id: '2ed3518f684fc84402ecb34925b02dec',
        tierName: '高级档次',
        tierDescription: '适用于复杂分析类智能体',
        basicModelCost: 15,
        advancedModelCost: 30,
        isActive: true,
        sortOrder: 3,
      ),
    };

    _cachedPricingTiers = defaultTiers;
    _cacheExpireTime = DateTime.now().add(const Duration(minutes: 1)); // 短期缓存，尽快重试
    print('PowerCalculationService: 使用默认配置');
  }

  /// 计算对话消耗的算力
  ///
  /// [agent] 智能体对象（包含档次ID）
  /// [model] 模型对象（包含等级信息）
  /// [token] 用户token（用于获取配置）
  ///
  /// 返回消耗的算力数量，如果无法计算则返回默认值5
  static Future<int> calculatePowerCost({
    required AgentModel agent,
    required AIModel model,
    required String token,
  }) async {
    try {
      // 确保配置已加载
      await _loadPricingConfiguration(token);

      // 获取智能体的档次ID
      final pricingTierId = agent.pricingTierId;
      if (pricingTierId.isEmpty) {
        print('警告: 智能体 ${agent.agentName}(${agent.id}) 没有配置档次ID，使用默认值');
        return 5; // 默认基础档次的初级模型消耗
      }

      // 获取档次配置
      final tierConfig = _cachedPricingTiers?[pricingTierId];
      if (tierConfig == null) {
        print('警告: 未找到档次 $pricingTierId 的配置，使用默认值');
        return 5; // 默认基础档次的初级模型消耗
      }

      // 根据模型等级获取对应的算力消耗
      final cost = tierConfig.getCostForModelLevel(model.modelLevel);

      print('PowerCalculationService: 计算算力消耗 - 智能体:${agent.agentName}, 模型:${model.modelDisplayName}, 档次:${tierConfig.tierName}, 等级:${model.modelLevel}, 消耗:$cost');
      return cost;
    } catch (e) {
      print('错误: 计算算力消耗时发生异常: $e，使用默认值');
      return 5; // 默认基础档次的初级模型消耗
    }
  }

  /// 根据智能体和模型对象计算算力消耗（兼容性方法）
  ///
  /// [agent] 智能体对象
  /// [model] 模型对象
  /// [token] 用户token（用于获取配置）
  ///
  /// 返回消耗的算力数量
  static Future<int> calculatePowerCostFromObjects({
    required AgentModel agent,
    required AIModel model,
    required String token,
  }) async {
    return await calculatePowerCost(
      agent: agent,
      model: model,
      token: token,
    );
  }

  /// 获取算力消耗的详细说明
  ///
  /// [agent] 智能体对象
  /// [model] 模型对象
  /// [token] 用户token（用于获取配置）
  ///
  /// 返回包含档次、等级和消耗的详细信息
  static Future<Map<String, dynamic>> getPowerCostDetails({
    required AgentModel agent,
    required AIModel model,
    required String token,
  }) async {
    // 确保配置已加载
    await _loadPricingConfiguration(token);

    final pricingTierId = agent.pricingTierId;
    final tierConfig = _cachedPricingTiers?[pricingTierId];
    final cost = await calculatePowerCost(agent: agent, model: model, token: token);

    // 获取档次名称
    final tierName = tierConfig?.tierName ?? '未知档次';

    // 获取等级名称
    String levelName = '初级模型';
    if (model.modelLevel == '高级') {
      levelName = '高级模型';
    }

    return {
      'agentId': agent.id,
      'agentName': agent.agentName,
      'modelId': model.id,
      'modelName': model.modelDisplayName,
      'pricingTierId': pricingTierId,
      'tierName': tierName,
      'modelLevel': model.modelLevel,
      'levelName': levelName,
      'cost': cost,
      'description': '$tierName + $levelName = $cost算力',
    };
  }

  /// 检查用户算力是否足够
  ///
  /// [userAvailablePower] 用户当前可用算力
  /// [agent] 智能体对象
  /// [model] 模型对象
  /// [token] 用户token（用于获取配置）
  ///
  /// 返回是否有足够算力
  static Future<bool> hasEnoughPower({
    required int userAvailablePower,
    required AgentModel agent,
    required AIModel model,
    required String token,
  }) async {
    final requiredPower = await calculatePowerCost(agent: agent, model: model, token: token);
    return userAvailablePower >= requiredPower;
  }

  /// 获取所有档次的算力消耗表
  ///
  /// [token] 用户token（用于获取配置）
  ///
  /// 返回完整的算力消耗配置表，用于UI显示
  static Future<Map<String, dynamic>> getAllPowerCosts(String token) async {
    // 确保配置已加载
    await _loadPricingConfiguration(token);

    // 构建档次信息
    final tiers = <String, dynamic>{};
    _cachedPricingTiers?.forEach((tierId, tierConfig) {
      tiers[tierConfig.tierName] = {
        'id': tierId,
        'tierName': tierConfig.tierName,
        'description': tierConfig.tierDescription,
        'basicModelCost': tierConfig.basicModelCost,
        'advancedModelCost': tierConfig.advancedModelCost,
        'sortOrder': tierConfig.sortOrder,
      };
    });

    return {
      'tiers': tiers,
      'tierCount': _cachedPricingTiers?.length ?? 0,
    };
  }

  /// 清除缓存（用于强制重新加载配置）
  static void clearCache() {
    _cachedPricingTiers = null;
    _cacheExpireTime = null;
    _pricingTierService.clearCache();
    print('PowerCalculationService: 缓存已清除');
  }
}
