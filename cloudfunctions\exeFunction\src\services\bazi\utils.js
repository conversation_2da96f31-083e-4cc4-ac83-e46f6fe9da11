/**
 * 八字分析工具函数
 * 包含基础工具函数，如五行、藏干、空亡等计算函数
 */

const constants = require('./constants');

/**
 * 获取十天干对应的五行
 * @param {string} gan - 天干
 * @returns {string} - 五行
 */
function getWuXingByGan(gan) {
  return constants.ganWuXingMap[gan] || '';
}

/**
 * 获取十二地支对应的五行
 * @param {string} zhi - 地支
 * @returns {string} - 五行
 */
function getWuXingByZhi(zhi) {
  return constants.zhiWuXingMap[zhi] || '';
}

/**
 * 获取地支藏干
 * @param {string} zhi - 地支
 * @returns {Array} - 藏干数组
 */
function getCangGan(zhi) {
  return constants.cangGanMap[zhi] || [];
}

/**
 * 获取干支纳音五行
 * @param {string} ganZhi - 干支
 * @returns {string} - 纳音五行
 */
function getNaYin(gan<PERSON>hi) {
  return constants.naYinMap[ganZhi] || '';
}

/**
 * 计算十神
 * @param {string} dayGan - 日干
 * @param {Array} gans - 天干数组
 * @returns {Array} - 十神数组
 */
function calculateShiShen(dayGan, gans) {
  return gans.map((gan, index) => {
    const shiShen = constants.shiShenMap[dayGan][gan] || '';
    return { 
      gan, 
      shiShen,
      shiShenRelation: constants.shiShenRelation[shiShen] || '',
      // 为了区分年柱、月柱、时柱，通常会对应不同的名称
      shiShenName: index === 0 ? '年柱主星' : (index === 1 ? '月柱主星' : '时柱主星')
    };
  });
}

/**
 * 计算长生十二神
 * @param {string} dayGan - 日干
 * @param {Array} zhis - 地支数组
 * @returns {Array} - 长生十二神数组
 */
function calculateStarDestiny(dayGan, zhis) {
  // 确定使用哪个顺序表
  const orderMap = '甲丙戊庚壬'.includes(dayGan) ? constants.yangOrderMap : constants.yinOrderMap;
  const order = orderMap[dayGan];
  
  if (!order) {
    return zhis.map(zhi => ({ zhi, starDestiny: '' }));
  }
  
  return zhis.map(zhi => {
    const index = order.indexOf(zhi);
    if (index === -1) return { zhi, starDestiny: '' };
    return { zhi, starDestiny: constants.starDestinyNames[index] };
  });
}

/**
 * 计算空亡
 * @param {string} ganZhi - 干支
 * @returns {string} - 空亡
 */
function calculateKongWang(ganZhi) {
  return constants.kongWangMap[ganZhi] || '';
}

/**
 * 获取克制某个五行的五行
 * @param {string} wuXing - 五行
 * @returns {string} - 克制的五行
 */
function getRestrained(wuXing) {
  return constants.restrainMap[wuXing] || '';
}

/**
 * 获取生助某个五行的五行
 * @param {string} wuXing - 五行
 * @returns {string} - 生助的五行
 */
function getGenerating(wuXing) {
  return constants.generatingMap[wuXing] || '';
}

/**
 * 获取我所生之五行
 * @param {string} wuXing - 五行
 * @returns {string} - 我所生之五行
 */
function getGenerated(wuXing) {
  return constants.generatedMap[wuXing] || '';
}

/**
 * 获取年柱主星
 * @param {string} dayGan - 日干
 * @param {string} yearGan - 年干
 * @returns {string} - 主星名称
 */
function getYearZhuXing(dayGan, yearGan) {
  return constants.shiShenMap[dayGan][yearGan] || '未知';
}

/**
 * 获取月柱主星
 * @param {string} dayGan - 日干
 * @param {string} monthGan - 月干
 * @returns {string} - 主星名称
 */
function getMonthZhuXing(dayGan, monthGan) {
  return constants.shiShenMap[dayGan][monthGan] || '未知';
}

/**
 * 获取日柱主星
 * @param {string} dayGan - 日干
 * @param {boolean} isMale - 是否为男性
 * @returns {string} - 主星名称
 */
function getDayZhuZhuXing(dayGan, isMale) {
  // 直接根据性别判断，男性是元男，女性是元女
  return isMale ? '元男' : '元女';
}

/**
 * 获取时柱主星
 * @param {string} dayGan - 日干
 * @param {string} timeGan - 时干
 * @returns {string} - 主星名称
 */
function getTimeZhuXing(dayGan, timeGan) {
  return constants.shiShenMap[dayGan][timeGan] || '未知';
}

/**
 * 获取五行简要分析结果
 * @param {Object} wuXingCount - 五行统计
 * @returns {string} - 五行简要分析结果
 */
function getBriefWuXingAnalysis(wuXingCount) {
  const sortedWuXing = Object.entries(wuXingCount)
    .sort((a, b) => b[1] - a[1]);
  
  const mostWuXing = sortedWuXing[0];
  const leastWuXing = sortedWuXing[sortedWuXing.length - 1];
  
  let result = '';
  if (mostWuXing[1] > 0) {
    result += `${mostWuXing[0]}最旺(${mostWuXing[1]}个)`;
  }
  
  if (leastWuXing[1] === 0) {
    result += `，缺${leastWuXing[0]}`;
  } else if (leastWuXing[1] < mostWuXing[1] / 2) {
    result += `，${leastWuXing[0]}较弱(${leastWuXing[1]}个)`;
  }
  
  return result;
}

/**
 * 计算副星
 * 根据日干与年支藏干、月支藏干、日支藏干、时支藏干得出
 * @param {string} dayGan - 日干
 * @param {Array} cangGan - 藏干数组，格式为[{zhi: '寅', cangGan: ['甲', '丙', '戊']}, ...]
 * @returns {Array} - 副星数组
 */
function calculateFuXing(dayGan, cangGan) {
  const result = [];
  
  // 遍历所有藏干
  for (let i = 0; i < cangGan.length; i++) {
    const pillar = cangGan[i];
    const pillarName = i === 0 ? '年柱' : (i === 1 ? '月柱' : (i === 2 ? '日柱' : '时柱'));
    const fuXingList = [];
    
    // 对每个地支的藏干计算副星
    for (const gan of pillar.cangGan) {
      const key = dayGan + gan;
      const fuXing = constants.shiShenMingPanMap[key] || '';
      fuXingList.push({
        gan,
        fuXing
      });
    }
    
    result.push({
      pillar: pillarName,
      zhi: pillar.zhi,
      fuXingList
    });
  }
  
  return result;
}

// 导出所有工具函数
module.exports = {
  getWuXingByGan,
  getWuXingByZhi,
  getCangGan,
  getNaYin,
  calculateShiShen,
  calculateStarDestiny,
  calculateKongWang,
  getRestrained,
  getGenerating,
  getGenerated,
  getYearZhuXing,
  getMonthZhuXing,
  getDayZhuZhuXing,
  getTimeZhuXing,
  getBriefWuXingAnalysis,
  calculateFuXing
}; 