# 手机端直播模式功能需求文档

## 1. 项目概述

### 1.1 需求背景
用户需要在手机端直播展示numerology_ai_chat_new程序，但现有的桌面版UI在手机端显示存在以下问题：
- 宽高比例不符合手机端显示（当前为桌面比例）
- 字体太小，手机端无法看清
- 侧边栏、标题栏等非核心元素占用过多空间
- 需要保持所有原有功能不变

### 1.2 解决方案
添加一个"手机端直播模式"功能，通过按钮切换，在该模式下：
- 隐藏除聊天对话框以外的所有UI元素
- 调整窗口比例为手机端比例（9:16）
- 放大字体以适应手机端显示
- 保持所有聊天功能正常工作

## 2. 现有架构分析

### 2.1 UI布局结构
根据代码分析，当前UI结构如下：

```
HomeScreen (主容器)
├── CustomTitleBar (自定义标题栏)
│   ├── 应用Logo和标题
│   └── 窗口控制按钮 (最小化/最大化/关闭)
└── Row (主体布局)
    ├── NavigationRail (左侧导航栏, 宽度280px)
    │   ├── 导航项目 (AI对话/八字排盘/充值算力/个人中心/设置)
    │   ├── 主题切换
    │   └── 用户信息区域
    └── Expanded (主内容区域)
        └── ChatScreen (聊天页面)
            └── Row
                ├── SizedBox(width: 320) (左侧面板)
                │   ├── AgentPanel (智能体选择)
                │   └── ConversationSelector (对话历史)
                └── Expanded (聊天面板)
                    └── ChatPanel
                        ├── Header (智能体信息/停止按钮)
                        ├── MessageList (消息列表)
                        └── InputArea (输入区域)
```

### 2.2 窗口管理
- 使用window_manager包管理窗口
- 默认窗口尺寸：1400x900
- 最小窗口尺寸：1200x800
- 支持最大化/最小化/拖拽等操作

### 2.3 主题系统
- 使用ThemeProvider管理主题
- 支持明亮/暗色模式切换
- 使用Material 3设计规范

## 3. 功能设计

### 3.1 直播模式状态管理
创建LiveModeProvider来管理直播模式状态：

```dart
class LiveModeProvider extends ChangeNotifier {
  bool _isLiveModeEnabled = false;
  Size? _originalWindowSize;
  
  bool get isLiveModeEnabled => _isLiveModeEnabled;
  
  // 进入直播模式
  Future<void> enterLiveMode();
  
  // 退出直播模式
  Future<void> exitLiveMode();
  
  // 切换直播模式
  Future<void> toggleLiveMode();
}
```

### 3.2 UI适配方案

#### 3.2.1 HomeScreen适配
在HomeScreen中监听LiveModeProvider状态：
- 直播模式下隐藏CustomTitleBar
- 直播模式下隐藏NavigationRail
- 直播模式下ChatScreen占满整个窗口

#### 3.2.2 ChatScreen适配
在ChatScreen中适配直播模式：
- 直播模式下隐藏左侧面板（AgentPanel + ConversationSelector）
- ChatPanel占满整个屏幕
- 调整内边距以适应手机比例

#### 3.2.3 ChatPanel适配
在ChatPanel中适配直播模式：
- 增大字体尺寸（建议放大1.5-2倍）
- 调整消息气泡间距
- 优化输入区域布局
- 保持所有功能（图片上传、发送消息等）

### 3.3 窗口尺寸调整
直播模式下的窗口尺寸计算：
- 目标比例：9:16（手机竖屏比例）
- 建议尺寸：540x960 或 450x800
- 进入直播模式时保存当前窗口尺寸
- 退出直播模式时恢复原始尺寸

### 3.4 切换按钮设计
在ChatPanel的Header区域添加直播模式切换按钮：
- 位置：Header右侧，停止按钮旁边
- 图标：手机图标（Icons.phone_android）
- 状态指示：不同颜色表示开启/关闭状态
- 工具提示：显示当前状态和操作提示

## 4. 技术实现细节

### 4.1 文件修改清单

#### 4.1.1 新增文件
- `lib/src/providers/live_mode_provider.dart` - 直播模式状态管理
- `lib/src/widgets/live_mode_button.dart` - 直播模式切换按钮

#### 4.1.2 修改文件
- `lib/src/screens/home_screen.dart` - 添加直播模式布局切换
- `lib/src/screens/chat_screen.dart` - 添加直播模式适配
- `lib/src/widgets/chat_panel.dart` - 添加直播模式样式和切换按钮
- `lib/src/core/constants/app_constants.dart` - 添加直播模式相关常量

### 4.2 关键实现点

#### 4.2.1 响应式字体大小
```dart
double getFontSize(BuildContext context, double baseSize) {
  final liveModeProvider = context.read<LiveModeProvider>();
  return liveModeProvider.isLiveModeEnabled ? baseSize * 1.8 : baseSize;
}
```

#### 4.2.2 条件渲染组件
```dart
Widget build(BuildContext context) {
  final isLiveMode = context.watch<LiveModeProvider>().isLiveModeEnabled;
  
  return Scaffold(
    body: Column(
      children: [
        if (!isLiveMode) CustomTitleBar(...),
        Expanded(
          child: isLiveMode 
            ? _buildLiveModeLayout() 
            : _buildDesktopLayout(),
        ),
      ],
    ),
  );
}
```

#### 4.2.3 窗口尺寸管理
```dart
Future<void> _adjustWindowForLiveMode(bool isLiveMode) async {
  if (isLiveMode) {
    // 保存当前尺寸
    final currentSize = await windowManager.getSize();
    await _storageService.set('original_window_size', currentSize);
    
    // 设置手机比例
    await windowManager.setSize(const Size(450, 800));
    await windowManager.center();
  } else {
    // 恢复原始尺寸
    final originalSize = await _storageService.get('original_window_size');
    if (originalSize != null) {
      await windowManager.setSize(originalSize);
    }
  }
}
```

## 5. 用户体验设计

### 5.1 切换流程
1. 用户点击聊天界面右上角的手机图标按钮
2. 系统显示确认对话框，说明即将进入直播模式
3. 确认后，窗口自动调整为手机比例
4. UI元素隐藏，字体放大
5. 再次点击按钮可退出直播模式

### 5.2 视觉反馈
- 按钮状态变化：未激活时为灰色，激活时为主题色
- 平滑的动画过渡
- 窗口尺寸变化时的居中处理

### 5.3 功能保持
- 所有聊天功能保持不变
- 消息发送、接收正常
- 图片上传功能正常
- 智能体和模型选择功能通过其他方式访问（如长按等）

## 6. 测试计划

### 6.1 功能测试
- 直播模式开启/关闭功能
- 窗口尺寸调整功能
- UI元素显示/隐藏功能
- 字体大小调整功能

### 6.2 兼容性测试
- 不同屏幕分辨率下的显示效果
- 明亮/暗色主题下的显示效果
- 窗口最大化/最小化状态的处理

### 6.3 用户体验测试
- 切换流程的流畅性
- 手机端可读性验证
- 功能完整性验证

## 7. 风险评估

### 7.1 技术风险
- 窗口管理API的兼容性
- 布局切换时的性能影响
- 状态管理的复杂性

### 7.2 用户体验风险
- 字体过大导致的布局问题
- 功能访问的便利性降低
- 切换过程中的视觉跳跃

### 7.3 缓解措施
- 充分测试不同场景
- 提供用户设置选项
- 保留快速退出机制

## 8. 后续优化

### 8.1 可选功能
- 自定义字体大小倍数
- 多种手机比例选择
- 快捷键支持
- 记住用户偏好设置

### 8.2 扩展可能
- 平板模式支持
- 横屏直播模式
- 多窗口管理
- 直播录制功能

---

## 9. 实现状态

### 9.1 已完成功能
✅ **LiveModeProvider状态管理** - 完成直播模式状态管理，包括窗口尺寸调整、字体缩放等功能
✅ **LiveModeButton切换按钮** - 完成直播模式切换按钮组件，支持确认对话框和状态指示
✅ **HomeScreen适配** - 完成主屏幕的直播模式布局切换，隐藏标题栏和侧边栏
✅ **ChatScreen适配** - 完成聊天屏幕的直播模式布局，隐藏左侧面板
✅ **ChatPanel适配** - 完成聊天面板的直播模式样式，包括字体放大、间距调整等
✅ **ChatBubble适配** - 完成聊天气泡的直播模式样式，支持字体和图片尺寸放大
✅ **窗口管理** - 完成窗口尺寸自动调整为手机比例(9:16)
✅ **常量配置** - 添加直播模式相关常量配置

### 9.2 使用说明
1. 在聊天界面右上角点击"直播模式"按钮
2. 确认进入直播模式后，窗口将自动调整为450x800像素
3. UI将隐藏侧边栏、标题栏等非核心元素
4. 字体将放大1.8倍以适应手机端显示
5. 再次点击按钮可退出直播模式，恢复原始布局

### 9.3 技术特点
- **无损切换**: 进入/退出直播模式不会影响聊天功能
- **状态保持**: 智能体选择、对话历史等状态完全保持
- **响应式设计**: 字体、间距、图片尺寸自动适配
- **用户友好**: 提供确认对话框和状态提示

---

本文档详细描述了手机端直播模式功能的实现方案，确保在不影响现有功能的前提下，为用户提供适合手机端直播的UI体验。功能已完整实现并可投入使用。
