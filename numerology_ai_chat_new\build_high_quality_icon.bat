@echo off
chcp 65001 >nul
echo ════════════════════════════════════════════
echo    高质量图标构建脚本 (Flutter原生)
echo ════════════════════════════════════════════
echo.

echo 📋 本脚本将执行以下操作:
echo 1. 检查源图片文件
echo 2. 使用Flutter原生方式生成高质量图标 (256x256)
echo 3. 清理构建缓存
echo 4. 重新构建应用
echo.

echo 🔍 步骤1: 检查源图片文件...
if not exist "assets\images\logo.png" (
    echo ❌ 源图片文件不存在: assets\images\logo.png
    echo 💡 请确保将您的logo图片放在 assets\images\logo.png
    pause
    exit /b 1
)
echo ✅ 源图片文件存在

echo.
echo 🔄 步骤2: 生成高质量图标 (256x256)...
echo 💡 使用Flutter Launcher Icons插件生成图标
dart run flutter_launcher_icons
if %errorlevel% neq 0 (
    echo ❌ 图标生成失败！
    echo 💡 可能原因:
    echo    - flutter_launcher_icons插件未安装
    echo    - pubspec.yaml配置有误
    echo    - 源图片格式不支持
    echo.
    echo 📝 解决方案:
    echo    1. 运行: flutter pub get
    echo    2. 检查pubspec.yaml中的flutter_launcher_icons配置
    echo    3. 确保源图片为JPG或PNG格式
    pause
    exit /b 1
)
echo ✅ 图标生成完成

echo.
echo 🔄 步骤3: 清理构建缓存...
flutter clean
if %errorlevel% neq 0 (
    echo ⚠️ 清理缓存失败，继续构建...
)
echo ✅ 缓存清理完成

echo.
echo 🔄 步骤4: 获取依赖包...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ 获取依赖包失败！
    pause
    exit /b 1
)
echo ✅ 依赖包获取完成

echo.
echo 🔄 步骤5: 构建Windows应用...
flutter build windows --release
if %errorlevel% neq 0 (
    echo ❌ 构建失败！
    pause
    exit /b 1
)

echo.
echo 🎉 高质量图标构建完成！
echo ════════════════════════════════════════════
echo 📁 可执行文件位置: build\windows\x64\runner\Release\numerology_ai_chat.exe
echo 💡 图标特性:
echo    - 使用256x256高分辨率图标
echo    - 支持高DPI显示
echo    - 自动应用到exe文件
echo.
echo 📝 如果图标仍然模糊，请尝试:
echo 1. 确保源图片 assets\images\logo.png 分辨率至少512x512
echo 2. 重启Windows资源管理器刷新图标缓存
echo 3. 检查Windows显示缩放设置
echo.
pause
