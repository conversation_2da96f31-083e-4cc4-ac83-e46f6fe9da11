import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

// 创建axios实例
const api = axios.create({
  baseURL: 'https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    
    // 添加token到请求头
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    
    return config
  },
  (error) => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 处理云函数返回的特殊格式
    if (response.data && typeof response.data === 'string') {
      try {
        response.data = JSON.parse(response.data)
      } catch (e) {
        // 如果解析失败，保持原样
      }
    }
    return response
  },
  async (error) => {
    console.error('Response error:', error)

    // 处理网络错误
    if (!error.response) {
      ElMessage.error('网络连接失败，请检查网络设置')
      return Promise.reject(error)
    }

    const { status, data } = error.response
    const originalRequest = error.config

    // 处理401错误（token过期）
    if (status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        const authStore = useAuthStore()
        console.log('API拦截器: 收到401错误，尝试刷新token')

        // 尝试刷新token
        const refreshSuccess = await authStore.forceRefreshToken()

        if (refreshSuccess) {
          console.log('API拦截器: Token刷新成功，重试原请求')
          // 更新请求头中的token
          originalRequest.headers.Authorization = `Bearer ${authStore.token}`
          // 重试原请求
          return api(originalRequest)
        } else {
          console.log('API拦截器: Token刷新失败，跳转登录页')
          ElMessage.error('登录已过期，请重新登录')
          authStore.logout()
          window.location.href = '/login'
        }
      } catch (refreshError) {
        console.error('API拦截器: Token刷新异常:', refreshError)
        ElMessage.error('登录已过期，请重新登录')
        const authStore = useAuthStore()
        authStore.logout()
        window.location.href = '/login'
      }

      return Promise.reject(error)
    }

    // 处理其他HTTP状态码错误
    switch (status) {
      case 403:
        ElMessage.error('权限不足')
        break
      case 404:
        ElMessage.error('请求的资源不存在')
        break
      case 500:
        ElMessage.error('服务器内部错误')
        break
      default:
        ElMessage.error(data?.error?.message || data?.message || '请求失败')
    }

    return Promise.reject(error)
  }
)

export default api
