/// 应用常量定义
class AppConstants {
  // 应用信息
  static const String appName = '鲸准大师';
  static const String appVersion = '1.0.2';
  static const String appDescription = '专业的命理分析与AI对话平台';

  // 网络配置
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 300); // 增加到5分钟

  // 云函数配置（固定地址）
  static const String cloudFunctionBaseUrl = 'https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com';
  static const String exeFunctionUrl = '$cloudFunctionBaseUrl/exeFunction';
  static const String exeAdminUrl = '$cloudFunctionBaseUrl/exeAdmin';

  // 存储键名
  static const String themeKey = 'theme_mode';
  static const String authTokenKey = 'auth_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';
  static const String conversationsKey = 'conversations';
  static const String settingsKey = 'app_settings';
  static const String defaultAgentKey = 'default_agent';
  static const String defaultModelKey = 'default_model';
  static const String agentsCacheKey = 'agents_cache';
  static const String modelsCacheKey = 'models_cache';
  static const String lastUpdateKey = 'last_update';
  static const String versionCheckKey = 'version_check';
  static const String lastVersionCheckKey = 'last_version_check';
  static const String systemConfigCacheKey = 'system_config_cache';
  static const String goProxyApiUrlKey = 'go_proxy_api_url';

  // 密码记住功能相关键名
  static const String rememberedUsernameKey = 'remembered_username';
  static const String rememberedPasswordKey = 'remembered_password';
  static const String rememberPasswordEnabledKey = 'remember_password_enabled';

  // 缓存配置
  static const Duration cacheExpiry = Duration(minutes: 30);

  // 版本检查配置
  static const Duration versionCheckInterval = Duration(hours: 24); // 24小时检查一次
  static const Duration versionCheckTimeout = Duration(seconds: 10); // 版本检查超时时间

  // 聊天存储配置
  static const String chatStorageKey = 'chat_storage';
  static const int maxConversations = 100; // 最大对话数量
  static const int maxMessagesPerConversation = 500; // 每个对话最大消息数
  static const int maxLoadConversationsOnStart = 5; // 启动时最多加载的对话数
  static const int conversationCleanupDays = 30; // 对话清理天数

  // API动作名称
  static const String actionRegister = 'register';
  static const String actionLogin = 'login';
  static const String actionRefreshToken = 'refreshToken';
  static const String actionGetAgents = 'getAgents';
  static const String actionGetModels = 'getModels';
  static const String actionGetUserInfo = 'getUserInfo';
  static const String actionUpdateUsage = 'updateUsage';
  static const String actionCheckVersion = 'checkVersion';
  static const String actionGetSystemConfig = 'getSystemConfig';
  static const String actionGetGoProxyApiUrl = 'getGoProxyApiUrl';

  // 窗口配置
  static const double minWindowWidth = 450;  // 允许更小的最小宽度以支持竖屏模式
  static const double minWindowHeight = 600; // 允许更小的最小高度
  static const double defaultWindowWidth = 1400;
  static const double defaultWindowHeight = 900;

  // 竖屏模式窗口配置 - 手机竖屏比例，适合消息显示
  static const double portraitModeWindowWidth = 450;  // 适中的宽度，便于消息阅读
  static const double portraitModeWindowHeight = 800; // 调整高度保持比例
  static const double portraitModeFontScaleFactor = 1.4; // 稍微减小字体缩放

  // 布局配置
  static const double sidebarWidth = 280;
  static const double titleBarHeight = 40;
  static const double defaultPadding = 16.0;
  static const double defaultBorderRadius = 12.0;

  // 动画配置
  static const Duration defaultAnimationDuration = Duration(milliseconds: 300);
  static const Duration fastAnimationDuration = Duration(milliseconds: 150);
  static const Duration slowAnimationDuration = Duration(milliseconds: 500);



  // AI配置
  static const int maxTokens = 2000;
  static const double defaultTemperature = 0.7;
  static const int maxConversationHistory = 50;

  // 文件配置
  static const List<String> supportedImageFormats = ['.jpg', '.jpeg', '.png', '.gif'];
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB

  // 错误消息
  static const String networkErrorMessage = '网络连接失败，请检查网络设置';
  static const String serverErrorMessage = '服务器错误，请稍后重试';
  static const String authErrorMessage = '认证失败，请重新登录';
  static const String unknownErrorMessage = '未知错误，请联系技术支持';
}

/// 路由路径常量
class AppRoutes {
  static const String splash = '/';
  static const String login = '/login';
  static const String register = '/register';
  static const String home = '/home';
  static const String chat = '/chat';
  static const String bazi = '/bazi';
  static const String purchase = '/purchase';

  static const String settings = '/settings';
  static const String profile = '/profile';
}

/// 主题相关常量
class ThemeConstants {
  // 字体
  static const String primaryFontFamily = 'LXGWWenKai';
  
  // 颜色
  static const int primaryColorValue = 0xFF6366F1;
  static const int secondaryColorValue = 0xFF8B5CF6;
  static const int accentColorValue = 0xFFF59E0B;
  
  // 尺寸
  static const double iconSizeSmall = 16.0;
  static const double iconSizeMedium = 24.0;
  static const double iconSizeLarge = 32.0;
  
  // 间距
  static const double spacingXs = 4.0;
  static const double spacingSm = 8.0;
  static const double spacingMd = 16.0;
  static const double spacingLg = 24.0;
  static const double spacingXl = 32.0;
}

/// 存储盒子名称
class HiveBoxes {
  static const String settings = 'settings';
  static const String conversations = 'conversations';
  static const String userProfiles = 'user_profiles';
  static const String cache = 'cache';
}
