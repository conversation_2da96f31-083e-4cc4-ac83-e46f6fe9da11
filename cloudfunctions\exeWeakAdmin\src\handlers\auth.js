const bcrypt = require('bcryptjs')
const { userCollection } = require('../utils/db')
const { validateWeakAdminLogin } = require('../utils/validate')
const { generateTokenPair, verifyToken } = require('../middleware/auth')
const { createBusinessError, successResponse, ERROR_CODES } = require('../middleware/error_handler')
const logger = require('../utils/logger')

/**
 * 管理员登录
 * @param {object} params 登录参数
 * @returns {object} 登录结果
 */
async function weakAdminLogin(params) {
  try {
    // 参数校验
    const { username, password } = validateWeakAdminLogin(params)
    
    logger.info('管理员登录尝试', { username })
    
    // 查找用户
    const user = await userCollection.findByUsername(username)
    if (!user) {
      throw createBusinessError(
        ERROR_CODES.INVALID_CREDENTIALS,
        '用户名或密码错误',
        401
      )
    }
    
    // 检查用户状态
    if (user.status !== '激活') {
      throw createBusinessError(
        ERROR_CODES.USER_DISABLED,
        '用户账户已被禁用',
        401
      )
    }
    
    // 检查管理员权限
    if (!user.isWeakAdmin) {
      throw createBusinessError(
        ERROR_CODES.INSUFFICIENT_PERMISSIONS,
        '无管理员权限',
        403
      )
    }
    
    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password)
    if (!isPasswordValid) {
      throw createBusinessError(
        ERROR_CODES.INVALID_CREDENTIALS,
        '用户名或密码错误',
        401
      )
    }
    
    // 生成Token对
    const tokens = generateTokenPair(user._id, user.username)

    // 计算token过期时间
    const accessTokenExpiresAt = new Date()
    accessTokenExpiresAt.setTime(accessTokenExpiresAt.getTime() + 1 * 60 * 60 * 1000) // 1小时后过期
    
    logger.info('管理员登录成功', {
      userId: user._id,
      username: user.username
    })
    
    // 返回用户信息和Token（不包含敏感信息）
    return successResponse({
      user: {
        id: user._id,
        username: user.username,
        isWeakAdmin: user.isWeakAdmin
      },
      tokens: {
        ...tokens,
        expiresAt: accessTokenExpiresAt.toISOString()
      }
    }, '登录成功')
    
  } catch (error) {
    logger.error('管理员登录失败', {
      username: params.username,
      error: error.message
    })
    throw error
  }
}

/**
 * 刷新Token
 * @param {object} params 刷新参数
 * @returns {object} 新的Token
 */
async function refreshToken(params) {
  try {
    const { refreshToken: token } = params

    if (!token) {
      throw createBusinessError(
        ERROR_CODES.TOKEN_MISSING,
        '缺少refresh token',
        400
      )
    }

    logger.info('管理员Token刷新尝试')

    // 验证refreshToken
    const decoded = verifyToken(token, 'refresh')

    // 查找用户
    const user = await userCollection.findById(decoded.userId)
    if (!user) {
      throw createBusinessError(
        ERROR_CODES.USER_NOT_FOUND,
        '用户不存在',
        401
      )
    }

    // 检查用户状态
    if (user.status !== '激活') {
      throw createBusinessError(
        ERROR_CODES.USER_DISABLED,
        '用户账户已被禁用',
        401
      )
    }

    // 检查管理员权限
    if (!user.isWeakAdmin) {
      throw createBusinessError(
        ERROR_CODES.INSUFFICIENT_PERMISSIONS,
        '无管理员权限',
        403
      )
    }

    // 生成新的Token对
    const tokens = generateTokenPair(user._id, user.username)

    // 计算新的token过期时间
    const accessTokenExpiresAt = new Date()
    accessTokenExpiresAt.setTime(accessTokenExpiresAt.getTime() + 1 * 60 * 60 * 1000) // 1小时后过期

    logger.info('管理员Token刷新成功', {
      userId: user._id,
      username: user.username
    })

    // 返回新的Token信息
    return successResponse({
      user: {
        id: user._id,
        username: user.username,
        isWeakAdmin: user.isWeakAdmin
      },
      tokens: {
        ...tokens,
        expiresAt: accessTokenExpiresAt.toISOString()
      }
    }, 'Token刷新成功')

  } catch (error) {
    logger.error('管理员Token刷新失败', {
      error: error.message
    })
    throw error
  }
}

module.exports = {
  weakAdminLogin,
  refreshToken
}
