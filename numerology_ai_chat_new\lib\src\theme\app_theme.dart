import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../core/constants/app_constants.dart';

/// 应用主题配置类
class AppTheme {
  // 主色调
  static const Color primaryColor = Color(ThemeConstants.primaryColorValue);
  static const Color secondaryColor = Color(ThemeConstants.secondaryColorValue);
  static const Color accentColor = Color(ThemeConstants.accentColorValue);

  // 语义化颜色
  static const Color successColor = Color(0xFF10B981);
  static const Color warningColor = Color(0xFFF59E0B);
  static const Color errorColor = Color(0xFFEF4444);
  static const Color infoColor = Color(0xFF3B82F6);

  // 明亮主题颜色 - 优化层级设计
  static const Color lightBackground = Color(0xFFF8F9FA);      // 主背景：更温和的灰白色
  static const Color lightSurface = Color(0xFFFFFFFF);         // 主表面：纯白色
  static const Color lightCardColor = Color(0xFFFFFFFF);       // 卡片背景：纯白色
  static const Color lightSecondaryCard = Color(0xFFF8F9FA);   // 次级卡片：与背景同色但有边框区分
  static const Color lightElevatedSurface = Color(0xFFFCFCFD); // 悬浮表面：微妙的提升感
  static const Color lightTextPrimary = Color(0xFF1A202C);     // 主文本：更深的灰色，提升对比度
  static const Color lightTextSecondary = Color(0xFF4A5568);   // 次要文本：中等灰色
  static const Color lightTextTertiary = Color(0xFF718096);    // 三级文本：较淡灰色
  static const Color lightBorder = Color(0xFFE2E8F0);         // 主边框：清晰可见
  static const Color lightBorderSecondary = Color(0xFFEDF2F7); // 次要边框：更淡
  static const Color lightDivider = Color(0xFFF7FAFC);        // 分割线：最淡的分割
  static const Color lightHover = Color(0xFFF7FAFC);          // 悬停状态：淡蓝灰色
  static const Color lightPressed = Color(0xFFEDF2F7);        // 按压状态：稍深的灰色

  // 暗色主题颜色
  static const Color darkBackground = Color(0xFF0F172A);
  static const Color darkSurface = Color(0xFF1E293B);
  static const Color darkCardColor = Color(0xFF334155);
  static const Color darkTextPrimary = Color(0xFFF1F5F9);
  static const Color darkTextSecondary = Color(0xFFCBD5E1);
  static const Color darkBorder = Color(0xFF475569);
  static const Color darkDivider = Color(0xFF374151);

  /// 获取明亮主题
  static ThemeData get lightTheme {
    const colorScheme = ColorScheme.light(
      primary: primaryColor,
      secondary: secondaryColor,
      tertiary: accentColor,
      surface: lightSurface,
      surfaceContainerHighest: lightElevatedSurface,
      surfaceVariant: Color(0xFFF1F5F9), // AI消息气泡背景色 - 淡蓝灰色
      onSurfaceVariant: Color(0xFF334155), // AI消息气泡文字色 - 深蓝灰色
      secondaryContainer: Color(0xFFE0E7FF), // 系统消息背景色 - 淡紫色
      onSecondaryContainer: Color(0xFF3730A3), // 系统消息文字色 - 深紫色
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: lightTextPrimary,
      outline: lightBorder,
      outlineVariant: lightBorderSecondary,
      error: errorColor,
      onError: Colors.white,
      shadow: Color(0x1A000000),
      scrim: Color(0x80000000),
    );

    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: colorScheme,
      scaffoldBackgroundColor: lightBackground,

      // 文本主题
      textTheme: _buildTextTheme(lightTextPrimary, lightTextSecondary),

      // 系统UI样式
      appBarTheme: _buildAppBarTheme(colorScheme, true),
      cardTheme: _buildCardTheme(colorScheme, true),
      elevatedButtonTheme: _buildElevatedButtonTheme(colorScheme),
      outlinedButtonTheme: _buildOutlinedButtonTheme(colorScheme),
      textButtonTheme: _buildTextButtonTheme(colorScheme),
      inputDecorationTheme: _buildInputDecorationTheme(colorScheme, true),
      dividerTheme: _buildDividerTheme(true),
      chipTheme: _buildChipTheme(colorScheme),
      snackBarTheme: _buildSnackBarTheme(colorScheme),
      dialogTheme: _buildDialogTheme(colorScheme),
      bottomSheetTheme: _buildBottomSheetTheme(colorScheme),
      navigationRailTheme: _buildNavigationRailTheme(colorScheme),
    );
  }

  /// 获取暗色主题
  static ThemeData get darkTheme {
    const colorScheme = ColorScheme.dark(
      primary: primaryColor,
      secondary: secondaryColor,
      tertiary: accentColor,
      surface: darkSurface,
      surfaceVariant: Color(0xFF475569), // AI消息气泡背景色 - 中等灰色
      onSurfaceVariant: Color(0xFFE2E8F0), // AI消息气泡文字色 - 浅灰色
      secondaryContainer: Color(0xFF4C1D95), // 系统消息背景色 - 深紫色
      onSecondaryContainer: Color(0xFFDDD6FE), // 系统消息文字色 - 淡紫色
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: darkTextPrimary,
      outline: darkBorder,
      error: errorColor,
      onError: Colors.white,
    );

    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: colorScheme,
      scaffoldBackgroundColor: darkBackground,

      // 文本主题
      textTheme: _buildTextTheme(darkTextPrimary, darkTextSecondary),

      // 系统UI样式
      appBarTheme: _buildAppBarTheme(colorScheme, false),
      cardTheme: _buildCardTheme(colorScheme, false),
      elevatedButtonTheme: _buildElevatedButtonTheme(colorScheme),
      outlinedButtonTheme: _buildOutlinedButtonTheme(colorScheme),
      textButtonTheme: _buildTextButtonTheme(colorScheme),
      inputDecorationTheme: _buildInputDecorationTheme(colorScheme, false),
      dividerTheme: _buildDividerTheme(false),
      chipTheme: _buildChipTheme(colorScheme),
      snackBarTheme: _buildSnackBarTheme(colorScheme),
      dialogTheme: _buildDialogTheme(colorScheme),
      bottomSheetTheme: _buildBottomSheetTheme(colorScheme),
      navigationRailTheme: _buildNavigationRailTheme(colorScheme),
    );
  }

  /// 构建文本主题
  static TextTheme _buildTextTheme(Color primaryColor, Color secondaryColor) {
    return const TextTheme().copyWith(
      displayLarge: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        fontSize: 32,
        fontWeight: FontWeight.w700,
        color: primaryColor,
        height: 1.2,
      ),
      displayMedium: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        fontSize: 28,
        fontWeight: FontWeight.w600,
        color: primaryColor,
        height: 1.2,
      ),
      displaySmall: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: primaryColor,
        height: 1.3,
      ),
      headlineLarge: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: primaryColor,
        height: 1.3,
      ),
      headlineMedium: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: primaryColor,
        height: 1.4,
      ),
      headlineSmall: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: primaryColor,
        height: 1.4,
      ),
      titleLarge: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: primaryColor,
        height: 1.4,
      ),
      titleMedium: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: primaryColor,
        height: 1.4,
      ),
      titleSmall: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: primaryColor,
        height: 1.4,
      ),
      bodyLarge: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: primaryColor,
        height: 1.5,
      ),
      bodyMedium: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        fontSize: 13,
        fontWeight: FontWeight.w400,
        color: primaryColor,
        height: 1.5,
      ),
      bodySmall: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        fontSize: 12,
        fontWeight: FontWeight.w400,
        color: secondaryColor,
        height: 1.5,
      ),
      labelLarge: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: primaryColor,
        height: 1.4,
      ),
      labelMedium: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: secondaryColor,
        height: 1.4,
      ),
      labelSmall: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        fontSize: 10,
        fontWeight: FontWeight.w500,
        color: secondaryColor,
        height: 1.4,
      ),
    );
  }

  /// 构建AppBar主题
  static AppBarTheme _buildAppBarTheme(ColorScheme colorScheme, bool isLight) {
    return AppBarTheme(
      backgroundColor: colorScheme.surface,
      foregroundColor: colorScheme.onSurface,
      elevation: 0,
      scrolledUnderElevation: 1,
      centerTitle: false,
      titleTextStyle: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: colorScheme.onSurface,
      ),
      systemOverlayStyle: isLight
          ? SystemUiOverlayStyle.dark
          : SystemUiOverlayStyle.light,
    );
  }

  /// 构建卡片主题
  static CardTheme _buildCardTheme(ColorScheme colorScheme, bool isLight) {
    return CardTheme(
      color: isLight ? lightCardColor : darkCardColor,
      elevation: 0,
      shadowColor: colorScheme.shadow.withOpacity(0.08),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        side: BorderSide(
          color: isLight ? lightBorder : colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
      ),
      margin: EdgeInsets.zero,
    );
  }

  /// 构建按钮主题
  static ElevatedButtonThemeData _buildElevatedButtonTheme(ColorScheme colorScheme) {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        elevation: 0,
        shadowColor: colorScheme.shadow.withOpacity(0.2),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        minimumSize: const Size(64, 40),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: const TextStyle(
          fontFamily: ThemeConstants.primaryFontFamily,
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
      ),
    );
  }
  /// 构建轮廓按钮主题
  static OutlinedButtonThemeData _buildOutlinedButtonTheme(ColorScheme colorScheme) {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: colorScheme.primary,
        side: BorderSide(color: colorScheme.primary),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        minimumSize: const Size(64, 40),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: const TextStyle(
          fontFamily: ThemeConstants.primaryFontFamily,
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
      ),
    );
  }

  /// 构建文本按钮主题
  static TextButtonThemeData _buildTextButtonTheme(ColorScheme colorScheme) {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: colorScheme.primary,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        minimumSize: const Size(64, 36),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6),
        ),
        textStyle: const TextStyle(
          fontFamily: ThemeConstants.primaryFontFamily,
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
      ),
    );
  }

  /// 构建输入框主题
  static InputDecorationTheme _buildInputDecorationTheme(ColorScheme colorScheme, bool isLight) {
    return InputDecorationTheme(
      filled: true,
      fillColor: isLight ? lightElevatedSurface : colorScheme.surface,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: isLight ? lightBorder : colorScheme.outline),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: isLight ? lightBorderSecondary : colorScheme.outline.withValues(alpha: 0.5)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: colorScheme.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: colorScheme.error),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: colorScheme.error, width: 2),
      ),
      disabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: isLight ? lightBorder : colorScheme.outline.withValues(alpha: 0.3)),
      ),
      labelStyle: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        color: isLight ? lightTextSecondary : colorScheme.onSurface.withOpacity(0.6),
      ),
      hintStyle: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        color: isLight ? lightTextTertiary : colorScheme.onSurface.withOpacity(0.4),
      ),
      helperStyle: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        fontSize: 12,
        color: isLight ? lightTextSecondary : colorScheme.onSurface.withOpacity(0.6),
      ),
      errorStyle: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        fontSize: 12,
        color: colorScheme.error,
      ),
    );
  }

  /// 构建分割线主题
  static DividerThemeData _buildDividerTheme(bool isLight) {
    return DividerThemeData(
      color: isLight ? lightBorderSecondary : darkDivider,
      thickness: 1,
      space: 1,
    );
  }

  /// 构建芯片主题
  static ChipThemeData _buildChipTheme(ColorScheme colorScheme) {
    return ChipThemeData(
      backgroundColor: colorScheme.surfaceContainerHighest,
      selectedColor: colorScheme.primary.withValues(alpha: 0.12),
      disabledColor: colorScheme.onSurface.withValues(alpha: 0.12),
      deleteIconColor: colorScheme.onSurface.withValues(alpha: 0.6),
      labelStyle: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        color: colorScheme.onSurface,
      ),
      secondaryLabelStyle: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        color: colorScheme.onSurface.withValues(alpha: 0.6),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: colorScheme.outlineVariant),
      ),
    );
  }

  /// 构建SnackBar主题
  static SnackBarThemeData _buildSnackBarTheme(ColorScheme colorScheme) {
    return SnackBarThemeData(
      backgroundColor: colorScheme.inverseSurface,
      contentTextStyle: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        color: colorScheme.onInverseSurface,
      ),
      actionTextColor: colorScheme.primary,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      behavior: SnackBarBehavior.floating,
    );
  }

  /// 构建对话框主题
  static DialogTheme _buildDialogTheme(ColorScheme colorScheme) {
    return DialogTheme(
      backgroundColor: colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      elevation: 8,
      shadowColor: colorScheme.shadow.withOpacity(0.2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      titleTextStyle: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: colorScheme.onSurface,
      ),
      contentTextStyle: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        fontSize: 14,
        color: colorScheme.onSurface.withOpacity(0.8),
      ),
    );
  }

  /// 构建底部表单主题
  static BottomSheetThemeData _buildBottomSheetTheme(ColorScheme colorScheme) {
    return BottomSheetThemeData(
      backgroundColor: colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      elevation: 8,
      shadowColor: colorScheme.shadow.withOpacity(0.2),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
    );
  }

  /// 构建导航栏主题
  static NavigationRailThemeData _buildNavigationRailTheme(ColorScheme colorScheme) {
    return NavigationRailThemeData(
      backgroundColor: colorScheme.surface,
      elevation: 0,
      selectedIconTheme: IconThemeData(
        color: colorScheme.primary,
        size: ThemeConstants.iconSizeMedium,
      ),
      unselectedIconTheme: IconThemeData(
        color: colorScheme.onSurface.withOpacity(0.6),
        size: ThemeConstants.iconSizeMedium,
      ),
      selectedLabelTextStyle: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        color: colorScheme.primary,
        fontWeight: FontWeight.w500,
      ),
      unselectedLabelTextStyle: TextStyle(
        fontFamily: ThemeConstants.primaryFontFamily,
        color: colorScheme.onSurface.withOpacity(0.6),
        fontWeight: FontWeight.w400,
      ),
    );
  }

  // 新增：层级颜色辅助方法

  /// 获取浅色主题的悬停颜色
  static Color getLightHoverColor() => lightHover;

  /// 获取浅色主题的按压颜色
  static Color getLightPressedColor() => lightPressed;

  /// 获取浅色主题的次级卡片颜色
  static Color getLightSecondaryCardColor() => lightSecondaryCard;

  /// 获取浅色主题的三级文本颜色
  static Color getLightTertiaryTextColor() => lightTextTertiary;

  /// 获取浅色主题的次要边框颜色
  static Color getLightSecondaryBorderColor() => lightBorderSecondary;
}
