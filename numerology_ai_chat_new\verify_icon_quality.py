#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证应用图标质量
检查图标文件是否包含足够的尺寸和质量
"""

from PIL import Image
import os
import struct

def analyze_ico_file(ico_path):
    """
    分析ICO文件的详细信息
    
    Args:
        ico_path: ICO文件路径
    
    Returns:
        dict: 包含图标信息的字典
    """
    if not os.path.exists(ico_path):
        return {"error": f"文件不存在: {ico_path}"}
    
    try:
        # 读取ICO文件头
        with open(ico_path, 'rb') as f:
            # ICO文件头: 6字节
            header = f.read(6)
            if len(header) < 6:
                return {"error": "文件太小，不是有效的ICO文件"}
            
            # 解析头部
            reserved, type_field, count = struct.unpack('<HHH', header)
            
            if reserved != 0 or type_field != 1:
                return {"error": "不是有效的ICO文件"}
            
            print(f"📊 ICO文件分析: {ico_path}")
            print(f"🔢 包含图标数量: {count}")
            
            # 读取每个图标的目录条目
            icons_info = []
            for i in range(count):
                # 每个目录条目: 16字节
                entry = f.read(16)
                if len(entry) < 16:
                    break
                
                width, height, colors, reserved, planes, bpp, size, offset = struct.unpack('<BBBBHHLL', entry)
                
                # 宽度和高度为0表示256像素
                if width == 0:
                    width = 256
                if height == 0:
                    height = 256
                
                icons_info.append({
                    'width': width,
                    'height': height,
                    'colors': colors,
                    'bpp': bpp,
                    'size': size,
                    'offset': offset
                })
                
                print(f"  📏 图标 {i+1}: {width}x{height} 像素, {bpp} 位色深, {size} 字节")
            
            return {
                "count": count,
                "icons": icons_info,
                "file_size": os.path.getsize(ico_path)
            }
            
    except Exception as e:
        return {"error": f"分析文件失败: {e}"}

def check_icon_quality(ico_path):
    """
    检查图标质量是否符合要求
    
    Args:
        ico_path: ICO文件路径
    
    Returns:
        dict: 质量检查结果
    """
    analysis = analyze_ico_file(ico_path)
    
    if "error" in analysis:
        return {"quality": "error", "message": analysis["error"]}
    
    # 质量检查标准
    required_sizes = [16, 32, 48, 64, 128, 256]
    recommended_sizes = [16, 20, 24, 32, 40, 48, 64, 96, 128, 256]
    high_quality_sizes = [16, 20, 24, 30, 32, 40, 48, 60, 64, 72, 80, 96, 128, 256, 512]
    
    available_sizes = [icon['width'] for icon in analysis['icons']]
    
    # 检查基本要求
    missing_required = [size for size in required_sizes if size not in available_sizes]
    missing_recommended = [size for size in recommended_sizes if size not in available_sizes]
    
    # 计算质量等级
    if len(missing_required) == 0:
        if len(missing_recommended) == 0:
            if len([size for size in high_quality_sizes if size in available_sizes]) >= 12:
                quality = "excellent"
                message = "超高质量图标，支持所有显示场景"
            else:
                quality = "good"
                message = "高质量图标，支持大部分显示场景"
        else:
            quality = "acceptable"
            message = f"基本质量图标，建议添加尺寸: {missing_recommended}"
    else:
        quality = "poor"
        message = f"质量不足，缺少必需尺寸: {missing_required}"
    
    # 检查文件大小
    file_size_kb = analysis['file_size'] / 1024
    if file_size_kb < 10:
        size_comment = "文件较小，可能缺少高分辨率版本"
    elif file_size_kb > 500:
        size_comment = "文件较大，包含丰富的尺寸版本"
    else:
        size_comment = "文件大小适中"
    
    return {
        "quality": quality,
        "message": message,
        "file_size_kb": file_size_kb,
        "size_comment": size_comment,
        "available_sizes": sorted(available_sizes),
        "missing_required": missing_required,
        "missing_recommended": missing_recommended,
        "icon_count": analysis['count']
    }

def test_icon_with_pil(ico_path):
    """
    使用PIL测试图标文件
    
    Args:
        ico_path: ICO文件路径
    """
    try:
        print(f"🔍 PIL测试: {ico_path}")
        with Image.open(ico_path) as img:
            print(f"📏 主图标尺寸: {img.size[0]}x{img.size[1]} 像素")
            print(f"🎨 图标模式: {img.mode}")
            print(f"📄 图标格式: {img.format}")
            
            # 尝试获取所有尺寸
            if hasattr(img, 'n_frames'):
                print(f"🔢 包含帧数: {img.n_frames}")
            
            return True
    except Exception as e:
        print(f"❌ PIL测试失败: {e}")
        return False

def main():
    print("🔍 应用图标质量验证")
    print("=" * 50)
    
    # 检查的图标文件
    icon_files = [
        "windows/runner/resources/app_icon.ico",
        "assets/images/logo.png"
    ]
    
    for icon_file in icon_files:
        if not os.path.exists(icon_file):
            print(f"⚠️ 文件不存在: {icon_file}")
            continue
        
        print(f"\n📁 检查文件: {icon_file}")
        print("-" * 40)
        
        if icon_file.endswith('.ico'):
            # 详细分析ICO文件
            result = check_icon_quality(icon_file)
            
            # 显示质量等级
            quality_emoji = {
                "excellent": "🌟",
                "good": "✅", 
                "acceptable": "⚠️",
                "poor": "❌",
                "error": "💥"
            }
            
            print(f"{quality_emoji.get(result['quality'], '❓')} 质量等级: {result['quality'].upper()}")
            print(f"📝 评价: {result['message']}")
            
            if 'file_size_kb' in result:
                print(f"📦 文件大小: {result['file_size_kb']:.1f} KB")
                print(f"💬 大小评价: {result['size_comment']}")
                print(f"🔢 图标数量: {result['icon_count']}")
                print(f"📏 可用尺寸: {result['available_sizes']}")
                
                if result['missing_recommended']:
                    print(f"⚠️ 建议添加尺寸: {result['missing_recommended']}")
            
            # PIL测试
            test_icon_with_pil(icon_file)
            
        elif icon_file.endswith(('.jpg', '.jpeg', '.png')):
            # 分析源图片
            try:
                with Image.open(icon_file) as img:
                    print(f"📏 图片尺寸: {img.size[0]}x{img.size[1]} 像素")
                    print(f"🎨 图片模式: {img.mode}")
                    print(f"📄 图片格式: {img.format}")
                    
                    # 质量建议
                    min_size = min(img.size)
                    if min_size >= 512:
                        print("✅ 源图片质量优秀，适合生成高质量图标")
                    elif min_size >= 256:
                        print("⚠️ 源图片质量良好，建议使用更高分辨率")
                    else:
                        print("❌ 源图片分辨率较低，建议使用至少512x512的图片")
                        
            except Exception as e:
                print(f"❌ 分析源图片失败: {e}")
    
    print("\n" + "=" * 50)
    print("📝 质量标准说明:")
    print("🌟 Excellent: 包含15+种尺寸，支持4K和高DPI")
    print("✅ Good: 包含10+种尺寸，支持高DPI")
    print("⚠️ Acceptable: 包含基本尺寸，满足一般需求")
    print("❌ Poor: 缺少必需尺寸，显示效果差")
    
    print("\n💡 改进建议:")
    print("1. 运行 python generate_high_quality_icon.py 生成超高质量图标")
    print("2. 确保源图片 assets/images/logo.png 分辨率至少512x512")
    print("3. 使用 build_with_icon.bat 进行自动化构建")

if __name__ == "__main__":
    main()
