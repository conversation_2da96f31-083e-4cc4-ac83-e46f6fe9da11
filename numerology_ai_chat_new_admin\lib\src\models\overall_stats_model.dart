import 'package:json_annotation/json_annotation.dart';

part 'overall_stats_model.g.dart';

@JsonSerializable()
class OverallStatsModel {
  final UserStatsModel users;
  final OrderStatsModel orders;
  final PaymentStatsModel payments;
  final UsageStatsModel usage;
  final AgentStatsModel agents;
  final ModelStatsModel models;
  final DateTime lastUpdated;

  const OverallStatsModel({
    required this.users,
    required this.orders,
    required this.payments,
    required this.usage,
    required this.agents,
    required this.models,
    required this.lastUpdated,
  });

  factory OverallStatsModel.fromJson(Map<String, dynamic> json) {
    // 处理时间字段
    DateTime parseDateTime(dynamic value) {
      if (value == null) return DateTime.now();
      if (value is String) {
        try {
          return DateTime.parse(value);
        } catch (e) {
          return DateTime.now();
        }
      }
      return DateTime.now();
    }

    return OverallStatsModel(
      users: UserStatsModel.fromJson(json['users'] as Map<String, dynamic>),
      orders: OrderStatsModel.fromJson(json['orders'] as Map<String, dynamic>),
      payments: PaymentStatsModel.fromJson(json['payments'] as Map<String, dynamic>),
      usage: UsageStatsModel.fromJson(json['usage'] as Map<String, dynamic>),
      agents: AgentStatsModel.fromJson(json['agents'] as Map<String, dynamic>),
      models: ModelStatsModel.fromJson(json['models'] as Map<String, dynamic>),
      lastUpdated: parseDateTime(json['lastUpdated']),
    );
  }

  Map<String, dynamic> toJson() => _$OverallStatsModelToJson(this);
}

@JsonSerializable()
class UserStatsModel {
  final int total;
  final int todayNew;
  final int activeLastWeek;

  const UserStatsModel({
    required this.total,
    required this.todayNew,
    required this.activeLastWeek,
  });

  factory UserStatsModel.fromJson(Map<String, dynamic> json) => _$UserStatsModelFromJson(json);
  Map<String, dynamic> toJson() => _$UserStatsModelToJson(this);
}

@JsonSerializable()
class OrderStatsModel {
  final int total;
  final int completed;
  final int pending;
  final int todayNew;
  final double totalRevenue;

  const OrderStatsModel({
    required this.total,
    required this.completed,
    required this.pending,
    required this.todayNew,
    required this.totalRevenue,
  });

  factory OrderStatsModel.fromJson(Map<String, dynamic> json) => _$OrderStatsModelFromJson(json);
  Map<String, dynamic> toJson() => _$OrderStatsModelToJson(this);
}

@JsonSerializable()
class PaymentStatsModel {
  final int total;
  final int success;
  final int failed;
  final int todayNew;
  final String successRate;

  const PaymentStatsModel({
    required this.total,
    required this.success,
    required this.failed,
    required this.todayNew,
    required this.successRate,
  });

  factory PaymentStatsModel.fromJson(Map<String, dynamic> json) => _$PaymentStatsModelFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentStatsModelToJson(this);
}

@JsonSerializable()
class UsageStatsModel {
  final int total;
  final int todayNew;
  final int totalPowerConsumed;
  final int todayPowerConsumed;

  const UsageStatsModel({
    required this.total,
    required this.todayNew,
    required this.totalPowerConsumed,
    required this.todayPowerConsumed,
  });

  factory UsageStatsModel.fromJson(Map<String, dynamic> json) => _$UsageStatsModelFromJson(json);
  Map<String, dynamic> toJson() => _$UsageStatsModelToJson(this);
}

@JsonSerializable()
class AgentStatsModel {
  final int total;
  final int enabled;
  final List<PopularAgentModel> popular;

  const AgentStatsModel({
    required this.total,
    required this.enabled,
    required this.popular,
  });

  factory AgentStatsModel.fromJson(Map<String, dynamic> json) {
    final popularData = json['popular'] as List? ?? [];
    final popular = popularData.map((item) => PopularAgentModel.fromJson(item as Map<String, dynamic>)).toList();
    
    return AgentStatsModel(
      total: json['total'] as int,
      enabled: json['enabled'] as int,
      popular: popular,
    );
  }

  Map<String, dynamic> toJson() => _$AgentStatsModelToJson(this);
}

@JsonSerializable()
class PopularAgentModel {
  final String id;
  final String name;
  final int count;

  const PopularAgentModel({
    required this.id,
    required this.name,
    required this.count,
  });

  factory PopularAgentModel.fromJson(Map<String, dynamic> json) => _$PopularAgentModelFromJson(json);
  Map<String, dynamic> toJson() => _$PopularAgentModelToJson(this);
}

@JsonSerializable()
class ModelStatsModel {
  final int total;
  final int enabled;
  final Map<String, int> usageByLevel;

  const ModelStatsModel({
    required this.total,
    required this.enabled,
    required this.usageByLevel,
  });

  factory ModelStatsModel.fromJson(Map<String, dynamic> json) {
    final usageByLevel = Map<String, int>.from(json['usageByLevel'] as Map? ?? {});
    
    return ModelStatsModel(
      total: json['total'] as int,
      enabled: json['enabled'] as int,
      usageByLevel: usageByLevel,
    );
  }

  Map<String, dynamic> toJson() => _$ModelStatsModelToJson(this);
}
