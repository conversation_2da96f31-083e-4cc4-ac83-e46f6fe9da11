const { userCollection, quotaOperationCollection } = require('../utils/db')
const { validateGetUserInfo, validateModifyUserQuota, validatePagination } = require('../utils/validate')
const { weakAdminAuthMiddleware } = require('../middleware/auth')
const { createBusinessError, successResponse, ERROR_CODES } = require('../middleware/error_handler')
const logger = require('../utils/logger')

/**
 * 查询用户信息
 * @param {object} params 查询参数
 * @returns {object} 用户信息
 */
async function getUserInfo(params) {
  try {
    // 参数校验
    const { username, token } = validateGetUserInfo(params)
    
    // 权限验证
    const operatorInfo = await weakAdminAuthMiddleware(token)
    
    logger.info('查询用户信息', {
      operator: operatorInfo.username,
      targetUsername: username
    })
    
    // 查找目标用户
    const user = await userCollection.findByUsername(username)
    if (!user) {
      throw createBusinessError(
        ERROR_CODES.USER_NOT_FOUND,
        '用户不存在',
        404
      )
    }
    
    // 返回用户基本信息和算力余额
    return successResponse({
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        phone: user.phone,
        status: user.status,
        availableCount: user.availableCount,
        totalUsageCount: user.totalUsageCount,
        membership: user.membership,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt
      }
    }, '查询成功')
    
  } catch (error) {
    logger.error('查询用户信息失败', {
      error: error.message,
      params: { ...params, token: '[HIDDEN]' }
    })
    throw error
  }
}

/**
 * 修改用户算力
 * @param {object} params 修改参数
 * @returns {object} 修改结果
 */
async function modifyUserQuota(params) {
  try {
    // 参数校验
    const { targetUsername, operationType, quotaAmount, reason, token } = validateModifyUserQuota(params)
    
    // 权限验证
    const operatorInfo = await weakAdminAuthMiddleware(token)
    
    logger.info('修改用户算力', {
      operator: operatorInfo.username,
      targetUsername,
      operationType,
      quotaAmount,
      reason
    })
    
    // 查找目标用户
    const user = await userCollection.findByUsername(targetUsername)
    if (!user) {
      throw createBusinessError(
        ERROR_CODES.USER_NOT_FOUND,
        '目标用户不存在',
        404
      )
    }
    
    // 记录操作前的算力余额
    const quotaBefore = user.availableCount
    let quotaAfter
    
    // 根据操作类型计算新的算力余额
    if (operationType === 'increase') {
      quotaAfter = quotaBefore + quotaAmount
    } else if (operationType === 'decrease') {
      quotaAfter = Math.max(0, quotaBefore - quotaAmount) // 不能小于0
    }
    
    // 更新用户算力
    await userCollection.updateQuota(user._id, quotaAfter)
    
    // 记录操作历史
    await quotaOperationCollection.recordOperation(
      operatorInfo.userId,
      operatorInfo.username,
      user._id,
      user.username,
      operationType,
      quotaAmount,
      quotaBefore,
      quotaAfter,
      reason || ''
    )
    
    logger.info('用户算力修改成功', {
      operator: operatorInfo.username,
      targetUsername,
      operationType,
      quotaAmount,
      quotaBefore,
      quotaAfter
    })
    
    return successResponse({
      operation: {
        operationType,
        quotaAmount,
        quotaBefore,
        quotaAfter,
        reason: reason || '',
        operatedAt: new Date().toISOString()
      },
      user: {
        username: user.username,
        availableCount: quotaAfter
      }
    }, `算力${operationType === 'increase' ? '增加' : '减少'}成功`)
    
  } catch (error) {
    logger.error('修改用户算力失败', {
      error: error.message,
      params: { ...params, token: '[HIDDEN]' }
    })
    throw error
  }
}

/**
 * 获取算力操作记录
 * @param {object} params 查询参数
 * @returns {object} 操作记录
 */
async function getQuotaOperations(params) {
  try {
    // 参数校验
    const { page, pageSize, token } = validatePagination(params)
    
    // 权限验证
    const operatorInfo = await weakAdminAuthMiddleware(token)
    
    logger.info('查询算力操作记录', {
      operator: operatorInfo.username,
      page,
      pageSize
    })
    
    // 查询操作记录（只显示当前管理员的操作）
    const filters = {
      operatorId: operatorInfo.userId
    }
    
    // 如果提供了目标用户名，添加过滤条件
    if (params.targetUsername) {
      filters.targetUsername = params.targetUsername
    }
    
    const result = await quotaOperationCollection.getOperations(page, pageSize, filters)
    
    return successResponse(result, '查询成功')
    
  } catch (error) {
    logger.error('查询算力操作记录失败', {
      error: error.message,
      params: { ...params, token: '[HIDDEN]' }
    })
    throw error
  }
}

module.exports = {
  getUserInfo,
  modifyUserQuota,
  getQuotaOperations
}
