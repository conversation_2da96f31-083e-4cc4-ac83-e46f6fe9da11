#!/bin/bash

# Go Proxy 启动脚本

echo "启动 Go Proxy 服务..."

# 检查进程是否已经在运行
if pgrep -f "go_proxy_linux" > /dev/null; then
    echo "Go Proxy 服务已在运行中"
    echo "如需重启，请先运行: ./stop.sh"
    exit 1
fi

# 启动服务
nohup ./go_proxy_linux > go_proxy.log 2>&1 &

# 获取进程ID
PID=$!
echo "Go Proxy 服务已启动，PID: $PID"
echo "日志文件: go_proxy.log"
echo "查看日志: tail -f go_proxy.log"
echo "停止服务: ./stop.sh"

# 等待一秒检查服务是否正常启动
sleep 1
if ps -p $PID > /dev/null; then
    echo "服务启动成功！"
    echo "健康检查: curl http://localhost:8080/healthz"
else
    echo "服务启动失败，请检查日志文件"
    exit 1
fi
