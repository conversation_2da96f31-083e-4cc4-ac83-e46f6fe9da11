const logger = require('../utils/logger')

/**
 * 错误码定义
 */
const ERROR_CODES = {
  // 通用错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  INVALID_PARAMS: 'INVALID_PARAMS',
  
  // 认证错误
  UNAUTHORIZED: 'UNAUTHORIZED',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  
  // 管理员错误
  ADMIN_NOT_FOUND: 'ADMIN_NOT_FOUND',
  ADMIN_DISABLED: 'ADMIN_DISABLED',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  
  // 用户错误
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  USER_ALREADY_EXISTS: 'USER_ALREADY_EXISTS',
  USER_DISABLED: 'USER_DISABLED',
  
  // 智能体错误
  AGENT_NOT_FOUND: 'AGENT_NOT_FOUND',
  AGENT_NAME_EXISTS: 'AGENT_NAME_EXISTS',
  
  // 模型错误
  MODEL_NOT_FOUND: 'MODEL_NOT_FOUND',
  MODEL_NAME_EXISTS: 'MODEL_NAME_EXISTS',
  
  // 页面配置错误
  PAGE_NOT_FOUND: 'PAGE_NOT_FOUND',
  SLUG_ALREADY_EXISTS: 'SLUG_ALREADY_EXISTS',
  
  // 数据库错误
  DATABASE_ERROR: 'DATABASE_ERROR',
  UPDATE_FAILED: 'UPDATE_FAILED',
  CREATE_FAILED: 'CREATE_FAILED',
  DELETE_FAILED: 'DELETE_FAILED'
}

/**
 * 自定义业务异常类
 */
class BusinessError extends Error {
  constructor(code, message, statusCode = 400) {
    super(message)
    this.name = 'BusinessError'
    this.code = code
    this.statusCode = statusCode
  }
}

/**
 * 创建业务异常
 * @param {string} code 错误码
 * @param {string} message 错误信息
 * @param {number} statusCode HTTP状态码
 * @returns {BusinessError} 业务异常对象
 */
function createBusinessError(code, message, statusCode = 400) {
  return new BusinessError(code, message, statusCode)
}

/**
 * 成功响应
 * @param {any} data 响应数据
 * @param {string} message 响应消息
 * @returns {object} 标准化的成功响应
 */
function successResponse(data = null, message = '操作成功') {
  return {
    success: true,
    message,
    data,
    timestamp: new Date().toISOString()
  }
}

/**
 * 统一错误处理函数
 * @param {Error} error 错误对象
 * @returns {object} 标准化的错误响应
 */
function errorHandler(error) {
  logger.error('Error occurred', {
    name: error.name,
    message: error.message,
    stack: error.stack,
    code: error.code
  })
  
  // 业务异常
  if (error instanceof BusinessError) {
    return {
      success: false,
      code: error.code,
      message: error.message,
      statusCode: error.statusCode,
      timestamp: new Date().toISOString()
    }
  }
  
  // 参数校验错误
  if (error.message && error.message.includes('参数校验失败')) {
    return {
      success: false,
      code: ERROR_CODES.INVALID_PARAMS,
      message: error.message,
      statusCode: 400,
      timestamp: new Date().toISOString()
    }
  }
  
  // JWT相关错误
  if (error.name === 'JsonWebTokenError') {
    return {
      success: false,
      code: ERROR_CODES.TOKEN_INVALID,
      message: 'Token无效',
      statusCode: 401,
      timestamp: new Date().toISOString()
    }
  }
  
  if (error.name === 'TokenExpiredError') {
    return {
      success: false,
      code: ERROR_CODES.TOKEN_EXPIRED,
      message: 'Token已过期',
      statusCode: 401,
      timestamp: new Date().toISOString()
    }
  }
  
  // 数据库错误
  if (error.message && error.message.includes('database')) {
    return {
      success: false,
      code: ERROR_CODES.DATABASE_ERROR,
      message: '数据库操作失败',
      statusCode: 500,
      timestamp: new Date().toISOString()
    }
  }
  
  // 未知错误
  return {
    success: false,
    code: ERROR_CODES.UNKNOWN_ERROR,
    message: '系统内部错误',
    statusCode: 500,
    timestamp: new Date().toISOString()
  }
}

module.exports = {
  ERROR_CODES,
  BusinessError,
  createBusinessError,
  successResponse,
  errorHandler
}