import{u as M,a as b,c as o,d as C,e as E,g as e,w as t,f as a,i as s,s as k,m as L,j as i,t as N,n as S,p as T,q as V,v as q,x as j,E as A}from"./index-0bf154dd.js";import{_ as D}from"./_plugin-vue_export-helper-c27b6911.js";const I={class:"main-layout"},R={class:"header-content"},$={class:"header-right"},z={class:"user-dropdown"},F={__name:"MainLayout",setup(G){const c=M(),u=b(),p=async d=>{if(d==="logout")try{await j.confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),u.logout(),A.success("已退出登录"),c.push("/login")}catch{}};return(d,n)=>{const l=o("el-icon"),m=o("el-dropdown-item"),f=o("el-dropdown-menu"),w=o("el-dropdown"),h=o("el-header"),_=o("el-menu-item"),v=o("el-menu"),x=o("el-aside"),g=o("router-view"),y=o("el-main"),B=o("el-container");return C(),E("div",I,[e(h,{class:"header"},{default:t(()=>[a("div",R,[n[1]||(n[1]=a("div",{class:"header-left"},[a("h1",{class:"app-title"},"管理员系统")],-1)),a("div",$,[e(w,{onCommand:p},{dropdown:t(()=>[e(f,null,{default:t(()=>[e(m,{command:"logout"},{default:t(()=>[e(l,null,{default:t(()=>[e(s(k))]),_:1}),n[0]||(n[0]=L(" 退出登录 "))]),_:1,__:[0]})]),_:1})]),default:t(()=>{var r;return[a("span",z,[e(l,null,{default:t(()=>[e(s(i))]),_:1}),a("span",null,N((r=s(u).user)==null?void 0:r.username),1),e(l,{class:"el-icon--right"},{default:t(()=>[e(s(S))]),_:1})])]}),_:1})])])]),_:1}),e(B,{class:"main-container"},{default:t(()=>[e(x,{width:"200px",class:"sidebar"},{default:t(()=>[e(v,{"default-active":d.$route.path,router:"",class:"sidebar-menu",collapse:!1},{default:t(()=>[e(_,{index:"/dashboard"},{default:t(()=>[e(l,null,{default:t(()=>[e(s(T))]),_:1}),n[2]||(n[2]=a("span",null,"仪表板",-1))]),_:1,__:[2]}),e(_,{index:"/activation-codes"},{default:t(()=>[e(l,null,{default:t(()=>[e(s(V))]),_:1}),n[3]||(n[3]=a("span",null,"激活码管理",-1))]),_:1,__:[3]}),e(_,{index:"/user-quota"},{default:t(()=>[e(l,null,{default:t(()=>[e(s(i))]),_:1}),n[4]||(n[4]=a("span",null,"用户算力管理",-1))]),_:1,__:[4]}),e(_,{index:"/history"},{default:t(()=>[e(l,null,{default:t(()=>[e(s(q))]),_:1}),n[5]||(n[5]=a("span",null,"操作历史",-1))]),_:1,__:[5]})]),_:1},8,["default-active"])]),_:1}),e(y,{class:"content"},{default:t(()=>[e(g)]),_:1})]),_:1})])}}},K=D(F,[["__scopeId","data-v-67320999"]]);export{K as default};
