import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:gap/gap.dart';

import '../core/constants/app_constants.dart';
import '../providers/auth_provider.dart';
import '../providers/theme_provider.dart';
import '../providers/agent_provider.dart';
import '../providers/model_provider.dart';
import '../providers/chat_provider.dart';
import '../providers/portrait_mode_provider.dart';
import '../widgets/custom_title_bar.dart';

/// 主页面布局
class HomeScreen extends ConsumerStatefulWidget {
  final Widget child;

  const HomeScreen({super.key, required this.child});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  int _selectedIndex = 0;

  final List<NavigationItem> _navigationItems = [
    NavigationItem(
      icon: Icons.chat_bubble_outline,
      selectedIcon: Icons.chat_bubble,
      label: 'AI对话',
      route: AppRoutes.chat,
    ),
    NavigationItem(
      icon: Icons.calculate_outlined,
      selectedIcon: Icons.calculate,
      label: '八字排盘',
      route: AppRoutes.bazi,
    ),
    NavigationItem(
      icon: Icons.shopping_cart_outlined,
      selectedIcon: Icons.shopping_cart,
      label: '充值算力',
      route: AppRoutes.purchase,
    ),
    NavigationItem(
      icon: Icons.person_outline,
      selectedIcon: Icons.person,
      label: '个人中心',
      route: AppRoutes.profile,
    ),
    NavigationItem(
      icon: Icons.settings_outlined,
      selectedIcon: Icons.settings,
      label: '设置',
      route: AppRoutes.settings,
    ),
  ];

  @override
  void initState() {
    super.initState();
    // 延迟到下一帧执行初始化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 检查是否需要加载数据（避免在对话锁定时重复请求）
      _conditionallyLoadData();
      // 更新选中索引
      _updateSelectedIndex();
    });
  }

  /// 根据当前状态条件性地加载数据
  void _conditionallyLoadData() {
    if (!mounted) return;

    final chatState = ref.read(chatProvider);

    // 如果当前对话已经开始且智能体、模型已锁定，则不需要重新加载数据
    final hasActiveConversation = chatState.currentConversation != null &&
                                  !chatState.currentConversation!.isEmpty;
    final hasLockedSelections = chatState.selectedAgent != null &&
                               chatState.selectedModel != null;

    // 只有在没有活跃对话或没有锁定选择时才加载数据
    if (!hasActiveConversation || !hasLockedSelections) {
      // 触发自动加载智能体和模型
      ref.read(autoLoadAgentsProvider);
      ref.read(autoLoadModelsProvider);
    }

    // 每次进入主页面时刷新用户信息以同步算力
    _refreshUserInfo();
  }

  /// 刷新用户信息
  void _refreshUserInfo() {
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        ref.read(authProvider.notifier).refreshUser();
      }
    });
  }

  void _updateSelectedIndex() {
    if (!mounted) return;

    final location = GoRouterState.of(context).uri.path;
    final index = _navigationItems.indexWhere((item) => item.route == location);
    if (index != -1 && index != _selectedIndex) {
      final previousIndex = _selectedIndex;
      setState(() {
        _selectedIndex = index;
      });

      // 如果切换到了显示算力的页面（购买页面、个人中心），刷新用户信息
      if (previousIndex != _selectedIndex &&
          (_selectedIndex == 2 || _selectedIndex == 4)) {
        _refreshUserInfo();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isPortraitMode = ref.watch(portraitModeStateProvider);

    return Scaffold(
      body: Column(
        children: [
          // 自定义标题栏（竖屏模式下保留）
          CustomTitleBar(
              title: Row(
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: Image.asset(
                        'assets/images/logo.png',
                        width: 24,
                        height: 24,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          // 如果图片加载失败，显示默认图标
                          return Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Theme.of(context).colorScheme.primary,
                                  Theme.of(context).colorScheme.secondary,
                                ],
                              ),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: const Icon(
                              Icons.auto_awesome,
                              size: 16,
                              color: Colors.white,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(AppConstants.appName),
                ],
              ),
            ),

          // 主体内容
          Expanded(
            child: isPortraitMode ? _buildPortraitModeLayout(context) : _buildDesktopLayout(context),
          ),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Row(
      children: [
        // 左侧导航栏
        _buildNavigationRail(context),

        // 主内容区域
        Expanded(
          child: widget.child,
        ),
      ],
    );
  }

  /// 构建竖屏模式布局
  Widget _buildPortraitModeLayout(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      child: widget.child,
    );
  }

  Widget _buildNavigationRail(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      width: AppConstants.sidebarWidth,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          right: BorderSide(
            color: theme.colorScheme.outline.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // 导航项目
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 8),
              itemCount: _navigationItems.length,
              itemBuilder: (context, index) {
                final item = _navigationItems[index];
                final isSelected = index == _selectedIndex;

                return _buildNavigationItem(context, item, isSelected, index);
              },
            ),
          ),

          const Divider(height: 1),

          // 用户信息
          _buildUserSection(context),
        ],
      ),
    );
  }

  Widget _buildNavigationItem(BuildContext context, NavigationItem item, bool isSelected, int index) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      child: ListTile(
        leading: Icon(
          isSelected ? item.selectedIcon : item.icon,
          color: isSelected
              ? theme.colorScheme.primary
              : theme.colorScheme.onSurface.withOpacity(0.6),
        ),
        title: Text(
          item.label,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isSelected
                ? theme.colorScheme.primary
                : theme.colorScheme.onSurface.withOpacity(0.8),
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
        selected: isSelected,
        selectedTileColor: theme.colorScheme.primary.withOpacity(0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        onTap: () {
          final previousIndex = _selectedIndex;
          setState(() {
            _selectedIndex = index;
          });
          context.go(item.route);

          // 如果切换到了显示算力的页面（购买页面、个人中心），刷新用户信息
          if (previousIndex != _selectedIndex &&
              (_selectedIndex == 2 || _selectedIndex == 4)) {
            _refreshUserInfo();
          }
        },
      ),
    );
  }

  Widget _buildUserSection(BuildContext context) {
    final theme = Theme.of(context);
    final authState = ref.watch(authProvider);
    final themeNotifier = ref.watch(themeProviderNotifier);
    final user = authState.user;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 主题切换
          ListTile(
            leading: Icon(
              themeNotifier.isDarkMode ? Icons.dark_mode : Icons.light_mode,
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
            title: Text(
              themeNotifier.themeModeDisplayName,
              style: theme.textTheme.bodyMedium,
            ),
            trailing: Switch(
              value: themeNotifier.isDarkMode,
              onChanged: (_) => ref.read(themeProviderNotifier.notifier).toggleTheme(),
            ),
            contentPadding: EdgeInsets.zero,
          ),

          const Gap(8),

          // 用户信息
          ListTile(
            leading: CircleAvatar(
              radius: 16,
              backgroundColor: theme.colorScheme.primary,
              child: Text(
                user?.username.substring(0, 1).toUpperCase() ?? 'U',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ),
            title: Text(
              user?.username ?? '未知用户',
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            subtitle: Text(
              user?.email ?? '',
              style: theme.textTheme.bodySmall,
            ),
            trailing: PopupMenuButton<String>(
              icon: Icon(
                Icons.more_vert,
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
              onSelected: (value) {
                switch (value) {
                  case 'logout':
                    _handleLogout(context);
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'logout',
                  child: Row(
                    children: [
                      Icon(Icons.logout),
                      Gap(12),
                      Text('退出登录'),
                    ],
                  ),
                ),
              ],
            ),
            contentPadding: EdgeInsets.zero,
          ),

          // 剩余额度信息
          if (user != null) ...[
            const Gap(8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer.withOpacity(0.3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.primary.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.account_balance_wallet_outlined,
                    size: 20,
                    color: theme.colorScheme.primary,
                  ),
                  const Gap(8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '剩余算力',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                        Text(
                          '${user.availableCount} 算力',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (user.availableCount < 10)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.error,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '算力不足',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onError,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }



  Future<void> _handleLogout(BuildContext context) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认退出'),
        content: const Text('确定要退出登录吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('确定'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      await ref.read(authProvider.notifier).logout();
      if (mounted) {
        context.go(AppRoutes.login);
      }
    }
  }
}

/// 导航项目数据类
class NavigationItem {
  final IconData icon;
  final IconData selectedIcon;
  final String label;
  final String route;

  const NavigationItem({
    required this.icon,
    required this.selectedIcon,
    required this.label,
    required this.route,
  });
}
