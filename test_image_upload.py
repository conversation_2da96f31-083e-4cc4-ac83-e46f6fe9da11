#!/usr/bin/env python3
"""
测试图片上传功能的脚本
用于验证前端发送的图片数据是否能被Go服务正确接收和处理
"""

import requests
import json
import base64
import os

def create_test_image_base64():
    """创建一个简单的测试图片的base64编码"""
    # 创建一个简单的1x1像素的PNG图片的base64编码
    # 这是一个透明的1x1像素PNG图片
    png_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89\x00\x00\x00\rIDATx\x9cc\xf8\x0f\x00\x00\x01\x00\x01\x00\x18\xdd\x8d\xb4\x00\x00\x00\x00IEND\xaeB`\x82'
    return "data:image/png;base64," + base64.b64encode(png_data).decode('utf-8')

def test_image_upload():
    """测试图片上传功能"""
    
    # Go服务的URL
    url = "http://go.ziyuanit.com/api/chat/completions"
    
    # 创建测试图片
    test_image_base64 = create_test_image_base64()
    
    # 构造测试请求数据（模拟前端发送的格式）
    test_data = {
        "agentId": "d77d384f684d773c02cee7a2638103ef",  # 通用AI助手
        "modelId": "684e58dedc6caee3a2102b76",  # Gemini模型（支持多模态）
        "messages": [
            {
                "role": "user",
                "content": "请分析这张图片",
                "images": [
                    {
                        "id": "test_image_001",
                        "base64_data": test_image_base64,
                        "mime_type": "image/png",
                        "width": 1,
                        "height": 1,
                        "file_size": len(test_image_base64)
                    }
                ],
                "message_type": "mixed"
            }
        ],
        "stream": False
    }
    
    # 设置请求头
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer test_token"
    }
    
    print("🧪 开始测试图片上传功能...")
    print(f"📤 发送请求到: {url}")
    print(f"🖼️ 图片数据长度: {len(test_image_base64)} 字符")
    print(f"📝 消息内容: {test_data['messages'][0]['content']}")
    
    try:
        # 发送请求
        response = requests.post(url, json=test_data, headers=headers, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ 请求成功！")
            try:
                response_data = response.json()
                print(f"📄 响应内容: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"📄 响应内容（文本）: {response.text}")
        else:
            print(f"❌ 请求失败！状态码: {response.status_code}")
            print(f"📄 错误内容: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ 请求超时")
    except requests.exceptions.ConnectionError:
        print("🔌 连接错误，请确保Go服务正在运行")
    except Exception as e:
        print(f"💥 发生错误: {str(e)}")

def test_text_only():
    """测试纯文本消息（对比测试）"""

    # 注意：这个URL现在应该从数据库动态获取，这里仅用于测试
    url = "http://go.ziyuanit.com/api/chat/completions"
    
    test_data = {
        "agentId": "d77d384f684d773c02cee7a2638103ef",  # 通用AI助手
        "modelId": "684e58d3dc6caee3a2101c6b",  # DeepSeek模型
        "messages": [
            {
                "role": "user",
                "content": "你好，请介绍一下你自己",
                "message_type": "text"
            }
        ],
        "stream": False
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer test_token"
    }
    
    print("\n🧪 开始测试纯文本消息...")
    print(f"📤 发送请求到: {url}")
    print(f"📝 消息内容: {test_data['messages'][0]['content']}")
    
    try:
        response = requests.post(url, json=test_data, headers=headers, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 纯文本请求成功！")
            try:
                response_data = response.json()
                print(f"📄 响应内容: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"📄 响应内容（文本）: {response.text}")
        else:
            print(f"❌ 纯文本请求失败！状态码: {response.status_code}")
            print(f"📄 错误内容: {response.text}")
            
    except Exception as e:
        print(f"💥 发生错误: {str(e)}")

if __name__ == "__main__":
    print("🚀 图片传输功能测试脚本")
    print("=" * 50)
    
    # 测试纯文本消息
    test_text_only()
    
    # 测试图片消息
    test_image_upload()
    
    print("\n" + "=" * 50)
    print("🏁 测试完成")
