import 'package:uuid/uuid.dart';
import 'chat_message.dart';
import 'agent_model.dart';
import 'ai_model.dart';
import 'bazi_model.dart';

/// 对话会话模型
class ConversationModel {
  final String id;
  final String title;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<ChatMessage> messages;
  final AgentModel? selectedAgent;
  final AIModel? selectedModel;
  final BaziResultModel? baziData;

  // 流式输出状态管理
  final bool isStreaming;
  final String? streamingMessageId;
  final String? streamingContent;
  final Map<String, dynamic>? streamingContext;

  ConversationModel({
    required this.id,
    required this.title,
    required this.createdAt,
    required this.updatedAt,
    required this.messages,
    this.selectedAgent,
    this.selectedModel,
    this.baziData,
    this.isStreaming = false,
    this.streamingMessageId,
    this.streamingContent,
    this.streamingContext,
  });

  /// 创建新对话
  factory ConversationModel.create({
    String? title,
    AgentModel? agent,
    AIModel? model,
    BaziResultModel? baziData,
  }) {
    final now = DateTime.now();
    return ConversationModel(
      id: const Uuid().v4(),
      title: title ?? '新对话',
      createdAt: now,
      updatedAt: now,
      messages: [],
      selectedAgent: agent,
      selectedModel: model,
      baziData: baziData,
      isStreaming: false,
      streamingMessageId: null,
      streamingContent: null,
      streamingContext: null,
    );
  }

  /// 从JSON创建
  factory ConversationModel.fromJson(Map<String, dynamic> json) {
    return ConversationModel(
      id: json['id'] as String,
      title: json['title'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      messages: (json['messages'] as List<dynamic>)
          .map((m) => ChatMessage.fromJson(m as Map<String, dynamic>))
          .toList(),
      selectedAgent: json['selected_agent'] != null
          ? AgentModel.fromJson(json['selected_agent'] as Map<String, dynamic>)
          : null,
      selectedModel: json['selected_model'] != null
          ? AIModel.fromJson(json['selected_model'] as Map<String, dynamic>)
          : null,
      baziData: json['bazi_data'] != null
          ? BaziResultModel.fromJson(json['bazi_data'] as Map<String, dynamic>)
          : null,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'messages': messages.map((m) => m.toJson()).toList(),
      'selected_agent': selectedAgent?.toJson(),
      'selected_model': selectedModel?.toJson(),
      'bazi_data': baziData?.toJson(),
    };
  }

  /// 复制并修改
  ConversationModel copyWith({
    String? id,
    String? title,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<ChatMessage>? messages,
    AgentModel? selectedAgent,
    AIModel? selectedModel,
    BaziResultModel? baziData,
    bool? isStreaming,
    String? streamingMessageId,
    String? streamingContent,
    Map<String, dynamic>? streamingContext,
    bool clearAgent = false,
    bool clearModel = false,
    bool clearBaziData = false,
    bool clearStreamingState = false,
  }) {
    return ConversationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      messages: messages ?? this.messages,
      selectedAgent: clearAgent ? null : (selectedAgent ?? this.selectedAgent),
      selectedModel: clearModel ? null : (selectedModel ?? this.selectedModel),
      baziData: clearBaziData ? null : (baziData ?? this.baziData),
      isStreaming: clearStreamingState ? false : (isStreaming ?? this.isStreaming),
      streamingMessageId: clearStreamingState ? null : (streamingMessageId ?? this.streamingMessageId),
      streamingContent: clearStreamingState ? null : (streamingContent ?? this.streamingContent),
      streamingContext: clearStreamingState ? null : (streamingContext ?? this.streamingContext),
    );
  }

  /// 添加消息
  ConversationModel addMessage(ChatMessage message) {
    final newMessages = List<ChatMessage>.from(messages)..add(message);
    return copyWith(
      messages: newMessages,
      updatedAt: DateTime.now(),
    );
  }

  /// 更新最后一条消息
  ConversationModel updateLastMessage(ChatMessage message) {
    if (messages.isEmpty) return this;

    final newMessages = List<ChatMessage>.from(messages);
    newMessages[newMessages.length - 1] = message;

    return copyWith(
      messages: newMessages,
      updatedAt: DateTime.now(),
    );
  }

  /// 根据ID更新指定消息
  ConversationModel updateMessage(String messageId, ChatMessage Function(ChatMessage) updater) {
    final newMessages = messages.map((msg) {
      if (msg.id == messageId) {
        return updater(msg);
      }
      return msg;
    }).toList();

    return copyWith(
      messages: newMessages,
      updatedAt: DateTime.now(),
    );
  }

  /// 移除最后一条消息
  ConversationModel removeLastMessage() {
    if (messages.isEmpty) return this;
    
    final newMessages = List<ChatMessage>.from(messages);
    newMessages.removeLast();
    
    return copyWith(
      messages: newMessages,
      updatedAt: DateTime.now(),
    );
  }

  /// 获取显示标题
  String get displayTitle {
    if (title != '新对话') return title;
    
    // 如果是默认标题，尝试从第一条用户消息生成标题
    final firstUserMessage = messages
        .where((m) => m.isUser && m.content.trim().isNotEmpty)
        .firstOrNull;
    
    if (firstUserMessage != null) {
      final content = firstUserMessage.content.trim();
      if (content.length > 20) {
        return '${content.substring(0, 20)}...';
      }
      return content;
    }
    
    return title;
  }

  /// 是否为空对话
  bool get isEmpty => messages.where((m) => !m.isSystem).isEmpty;

  /// 获取最后活动时间的友好显示
  String get lastActivityDisplay {
    final now = DateTime.now();
    final diff = now.difference(updatedAt);
    
    if (diff.inMinutes < 1) {
      return '刚刚';
    } else if (diff.inHours < 1) {
      return '${diff.inMinutes}分钟前';
    } else if (diff.inDays < 1) {
      return '${diff.inHours}小时前';
    } else if (diff.inDays < 7) {
      return '${diff.inDays}天前';
    } else {
      return '${updatedAt.month}月${updatedAt.day}日';
    }
  }

  @override
  String toString() {
    return 'ConversationModel(id: $id, title: $title, messages: ${messages.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ConversationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
