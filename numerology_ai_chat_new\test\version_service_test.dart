import 'package:flutter_test/flutter_test.dart';
import 'package:numerology_ai_chat/src/services/version_service.dart';

void main() {
  group('VersionService Tests', () {
    late VersionService versionService;

    setUp(() {
      versionService = VersionService();
    });

    group('Version Comparison', () {
      test('should correctly compare version numbers', () {
        // 测试相等版本
        expect(versionService.compareVersions('1.0.0', '1.0.0'), equals(0));
        
        // 测试第一个版本大于第二个版本
        expect(versionService.compareVersions('1.1.0', '1.0.0'), equals(1));
        expect(versionService.compareVersions('2.0.0', '1.9.9'), equals(1));
        expect(versionService.compareVersions('1.0.1', '1.0.0'), equals(1));
        
        // 测试第一个版本小于第二个版本
        expect(versionService.compareVersions('1.0.0', '1.1.0'), equals(-1));
        expect(versionService.compareVersions('1.9.9', '2.0.0'), equals(-1));
        expect(versionService.compareVersions('1.0.0', '1.0.1'), equals(-1));
      });

      test('should handle different version number lengths', () {
        expect(versionService.compareVersions('1.0', '1.0.0'), equals(0));
        expect(versionService.compareVersions('1.1', '1.0.0'), equals(1));
        expect(versionService.compareVersions('1.0.0', '1.1'), equals(-1));
      });
    });

    group('Version Validation', () {
      test('should validate correct version formats', () {
        expect(versionService.isValidVersion('1.0.0'), isTrue);
        expect(versionService.isValidVersion('10.20.30'), isTrue);
        expect(versionService.isValidVersion('0.0.1'), isTrue);
      });

      test('should reject invalid version formats', () {
        expect(versionService.isValidVersion('1.0'), isFalse);
        expect(versionService.isValidVersion('1.0.0.0'), isFalse);
        expect(versionService.isValidVersion('v1.0.0'), isFalse);
        expect(versionService.isValidVersion('1.0.0-beta'), isFalse);
        expect(versionService.isValidVersion(''), isFalse);
      });
    });

    group('Update Detection', () {
      test('should correctly detect when update is needed', () {
        expect(versionService.needsUpdate('1.0.0', '1.1.0'), isTrue);
        expect(versionService.needsUpdate('1.0.0', '2.0.0'), isTrue);
        expect(versionService.needsUpdate('1.0.0', '1.0.1'), isTrue);
      });

      test('should correctly detect when no update is needed', () {
        expect(versionService.needsUpdate('1.1.0', '1.0.0'), isFalse);
        expect(versionService.needsUpdate('1.0.0', '1.0.0'), isFalse);
        expect(versionService.needsUpdate('2.0.0', '1.9.9'), isFalse);
      });
    });

    group('Update Type Detection', () {
      test('should correctly identify major updates', () {
        final updateType = versionService.getUpdateType('1.0.0', '2.0.0');
        expect(updateType, equals(VersionUpdateType.major));
      });

      test('should correctly identify minor updates', () {
        final updateType = versionService.getUpdateType('1.0.0', '1.1.0');
        expect(updateType, equals(VersionUpdateType.minor));
      });

      test('should correctly identify patch updates', () {
        final updateType = versionService.getUpdateType('1.0.0', '1.0.1');
        expect(updateType, equals(VersionUpdateType.patch));
      });

      test('should correctly identify no update', () {
        final updateType = versionService.getUpdateType('1.1.0', '1.0.0');
        expect(updateType, equals(VersionUpdateType.none));
        
        final sameVersionType = versionService.getUpdateType('1.0.0', '1.0.0');
        expect(sameVersionType, equals(VersionUpdateType.none));
      });
    });

    group('Version Support Check', () {
      test('should correctly check version support', () {
        expect(versionService.isVersionSupported('1.1.0', '1.0.0'), isTrue);
        expect(versionService.isVersionSupported('1.0.0', '1.0.0'), isTrue);
        expect(versionService.isVersionSupported('0.9.0', '1.0.0'), isFalse);
      });
    });

    group('Version Display Formatting', () {
      test('should format version display correctly', () {
        expect(
          versionService.formatVersionDisplay('1.0.0', '正式版'),
          equals('1.0.0 (正式版)'),
        );
        
        expect(
          versionService.formatVersionDisplay('1.0.0', null),
          equals('1.0.0'),
        );
        
        expect(
          versionService.formatVersionDisplay('1.0.0', ''),
          equals('1.0.0'),
        );
      });
    });

    group('Current Version', () {
      test('should return current app version', () {
        final currentVersion = versionService.getCurrentVersion();
        expect(currentVersion, isNotEmpty);
        expect(versionService.isValidVersion(currentVersion), isTrue);
      });
    });
  });
}
