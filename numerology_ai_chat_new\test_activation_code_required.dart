import 'package:flutter_test/flutter_test.dart';

/// 测试激活码必填功能
void main() {
  group('激活码必填测试', () {

    test('激活码验证函数测试', () {
      // 创建RegisterScreen实例来测试验证函数
      // 注意：这里我们无法直接测试私有方法，但可以通过UI测试来验证行为
      
      // 测试空值情况
      String? result1 = _testValidateActivationCode(null);
      expect(result1, '请输入激活码', reason: '空值应该返回错误信息');

      // 测试空字符串情况
      String? result2 = _testValidateActivationCode('');
      expect(result2, '请输入激活码', reason: '空字符串应该返回错误信息');

      // 测试空格字符串情况
      String? result3 = _testValidateActivationCode('   ');
      expect(result3, '请输入激活码', reason: '空格字符串应该返回错误信息');

      // 测试长度不足情况
      String? result4 = _testValidateActivationCode('123456789');
      expect(result4, '激活码长度不足，请检查输入是否完整', reason: '长度不足应该返回错误信息');

      // 测试非法字符情况
      String? result5 = _testValidateActivationCode('1234567890中文');
      expect(result5, '激活码包含非法字符，请重新输入', reason: '包含非法字符应该返回错误信息');

      // 测试有效激活码情况
      String? result6 = _testValidateActivationCode('1234567890ABCDEF+/=');
      expect(result6, null, reason: '有效激活码应该返回null');

      print('✅ 激活码验证函数测试通过');
    });
  });
}

/// 模拟激活码验证函数（复制自RegisterScreen的逻辑）
String? _testValidateActivationCode(String? value) {
  if (value == null || value.trim().isEmpty) {
    return '请输入激活码'; // 必填字段，不允许为空
  }

  // 基本格式检查
  if (value.trim().length < 10) {
    return '激活码长度不足，请检查输入是否完整';
  }

  // 检查是否包含非法字符（激活码应该是Base64格式）
  if (!RegExp(r'^[A-Za-z0-9+/=]+$').hasMatch(value.trim())) {
    return '激活码包含非法字符，请重新输入';
  }

  return null;
}
