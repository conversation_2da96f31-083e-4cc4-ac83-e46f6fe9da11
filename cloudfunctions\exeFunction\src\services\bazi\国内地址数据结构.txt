国内地址数据结构.txt 文件的内容是一个 JSON 数组，其中包含表示中国行政区划（省、市、区/县）的对象。

每个行政区划对象都具有以下基本结构：

- id：字符串类型，表示该行政区划的唯一代码。
- text：字符串类型，表示该行政区划的名称。
- children：可选字段，如果当前行政区划包含下一级行政区划，则此字段是一个包含子行政区划对象的数组。

对于最底层的行政区划（区/县），还包含以下经纬度信息：

- gisGcj02Lat：浮点数类型，表示该区域的GCJ-02坐标系纬度。
- gisGcj02Lng：浮点数类型，表示该区域的GCJ-02坐标系经度。

整体结构是分层的，例如：
[
    {
        "id": "省代码",
        "text": "省名称",
        "children": [
            {
                "id": "市代码",
                "text": "市名称",
                "children": [
                    {
                        "id": "区县代码",
                        "text": "区县名称",
                        "gisGcj02Lat": 纬度,
                        "gisGcj02Lng": 经度
                    },
                    // ... 更多区县
                ]
            },
            // ... 更多市
        ]
    },
    // ... 更多省份
] 