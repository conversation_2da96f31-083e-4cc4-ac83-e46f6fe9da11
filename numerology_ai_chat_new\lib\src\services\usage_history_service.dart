import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/usage_history_model.dart';
import '../core/constants/app_constants.dart';

/// 额度消耗历史服务
/// 负责与云函数交互，获取用户的消耗历史数据
class UsageHistoryService {
  static const String _baseUrl = AppConstants.exeFunctionUrl;
  static const Duration _timeout = Duration(seconds: 30);

  /// 缓存的消耗历史数据
  static Map<String, UsageHistoryPageModel>? _cachedHistory;
  static DateTime? _cacheExpireTime;

  /// 获取用户消耗历史
  ///
  /// [token] 用户认证token
  /// [page] 页码，默认为1
  /// [limit] 每页数量，默认为20
  /// [startDate] 开始日期（可选）
  /// [endDate] 结束日期（可选）
  /// [useCache] 是否使用缓存，默认为true
  ///
  /// 返回消耗历史分页数据
  static Future<UsageHistoryPageModel> getUserUsageHistory({
    required String token,
    int page = 1,
    int limit = 20,
    DateTime? startDate,
    DateTime? endDate,
    bool useCache = true,
  }) async {
    try {
      // 构建缓存键
      final cacheKey = _buildCacheKey(page, limit, startDate, endDate);

      // 检查缓存
      if (useCache && _isCacheValid() && _cachedHistory?.containsKey(cacheKey) == true) {
        print('UsageHistoryService: 使用缓存数据 - $cacheKey');
        return _cachedHistory![cacheKey]!;
      }

      print('UsageHistoryService: 从云函数获取消耗历史 - page: $page, limit: $limit');

      // 构建请求参数
      final requestBody = {
        'action': 'getUserUsageHistory',
        'page': page,
        'limit': limit,
      };

      // 添加日期范围参数
      if (startDate != null) {
        requestBody['startDate'] = startDate.toIso8601String();
      }
      if (endDate != null) {
        requestBody['endDate'] = endDate.toIso8601String();
      }

      // 发送请求
      final response = await http
          .post(
            Uri.parse(_baseUrl),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': token,
            },
            body: json.encode(requestBody),
          )
          .timeout(_timeout);

      print('UsageHistoryService: 响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body) as Map<String, dynamic>;

        print('UsageHistoryService: 响应数据: $responseData');

        // 检查云函数返回的code是否为0（成功）
        if (responseData['code'] == 0) {
          final cloudFunctionData = responseData['data'] as Map<String, dynamic>;

          // 检查云函数内部的success标志
          if (cloudFunctionData['success'] == true) {
            final data = cloudFunctionData['data'] as Map<String, dynamic>;
            final historyPage = UsageHistoryPageModel.fromJson(data);

            // 更新缓存
            _updateCache(cacheKey, historyPage);

            print('UsageHistoryService: 获取消耗历史成功 - 总数: ${historyPage.total}, 当前页: ${historyPage.page}');
            return historyPage;
          } else {
            final errorMessage = cloudFunctionData['message'] as String? ?? '获取消耗历史失败';
            throw Exception(errorMessage);
          }
        } else {
          final errorMessage = responseData['message'] as String? ?? '云函数调用失败';
          throw Exception(errorMessage);
        }
      } else {
        throw Exception('网络请求失败: ${response.statusCode}');
      }
    } catch (e) {
      print('UsageHistoryService: 获取消耗历史失败: $e');
      
      // 如果网络请求失败，尝试返回缓存数据
      if (useCache && _cachedHistory?.isNotEmpty == true) {
        final cacheKey = _buildCacheKey(page, limit, startDate, endDate);
        if (_cachedHistory!.containsKey(cacheKey)) {
          print('UsageHistoryService: 网络失败，返回缓存数据');
          return _cachedHistory![cacheKey]!;
        }
      }
      
      rethrow;
    }
  }

  /// 刷新消耗历史（清除缓存并重新获取）
  ///
  /// [token] 用户认证token
  /// [page] 页码，默认为1
  /// [limit] 每页数量，默认为20
  /// [startDate] 开始日期（可选）
  /// [endDate] 结束日期（可选）
  ///
  /// 返回消耗历史分页数据
  static Future<UsageHistoryPageModel> refreshUsageHistory({
    required String token,
    int page = 1,
    int limit = 20,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    // 清除相关缓存
    clearCache();
    
    return await getUserUsageHistory(
      token: token,
      page: page,
      limit: limit,
      startDate: startDate,
      endDate: endDate,
      useCache: false,
    );
  }

  /// 获取指定日期范围的消耗历史
  ///
  /// [token] 用户认证token
  /// [startDate] 开始日期
  /// [endDate] 结束日期
  /// [page] 页码，默认为1
  /// [limit] 每页数量，默认为20
  ///
  /// 返回消耗历史分页数据
  static Future<UsageHistoryPageModel> getUsageHistoryByDateRange({
    required String token,
    required DateTime startDate,
    required DateTime endDate,
    int page = 1,
    int limit = 20,
  }) async {
    return await getUserUsageHistory(
      token: token,
      page: page,
      limit: limit,
      startDate: startDate,
      endDate: endDate,
    );
  }

  /// 构建缓存键
  static String _buildCacheKey(int page, int limit, DateTime? startDate, DateTime? endDate) {
    final parts = ['page_$page', 'limit_$limit'];
    
    if (startDate != null) {
      parts.add('start_${startDate.toIso8601String()}');
    }
    if (endDate != null) {
      parts.add('end_${endDate.toIso8601String()}');
    }
    
    return parts.join('_');
  }

  /// 检查缓存是否有效
  static bool _isCacheValid() {
    return _cacheExpireTime != null && 
           DateTime.now().isBefore(_cacheExpireTime!) &&
           _cachedHistory != null;
  }

  /// 更新缓存
  static void _updateCache(String cacheKey, UsageHistoryPageModel data) {
    _cachedHistory ??= {};
    _cachedHistory![cacheKey] = data;
    _cacheExpireTime = DateTime.now().add(const Duration(minutes: 5)); // 缓存5分钟
  }

  /// 清除缓存
  static void clearCache() {
    _cachedHistory = null;
    _cacheExpireTime = null;
    print('UsageHistoryService: 缓存已清除');
  }

  /// 获取缓存统计信息
  static Map<String, dynamic> getCacheStats() {
    return {
      'hasCachedData': _cachedHistory != null,
      'cacheSize': _cachedHistory?.length ?? 0,
      'isValid': _isCacheValid(),
      'expireTime': _cacheExpireTime?.toIso8601String(),
    };
  }
}
