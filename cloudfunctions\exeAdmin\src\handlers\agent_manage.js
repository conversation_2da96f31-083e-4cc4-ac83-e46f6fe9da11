const { createBusinessError, ERROR_CODES, formatSuccessResponse } = require('../utils/error_handler')
const { agentCollection } = require('../utils/db')
const { checkPermission } = require('../middleware/auth')
const logger = require('../utils/logger')

/**
 * 获取智能体列表
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 智能体列表
 */
async function getAgentList(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'agent_read')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限查看智能体列表', 403)
    }
    
    const { page = 1, pageSize = 20, keyword = '', isActive } = event || {}
    
    logger.info('管理员获取智能体列表', {
      adminId: adminAuth.adminId,
      page,
      pageSize,
      keyword
    })
    
    // 获取所有智能体数据
    const allAgents = await agentCollection.getList()
    
    // 应用过滤条件
    let filteredAgents = allAgents
    if (keyword) {
      filteredAgents = allAgents.filter(agent => 
        agent.agentName.toLowerCase().includes(keyword.toLowerCase()) ||
        agent.agentDisplayName.toLowerCase().includes(keyword.toLowerCase()) ||
        (agent.description && agent.description.toLowerCase().includes(keyword.toLowerCase()))
      )
    }
    if (isActive !== undefined) {
      filteredAgents = filteredAgents.filter(agent => agent.isActive === isActive)
    }
    
    // 手动分页
    const total = filteredAgents.length
    const totalPages = Math.ceil(total / pageSize)
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedAgents = filteredAgents.slice(startIndex, endIndex)
    
    // 处理返回数据，隐藏敏感信息
    const agents = paginatedAgents.map(agent => ({
      agentId: agent._id,
      agentName: agent.agentName,
      agentDisplayName: agent.agentDisplayName || agent.agentName,
      agentType: agent.agentType,
      description: agent.description,
      isActive: agent.isActive,
      sortOrder: agent.sortOrder,
      createdAt: agent.createdAt,
      updatedAt: agent.updatedAt,
      hasPrompt: !!agent.agentPrompt // 只显示是否有提示词
    }))
    
    const responseData = {
      agents,
      pagination: {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        total,
        totalPages
      }
    }
    
    return formatSuccessResponse(responseData, '获取智能体列表成功')
    
  } catch (error) {
    logger.error('管理员获取智能体列表异常', {
      adminId: adminAuth.adminId,
      error: error.message
    })
    throw error
  }
}

/**
 * 创建智能体
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 创建结果
 */
async function createAgentByAdmin(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'agent_create')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限创建智能体', 403)
    }
    
    const {
      agentName,
      agentDisplayName,
      systemPrompt,
      agentPrompt,
      agentType,
      description,
      isActive = true,
      sortOrder = 0
    } = event

    // 参数校验
    const prompt = systemPrompt || agentPrompt
    if (!agentName || !agentDisplayName || !prompt) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '智能体名称、显示名称和提示词不能为空', 400)
    }
    
    logger.info('管理员创建智能体', {
      adminId: adminAuth.adminId,
      agentName,
      agentDisplayName
    })
    
    // 检查智能体名称是否已存在
    const allAgents = await agentCollection.getList()
    const existingAgent = allAgents.find(agent => agent.agentName === agentName)
    if (existingAgent) {
      throw createBusinessError(ERROR_CODES.AGENT_ALREADY_EXISTS, '智能体名称已存在', 400)
    }
    
    // 创建智能体数据
    const agentData = {
      agentName,
      agentDisplayName,
      agentPrompt: prompt,
      agentType: agentType || '自定义',
      description: description || '',
      isActive,
      sortOrder,
      createdBy: adminAuth.adminId,
      updatedBy: adminAuth.adminId
    }
    
    // 创建智能体
    const newAgentId = await agentCollection.create(agentData)
    const newAgent = await agentCollection.findById(newAgentId)
    
    logger.info('管理员创建智能体成功', {
      adminId: adminAuth.adminId,
      agentId: newAgentId,
      agentName
    })
    
    const responseData = {
      agentId: newAgent._id,
      agentName: newAgent.agentName,
      agentDisplayName: newAgent.agentDisplayName,
      description: newAgent.description,
      isActive: newAgent.isActive,
      sortOrder: newAgent.sortOrder,
      createdAt: newAgent.createdAt
    }
    
    return formatSuccessResponse(responseData, '智能体创建成功')
    
  } catch (error) {
    logger.error('管理员创建智能体异常', {
      adminId: adminAuth.adminId,
      agentName: event.agentName,
      error: error.message
    })
    throw error
  }
}

/**
 * 更新智能体
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 更新结果
 */
async function updateAgentByAdmin(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'agent_update')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限更新智能体', 403)
    }
    
    const { agentId, ...updateData } = event
    
    if (!agentId) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '智能体ID不能为空', 400)
    }
    
    logger.info('管理员更新智能体', {
      adminId: adminAuth.adminId,
      agentId,
      agentName: updateData.agentName
    })
    
    // 检查智能体是否存在
    const existingAgent = await agentCollection.findById(agentId)
    if (!existingAgent) {
      throw createBusinessError(ERROR_CODES.AGENT_NOT_FOUND, '智能体不存在', 404)
    }
    
    // 如果更新名称，检查是否与其他智能体重名
    if (updateData.agentName && updateData.agentName !== existingAgent.agentName) {
      const allAgents = await agentCollection.getList()
      const duplicateAgent = allAgents.find(agent => 
        agent.agentName === updateData.agentName && agent._id !== agentId
      )
      if (duplicateAgent) {
        throw createBusinessError(ERROR_CODES.AGENT_ALREADY_EXISTS, '智能体名称已存在', 400)
      }
    }
    
    // 准备更新数据
    const updateFields = {
      ...updateData,
      updatedBy: adminAuth.adminId
    }
    
    // 更新智能体
    await agentCollection.update(agentId, updateFields)
    
    logger.info('管理员更新智能体成功', {
      adminId: adminAuth.adminId,
      agentId
    })
    
    return formatSuccessResponse(null, '智能体更新成功')
    
  } catch (error) {
    logger.error('管理员更新智能体异常', {
      adminId: adminAuth.adminId,
      agentId: event.agentId,
      error: error.message
    })
    throw error
  }
}

/**
 * 删除智能体
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 删除结果
 */
async function deleteAgentByAdmin(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'agent_delete')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限删除智能体', 403)
    }
    
    const { agentId } = event
    
    if (!agentId) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '智能体ID不能为空', 400)
    }
    
    logger.info('管理员删除智能体', {
      adminId: adminAuth.adminId,
      agentId
    })
    
    // 检查智能体是否存在
    const existingAgent = await agentCollection.findById(agentId)
    if (!existingAgent) {
      throw createBusinessError(ERROR_CODES.AGENT_NOT_FOUND, '智能体不存在', 404)
    }
    
    // 删除智能体
    await agentCollection.delete(agentId)
    
    logger.info('管理员删除智能体成功', {
      adminId: adminAuth.adminId,
      agentId,
      agentName: existingAgent.agentName
    })
    
    return formatSuccessResponse(null, '智能体删除成功')
    
  } catch (error) {
    logger.error('管理员删除智能体异常', {
      adminId: adminAuth.adminId,
      agentId: event.agentId,
      error: error.message
    })
    throw error
  }
}

module.exports = {
  getAgentList,
  createAgentByAdmin,
  updateAgentByAdmin,
  deleteAgentByAdmin
}
