import 'package:flutter/material.dart';

/// 应用颜色方案
class AppColorSchemes {
  // 主色调
  static const Color primaryColor = Color(0xFF6366F1);
  static const Color primaryVariant = Color(0xFF4F46E5);
  static const Color secondaryColor = Color(0xFF10B981);
  static const Color secondaryVariant = Color(0xFF059669);
  
  // 背景色
  static const Color backgroundColor = Color(0xFFF8FAFC);
  static const Color surfaceColor = Color(0xFFFFFFFF);
  static const Color cardColor = Color(0xFFFFFFFF);
  
  // 文本色
  static const Color textPrimary = Color(0xFF1F2937);
  static const Color textSecondary = Color(0xFF6B7280);
  static const Color textTertiary = Color(0xFF9CA3AF);
  
  // 状态色
  static const Color successColor = Color(0xFF10B981);
  static const Color warningColor = Color(0xFFF59E0B);
  static const Color errorColor = Color(0xFFEF4444);
  static const Color infoColor = Color(0xFF3B82F6);
  
  // 聊天气泡色
  static const Color userBubbleColor = Color(0xFF6366F1);
  static const Color assistantBubbleColor = Color(0xFFF3F4F6);
  static const Color systemBubbleColor = Color(0xFFE5E7EB);
  
  // 智能体类型色
  static const Color baziColor = Color(0xFF8B5CF6);
  static const Color ziweiColor = Color(0xFFF59E0B);
  static const Color generalColor = Color(0xFF10B981);
  
  // 边框色
  static const Color borderColor = Color(0xFFE5E7EB);
  static const Color dividerColor = Color(0xFFE5E7EB);
  
  // 阴影色
  static const Color shadowColor = Color(0x1A000000);
  
  // 私有构造函数
  AppColorSchemes._();
}

/// 应用渐变色
class AppGradients {
  static const LinearGradient primary = LinearGradient(
    colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient secondary = LinearGradient(
    colors: [Color(0xFF10B981), Color(0xFF059669)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient bazi = LinearGradient(
    colors: [Color(0xFF8B5CF6), Color(0xFFA855F7)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient ziwei = LinearGradient(
    colors: [Color(0xFFF59E0B), Color(0xFFEAB308)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient general = LinearGradient(
    colors: [Color(0xFF10B981), Color(0xFF059669)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient card = LinearGradient(
    colors: [Color(0xFFFFFFFF), Color(0xFFF8FAFC)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  // 私有构造函数
  AppGradients._();
}

/// 明亮主题颜色方案
const ColorScheme lightColorScheme = ColorScheme(
  brightness: Brightness.light,
  primary: AppColorSchemes.primaryColor,
  onPrimary: Colors.white,
  secondary: AppColorSchemes.secondaryColor,
  onSecondary: Colors.white,
  error: AppColorSchemes.errorColor,
  onError: Colors.white,
  surface: AppColorSchemes.surfaceColor,
  onSurface: AppColorSchemes.textPrimary,
  background: AppColorSchemes.backgroundColor,
  onBackground: AppColorSchemes.textPrimary,
);

/// 深色主题颜色方案
const ColorScheme darkColorScheme = ColorScheme(
  brightness: Brightness.dark,
  primary: AppColorSchemes.primaryColor,
  onPrimary: Colors.white,
  secondary: AppColorSchemes.secondaryColor,
  onSecondary: Colors.white,
  error: AppColorSchemes.errorColor,
  onError: Colors.white,
  surface: Color(0xFF1F2937),
  onSurface: Colors.white,
  background: Color(0xFF111827),
  onBackground: Colors.white,
);
