<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弱管理端路由测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            display: inline-block;
            margin: 5px;
            padding: 10px 15px;
            background-color: #409eff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
        }
        .test-button:hover {
            background-color: #337ecc;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #f0f9ff;
            border: 1px solid #67c23a;
            color: #67c23a;
        }
        .error {
            background-color: #fef0f0;
            border: 1px solid #f56c6c;
            color: #f56c6c;
        }
    </style>
</head>
<body>
    <h1>弱管理端路由切换测试</h1>
    
    <div class="test-section">
        <h2>测试说明</h2>
        <p>1. 首先在新标签页中打开 <a href="http://localhost:3001/" target="_blank">http://localhost:3001/</a></p>
        <p>2. 使用测试账号登录：用户名 <strong>weakadmin001</strong>，密码 <strong>12345678</strong></p>
        <p>3. 登录成功后，测试以下路由切换功能：</p>
    </div>

    <div class="test-section">
        <h2>路由测试链接</h2>
        <p>点击以下链接测试各个页面的路由切换（需要先登录）：</p>
        
        <a href="http://localhost:3001/dashboard" target="_blank" class="test-button">
            仪表板 (/dashboard)
        </a>
        
        <a href="http://localhost:3001/activation-codes" target="_blank" class="test-button">
            激活码管理 (/activation-codes)
        </a>
        
        <a href="http://localhost:3001/user-quota" target="_blank" class="test-button">
            用户算力管理 (/user-quota)
        </a>
        
        <a href="http://localhost:3001/history" target="_blank" class="test-button">
            操作历史 (/history)
        </a>
    </div>

    <div class="test-section">
        <h2>测试检查项</h2>
        <ul>
            <li>✅ 每个页面都应该显示统一的顶部导航栏（包含"管理员系统"标题和用户下拉菜单）</li>
            <li>✅ 每个页面都应该显示左侧导航菜单（仪表板、激活码管理、用户算力管理、操作历史）</li>
            <li>✅ 点击左侧菜单项应该能够正常切换到对应页面</li>
            <li>✅ 当前页面的菜单项应该高亮显示</li>
            <li>✅ 页面内容应该在右侧主内容区域正常显示</li>
            <li>✅ 所有页面的功能应该正常工作（生成激活码、查询用户、查看历史等）</li>
            <li>✅ 退出登录功能应该正常工作</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>预期结果</h2>
        <div class="status success">
            <strong>成功标准：</strong>
            <ul>
                <li>所有页面都能正常访问和切换</li>
                <li>导航菜单在所有页面都保持可见和可用</li>
                <li>页面切换时URL正确更新</li>
                <li>浏览器前进/后退按钮正常工作</li>
                <li>所有现有功能保持正常工作</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>问题排查</h2>
        <p>如果遇到问题，请检查：</p>
        <ul>
            <li>浏览器开发者工具的控制台是否有错误信息</li>
            <li>网络请求是否正常（特别是API调用）</li>
            <li>Vue Router是否正确配置</li>
            <li>组件是否正确加载</li>
        </ul>
    </div>

    <script>
        // 简单的状态检查
        function checkServerStatus() {
            fetch('http://localhost:3001/')
                .then(response => {
                    if (response.ok) {
                        document.getElementById('server-status').className = 'status success';
                        document.getElementById('server-status').innerHTML = '✅ 开发服务器运行正常';
                    } else {
                        throw new Error('Server response not ok');
                    }
                })
                .catch(error => {
                    document.getElementById('server-status').className = 'status error';
                    document.getElementById('server-status').innerHTML = '❌ 开发服务器连接失败';
                });
        }

        // 页面加载时检查服务器状态
        window.onload = function() {
            const statusDiv = document.createElement('div');
            statusDiv.id = 'server-status';
            statusDiv.className = 'status';
            statusDiv.innerHTML = '🔄 检查服务器状态...';
            document.querySelector('.test-section').appendChild(statusDiv);
            
            setTimeout(checkServerStatus, 1000);
        };
    </script>
</body>
</html>
