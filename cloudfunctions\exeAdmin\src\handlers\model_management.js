const { validate, modelSchema } = require('../utils/validate')
const { createBusinessError, ERROR_CODES, formatSuccessResponse } = require('../utils/error_handler')
const { modelCollection } = require('../utils/db')
const { checkPermission } = require('../middleware/auth')
const logger = require('../utils/logger')

/**
 * 创建模型
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 创建结果
 */
async function createModelByAdmin(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'model_create')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限创建模型', 403)
    }
    
    // 参数校验
    const validatedData = validate(modelSchema, event)
    const {
      modelName,
      modelApiKey,
      modelApiUrl,
      modelDisplayName,
      maxTokens,
      temperature,
      description,
      isActive,
      sortOrder
    } = validatedData

    logger.info('管理员创建模型', {
      adminId: adminAuth.adminId,
      modelName,
      modelDisplayName
    })

    // 检查模型名称是否已存在 - 简化检查，通过获取列表来查找
    const existingModels = await modelCollection.getList()
    const existingModel = existingModels.find(model => model.modelName === modelName)
    if (existingModel) {
      throw createBusinessError(ERROR_CODES.MODEL_ALREADY_EXISTS, '模型名称已存在', 400)
    }

    // 创建模型数据
    const modelData = {
      modelName,
      modelApiKey,
      modelApiUrl,
      modelDisplayName,
      maxTokens: maxTokens || 4000,
      temperature: temperature !== undefined ? temperature : 0.7,
      description: description || '',
      isActive: isActive !== undefined ? isActive : true,
      sortOrder: sortOrder || 0,
      createdBy: adminAuth.adminId,
      updatedBy: adminAuth.adminId
    }

    // 创建模型
    const newModelId = await modelCollection.create(modelData)
    const newModel = await modelCollection.findById(newModelId)
    
    logger.info('管理员创建模型成功', {
      adminId: adminAuth.adminId,
      modelId: newModel._id,
      modelName
    })
    
    // 返回数据时隐藏API密钥
    const responseData = {
      modelId: newModel._id,
      modelName: newModel.modelName,
      modelApiUrl: newModel.modelApiUrl,
      modelDisplayName: newModel.modelDisplayName,
      maxTokens: newModel.maxTokens,
      temperature: newModel.temperature,
      description: newModel.description,
      isActive: newModel.isActive,
      sortOrder: newModel.sortOrder,
      createdTime: newModel.createdTime
    }
    
    return formatSuccessResponse(responseData, '模型创建成功')
    
  } catch (error) {
    logger.error('管理员创建模型异常', {
      adminId: adminAuth.adminId,
      modelName: event.data?.modelName,
      error: error.message
    })
    throw error
  }
}

/**
 * 获取模型列表（分页）
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 模型列表
 */
async function getModelList(event, adminAuth) {
  try {
    // 调试日志
    logger.info('管理员权限检查', {
      adminAuth: adminAuth,
      adminRole: adminAuth.adminRole,
      checkResult: checkPermission(adminAuth.adminRole, 'model_read')
    })

    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'model_read')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限查看模型列表', 403)
    }
    
    const { page = 1, pageSize = 20, keyword = '', isActive } = event || {}

    logger.info('管理员获取模型列表', {
      adminId: adminAuth.adminId,
      page,
      pageSize,
      keyword
    })

    // 获取所有模型数据
    const allModels = await modelCollection.getList()

    // 应用过滤条件
    let filteredModels = allModels
    if (keyword) {
      filteredModels = allModels.filter(model =>
        model.modelName.toLowerCase().includes(keyword.toLowerCase()) ||
        model.modelDisplayName.toLowerCase().includes(keyword.toLowerCase()) ||
        (model.description && model.description.toLowerCase().includes(keyword.toLowerCase()))
      )
    }
    if (isActive !== undefined) {
      filteredModels = filteredModels.filter(model => model.isActive === isActive)
    }

    // 手动分页
    const total = filteredModels.length
    const totalPages = Math.ceil(total / pageSize)
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedModels = filteredModels.slice(startIndex, endIndex)

    // 处理返回数据，隐藏API密钥
    const models = paginatedModels.map(model => ({
      modelId: model._id,
      modelName: model.modelName,
      modelApiUrl: model.modelApiUrl,
      modelDisplayName: model.modelDisplayName,
      maxTokens: model.maxTokens,
      temperature: model.temperature,
      description: model.description,
      isActive: model.isActive,
      sortOrder: model.sortOrder,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      hasApiKey: !!model.modelApiKey // 只显示是否有API密钥
    }))

    const responseData = {
      models,
      pagination: {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        total,
        totalPages
      }
    }
    
    return formatSuccessResponse(responseData, '获取模型列表成功')
    
  } catch (error) {
    logger.error('管理员获取模型列表异常', {
      adminId: adminAuth.adminId,
      error: error.message
    })
    throw error
  }
}

/**
 * 获取模型详情
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 模型详情
 */
async function getModelDetail(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'model_read')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限查看模型详情', 403)
    }
    
    const { modelId } = event

    if (!modelId) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '模型ID不能为空', 400)
    }

    logger.info('管理员获取模型详情', {
      adminId: adminAuth.adminId,
      modelId
    })

    // 查找模型
    const model = await modelCollection.findById(modelId)
    if (!model) {
      throw createBusinessError(ERROR_CODES.MODEL_NOT_FOUND, '模型不存在', 404)
    }
    
    // 返回详情时隐藏API密钥的部分内容
    const responseData = {
      ...model,
      modelApiKey: model.modelApiKey ? 
        model.modelApiKey.substring(0, 8) + '****' + model.modelApiKey.substring(model.modelApiKey.length - 4) :
        ''
    }
    
    return formatSuccessResponse(responseData, '获取模型详情成功')
    
  } catch (error) {
    logger.error('管理员获取模型详情异常', {
      adminId: adminAuth.adminId,
      modelId: event.data?.modelId,
      error: error.message
    })
    throw error
  }
}

/**
 * 更新模型
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 更新结果
 */
async function updateModelByAdmin(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'model_update')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限更新模型', 403)
    }
    
    const { modelId, ...updateData } = event

    if (!modelId) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '模型ID不能为空', 400)
    }

    // 对于更新操作，只校验提供的字段，不要求所有字段都存在
    // 创建一个只包含提供字段的临时schema进行校验
    const updateFields = {}

    // 只校验和处理实际提供的字段
    if (updateData.modelName !== undefined) updateFields.modelName = updateData.modelName
    if (updateData.modelApiKey !== undefined) updateFields.modelApiKey = updateData.modelApiKey
    if (updateData.modelApiUrl !== undefined) updateFields.modelApiUrl = updateData.modelApiUrl
    if (updateData.modelDisplayName !== undefined) updateFields.modelDisplayName = updateData.modelDisplayName
    if (updateData.maxTokens !== undefined) updateFields.maxTokens = updateData.maxTokens
    if (updateData.temperature !== undefined) updateFields.temperature = updateData.temperature
    if (updateData.description !== undefined) updateFields.description = updateData.description
    if (updateData.isActive !== undefined) updateFields.isActive = updateData.isActive
    if (updateData.sortOrder !== undefined) updateFields.sortOrder = updateData.sortOrder

    logger.info('管理员更新模型', {
      adminId: adminAuth.adminId,
      modelId,
      modelName: updateFields.modelName
    })

    // 检查模型是否存在
    const existingModel = await modelCollection.findById(modelId)
    if (!existingModel) {
      throw createBusinessError(ERROR_CODES.MODEL_NOT_FOUND, '模型不存在', 404)
    }

    // 如果更新名称，检查是否与其他模型重名
    if (updateFields.modelName && updateFields.modelName !== existingModel.modelName) {
      const allModels = await modelCollection.getList()
      const duplicateModel = allModels.find(model =>
        model.modelName === updateFields.modelName && model._id !== modelId
      )
      if (duplicateModel) {
        throw createBusinessError(ERROR_CODES.MODEL_ALREADY_EXISTS, '模型名称已存在', 400)
      }
    }

    // 添加更新者信息
    updateFields.updatedBy = adminAuth.adminId

    // 更新模型
    await modelCollection.update(modelId, updateFields)

    logger.info('管理员更新模型成功', {
      adminId: adminAuth.adminId,
      modelId,
      modelName: updateFields.modelName
    })
    
    return formatSuccessResponse(null, '模型更新成功')
    
  } catch (error) {
    logger.error('管理员更新模型异常', {
      adminId: adminAuth.adminId,
      modelId: event.data?.modelId,
      error: error.message
    })
    throw error
  }
}

/**
 * 删除模型
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 删除结果
 */
async function deleteModelByAdmin(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'model_delete')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限删除模型', 403)
    }
    
    const { modelId } = event

    if (!modelId) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '模型ID不能为空', 400)
    }

    logger.info('管理员删除模型', {
      adminId: adminAuth.adminId,
      modelId
    })

    // 检查模型是否存在
    const existingModel = await modelCollection.findById(modelId)
    if (!existingModel) {
      throw createBusinessError(ERROR_CODES.MODEL_NOT_FOUND, '模型不存在', 404)
    }

    // 删除模型
    await modelCollection.delete(modelId)
    
    logger.info('管理员删除模型成功', {
      adminId: adminAuth.adminId,
      modelId,
      modelName: existingModel.modelName
    })
    
    return formatSuccessResponse(null, '模型删除成功')
    
  } catch (error) {
    logger.error('管理员删除模型异常', {
      adminId: adminAuth.adminId,
      modelId: event.data?.modelId,
      error: error.message
    })
    throw error
  }
}

/**
 * 批量更新模型状态
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 更新结果
 */
async function batchUpdateModelStatus(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'model_update')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限批量更新模型状态', 403)
    }
    
    const { modelIds, isActive } = event

    if (!modelIds || !Array.isArray(modelIds) || modelIds.length === 0) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '模型ID列表不能为空', 400)
    }

    if (typeof isActive !== 'boolean') {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '状态值必须是布尔类型', 400)
    }

    logger.info('管理员批量更新模型状态', {
      adminId: adminAuth.adminId,
      modelIds,
      isActive
    })

    // 批量更新
    const updateFields = {
      isActive,
      updatedBy: adminAuth.adminId
    }

    let successCount = 0
    for (const modelId of modelIds) {
      try {
        await modelCollection.update(modelId, updateFields)
        successCount++
      } catch (error) {
        logger.warn('批量更新模型状态失败', {
          modelId,
          error: error.message
        })
      }
    }
    
    logger.info('管理员批量更新模型状态完成', {
      adminId: adminAuth.adminId,
      total: modelIds.length,
      success: successCount
    })
    
    const responseData = {
      total: modelIds.length,
      success: successCount,
      failed: modelIds.length - successCount
    }
    
    return formatSuccessResponse(responseData, `批量更新完成，成功${successCount}个`)
    
  } catch (error) {
    logger.error('管理员批量更新模型状态异常', {
      adminId: adminAuth.adminId,
      error: error.message
    })
    throw error
  }
}

/**
 * 测试模型连接
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 测试结果
 */
async function testModelConnection(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'model_test')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限测试模型连接', 403)
    }
    
    const { modelId } = event

    if (!modelId) {
      throw createBusinessError(ERROR_CODES.INVALID_PARAMS, '模型ID不能为空', 400)
    }

    logger.info('管理员测试模型连接', {
      adminId: adminAuth.adminId,
      modelId
    })

    // 查找模型
    const model = await modelCollection.findById(modelId)
    if (!model) {
      throw createBusinessError(ERROR_CODES.MODEL_NOT_FOUND, '模型不存在', 404)
    }
    
    // TODO: 实现实际的模型连接测试逻辑
    // 这里应该调用模型API进行连接测试
    
    // 模拟测试结果
    const testResult = {
      success: true,
      responseTime: Math.floor(Math.random() * 1000) + 500, // 模拟响应时间
      message: '连接测试成功'
    }
    
    logger.info('管理员测试模型连接完成', {
      adminId: adminAuth.adminId,
      modelId,
      success: testResult.success
    })
    
    return formatSuccessResponse(testResult, '模型连接测试完成')
    
  } catch (error) {
    logger.error('管理员测试模型连接异常', {
      adminId: adminAuth.adminId,
      modelId: event.data?.modelId,
      error: error.message
    })
    throw error
  }
}

/**
 * 获取模型统计信息
 * @param {object} event 云函数事件对象
 * @param {object} adminAuth 管理员鉴权信息
 * @returns {object} 统计信息
 */
async function getModelStatistics(event, adminAuth) {
  try {
    // 权限检查
    if (!checkPermission(adminAuth.adminRole, 'model_read')) {
      throw createBusinessError(ERROR_CODES.PERMISSION_DENIED, '无权限查看模型统计', 403)
    }

    logger.info('管理员获取模型统计', { adminId: adminAuth.adminId })

    // 获取所有模型并计算统计信息
    const allModels = await modelCollection.getList()
    const stats = {
      total: allModels.length,
      active: allModels.filter(model => model.isActive).length,
      inactive: allModels.filter(model => !model.isActive).length,
      withApiKey: allModels.filter(model => model.modelApiKey).length
    }

    return formatSuccessResponse(stats, '获取模型统计成功')

  } catch (error) {
    logger.error('管理员获取模型统计异常', {
      adminId: adminAuth.adminId,
      error: error.message
    })
    throw error
  }
}

module.exports = {
  createModelByAdmin,
  getModelList,
  getModelDetail,
  updateModelByAdmin,
  deleteModelByAdmin,
  batchUpdateModelStatus,
  testModelConnection,
  getModelStatistics
}