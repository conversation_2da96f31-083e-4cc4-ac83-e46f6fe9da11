import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:window_manager/window_manager.dart';

import '../core/storage/storage_service.dart';
import '../core/constants/app_constants.dart';

/// 竖屏模式状态管理
class PortraitModeProvider extends ChangeNotifier {
  final StorageService _storageService;
  
  bool _isPortraitModeEnabled = false;
  Size? _originalWindowSize;
  Offset? _originalWindowPosition;
  bool _wasMaximized = false;
  
  PortraitModeProvider(this._storageService);
  
  /// 是否启用竖屏模式
  bool get isPortraitModeEnabled => _isPortraitModeEnabled;
  
  /// 竖屏模式下的窗口尺寸 - 手机竖屏比例，适合消息显示
  static const Size portraitModeWindowSize = Size(450, 800);

  /// 竖屏模式下的字体缩放倍数
  static const double fontScaleFactor = 1.4;
  
  /// 进入竖屏模式
  Future<void> enterPortraitMode() async {
    if (_isPortraitModeEnabled) return;
    
    try {
      // 保存当前窗口状态
      _originalWindowSize = await windowManager.getSize();
      _originalWindowPosition = await windowManager.getPosition();
      _wasMaximized = await windowManager.isMaximized();
      
      // 如果窗口是最大化的，先取消最大化
      if (_wasMaximized) {
        await windowManager.unmaximize();
      }
      
      // 设置新的窗口尺寸
      await windowManager.setSize(portraitModeWindowSize);
      await windowManager.center();
      
      // 更新状态
      _isPortraitModeEnabled = true;
      notifyListeners();
      
      // 保存状态到本地存储
      await _storageService.set('portrait_mode_enabled', true);
      await _storageService.set('original_window_size', {
        'width': _originalWindowSize!.width,
        'height': _originalWindowSize!.height,
      });
      await _storageService.set('original_window_position', {
        'x': _originalWindowPosition!.dx,
        'y': _originalWindowPosition!.dy,
      });
      await _storageService.set('was_maximized', _wasMaximized);
      
      debugPrint('竖屏模式已启用，窗口尺寸调整为: ${portraitModeWindowSize.width}x${portraitModeWindowSize.height}');
    } catch (e) {
      debugPrint('进入竖屏模式失败: $e');
      // 如果失败，确保状态一致
      _isPortraitModeEnabled = false;
      notifyListeners();
    }
  }
  
  /// 退出竖屏模式
  Future<void> exitPortraitMode() async {
    if (!_isPortraitModeEnabled) return;
    
    try {
      // 恢复原始窗口尺寸和位置
      if (_originalWindowSize != null) {
        await windowManager.setSize(_originalWindowSize!);
      }
      
      if (_originalWindowPosition != null) {
        await windowManager.setPosition(_originalWindowPosition!);
      }
      
      // 如果之前是最大化的，恢复最大化状态
      if (_wasMaximized) {
        await windowManager.maximize();
      }
      
      // 更新状态
      _isPortraitModeEnabled = false;
      notifyListeners();
      
      // 清除本地存储
      await _storageService.delete('portrait_mode_enabled');
      await _storageService.delete('original_window_size');
      await _storageService.delete('original_window_position');
      await _storageService.delete('was_maximized');
      
      debugPrint('竖屏模式已退出，窗口状态已恢复');
    } catch (e) {
      debugPrint('退出竖屏模式失败: $e');
      // 即使失败也要更新状态
      _isPortraitModeEnabled = false;
      notifyListeners();
    }
  }
  
  /// 切换竖屏模式
  Future<void> togglePortraitMode() async {
    if (_isPortraitModeEnabled) {
      await exitPortraitMode();
    } else {
      await enterPortraitMode();
    }
  }
  
  /// 初始化时恢复状态
  Future<void> initPortraitMode() async {
    try {
      final result = await _storageService.get<bool>('portrait_mode_enabled');
      result.when(
        success: (isEnabled) {
          if (isEnabled == true) {
            // 如果上次退出时是竖屏模式，但不自动恢复，只记录状态
            debugPrint('检测到上次退出时处于竖屏模式，但不自动恢复');
            // 清除状态，避免下次启动时的混乱
            _storageService.delete('portrait_mode_enabled');
          }
        },
        failure: (error) {
          debugPrint('读取竖屏模式状态失败: $error');
        },
      );
    } catch (e) {
      debugPrint('初始化竖屏模式状态失败: $e');
    }
  }
  
  /// 获取竖屏模式下的字体大小
  double getPortraitModeTextSize(double baseSize) {
    return _isPortraitModeEnabled ? baseSize * fontScaleFactor : baseSize;
  }

  /// 动态计算消息气泡的最大宽度
  /// 根据实际窗口宽度动态调整，确保消息气泡不超过对方头像的位置
  /// [windowWidth] 当前窗口的实际宽度
  double getMessageBubbleMaxWidth(double windowWidth) {
    // 计算各种padding和间距
    final chatPanelPadding = getPortraitModePadding(const EdgeInsets.all(8.0)).horizontal;
    final messageListPadding = getPortraitModePadding(const EdgeInsets.all(16.0)).horizontal;
    final avatarDiameter = getPortraitModeTextSize(18.0) * 2; // 头像直径
    final spacing = getPortraitModeSpacing(8.0); // 头像与消息间距

    // 可用宽度 = 窗口宽度 - 各种padding - 头像宽度 - 间距
    final availableWidth = windowWidth - chatPanelPadding - messageListPadding - avatarDiameter - spacing;

    // 根据模式设置不同的宽度限制
    if (_isPortraitModeEnabled) {
      // 竖屏模式：确保最小宽度不小于120px，最大不超过可用宽度的90%
      return availableWidth.clamp(120.0, availableWidth * 0.9);
    } else {
      // 桌面模式：保持合理的最大宽度，避免消息气泡过宽
      return availableWidth.clamp(200.0, 500.0);
    }
  }
  
  /// 获取竖屏模式下的间距
  double getPortraitModeSpacing(double baseSpacing) {
    return _isPortraitModeEnabled ? baseSpacing * 1.0 : baseSpacing; // 减少间距倍数
  }

  /// 获取竖屏模式下的内边距
  EdgeInsets getPortraitModePadding(EdgeInsets basePadding) {
    if (!_isPortraitModeEnabled) return basePadding;

    // 竖屏模式下减少内边距以节省空间
    return EdgeInsets.only(
      left: basePadding.left * 0.8,
      top: basePadding.top * 0.8,
      right: basePadding.right * 0.8,
      bottom: basePadding.bottom * 0.8,
    );
  }
  
  /// 检查是否应该显示UI元素
  bool shouldShowUIElement(String elementName) {
    if (!_isPortraitModeEnabled) return true;
    
    // 在竖屏模式下隐藏的UI元素（注意：不再隐藏title_bar）
    const hiddenElements = {
      'navigation_rail',
      'agent_panel',
      'conversation_selector',
      'left_panel',
    };
    
    return !hiddenElements.contains(elementName);
  }
}

/// 竖屏模式提供者
final portraitModeProvider = ChangeNotifierProvider<PortraitModeProvider>((ref) {
  final storageService = ref.read(storageServiceProvider);
  final provider = PortraitModeProvider(storageService);
  
  // 异步初始化
  provider.initPortraitMode();
  
  return provider;
});

/// 竖屏模式状态提供者（只读）
final portraitModeStateProvider = Provider<bool>((ref) {
  return ref.watch(portraitModeProvider).isPortraitModeEnabled;
});

/// 竖屏模式字体大小提供者
final portraitModeFontSizeProvider = Provider.family<double, double>((ref, baseSize) {
  final portraitModeNotifier = ref.watch(portraitModeProvider);
  return portraitModeNotifier.getPortraitModeTextSize(baseSize);
});

/// 竖屏模式间距提供者
final portraitModeSpacingProvider = Provider.family<double, double>((ref, baseSpacing) {
  final portraitModeNotifier = ref.watch(portraitModeProvider);
  return portraitModeNotifier.getPortraitModeSpacing(baseSpacing);
});

/// 竖屏模式内边距提供者
final portraitModePaddingProvider = Provider.family<EdgeInsets, EdgeInsets>((ref, basePadding) {
  final portraitModeNotifier = ref.watch(portraitModeProvider);
  return portraitModeNotifier.getPortraitModePadding(basePadding);
});
