import 'package:json_annotation/json_annotation.dart';

part 'order_model.g.dart';

@JsonSerializable()
class OrderModel {
  @JsonKey(name: '_id')
  final String id;
  
  final String orderNo;
  final String userId;
  final String packageId;
  final String packageName;
  final int packageQuota;
  final int quantity;
  final int unitPrice;
  final int unitQuota;
  final int orderAmount;
  final String paymentMethod;
  final String status;
  final String? transactionId;
  final String? fuiouOrderId;
  final String? fuiouPayAmount;
  final String? fuiouPayTime;
  final String? fuiouPayType;
  final String paymentUrl;
  final String qrCodeData;
  final DateTime createTime;
  final DateTime expireTime;
  final DateTime? payTime;
  final DateTime updatedAt;

  const OrderModel({
    required this.id,
    required this.orderNo,
    required this.userId,
    required this.packageId,
    required this.packageName,
    required this.packageQuota,
    required this.quantity,
    required this.unitPrice,
    required this.unitQuota,
    required this.orderAmount,
    required this.paymentMethod,
    required this.status,
    this.transactionId,
    this.fuiouOrderId,
    this.fuiouPayAmount,
    this.fuiouPayTime,
    this.fuiouPayType,
    required this.paymentUrl,
    required this.qrCodeData,
    required this.createTime,
    required this.expireTime,
    this.payTime,
    required this.updatedAt,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    // 处理时间字段的特殊格式
    DateTime parseDateTime(dynamic value) {
      if (value == null) return DateTime.now();
      if (value is Map && value.containsKey('\$date')) {
        return DateTime.fromMillisecondsSinceEpoch(value['\$date']);
      } else if (value is String) {
        try {
          return DateTime.parse(value);
        } catch (e) {
          return DateTime.now();
        }
      } else if (value is int) {
        return DateTime.fromMillisecondsSinceEpoch(value);
      }
      return DateTime.now();
    }

    DateTime? parseOptionalDateTime(dynamic value) {
      if (value == null) return null;
      try {
        return parseDateTime(value);
      } catch (e) {
        return null;
      }
    }

    return OrderModel(
      id: json['_id'] as String,
      orderNo: json['orderNo'] as String,
      userId: json['userId'] as String,
      packageId: json['packageId'] as String,
      packageName: json['packageName'] as String,
      packageQuota: json['packageQuota'] as int,
      quantity: json['quantity'] as int,
      unitPrice: json['unitPrice'] as int,
      unitQuota: json['unitQuota'] as int,
      orderAmount: json['orderAmount'] as int,
      paymentMethod: json['paymentMethod'] as String,
      status: json['status'] as String,
      transactionId: json['transactionId'] as String?,
      fuiouOrderId: json['fuiouOrderId'] as String?,
      fuiouPayAmount: json['fuiouPayAmount'] as String?,
      fuiouPayTime: json['fuiouPayTime'] as String?,
      fuiouPayType: json['fuiouPayType'] as String?,
      paymentUrl: json['paymentUrl'] as String,
      qrCodeData: json['qrCodeData'] as String,
      createTime: parseDateTime(json['createTime']),
      expireTime: parseDateTime(json['expireTime']),
      payTime: parseOptionalDateTime(json['payTime']),
      updatedAt: parseDateTime(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() => _$OrderModelToJson(this);

  String get statusText {
    switch (status) {
      case 'PENDING':
        return '待支付';
      case 'COMPLETED':
        return '已完成';
      case 'CANCELLED':
        return '已取消';
      case 'EXPIRED':
        return '已过期';
      case 'REFUNDED':
        return '已退款';
      default:
        return status;
    }
  }

  String get paymentMethodText {
    switch (paymentMethod) {
      case 'WECHAT':
        return '微信支付';
      case 'ALIPAY':
        return '支付宝';
      case 'BANK':
        return '银行卡';
      default:
        return paymentMethod;
    }
  }

  bool get isPaid => status == 'COMPLETED';
  bool get isPending => status == 'PENDING';
  bool get isExpired => status == 'EXPIRED' || (status == 'PENDING' && DateTime.now().isAfter(expireTime));
}
