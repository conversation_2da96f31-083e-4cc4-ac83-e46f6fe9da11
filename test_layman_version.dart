import 'dart:convert';
import 'package:http/http.dart' as http;

/// 测试大白话版本功能
void main() async {
  print('开始测试大白话版本功能...');
  
  // 测试配置
  const String baseUrl = 'https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction';
  const String testToken = 'test_token'; // 这里需要替换为真实的token
  const String testAgentId = '676b8b8b8b8b8b8b8b8b8b8b'; // 大白话智能体ID
  const String testModelId = '676b8b8b8b8b8b8b8b8b8b8c'; // 模型ID
  
  // 测试消息
  final testMessages = [
    {
      'role': 'user',
      'content': '请帮我分析一下我的八字命理，我是1990年5月15日上午10点出生的男性。'
    }
  ];
  
  try {
    // 构造请求体
    final requestBody = {
      'agentId': testAgentId,
      'modelId': testModelId,
      'messages': testMessages,
      'stream': true,
    };
    
    print('发送请求到: $baseUrl');
    print('请求体: ${jsonEncode(requestBody)}');
    
    // 创建HTTP请求
    final request = http.Request('POST', Uri.parse(baseUrl));
    request.headers.addAll({
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $testToken',
    });
    request.body = jsonEncode(requestBody);
    
    // 发送请求并获取流式响应
    final client = http.Client();
    final response = await client.send(request);
    
    print('响应状态码: ${response.statusCode}');
    print('响应头: ${response.headers}');
    
    if (response.statusCode == 200) {
      print('\n开始接收流式响应:');
      print('=' * 50);
      
      String currentStage = '';
      final professionalBuffer = StringBuffer();
      final laymanBuffer = StringBuffer();
      
      await for (final chunk in response.stream.transform(utf8.decoder)) {
        final lines = chunk.split('\n');
        
        for (final line in lines) {
          if (line.startsWith('data: ') && line.length > 6) {
            final data = line.substring(6);
            
            if (data == '[DONE]') {
              print('\n流式响应完成');
              break;
            }
            
            try {
              final jsonData = jsonDecode(data) as Map<String, dynamic>;
              
              if (jsonData.containsKey('stage')) {
                currentStage = jsonData['stage'] as String;
                print('\n>>> 阶段切换: $currentStage');
              } else if (jsonData.containsKey('transition')) {
                print('>>> 转换消息: ${jsonData['transition']}');
              } else if (jsonData.containsKey('stage_complete')) {
                final stage = jsonData['stage_complete'] as String;
                final content = jsonData['content'] as String;
                print('\n>>> 阶段完成: $stage');
                print('>>> 完整内容长度: ${content.length}');
                print('>>> 内容预览: ${content.substring(0, content.length > 100 ? 100 : content.length)}...');
                
                if (stage == 'professional') {
                  professionalBuffer.write(content);
                } else if (stage == 'layman') {
                  laymanBuffer.write(content);
                }
              } else if (jsonData.containsKey('complete')) {
                print('\n>>> 最终完成标记');
              } else if (jsonData.containsKey('error')) {
                print('>>> 错误: ${jsonData['error']}');
              } else if (jsonData.containsKey('choices')) {
                final choices = jsonData['choices'] as List?;
                if (choices != null && choices.isNotEmpty) {
                  final choice = choices[0] as Map<String, dynamic>;
                  final delta = choice['delta'] as Map<String, dynamic>?;
                  
                  if (delta != null && delta['content'] != null) {
                    final content = delta['content'] as String;
                    print(content, end: '');
                    
                    if (currentStage == 'professional') {
                      professionalBuffer.write(content);
                    } else if (currentStage == 'layman') {
                      laymanBuffer.write(content);
                    }
                  }
                }
              }
            } catch (e) {
              // 忽略JSON解析错误
              continue;
            }
          }
        }
      }
      
      print('\n' + '=' * 50);
      print('测试结果总结:');
      print('专业版本长度: ${professionalBuffer.length}');
      print('大白话版本长度: ${laymanBuffer.length}');
      
      if (professionalBuffer.isNotEmpty && laymanBuffer.isNotEmpty) {
        print('\n✅ 测试成功！两个版本都生成了内容');
        print('\n专业版本内容:');
        print('-' * 30);
        print(professionalBuffer.toString());
        print('\n大白话版本内容:');
        print('-' * 30);
        print(laymanBuffer.toString());
      } else {
        print('\n❌ 测试失败！缺少内容');
        print('专业版本是否为空: ${professionalBuffer.isEmpty}');
        print('大白话版本是否为空: ${laymanBuffer.isEmpty}');
      }
      
    } else {
      print('请求失败，状态码: ${response.statusCode}');
      final responseBody = await response.stream.bytesToString();
      print('响应内容: $responseBody');
    }
    
    client.close();
    
  } catch (e) {
    print('测试过程中发生错误: $e');
  }
  
  print('\n测试完成');
}
