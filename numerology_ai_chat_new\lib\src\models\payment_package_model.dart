/// 算力套餐模型
class PaymentPackageModel {
  final String id;
  final String packageName;
  final String packageDescription;
  final int quotaCount;
  final int price;
  final int originalPrice;
  final double discountRate;
  final bool isRecommended;
  final List<String> tags;
  final int validDays;
  final int minPurchaseCount;
  final int maxPurchaseCount;
  final int sortOrder;

  const PaymentPackageModel({
    required this.id,
    required this.packageName,
    required this.packageDescription,
    required this.quotaCount,
    required this.price,
    required this.originalPrice,
    required this.discountRate,
    required this.isRecommended,
    required this.tags,
    required this.validDays,
    required this.minPurchaseCount,
    required this.maxPurchaseCount,
    required this.sortOrder,
  });

  /// 从JSON创建套餐模型
  factory PaymentPackageModel.fromJson(Map<String, dynamic> json) {
    return PaymentPackageModel(
      id: json['id'] as String,
      packageName: json['packageName'] as String,
      packageDescription: json['packageDescription'] as String? ?? '',
      quotaCount: json['quotaCount'] as int,
      price: json['price'] as int,
      originalPrice: json['originalPrice'] as int? ?? json['price'] as int,
      discountRate: (json['discountRate'] as num?)?.toDouble() ?? 1.0,
      isRecommended: json['isRecommended'] as bool? ?? false,
      tags: (json['tags'] as List<dynamic>?)?.cast<String>() ?? [],
      validDays: json['validDays'] as int? ?? 0,
      minPurchaseCount: json['minPurchaseCount'] as int? ?? 1,
      maxPurchaseCount: json['maxPurchaseCount'] as int? ?? 0,
      sortOrder: json['sortOrder'] as int? ?? 0,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'packageName': packageName,
      'packageDescription': packageDescription,
      'quotaCount': quotaCount,
      'price': price,
      'originalPrice': originalPrice,
      'discountRate': discountRate,
      'isRecommended': isRecommended,
      'tags': tags,
      'validDays': validDays,
      'minPurchaseCount': minPurchaseCount,
      'maxPurchaseCount': maxPurchaseCount,
      'sortOrder': sortOrder,
    };
  }

  /// 格式化价格显示（分转元）
  String get formattedPrice {
    return '¥${(price / 100).toStringAsFixed(2)}';
  }

  /// 格式化原价显示（分转元）
  String get formattedOriginalPrice {
    return '¥${(originalPrice / 100).toStringAsFixed(2)}';
  }

  /// 计算每算力的价格
  double get pricePerQuota {
    return price / quotaCount;
  }

  /// 格式化每算力价格显示
  String get formattedPricePerQuota {
    return '¥${(pricePerQuota / 100).toStringAsFixed(3)}';
  }

  /// 是否有折扣
  bool get hasDiscount {
    return discountRate < 1.0 && originalPrice > price;
  }

  /// 折扣百分比显示
  String get discountPercentage {
    if (!hasDiscount) return '';
    return '${((1 - discountRate) * 100).toInt()}折';
  }

  /// 节省金额（分）
  int get savedAmount {
    if (!hasDiscount) return 0;
    return originalPrice - price;
  }

  /// 格式化节省金额显示
  String get formattedSavedAmount {
    if (!hasDiscount) return '';
    return '省¥${(savedAmount / 100).toStringAsFixed(2)}';
  }

  /// 是否有购买数量限制
  bool get hasQuantityLimit {
    return maxPurchaseCount > 0;
  }

  /// 是否有有效期限制
  bool get hasValidityLimit {
    return validDays > 0;
  }

  /// 有效期描述
  String get validityDescription {
    if (!hasValidityLimit) return '永久有效';
    return '$validDays天有效';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentPackageModel &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PaymentPackageModel{id: $id, packageName: $packageName, quotaCount: $quotaCount, price: $price}';
  }

  /// 复制并修改部分属性
  PaymentPackageModel copyWith({
    String? id,
    String? packageName,
    String? packageDescription,
    int? quotaCount,
    int? price,
    int? originalPrice,
    double? discountRate,
    bool? isRecommended,
    List<String>? tags,
    int? validDays,
    int? minPurchaseCount,
    int? maxPurchaseCount,
    int? sortOrder,
  }) {
    return PaymentPackageModel(
      id: id ?? this.id,
      packageName: packageName ?? this.packageName,
      packageDescription: packageDescription ?? this.packageDescription,
      quotaCount: quotaCount ?? this.quotaCount,
      price: price ?? this.price,
      originalPrice: originalPrice ?? this.originalPrice,
      discountRate: discountRate ?? this.discountRate,
      isRecommended: isRecommended ?? this.isRecommended,
      tags: tags ?? this.tags,
      validDays: validDays ?? this.validDays,
      minPurchaseCount: minPurchaseCount ?? this.minPurchaseCount,
      maxPurchaseCount: maxPurchaseCount ?? this.maxPurchaseCount,
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }
}
