/// 用户信息模型
class UserModel {
  final String id;
  final String username;
  final String? email;
  final String? phone;
  final String? avatar;
  final int availableCount;
  final int totalUsageCount;
  final String status;
  final DateTime createdAt;
  final DateTime? lastLoginAt;

  const UserModel({
    required this.id,
    required this.username,
    this.email,
    this.phone,
    this.avatar,
    required this.availableCount,
    required this.totalUsageCount,
    required this.status,
    required this.createdAt,
    this.lastLoginAt,
  });

  /// 从JSON创建用户模型
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['_id'] as String? ?? json['id'] as String,
      username: json['username'] as String,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      avatar: json['avatar'] as String?,
      availableCount: json['availableCount'] as int? ?? 0,
      totalUsageCount: json['totalUsageCount'] as int? ?? 0,
      status: '激活', // 默认状态
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastLoginAt: json['lastLoginAt'] != null
          ? DateTime.parse(json['lastLoginAt'] as String)
          : null,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'phone': phone,
      'avatar': avatar,
      'availableCount': availableCount,
      'totalUsageCount': totalUsageCount,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
      'lastLoginAt': lastLoginAt?.toIso8601String(),
    };
  }

  /// 复制并修改部分属性
  UserModel copyWith({
    String? id,
    String? username,
    String? email,
    String? phone,
    String? avatar,
    int? availableCount,
    int? totalUsageCount,
    String? status,
    DateTime? createdAt,
    DateTime? lastLoginAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      availableCount: availableCount ?? this.availableCount,
      totalUsageCount: totalUsageCount ?? this.totalUsageCount,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
    );
  }

  /// 是否为激活状态
  bool get isActive => status == '激活';

  /// 是否被禁用
  bool get isDisabled => status == '禁用';

  /// 是否被暂停
  bool get isSuspended => status == '暂停';

  /// 获取状态显示名称
  String get statusDisplayName {
    switch (status) {
      case '激活':
        return '正常';
      case '禁用':
        return '已禁用';
      case '暂停':
        return '已暂停';
      default:
        return '未知状态';
    }
  }

  /// 获取状态颜色
  String get statusColor {
    switch (status) {
      case '激活':
        return '#10B981';
      case '禁用':
        return '#EF4444';
      case '暂停':
        return '#F59E0B';
      default:
        return '#6B7280';
    }
  }

  /// 获取可用算力显示
  String get availableCountDisplay => '$availableCount 算力';

  @override
  String toString() {
    return 'UserModel(id: $id, username: $username, email: $email, '
        'availableCount: $availableCount算力, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
