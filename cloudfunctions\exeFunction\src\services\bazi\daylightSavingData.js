/**
 * 夏令时历史数据
 * 直接使用提供的数据格式，无需复杂转换
 */

const DAYLIGHT_SAVING_DATA = {
  "大陆": [
    "1986-1986 5/4-9/14", 
    "1987-1987 4/12-9/13", 
    "1988-1988 4/10-9/11", 
    "1989-1989 4/16-9/17", 
    "1990-1990 4/15-9/16", 
    "1991-1991 4/14-9/15"
  ],
  "台湾": [
    "1945-1951 5/1-9/30", 
    "1952-1952 3/1-10/31", 
    "1953-1954 4/1-10/31", 
    "1955-1959 4/1-9/30", 
    "1960-1961 6/1-9/30", 
    "1974-1975 4/1-9/30", 
    "1979-1979 7/1-9/30"
  ],
  "澳门": [
    "1946-1946 4/30-12/1", 
    "1947-1947 4/19-12/30", 
    "1948-1948 5/2-10/29", 
    "1951-1951 3/31-10/29", 
    "1952-1952 4/5-11/2", 
    "1953-1953 4/4-10/31", 
    "1954-1954 3/20-10/30", 
    "1955-1955 3/19-11/5", 
    "1956-1956 3/18-11/4", 
    "1957-1957 3/24-11/3", 
    "1958-1958 3/23-11/4", 
    "1959-1959 3/22-11/1", 
    "1960-1960 3/20-11/6", 
    "1961-1961 3/19-11/5", 
    "1962-1962 3/18-11/3", 
    "1963-1963 3/24-11/3", 
    "1964-1964 3/22-11/1", 
    "1965-1965 4/18-10/17", 
    "1966-1966 4/17-10/22", 
    "1967-1967 4/16-10/22", 
    "1968-1968 4/21-10/20", 
    "1969-1969 4/20-10/19", 
    "1970-1970 4/19-10/18", 
    "1971-1971 4/18-10/17", 
    "1972-1972 4/16-10/22", 
    "1973-1973 4/22-10/21", 
    "1974-1974 3/24-10/20", 
    "1975-1975 4/20-10/10", 
    "1976-1976 4/18-10/17", 
    "1979-1979 5/13-10/21"
  ],
  "香港": [
    "1941-1941 6/15-9/30", 
    "1942-1942 *", 
    "1943-1943 *", 
    "1944-1944 *", 
    "1945-1945 *", 
    "1946-1946 4/20-12/1", 
    "1947-1947 4/13-11/30", 
    "1948-1948 5/2-10/31", 
    "1949-1949 4/3-10/30", 
    "1950-1950 4/2-10/29", 
    "1951-1951 4/1-10/28", 
    "1952-1952 4/6-11/2", 
    "1953-1953 4/5-11/1", 
    "1954-1954 3/21-10/31", 
    "1955-1955 3/20-11/6", 
    "1956-1956 3/18-11/4", 
    "1957-1957 3/24-11/3", 
    "1958-1958 3/23-11/2", 
    "1959-1959 3/22-11/1", 
    "1960-1960 3/20-11/6", 
    "1961-1961 3/19-11/5", 
    "1962-1962 3/18-11/4", 
    "1963-1963 3/24-11/3", 
    "1964-1964 3/22-11/1", 
    "1965-1965 4/18-10/17", 
    "1966-1966 4/17-10/16", 
    "1967-1967 4/16-10/22", 
    "1968-1968 4/21-10/20", 
    "1969-1969 4/20-10/19", 
    "1970-1970 4/19-10/18", 
    "1971-1971 4/18-10/17", 
    "1972-1972 4/16-10/22", 
    "1973-1973 4/22-10/21,12/30-*", 
    "1974-1974 1/1-10/20", 
    "1975-1975 4/20-10/19", 
    "1976-1976 4/18-10/17", 
    "1979-1979 5/13-10/21"
  ]
};

/**
 * 解析夏令时数据的函数
 * @param {string} periodStr - 格式如 "1986-1986 5/4-9/14"
 * @returns {Object} 解析后的时间段对象
 */
function parseDaylightSavingPeriod(periodStr) {
  // 处理特殊情况
  if (periodStr.includes('*')) {
    return null; // 跳过包含 * 的记录
  }
  
  try {
    const [yearRange, dateRange] = periodStr.split(' ');
    const [startYear, endYear] = yearRange.split('-').map(Number);
    
    // 处理复杂的日期范围（如 "4/22-10/21,12/30-*"）
    const dateRanges = dateRange.split(',');
    const mainRange = dateRanges[0];
    const [startDate, endDate] = mainRange.split('-');
    
    return {
      startYear,
      endYear,
      startMonth: parseInt(startDate.split('/')[0]),
      startDay: parseInt(startDate.split('/')[1]),
      endMonth: parseInt(endDate.split('/')[0]),
      endDay: parseInt(endDate.split('/')[1])
    };
  } catch (error) {
    console.warn(`解析夏令时数据失败: ${periodStr}`, error);
    return null;
  }
}

/**
 * 夏令时调整函数
 * @param {Date} inputDateTime - 输入的日期时间
 * @param {Date} birthDate - 出生日期
 * @param {string} region - 地区（大陆、台湾、香港、澳门）
 * @returns {Date} 调整后的日期时间
 */
function adjustForDaylightSaving(inputDateTime, birthDate, region = '大陆') {
  const year = birthDate.getFullYear();
  const dstPeriods = DAYLIGHT_SAVING_DATA[region] || [];
  
  for (const periodStr of dstPeriods) {
    const period = parseDaylightSavingPeriod(periodStr);
    
    if (!period) continue;
    
    // 检查年份是否在范围内
    if (year >= period.startYear && year <= period.endYear) {
      const startDate = new Date(year, period.startMonth - 1, period.startDay);
      const endDate = new Date(year, period.endMonth - 1, period.endDay);
      
      // 检查出生日期是否在夏令时期间
      if (birthDate >= startDate && birthDate <= endDate) {
        console.log(`检测到夏令时期间出生: ${year}年${region}, 调整时间-1小时`);
        // 夏令时期间出生，需要减去1小时
        return new Date(inputDateTime.getTime() - 60 * 60 * 1000);
      }
    }
  }
  
  return inputDateTime; // 非夏令时期间，无需调整
}

/**
 * 检查指定日期是否在夏令时期间
 * @param {Date} date - 要检查的日期
 * @param {string} region - 地区
 * @returns {boolean} 是否在夏令时期间
 */
function isDaylightSavingTime(date, region = '大陆') {
  const year = date.getFullYear();
  const dstPeriods = DAYLIGHT_SAVING_DATA[region] || [];
  
  for (const periodStr of dstPeriods) {
    const period = parseDaylightSavingPeriod(periodStr);
    
    if (!period) continue;
    
    if (year >= period.startYear && year <= period.endYear) {
      const startDate = new Date(year, period.startMonth - 1, period.startDay);
      const endDate = new Date(year, period.endMonth - 1, period.endDay);
      
      if (date >= startDate && date <= endDate) {
        return true;
      }
    }
  }
  
  return false;
}

/**
 * 获取夏令时信息
 * @param {Date} date - 日期
 * @param {string} region - 地区
 * @returns {Object|null} 夏令时信息
 */
function getDaylightSavingInfo(date, region = '大陆') {
  const year = date.getFullYear();
  const dstPeriods = DAYLIGHT_SAVING_DATA[region] || [];
  
  for (const periodStr of dstPeriods) {
    const period = parseDaylightSavingPeriod(periodStr);
    
    if (!period) continue;
    
    if (year >= period.startYear && year <= period.endYear) {
      const startDate = new Date(year, period.startMonth - 1, period.startDay);
      const endDate = new Date(year, period.endMonth - 1, period.endDay);
      
      if (date >= startDate && date <= endDate) {
        return {
          region,
          year,
          startDate,
          endDate,
          periodStr,
          isDaylightSaving: true
        };
      }
    }
  }
  
  return {
    region,
    year,
    isDaylightSaving: false
  };
}

module.exports = {
  DAYLIGHT_SAVING_DATA,
  parseDaylightSavingPeriod,
  adjustForDaylightSaving,
  isDaylightSavingTime,
  getDaylightSavingInfo
};
