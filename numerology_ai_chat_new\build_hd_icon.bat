@echo off
chcp 65001 >nul
echo ════════════════════════════════════════════════════════════════════
echo    🎯 高清图标构建脚本
echo ════════════════════════════════════════════════════════════════════
echo 📝 将logo.png转换为高清图标并构建应用
echo 🎯 支持高DPI显示器，包含超高清尺寸
echo ════════════════════════════════════════════════════════════════════
echo.

echo 🔍 步骤1: 生成高清图标...
python create_hd_icon.py
if %errorlevel% neq 0 (
    echo ❌ 高清图标生成失败！
    echo 💡 请确保安装了PIL库: pip install Pillow
    pause
    exit /b 1
)

echo.
echo 🧹 步骤2: 完全清理构建缓存...
flutter clean
if exist build rmdir /s /q build 2>nul

echo.
echo 📦 步骤3: 重新获取依赖...
flutter pub get

echo.
echo 🔨 步骤4: 构建高清图标应用...
flutter build windows --release

echo.
echo 🎉 高清图标构建完成！
echo ════════════════════════════════════════════════════════════════════
echo 📁 新的exe文件: build\windows\x64\runner\Release\numerology_ai_chat.exe
echo.

echo 🔍 步骤5: 验证图标文件...
if exist "windows\runner\resources\app_icon.ico" (
    echo ✅ ICO文件存在
    for %%A in ("windows\runner\resources\app_icon.ico") do (
        echo 📦 文件大小: %%~zA 字节
        if %%~zA gtr 50000 (
            echo ✨ 高清图标质量: 优秀 ^(文件大小 ^> 50KB^)
        ) else if %%~zA gtr 10000 (
            echo 📊 高清图标质量: 良好 ^(文件大小 ^> 10KB^)
        ) else (
            echo ⚠️ 图标质量可能不够高 ^(文件大小较小^)
        )
    )
) else (
    echo ❌ ICO文件不存在
)

echo.
echo 📝 高清图标说明:
echo ════════════════════════════════════════════════════════════════════
echo 1. 包含13种尺寸: 16x16 到 1024x1024
echo 2. 支持高DPI显示器和4K显示器
echo 3. 文件大小较大但质量更高
echo 4. 在所有Windows版本中都能正确显示
echo.
echo 🔧 如果图标仍然显示为默认图标:
echo    - 按 Ctrl+Shift+Esc 打开任务管理器
echo    - 找到 "Windows资源管理器" 进程
echo    - 右键选择 "重新启动"
echo.
echo 💡 提示: 高清图标在高分辨率显示器上效果最佳
echo    如果使用普通显示器，图标质量提升可能不太明显
echo.
pause
