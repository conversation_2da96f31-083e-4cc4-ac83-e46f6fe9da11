import 'dart:convert';
import 'dart:developer' as developer;
import 'package:http/http.dart' as http;
import '../core/constants/app_constants.dart';

/// HTTP客户端服务
/// 支持自动token续签和请求重试
class HttpClientService {
  static HttpClientService? _instance;
  static HttpClientService get instance => _instance ??= HttpClientService._();
  
  HttpClientService._();

  // 回调函数
  Function()? _onTokenExpired;
  Function(String newToken)? _onTokenRefreshed;
  Function()? _getCurrentToken;
  Function()? _refreshToken;

  /// 设置回调函数
  void setCallbacks({
    Function()? onTokenExpired,
    Function(String newToken)? onTokenRefreshed,
    Function()? getCurrentToken,
    Function()? refreshToken,
  }) {
    _onTokenExpired = onTokenExpired;
    _onTokenRefreshed = onTokenRefreshed;
    _getCurrentToken = getCurrentToken;
    _refreshToken = refreshToken;
  }

  /// 发送POST请求（支持自动token续签）
  Future<http.Response> post(
    String url,
    Map<String, dynamic> body, {
    Map<String, String>? headers,
    bool requiresAuth = false,
    int maxRetries = 1,
  }) async {
    return await _requestWithRetry(
      () => _makeRequest('POST', url, body, headers, requiresAuth),
      maxRetries: maxRetries,
    );
  }

  /// 发送GET请求（支持自动token续签）
  Future<http.Response> get(
    String url, {
    Map<String, String>? headers,
    bool requiresAuth = false,
    int maxRetries = 1,
  }) async {
    return await _requestWithRetry(
      () => _makeRequest('GET', url, null, headers, requiresAuth),
      maxRetries: maxRetries,
    );
  }

  /// 执行HTTP请求
  Future<http.Response> _makeRequest(
    String method,
    String url,
    Map<String, dynamic>? body,
    Map<String, String>? headers,
    bool requiresAuth,
  ) async {
    final requestHeaders = <String, String>{
      'Content-Type': 'application/json',
      ...?headers,
    };

    // 如果需要认证，添加token
    if (requiresAuth && _getCurrentToken != null) {
      final token = _getCurrentToken!();
      if (token != null) {
        requestHeaders['Authorization'] = 'Bearer $token';
      }
    }

    developer.log(
      'HttpClient: 发送$method请求到 $url',
      name: 'HttpClientService',
    );

    switch (method) {
      case 'POST':
        return await http.post(
          Uri.parse(url),
          headers: requestHeaders,
          body: body != null ? jsonEncode(body) : null,
        ).timeout(AppConstants.connectTimeout);
      case 'GET':
        return await http.get(
          Uri.parse(url),
          headers: requestHeaders,
        ).timeout(AppConstants.connectTimeout);
      default:
        throw UnsupportedError('不支持的HTTP方法: $method');
    }
  }

  /// 带重试的请求执行
  Future<http.Response> _requestWithRetry(
    Future<http.Response> Function() requestFunction, {
    int maxRetries = 1,
  }) async {
    int attempts = 0;
    
    while (attempts <= maxRetries) {
      try {
        final response = await requestFunction();
        
        // 检查是否是token过期错误
        if (response.statusCode == 401 && attempts < maxRetries) {
          developer.log(
            'HttpClient: 收到401错误，尝试刷新token',
            name: 'HttpClientService',
          );
          
          // 尝试刷新token
          final refreshed = await _attemptTokenRefresh();
          if (refreshed) {
            attempts++;
            continue; // 重试请求
          } else {
            // 刷新失败，通知token过期
            _onTokenExpired?.call();
            return response;
          }
        }
        
        // 请求成功或其他错误，直接返回
        return response;
      } catch (e) {
        attempts++;
        if (attempts > maxRetries) {
          rethrow;
        }
        
        developer.log(
          'HttpClient: 请求失败，尝试重试 ($attempts/$maxRetries): $e',
          name: 'HttpClientService',
        );
        
        // 等待一段时间后重试
        await Future.delayed(Duration(seconds: attempts));
      }
    }
    
    throw Exception('请求重试次数已用完');
  }

  /// 尝试刷新token
  Future<bool> _attemptTokenRefresh() async {
    if (_refreshToken == null) {
      developer.log(
        'HttpClient: 无法刷新token，未设置刷新回调',
        name: 'HttpClientService',
      );
      return false;
    }

    try {
      developer.log(
        'HttpClient: 开始刷新token',
        name: 'HttpClientService',
      );
      
      final result = await _refreshToken!();
      
      if (result == true) {
        developer.log(
          'HttpClient: Token刷新成功',
          name: 'HttpClientService',
        );
        return true;
      } else {
        developer.log(
          'HttpClient: Token刷新失败',
          name: 'HttpClientService',
        );
        return false;
      }
    } catch (e) {
      developer.log(
        'HttpClient: Token刷新异常: $e',
        name: 'HttpClientService',
      );
      return false;
    }
  }

  /// 清理资源
  void dispose() {
    _onTokenExpired = null;
    _onTokenRefreshed = null;
    _getCurrentToken = null;
    _refreshToken = null;
  }
}
