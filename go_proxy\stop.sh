#!/bin/bash

# Go Proxy 停止脚本

echo "停止 Go Proxy 服务..."

# 查找并停止进程
PID=$(pgrep -f "go_proxy_linux")

if [ -z "$PID" ]; then
    echo "Go Proxy 服务未在运行"
    exit 0
fi

echo "找到进程 PID: $PID"
kill $PID

# 等待进程结束
sleep 2

# 检查进程是否已结束
if ps -p $PID > /dev/null 2>&1; then
    echo "进程未正常结束，强制终止..."
    kill -9 $PID
    sleep 1
fi

if ps -p $PID > /dev/null 2>&1; then
    echo "无法停止进程，请手动处理"
    exit 1
else
    echo "Go Proxy 服务已停止"
fi
