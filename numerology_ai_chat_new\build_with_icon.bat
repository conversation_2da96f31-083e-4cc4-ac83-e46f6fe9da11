@echo off
chcp 65001 >nul
echo ════════════════════════════════════════════
echo    命理AI聊天应用 - 高质量图标构建脚本
echo ════════════════════════════════════════════
echo.

echo 🔄 步骤1: 生成超高质量图标...
python generate_high_quality_icon.py
if %errorlevel% neq 0 (
    echo ⚠️ 自定义图标生成失败，使用Flutter默认方式...
)

echo 🔄 步骤1.5: 运行Flutter图标生成器...
dart run flutter_launcher_icons
if %errorlevel% neq 0 (
    echo ❌ Flutter图标生成失败！
    pause
    exit /b 1
)
echo ✅ 图标生成完成

echo.
echo 🔄 步骤2: 清理构建缓存...
flutter clean
if %errorlevel% neq 0 (
    echo ⚠️ 清理缓存失败，继续构建...
)

echo.
echo 🔄 步骤3: 获取依赖包...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ 获取依赖包失败！
    pause
    exit /b 1
)
echo ✅ 依赖包获取完成

echo.
echo 🔄 步骤4: 构建Windows应用...
flutter build windows --release
if %errorlevel% neq 0 (
    echo ❌ 构建失败！
    pause
    exit /b 1
)

echo.
echo 🎉 构建完成！
echo 📁 可执行文件位置: build\windows\x64\runner\Release\numerology_ai_chat.exe
echo 💡 超高质量图标已自动应用，支持高DPI和4K显示
echo 🔍 图标特性: 15种尺寸、高DPI支持、4K兼容、增强清晰度
echo.
pause
