// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'agent_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Agent _$AgentFromJson(Map<String, dynamic> json) => Agent(
      agentId: json['agentId'] as String,
      agentName: json['agentName'] as String,
      agentDisplayName: json['agentDisplayName'] as String,
      agentType: json['agentType'] as String,
      description: json['description'] as String,
      isActive: json['isActive'] as bool,
      sortOrder: (json['sortOrder'] as num).toInt(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      hasPrompt: json['hasPrompt'] as bool,
    );

Map<String, dynamic> _$AgentToJson(Agent instance) => <String, dynamic>{
      'agentId': instance.agentId,
      'agentName': instance.agentName,
      'agentDisplayName': instance.agentDisplayName,
      'agentType': instance.agentType,
      'description': instance.description,
      'isActive': instance.isActive,
      'sortOrder': instance.sortOrder,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'hasPrompt': instance.hasPrompt,
    };
