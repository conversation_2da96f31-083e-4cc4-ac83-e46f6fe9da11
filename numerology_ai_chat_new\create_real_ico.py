#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建真正的ICO文件，解决图标显示问题
"""

from PIL import Image
import os
import shutil

def create_real_ico_file(jpg_path, ico_path):
    """
    创建真正的ICO文件
    """
    try:
        print(f"🔄 开始处理: {jpg_path}")
        
        # 打开源图片
        with Image.open(jpg_path) as img:
            print(f"📏 原始尺寸: {img.size}")
            
            # 转换为RGBA
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            # 裁剪为正方形
            min_size = min(img.size)
            left = (img.size[0] - min_size) // 2
            top = (img.size[1] - min_size) // 2
            right = left + min_size
            bottom = top + min_size
            img_square = img.crop((left, top, right, bottom))
            
            # Windows ICO标准尺寸
            sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
            
            # 创建不同尺寸的图像
            images = []
            for size in sizes:
                resized = img_square.resize(size, Image.Resampling.LANCZOS)
                images.append(resized)
                print(f"✅ 创建 {size[0]}x{size[1]} 图像")
            
            # 保存为真正的ICO文件
            images[0].save(
                ico_path,
                format='ICO',
                sizes=[(img.width, img.height) for img in images],
                append_images=images[1:]
            )
            
            print(f"✅ 成功创建ICO文件: {ico_path}")
            
            # 验证文件
            file_size = os.path.getsize(ico_path)
            print(f"📦 文件大小: {file_size} 字节")
            
            # 检查文件头
            with open(ico_path, 'rb') as f:
                header = f.read(6)
                if header[:2] == b'\x00\x00' and header[2:4] == b'\x01\x00':
                    print("✅ ICO文件格式验证成功")
                else:
                    print("❌ ICO文件格式验证失败")
                    return False
            
            return True
            
    except Exception as e:
        print(f"❌ 创建ICO失败: {e}")
        return False

def main():
    print("🔧 创建真正的ICO文件")
    print("=" * 40)
    
    jpg_path = "assets/images/logo.png"
    ico_path = "windows/runner/resources/app_icon.ico"
    
    # 检查源文件
    if not os.path.exists(jpg_path):
        print(f"❌ 源文件不存在: {jpg_path}")
        return False
    
    # 备份现有文件
    if os.path.exists(ico_path):
        backup_path = ico_path + ".broken_backup"
        shutil.copy2(ico_path, backup_path)
        print(f"📦 已备份损坏的图标: {backup_path}")
    
    # 创建真正的ICO文件
    success = create_real_ico_file(jpg_path, ico_path)
    
    if success:
        print("\n🎉 真正的ICO文件创建成功!")
        print("📝 接下来请运行:")
        print("1. flutter clean")
        print("2. flutter build windows --release")
        return True
    else:
        print("\n💥 ICO文件创建失败")
        return False

if __name__ == "__main__":
    main()
