import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../models/bazi_model.dart';
import '../core/constants/app_constants.dart';
import 'bazi_storage_service.dart';
import 'region_service.dart';

/// 排盘服务
class BaziService {
  static const String apiUrl = AppConstants.exeFunctionUrl;
  final BaziStorageService _storageService = BaziStorageService();

  /// 执行排盘
  Future<BaziResultModel> calculateBazi(BaziInputModel input) async {
    // 调用真实API
    final result = await _callRealBaziApi(input);

    // 保存排盘结果到本地文件
    await _saveBaziToFile(result);

    // 保存到存储服务
    await _storageService.saveBaziRecord(result);

    return result;
  }

  /// 获取所有八字记录
  Future<List<BaziRecordSummary>> getAllBaziRecords() async {
    return await _storageService.getAllBaziRecords();
  }

  /// 删除八字记录
  Future<void> deleteBaziRecord(String recordId) async {
    await _storageService.deleteBaziRecord(recordId);
  }

  /// 打开文件所在位置
  Future<void> openFileLocation(String filePath) async {
    await _storageService.openFileLocation(filePath);
  }

  /// 读取八字文件内容
  Future<String> readBaziFile(String filePath) async {
    return await _storageService.readBaziFile(filePath);
  }

  /// 调用真实的排盘API
  Future<BaziResultModel> _callRealBaziApi(BaziInputModel input) async {
    // 格式化日期时间
    final birthDate = '${input.birthDateTime.year}-${input.birthDateTime.month.toString().padLeft(2, '0')}-${input.birthDateTime.day.toString().padLeft(2, '0')}';
    final birthTime = '${input.birthDateTime.hour.toString().padLeft(2, '0')}:${input.birthDateTime.minute.toString().padLeft(2, '0')}';

    // 根据地址信息判断地区
    String region = '大陆'; // 默认大陆
    if (input.latitude != null && input.longitude != null) {
      // 这里需要根据实际的地址信息来判断地区
      // 由于BaziInputModel中没有详细的地址信息，我们需要从birthPlace中推断
      region = _inferRegionFromBirthPlace(input.birthPlace);
    }

    final requestBody = {
      "action": "baziAnalyze",
      "name": input.name,
      "gender": input.gender.displayName,
      "calendarType": input.calendarType.displayName,
      "birthDate": birthDate,
      "birthTime": birthTime,
      "isLeapMonth": input.isLeapMonth,
      "birthPlace": input.birthPlace,
      // 真太阳时相关参数（可选）
      if (input.latitude != null) "latitude": input.latitude,
      if (input.longitude != null) "longitude": input.longitude,
      if (input.latitude != null && input.longitude != null) "region": region,
      // 新增的夏令时和真太阳时计算选项
      "considerDaylightSaving": input.considerDaylightSaving,
      "enableSolarTimeCalculation": input.enableSolarTimeCalculation,
      // 子时处理设置
      "ziTimeHandling": input.ziTimeHandling,
    };

    final response = await http.post(
      Uri.parse(apiUrl),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(requestBody),
    ).timeout(const Duration(seconds: 30));

    if (response.statusCode != 200) {
      throw Exception('API请求失败: ${response.statusCode}');
    }

    final data = json.decode(response.body);

    if (data['code'] != 0 || data['data'] == null || data['data']['textResult'] == null) {
      throw Exception('API返回错误: ${data['message'] ?? '未知错误'}');
    }

    final baziText = data['data']['textResult'] as String;

    // 解析API返回的文本，提取四柱信息
    final detailedData = _parseApiResult(baziText);

    return BaziResultModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      input: input,
      baziText: baziText,
      detailedData: detailedData,
      createdAt: DateTime.now(),
    );
  }

  /// 从出生地字符串推断地区
  String _inferRegionFromBirthPlace(String birthPlace) {
    if (birthPlace.contains('台湾') || birthPlace.contains('臺灣')) {
      return '台湾';
    } else if (birthPlace.contains('香港')) {
      return '香港';
    } else if (birthPlace.contains('澳门') || birthPlace.contains('澳門')) {
      return '澳门';
    }
    return '大陆';
  }

  /// 解析API返回结果，提取结构化数据
  Map<String, dynamic> _parseApiResult(String baziText) {
    final detailedData = <String, dynamic>{
      'calculation_time': DateTime.now().toIso8601String(),
      'source': 'real_api',
    };

    // 尝试从文本中提取四柱信息
    final lines = baziText.split('\n');
    for (final line in lines) {
      if (line.contains('年柱') && line.contains('：')) {
        detailedData['year_pillar'] = line.split('：')[1].trim();
      } else if (line.contains('月柱') && line.contains('：')) {
        detailedData['month_pillar'] = line.split('：')[1].trim();
      } else if (line.contains('日柱') && line.contains('：')) {
        detailedData['day_pillar'] = line.split('：')[1].trim();
      } else if (line.contains('时柱') && line.contains('：')) {
        detailedData['hour_pillar'] = line.split('：')[1].trim();
      }
    }

    return detailedData;
  }

  /// 保存排盘结果到本地文件
  Future<String> _saveBaziToFile(BaziResultModel result) async {
    try {
      // 获取应用文档目录
      final directory = await getApplicationDocumentsDirectory();
      final baziDir = Directory(path.join(directory.path, 'bazi_results'));

      // 确保目录存在
      if (!await baziDir.exists()) {
        await baziDir.create(recursive: true);
      }

      // 生成文件名：姓名_年月日.txt
      final fileName = '${result.input.name}_${result.input.birthDateTime.year}${result.input.birthDateTime.month.toString().padLeft(2, '0')}${result.input.birthDateTime.day.toString().padLeft(2, '0')}.txt';
      final filePath = path.join(baziDir.path, fileName);

      // 写入文件
      final file = File(filePath);
      await file.writeAsString(result.baziText, encoding: utf8);

      // 更新结果模型中的文件路径
      result.detailedData['file_path'] = filePath;
      result.detailedData['file_name'] = fileName;

      return filePath;
    } catch (e) {
      print('保存文件失败: $e');
      rethrow;
    }
  }







  /// 验证排盘结果
  Future<bool> validateBaziResult(String resultId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 300));
      return true;
    } catch (e) {
      return false;
    }
  }
}
