import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:window_manager/window_manager.dart';
import 'src/core/constants/app_constants.dart';
import 'src/core/router/app_router.dart';
import 'src/theme/app_theme.dart';
import 'src/providers/theme_provider.dart';
import 'src/providers/auth_provider.dart';

void main() async {
  // 确保Flutter绑定初始化
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化窗口管理器
  await windowManager.ensureInitialized();

  // 设置窗口选项
  const windowOptions = WindowOptions(
    size: Size(AppConstants.defaultWindowWidth, AppConstants.defaultWindowHeight),
    minimumSize: Size(AppConstants.minWindowWidth, AppConstants.minWindowHeight),
    center: true,
    title: AppConstants.appName,
    titleBarStyle: TitleBarStyle.hidden,
  );

  // 等待窗口准备好后显示
  windowManager.waitUntilReadyToShow(windowOptions, () async {
    await windowManager.show();
    await windowManager.focus();
  });

  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends ConsumerStatefulWidget {
  const MyApp({super.key});

  @override
  ConsumerState<MyApp> createState() => _MyAppState();
}

class _MyAppState extends ConsumerState<MyApp> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // 当应用重新获得焦点时，检查token状态并刷新用户信息
    if (state == AppLifecycleState.resumed) {
      final authState = ref.read(authProvider);
      if (authState.isAuthenticated) {
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            // 检查token状态并续签
            ref.read(authProvider.notifier).onAppResumed();
            // 刷新用户信息
            ref.read(authProvider.notifier).refreshUser();
          }
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = ref.watch(themeProviderNotifier);
    final router = ref.watch(routerProvider);

    // 监听AuthProvider的导航标记
    ref.listen<AuthProvider>(authProvider, (previous, current) {
      debugPrint('MyApp: AuthProvider状态变化监听器被触发');
      debugPrint('MyApp: shouldNavigateToLogin = ${current.shouldNavigateToLogin}');
      debugPrint('MyApp: isAuthenticated = ${current.isAuthenticated}');
      debugPrint('MyApp: status = ${current.status}');

      if (current.shouldNavigateToLogin) {
        debugPrint('MyApp: 检测到需要跳转到登录页面');
        // 清除标记
        current.clearNavigationFlag();
        // 跳转到登录页面
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            debugPrint('MyApp: 准备执行跳转到登录页面');
            router.go('/login');
            debugPrint('MyApp: 已执行跳转到登录页面');
          } else {
            debugPrint('MyApp: 组件已销毁，无法跳转');
          }
        });
      } else {
        debugPrint('MyApp: 无需跳转到登录页面');
      }
    });

    return MaterialApp.router(
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,

      // 路由配置
      routerConfig: router,

      // 主题配置
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeProvider.themeMode,

      // 本地化配置
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('zh', 'CN'),
        Locale('en', 'US'),
      ],
      locale: const Locale('zh', 'CN'),
    );
  }
}


