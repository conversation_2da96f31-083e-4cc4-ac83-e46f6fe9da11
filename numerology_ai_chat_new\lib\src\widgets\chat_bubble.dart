import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../models/chat_message.dart';
import '../services/image_service.dart';
import '../providers/portrait_mode_provider.dart';

/// 聊天气泡组件
class ChatBubble extends ConsumerWidget {
  final ChatMessage message;
  final VoidCallback? onResend;
  final bool isRetrying;

  const ChatBubble({
    super.key,
    required this.message,
    this.onResend,
    this.isRetrying = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final portraitModeNotifier = ref.watch(portraitModeProvider);

    return Container(
      margin: EdgeInsets.only(
        bottom: portraitModeNotifier.getPortraitModeSpacing(8),
      ),
      child: Row(
        mainAxisAlignment: message.isUser
            ? MainAxisAlignment.end
            : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!message.isUser) ...[
            _buildAvatar(theme, portraitModeNotifier),
            SizedBox(width: portraitModeNotifier.getPortraitModeSpacing(8)),
          ],

          Flexible(
            child: Column(
              crossAxisAlignment: message.isUser
                  ? CrossAxisAlignment.end
                  : CrossAxisAlignment.start,
              children: [
                _buildMessageBubble(context, theme, portraitModeNotifier),
                SizedBox(height: portraitModeNotifier.getPortraitModeSpacing(4)),
                _buildMessageInfo(context, theme, portraitModeNotifier),
              ],
            ),
          ),

          if (message.isUser) ...[
            SizedBox(width: portraitModeNotifier.getPortraitModeSpacing(8)),
            _buildUserAvatar(theme, portraitModeNotifier),
          ],
        ],
      ),
    );
  }

  Widget _buildAvatar(ThemeData theme, PortraitModeProvider portraitModeNotifier) {
    IconData iconData;
    Color backgroundColor;

    if (message.isLaymanDisplay) {
      iconData = Icons.lightbulb_outline;
      backgroundColor = Colors.green.shade400;
    } else {
      switch (message.sender) {
        case MessageSender.assistant:
          iconData = Icons.smart_toy;
          backgroundColor = theme.colorScheme.primary;
          break;
        case MessageSender.system:
          iconData = Icons.info_outline;
          backgroundColor = theme.colorScheme.secondary;
          break;
        default:
          iconData = Icons.person;
          backgroundColor = theme.colorScheme.tertiary;
      }
    }

    final avatarSize = portraitModeNotifier.getPortraitModeTextSize(18);
    final iconSize = portraitModeNotifier.getPortraitModeTextSize(18);

    return CircleAvatar(
      radius: avatarSize,
      backgroundColor: backgroundColor,
      child: Icon(
        iconData,
        size: iconSize,
        color: Colors.white,
      ),
    );
  }

  Widget _buildUserAvatar(ThemeData theme, PortraitModeProvider portraitModeNotifier) {
    final avatarSize = portraitModeNotifier.getPortraitModeTextSize(18);
    final iconSize = portraitModeNotifier.getPortraitModeTextSize(18);

    return CircleAvatar(
      radius: avatarSize,
      backgroundColor: theme.colorScheme.tertiary,
      child: Icon(
        Icons.person,
        size: iconSize,
        color: Colors.white,
      ),
    );
  }

  Widget _buildMessageBubble(BuildContext context, ThemeData theme, PortraitModeProvider portraitModeNotifier) {
    Color backgroundColor;
    Color textColor;

    if (message.isLaymanDisplay) {
      backgroundColor = Colors.green.shade50;
      textColor = Colors.green.shade800;
    } else if (message.isUser) {
      backgroundColor = theme.colorScheme.primary;
      textColor = theme.colorScheme.onPrimary;
    } else {
      backgroundColor = theme.colorScheme.surfaceContainerHighest;
      textColor = theme.colorScheme.onSurface;
    }

    // 获取当前窗口宽度，动态计算消息气泡最大宽度
    final screenWidth = MediaQuery.of(context).size.width;
    final maxWidth = portraitModeNotifier.getMessageBubbleMaxWidth(screenWidth);
    final borderRadius = portraitModeNotifier.getPortraitModeTextSize(16);
    
    return Container(
      constraints: BoxConstraints(maxWidth: maxWidth),
      padding: portraitModeNotifier.getPortraitModePadding(
        const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(borderRadius).copyWith(
          bottomLeft: message.isUser ? Radius.circular(borderRadius) : const Radius.circular(4),
          bottomRight: message.isUser ? const Radius.circular(4) : Radius.circular(borderRadius),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 大白话版本标识
          if (message.isLaymanDisplay) ...[
            Container(
              padding: portraitModeNotifier.getPortraitModePadding(
                const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              ),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.green.shade200,
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.lightbulb_outline,
                    size: portraitModeNotifier.getPortraitModeTextSize(14),
                    color: Colors.green.shade600,
                  ),
                  SizedBox(width: portraitModeNotifier.getPortraitModeSpacing(4)),
                  Text(
                    '大白话版本',
                    style: TextStyle(
                      fontSize: portraitModeNotifier.getPortraitModeTextSize(12),
                      color: Colors.green.shade700,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: portraitModeNotifier.getPortraitModeSpacing(8)),
          ],

          // 图片内容
          if (message.hasImages) _buildImageContent(context, portraitModeNotifier),

          // 文本内容
          if (message.content.isNotEmpty) ...[
            if (message.hasImages) SizedBox(height: portraitModeNotifier.getPortraitModeSpacing(8)),
            if (message.isTyping && message.content.isEmpty)
              _buildTypingIndicator(textColor, portraitModeNotifier)
            else
              _buildMessageContent(textColor, portraitModeNotifier),
          ] else if (!message.hasImages) ...[
            if (message.isTyping)
              _buildTypingIndicator(textColor, portraitModeNotifier)
            else
              _buildMessageContent(textColor, portraitModeNotifier),
          ],

          if (message.isFailed)
            _buildErrorActions(theme, portraitModeNotifier),
        ],
      ),
    );
  }

  Widget _buildMessageContent(Color textColor, PortraitModeProvider portraitModeNotifier) {
    if (message.content.isEmpty) {
      return const SizedBox.shrink();
    }

    // 检查是否包含Markdown格式
    final hasMarkdown = message.content.contains('**') ||
        message.content.contains('*') ||
        message.content.contains('#') ||
        message.content.contains('```') ||
        message.content.contains('[') ||
        message.content.contains('|');

    if (!hasMarkdown) {
      // 纯文本显示
      return SelectableText(
        message.content,
        style: TextStyle(
          color: textColor,
          fontSize: portraitModeNotifier.getPortraitModeTextSize(14),
          height: 1.4,
        ),
      );
    }

    // 创建Markdown样式表
    final markdownStyleSheet = MarkdownStyleSheet(
      p: TextStyle(
        color: textColor,
        fontSize: portraitModeNotifier.getPortraitModeTextSize(14),
        height: 1.4,
      ),
      h1: TextStyle(
        color: textColor,
        fontSize: portraitModeNotifier.getPortraitModeTextSize(20),
        fontWeight: FontWeight.bold,
        height: 1.3,
      ),
      h2: TextStyle(
        color: textColor,
        fontSize: portraitModeNotifier.getPortraitModeTextSize(18),
        fontWeight: FontWeight.bold,
        height: 1.3,
      ),
      h3: TextStyle(
        color: textColor,
        fontSize: portraitModeNotifier.getPortraitModeTextSize(16),
        fontWeight: FontWeight.bold,
        height: 1.3,
      ),
      code: TextStyle(
        color: textColor,
        fontSize: portraitModeNotifier.getPortraitModeTextSize(13),
        fontFamily: 'monospace',
        backgroundColor: Colors.grey.withOpacity(0.1),
      ),
      codeblockDecoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      blockquote: TextStyle(
        color: textColor.withOpacity(0.8),
        fontSize: portraitModeNotifier.getPortraitModeTextSize(14),
        fontStyle: FontStyle.italic,
      ),
      strong: TextStyle(
        color: textColor,
        fontSize: portraitModeNotifier.getPortraitModeTextSize(14),
        fontWeight: FontWeight.bold,
      ),
      em: TextStyle(
        color: textColor,
        fontSize: portraitModeNotifier.getPortraitModeTextSize(14),
        fontStyle: FontStyle.italic,
      ),
    );

    return MarkdownBody(
      data: message.content,
      styleSheet: markdownStyleSheet,
      selectable: true,
      onTapLink: (text, href, title) {
        if (href != null) {
          // 处理链接点击
          debugPrint('Link tapped: $href');
        }
      },
    );
  }

  Widget _buildImageContent(BuildContext context, PortraitModeProvider portraitModeNotifier) {
    final images = message.images;
    if (images == null || images.isEmpty) return const SizedBox.shrink();

    if (images.length == 1) {
      return _buildSingleImage(context, images.first, portraitModeNotifier);
    } else {
      return _buildImageGrid(context, images, portraitModeNotifier);
    }
  }

  Widget _buildSingleImage(BuildContext context, ImageAttachment image, PortraitModeProvider portraitModeNotifier) {
    // 图片最大尺寸应该稍小于消息气泡最大宽度，留出内边距空间
    final screenWidth = MediaQuery.of(context).size.width;
    final bubbleMaxWidth = portraitModeNotifier.getMessageBubbleMaxWidth(screenWidth);
    final imagePadding = portraitModeNotifier.getPortraitModePadding(const EdgeInsets.symmetric(horizontal: 16)).horizontal;
    final maxSize = (bubbleMaxWidth - imagePadding).clamp(100.0, 400.0);

    return Container(
      constraints: BoxConstraints(
        maxWidth: maxSize,
        maxHeight: maxSize,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: GestureDetector(
          onTap: () => _showImageDialog(context, image),
          child: _buildImageWidget(image),
        ),
      ),
    );
  }

  Widget _buildImageGrid(BuildContext context, List<ImageAttachment> images, PortraitModeProvider portraitModeNotifier) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: images.length,
      itemBuilder: (context, index) {
        final image = images[index];
        return ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: GestureDetector(
            onTap: () => _showImageDialog(context, image),
            child: _buildImageWidget(image),
          ),
        );
      },
    );
  }

  Widget _buildImageWidget(ImageAttachment image) {
    if (image.base64Data != null) {
      try {
        final bytes = base64Decode(image.base64Data!);
        return Image.memory(
          bytes,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: Colors.grey[300],
              child: const Icon(Icons.broken_image, size: 50),
            );
          },
        );
      } catch (e) {
        return Container(
          color: Colors.grey[300],
          child: const Icon(Icons.broken_image, size: 50),
        );
      }
    } else if (image.localPath != null) {
      return Image.asset(
        image.localPath!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Colors.grey[300],
            child: const Icon(Icons.broken_image, size: 50),
          );
        },
      );
    } else {
      return Container(
        color: Colors.grey[300],
        child: const Icon(Icons.broken_image, size: 50),
      );
    }
  }

  Widget _buildTypingIndicator(Color textColor, PortraitModeProvider portraitModeNotifier) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: portraitModeNotifier.getPortraitModeTextSize(20),
          height: portraitModeNotifier.getPortraitModeTextSize(20),
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(textColor),
          ),
        ),
        SizedBox(width: portraitModeNotifier.getPortraitModeSpacing(8)),
        Text(
          '正在输入...',
          style: TextStyle(
            color: textColor.withOpacity(0.7),
            fontSize: portraitModeNotifier.getPortraitModeTextSize(14),
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }

  Widget _buildErrorActions(ThemeData theme, PortraitModeProvider portraitModeNotifier) {
    return Padding(
      padding: EdgeInsets.only(top: portraitModeNotifier.getPortraitModeSpacing(8)),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.error_outline,
            size: portraitModeNotifier.getPortraitModeTextSize(16),
            color: theme.colorScheme.error,
          ),
          SizedBox(width: portraitModeNotifier.getPortraitModeSpacing(4)),
          Text(
            '发送失败',
            style: TextStyle(
              color: theme.colorScheme.error,
              fontSize: portraitModeNotifier.getPortraitModeTextSize(12),
            ),
          ),
          if (onResend != null && !isRetrying) ...[
            SizedBox(width: portraitModeNotifier.getPortraitModeSpacing(8)),
            TextButton(
              onPressed: onResend,
              style: TextButton.styleFrom(
                padding: EdgeInsets.symmetric(
                  horizontal: portraitModeNotifier.getPortraitModeSpacing(8),
                  vertical: portraitModeNotifier.getPortraitModeSpacing(4),
                ),
                minimumSize: Size.zero,
              ),
              child: Text(
                '重试',
                style: TextStyle(
                  fontSize: portraitModeNotifier.getPortraitModeTextSize(12),
                ),
              ),
            ),
          ],
          if (isRetrying)
            SizedBox(
              width: portraitModeNotifier.getPortraitModeTextSize(16),
              height: portraitModeNotifier.getPortraitModeTextSize(16),
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: theme.colorScheme.primary,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMessageInfo(BuildContext context, ThemeData theme, PortraitModeProvider portraitModeNotifier) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          DateFormat('HH:mm').format(message.timestamp),
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.6),
            fontSize: portraitModeNotifier.getPortraitModeTextSize(11),
          ),
        ),

        if (message.isUser) ...[
          SizedBox(width: portraitModeNotifier.getPortraitModeSpacing(4)),
          _buildMessageStatus(theme, portraitModeNotifier),
        ],

        if (!message.isUser && !message.isSystem && !message.isTyping) ...[
          SizedBox(width: portraitModeNotifier.getPortraitModeSpacing(8)),
          InkWell(
            onTap: () => _copyToClipboard(context, message.content),
            child: Icon(
              Icons.copy,
              size: portraitModeNotifier.getPortraitModeTextSize(14),
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildMessageStatus(ThemeData theme, PortraitModeProvider portraitModeNotifier) {
    IconData iconData;
    Color iconColor;

    switch (message.status) {
      case MessageStatus.sending:
        iconData = Icons.access_time;
        iconColor = theme.colorScheme.onSurface.withOpacity(0.6);
        break;
      case MessageStatus.sent:
        iconData = Icons.check;
        iconColor = theme.colorScheme.onSurface.withOpacity(0.6);
        break;
      case MessageStatus.delivered:
        iconData = Icons.done_all;
        iconColor = theme.colorScheme.primary;
        break;
      case MessageStatus.failed:
        iconData = Icons.error_outline;
        iconColor = theme.colorScheme.error;
        break;
      default:
        iconData = Icons.help_outline;
        iconColor = theme.colorScheme.onSurface.withOpacity(0.6);
    }

    return Icon(
      iconData,
      size: portraitModeNotifier.getPortraitModeTextSize(12),
      color: iconColor,
    );
  }

  void _showImageDialog(BuildContext context, ImageAttachment image) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 800, maxHeight: 600),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AppBar(
                title: const Text('图片预览'),
                automaticallyImplyLeading: false,
                actions: [
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
              Expanded(
                child: InteractiveViewer(
                  child: _buildImageWidget(image),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _copyToClipboard(BuildContext context, String text) {
    Clipboard.setData(ClipboardData(text: text));

    // 显示复制成功的短暂提示
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('复制成功'),
        duration: Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.all(16),
        showCloseIcon: false,
      ),
    );
  }
}
