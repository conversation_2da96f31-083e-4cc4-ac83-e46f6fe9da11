import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 应用主题配置类
class AppTheme {
  // 主色调
  static const Color primaryColor = Color(0xFF4F46E5);
  static const Color secondaryColor = Color(0xFF7C3AED);
  static const Color accentColor = Color(0xFFEC4899);

  // 语义化颜色
  static const Color successColor = Color(0xFF10B981);
  static const Color warningColor = Color(0xFFF59E0B);
  static const Color errorColor = Color(0xFFEF4444);
  static const Color infoColor = Color(0xFF3B82F6);

  // 明亮主题颜色
  static const Color lightBackground = Color(0xFFF8F9FA);
  static const Color lightSurface = Color(0xFFFFFFFF);
  static const Color lightCardColor = Color(0xFFFFFFFF);
  static const Color lightTextPrimary = Color(0xFF1A202C);
  static const Color lightTextSecondary = Color(0xFF4A5568);
  static const Color lightBorder = Color(0xFFE2E8F0);
  static const Color lightDivider = Color(0xFFEDF2F7);

  // 暗色主题颜色
  static const Color darkBackground = Color(0xFF0F172A);
  static const Color darkSurface = Color(0xFF1E293B);
  static const Color darkCardColor = Color(0xFF334155);
  static const Color darkTextPrimary = Color(0xFFF1F5F9);
  static const Color darkTextSecondary = Color(0xFFCBD5E1);
  static const Color darkBorder = Color(0xFF475569);
  static const Color darkDivider = Color(0xFF374151);

  /// 获取明亮主题
  static ThemeData get lightTheme {
    final colorScheme = ColorScheme.light(
      primary: primaryColor,
      secondary: secondaryColor,
      surface: lightSurface,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: lightTextPrimary,
      background: lightBackground,
      error: errorColor,
      onError: Colors.white,
      brightness: Brightness.light,
    );

    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: colorScheme,
      scaffoldBackgroundColor: lightBackground,
      fontFamily: 'LXGWWenKai',
      textTheme: _buildTextTheme(lightTextPrimary, lightTextSecondary),
      appBarTheme: _buildAppBarTheme(colorScheme, true),
      cardTheme: _buildCardTheme(true),
      elevatedButtonTheme: _buildElevatedButtonTheme(colorScheme),
      inputDecorationTheme: _buildInputDecorationTheme(colorScheme, true),
      dividerTheme: _buildDividerTheme(true),
    );
  }

  /// 获取暗色主题
  static ThemeData get darkTheme {
    final colorScheme = ColorScheme.dark(
      primary: primaryColor,
      secondary: secondaryColor,
      surface: darkSurface,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: darkTextPrimary,
      background: darkBackground,
      error: errorColor,
      onError: Colors.white,
      brightness: Brightness.dark,
    );

    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: colorScheme,
      scaffoldBackgroundColor: darkBackground,
      fontFamily: 'LXGWWenKai',
      textTheme: _buildTextTheme(darkTextPrimary, darkTextSecondary),
      appBarTheme: _buildAppBarTheme(colorScheme, false),
      cardTheme: _buildCardTheme(false),
      elevatedButtonTheme: _buildElevatedButtonTheme(colorScheme),
      inputDecorationTheme: _buildInputDecorationTheme(colorScheme, false),
      dividerTheme: _buildDividerTheme(false),
    );
  }

  static TextTheme _buildTextTheme(Color primary, Color secondary) {
    return TextTheme(
      displayLarge: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: primary),
      titleLarge: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: primary),
      bodyLarge: TextStyle(fontSize: 16, color: primary),
      bodyMedium: TextStyle(fontSize: 14, color: secondary),
    );
  }

  static AppBarTheme _buildAppBarTheme(ColorScheme colors, bool isLight) {
    return AppBarTheme(
      backgroundColor: isLight ? lightSurface : darkSurface,
      elevation: 0,
      iconTheme: IconThemeData(color: colors.onSurface),
      titleTextStyle: TextStyle(color: colors.onSurface, fontSize: 20, fontWeight: FontWeight.bold, fontFamily: 'LXGWWenKai'),
      systemOverlayStyle: isLight ? SystemUiOverlayStyle.dark : SystemUiOverlayStyle.light,
    );
  }

  static CardTheme _buildCardTheme(bool isLight) {
    return CardTheme(
      elevation: 0,
      color: isLight ? lightCardColor : darkCardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: isLight ? lightBorder : darkBorder, width: 1),
      ),
    );
  }

  static ElevatedButtonThemeData _buildElevatedButtonTheme(ColorScheme colors) {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: colors.primary,
        foregroundColor: colors.onPrimary,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold, fontFamily: 'LXGWWenKai'),
      ),
    );
  }

  static InputDecorationTheme _buildInputDecorationTheme(ColorScheme colors, bool isLight) {
    return InputDecorationTheme(
      filled: true,
      fillColor: isLight ? lightBackground : darkSurface,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: isLight ? lightBorder : darkBorder),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: isLight ? lightBorder : darkBorder),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: colors.primary, width: 2),
      ),
    );
  }

  static DividerThemeData _buildDividerTheme(bool isLight) {
    return DividerThemeData(
      color: isLight ? lightDivider : darkDivider,
      thickness: 1,
      space: 1,
    );
  }
}
