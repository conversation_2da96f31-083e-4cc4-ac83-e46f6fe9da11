import{w as A}from"./weakAdminService-c6c1aace.js";import{_ as D}from"./_plugin-vue_export-helper-c27b6911.js";import{r as _,b as k,c as u,d as x,e as I,f as a,g as s,w as o,z as C,A as U,m as f,t as r,E as c,x as z}from"./index-0bf154dd.js";const L={class:"user-quota"},M={class:"user-info"},O={class:"info-row"},$={class:"value"},j={class:"info-row"},G={class:"value"},H={class:"info-row"},J={class:"value"},K={class:"info-row"},P={class:"info-row"},W={class:"value quota"},X={class:"info-row"},Y={class:"value"},Z={class:"info-row"},ee={class:"value"},ae={class:"info-row"},oe={class:"value"},se={__name:"UserQuotaView",setup(le){const w=_(),b=_(),v=k({username:""}),B={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:1,max:50,message:"用户名长度在 1 到 50 个字符",trigger:"blur"}]},t=k({operationType:"increase",quotaAmount:100,reason:""}),F={operationType:[{required:!0,message:"请选择操作类型",trigger:"change"}],quotaAmount:[{required:!0,message:"请输入算力数量",trigger:"blur"},{type:"number",min:1,max:1e5,message:"算力数量必须在1-100000之间",trigger:"blur"}]},g=_(!1),y=_(!1),l=_(null),R=async()=>{var n;if(w.value)try{if(!await w.value.validate())return;g.value=!0;const d=await A.getUserInfo(v.username);d.success?(l.value=d.data.user,c.success("查询成功")):(l.value=null,c.error(((n=d.error)==null?void 0:n.message)||"查询失败"))}catch(e){console.error("Search error:",e),l.value=null,c.error("查询用户失败")}finally{g.value=!1}},N=async()=>{var n;if(!(!b.value||!l.value))try{if(!await b.value.validate())return;const i=`确定要${t.operationType==="increase"?"增加":"减少"} ${t.quotaAmount} 算力吗？`;await z.confirm(i,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),y.value=!0;const p=await A.modifyUserQuota(l.value.username,t.operationType,t.quotaAmount,t.reason);p.success?(l.value.availableCount=p.data.user.availableCount,c.success(p.message),t.quotaAmount=100,t.reason=""):c.error(((n=p.error)==null?void 0:n.message)||"操作失败")}catch(e){e!=="cancel"&&(console.error("Operation error:",e),c.error("算力操作失败"))}finally{y.value=!1}},V=n=>n?new Date(n).toLocaleString("zh-CN"):"未知";return(n,e)=>{const d=u("el-input"),i=u("el-form-item"),p=u("el-button"),q=u("el-form"),h=u("el-card"),E=u("el-tag"),T=u("el-radio"),Q=u("el-radio-group"),S=u("el-input-number");return x(),I("div",L,[e[17]||(e[17]=a("div",{class:"page-header"},[a("h2",null,"用户算力管理"),a("p",null,"查询和管理用户算力")],-1)),s(h,{class:"search-card",shadow:"hover"},{header:o(()=>e[4]||(e[4]=[a("div",{class:"card-header"},[a("span",null,"用户查询")],-1)])),default:o(()=>[s(q,{ref_key:"searchFormRef",ref:w,model:v,rules:B,inline:"",class:"search-form"},{default:o(()=>[s(i,{label:"用户名",prop:"username"},{default:o(()=>[s(d,{modelValue:v.username,"onUpdate:modelValue":e[0]||(e[0]=m=>v.username=m),placeholder:"请输入用户名",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),s(i,null,{default:o(()=>[s(p,{type:"primary",loading:g.value,onClick:R},{default:o(()=>[f(r(g.value?"查询中...":"查询用户"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1}),l.value?(x(),C(h,{key:0,class:"user-info-card",shadow:"hover"},{header:o(()=>e[5]||(e[5]=[a("div",{class:"card-header"},[a("span",null,"用户信息")],-1)])),default:o(()=>[a("div",M,[a("div",O,[e[6]||(e[6]=a("span",{class:"label"},"用户名：",-1)),a("span",$,r(l.value.username),1)]),a("div",j,[e[7]||(e[7]=a("span",{class:"label"},"邮箱：",-1)),a("span",G,r(l.value.email||"未设置"),1)]),a("div",H,[e[8]||(e[8]=a("span",{class:"label"},"手机号：",-1)),a("span",J,r(l.value.phone||"未设置"),1)]),a("div",K,[e[9]||(e[9]=a("span",{class:"label"},"状态：",-1)),s(E,{type:l.value.status==="激活"?"success":"danger"},{default:o(()=>[f(r(l.value.status),1)]),_:1},8,["type"])]),a("div",P,[e[10]||(e[10]=a("span",{class:"label"},"当前算力：",-1)),a("span",W,r(l.value.availableCount),1)]),a("div",X,[e[11]||(e[11]=a("span",{class:"label"},"总使用次数：",-1)),a("span",Y,r(l.value.totalUsageCount),1)]),a("div",Z,[e[12]||(e[12]=a("span",{class:"label"},"注册时间：",-1)),a("span",ee,r(V(l.value.createdAt)),1)]),a("div",ae,[e[13]||(e[13]=a("span",{class:"label"},"最后登录：",-1)),a("span",oe,r(V(l.value.lastLoginAt)),1)])])]),_:1})):U("",!0),l.value?(x(),C(h,{key:1,class:"operation-card",shadow:"hover"},{header:o(()=>e[14]||(e[14]=[a("div",{class:"card-header"},[a("span",null,"算力操作")],-1)])),default:o(()=>[s(q,{ref_key:"operationFormRef",ref:b,model:t,rules:F,class:"operation-form"},{default:o(()=>[s(i,{label:"操作类型",prop:"operationType"},{default:o(()=>[s(Q,{modelValue:t.operationType,"onUpdate:modelValue":e[1]||(e[1]=m=>t.operationType=m)},{default:o(()=>[s(T,{label:"increase"},{default:o(()=>e[15]||(e[15]=[f("增加算力")])),_:1,__:[15]}),s(T,{label:"decrease"},{default:o(()=>e[16]||(e[16]=[f("减少算力")])),_:1,__:[16]})]),_:1},8,["modelValue"])]),_:1}),s(i,{label:"算力数量",prop:"quotaAmount"},{default:o(()=>[s(S,{modelValue:t.quotaAmount,"onUpdate:modelValue":e[2]||(e[2]=m=>t.quotaAmount=m),min:1,max:1e5,placeholder:"请输入算力数量"},null,8,["modelValue"])]),_:1}),s(i,{label:"操作原因"},{default:o(()=>[s(d,{modelValue:t.reason,"onUpdate:modelValue":e[3]||(e[3]=m=>t.reason=m),type:"textarea",rows:3,placeholder:"请输入操作原因（可选）",maxlength:"200","show-word-limit":"",style:{width:"400px"}},null,8,["modelValue"])]),_:1}),s(i,null,{default:o(()=>[s(p,{type:"primary",loading:y.value,onClick:N},{default:o(()=>[f(r(y.value?"操作中...":"确认操作"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1})):U("",!0)])}}},ue=D(se,[["__scopeId","data-v-dd5ea17e"]]);export{ue as default};
