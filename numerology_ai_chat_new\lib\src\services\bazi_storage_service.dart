import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:url_launcher/url_launcher.dart';
import '../models/bazi_model.dart';

/// 八字存储管理服务
class BaziStorageService {
  static const String _indexFileName = 'bazi_index.json';
  
  /// 获取八字存储目录
  Future<Directory> _getBaziDirectory() async {
    final directory = await getApplicationDocumentsDirectory();
    final baziDir = Directory(path.join(directory.path, 'bazi_results'));
    
    if (!await baziDir.exists()) {
      await baziDir.create(recursive: true);
    }
    
    return baziDir;
  }

  /// 获取索引文件路径
  Future<File> _getIndexFile() async {
    final directory = await _getBaziDirectory();
    return File(path.join(directory.path, _indexFileName));
  }

  /// 保存八字记录到索引
  Future<void> saveBaziRecord(BaziResultModel result) async {
    try {
      final indexFile = await _getIndexFile();
      List<Map<String, dynamic>> records = [];
      
      // 读取现有记录
      if (await indexFile.exists()) {
        final content = await indexFile.readAsString();
        if (content.isNotEmpty) {
          final List<dynamic> jsonList = json.decode(content);
          records = jsonList.cast<Map<String, dynamic>>();
        }
      }
      
      // 检查是否已存在相同记录（基于姓名和出生日期）
      final existingIndex = records.indexWhere((record) {
        return record['name'] == result.input.name &&
               record['birth_date'] == result.input.birthDateTime.toIso8601String().split('T')[0];
      });
      
      // 创建新记录
      final newRecord = {
        'id': result.id,
        'name': result.input.name,
        'gender': result.input.gender.displayName,
        'birth_date': result.input.birthDateTime.toIso8601String().split('T')[0],
        'birth_time': '${result.input.birthDateTime.hour.toString().padLeft(2, '0')}:${result.input.birthDateTime.minute.toString().padLeft(2, '0')}',
        'calendar_type': result.input.calendarType.displayName,
        'birth_place': result.input.birthPlace,
        'file_path': result.detailedData['file_path'],
        'file_name': result.detailedData['file_name'],
        'four_pillars': result.fourPillars,
        'created_at': result.createdAt.toIso8601String(),
      };
      
      if (existingIndex >= 0) {
        // 更新现有记录
        records[existingIndex] = newRecord;
      } else {
        // 添加新记录
        records.add(newRecord);
      }
      
      // 按创建时间倒序排列
      records.sort((a, b) => DateTime.parse(b['created_at']).compareTo(DateTime.parse(a['created_at'])));
      
      // 保存到文件
      await indexFile.writeAsString(json.encode(records));
    } catch (e) {
      print('保存八字记录失败: $e');
      rethrow;
    }
  }

  /// 获取所有八字记录
  Future<List<BaziRecordSummary>> getAllBaziRecords() async {
    try {
      final indexFile = await _getIndexFile();
      
      if (!await indexFile.exists()) {
        return [];
      }
      
      final content = await indexFile.readAsString();
      if (content.isEmpty) {
        return [];
      }
      
      final List<dynamic> jsonList = json.decode(content);
      final records = jsonList.cast<Map<String, dynamic>>();
      
      return records.map((record) => BaziRecordSummary.fromJson(record)).toList();
    } catch (e) {
      print('读取八字记录失败: $e');
      return [];
    }
  }

  /// 删除八字记录
  Future<void> deleteBaziRecord(String recordId) async {
    try {
      final indexFile = await _getIndexFile();
      
      if (!await indexFile.exists()) {
        return;
      }
      
      final content = await indexFile.readAsString();
      if (content.isEmpty) {
        return;
      }
      
      final List<dynamic> jsonList = json.decode(content);
      final records = jsonList.cast<Map<String, dynamic>>();
      
      // 找到要删除的记录
      final recordToDelete = records.firstWhere(
        (record) => record['id'] == recordId,
        orElse: () => <String, dynamic>{},
      );
      
      if (recordToDelete.isNotEmpty) {
        // 删除文件
        final filePath = recordToDelete['file_path'] as String?;
        if (filePath != null) {
          final file = File(filePath);
          if (await file.exists()) {
            await file.delete();
          }
        }
        
        // 从记录中移除
        records.removeWhere((record) => record['id'] == recordId);
        
        // 保存更新后的记录
        await indexFile.writeAsString(json.encode(records));
      }
    } catch (e) {
      print('删除八字记录失败: $e');
      rethrow;
    }
  }

  /// 打开文件所在位置
  Future<void> openFileLocation(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('文件不存在');
      }
      
      // 获取文件所在目录
      final directory = file.parent;
      
      if (Platform.isWindows) {
        // Windows: 使用 explorer 打开文件夹并选中文件
        await Process.run('explorer', ['/select,', filePath]);
      } else if (Platform.isMacOS) {
        // macOS: 使用 Finder 打开文件夹并选中文件
        await Process.run('open', ['-R', filePath]);
      } else if (Platform.isLinux) {
        // Linux: 尝试使用文件管理器打开目录
        try {
          await Process.run('xdg-open', [directory.path]);
        } catch (e) {
          // 如果 xdg-open 失败，尝试其他文件管理器
          await Process.run('nautilus', [directory.path]);
        }
      } else {
        // 其他平台：使用 url_launcher 打开目录
        final uri = Uri.file(directory.path);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri);
        } else {
          throw Exception('无法打开文件位置');
        }
      }
    } catch (e) {
      print('打开文件位置失败: $e');
      rethrow;
    }
  }

  /// 读取八字文件内容
  Future<String> readBaziFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('文件不存在');
      }
      
      return await file.readAsString();
    } catch (e) {
      print('读取八字文件失败: $e');
      rethrow;
    }
  }

  /// 检查文件是否存在
  Future<bool> fileExists(String filePath) async {
    try {
      final file = File(filePath);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  /// 获取存储目录路径
  Future<String> getStorageDirectoryPath() async {
    final directory = await _getBaziDirectory();
    return directory.path;
  }

  /// 清除所有八字记录
  Future<void> clearAllBaziRecords() async {
    try {
      final indexFile = await _getIndexFile();

      // 如果索引文件存在，先读取所有记录以删除对应的文件
      if (await indexFile.exists()) {
        final content = await indexFile.readAsString();
        if (content.isNotEmpty) {
          final List<dynamic> jsonList = json.decode(content);
          final records = jsonList.cast<Map<String, dynamic>>();

          // 删除所有八字文件
          for (final record in records) {
            final filePath = record['file_path'] as String?;
            if (filePath != null) {
              final file = File(filePath);
              if (await file.exists()) {
                await file.delete();
              }
            }
          }
        }

        // 删除索引文件
        await indexFile.delete();
      }

      // 清空整个八字目录（如果存在其他文件）
      final baziDir = await _getBaziDirectory();
      if (await baziDir.exists()) {
        final files = await baziDir.list().toList();
        for (final file in files) {
          if (file is File) {
            await file.delete();
          }
        }
      }

      print('所有八字记录已清除');
    } catch (e) {
      print('清除八字记录失败: $e');
      rethrow;
    }
  }
}

/// 八字记录摘要模型
class BaziRecordSummary {
  final String id;
  final String name;
  final String gender;
  final String birthDate;
  final String birthTime;
  final String calendarType;
  final String birthPlace;
  final String? filePath;
  final String? fileName;
  final String fourPillars;
  final DateTime createdAt;

  const BaziRecordSummary({
    required this.id,
    required this.name,
    required this.gender,
    required this.birthDate,
    required this.birthTime,
    required this.calendarType,
    required this.birthPlace,
    this.filePath,
    this.fileName,
    required this.fourPillars,
    required this.createdAt,
  });

  factory BaziRecordSummary.fromJson(Map<String, dynamic> json) {
    return BaziRecordSummary(
      id: json['id'] as String,
      name: json['name'] as String,
      gender: json['gender'] as String,
      birthDate: json['birth_date'] as String,
      birthTime: json['birth_time'] as String,
      calendarType: json['calendar_type'] as String,
      birthPlace: json['birth_place'] as String,
      filePath: json['file_path'] as String?,
      fileName: json['file_name'] as String?,
      fourPillars: json['four_pillars'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'gender': gender,
      'birth_date': birthDate,
      'birth_time': birthTime,
      'calendar_type': calendarType,
      'birth_place': birthPlace,
      'file_path': filePath,
      'file_name': fileName,
      'four_pillars': fourPillars,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// 获取显示用的简短信息
  String get displayInfo => '$name ($gender) $birthDate $birthTime';
  
  /// 获取完整的出生信息
  String get fullBirthInfo => '$birthDate $birthTime ($calendarType)';
}
