import 'package:json_annotation/json_annotation.dart';

part 'page_model.g.dart';

@JsonSerializable()
class PageModel {
  @JsonKey(name: '_id')
  final String id;
  final String pageTitle;
  final String pageContent;
  final String pageType;
  final bool isActive;
  final int sortOrder;
  final String slug;
  final String? metaDescription;
  final List<String>? keywords;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? createdBy;
  final String? updatedBy;

  PageModel({
    required this.id,
    required this.pageTitle,
    required this.pageContent,
    required this.pageType,
    required this.isActive,
    required this.sortOrder,
    required this.slug,
    this.metaDescription,
    this.keywords,
    required this.createdAt,
    required this.updatedAt,
    this.createdBy,
    this.updatedBy,
  });

  factory PageModel.fromJson(Map<String, dynamic> json) => _$PageModelFromJson(json);
  Map<String, dynamic> toJson() => _$PageModelToJson(this);
}

@JsonSerializable()
class CreatePageRequest {
  final String pageTitle;
  final String pageContent;
  final String pageType;
  final bool isActive;
  final int sortOrder;
  final String slug;
  final String? metaDescription;
  final List<String>? keywords;

  CreatePageRequest({
    required this.pageTitle,
    required this.pageContent,
    required this.pageType,
    this.isActive = true,
    this.sortOrder = 0,
    required this.slug,
    this.metaDescription,
    this.keywords,
  });

  factory CreatePageRequest.fromJson(Map<String, dynamic> json) => _$CreatePageRequestFromJson(json);
  Map<String, dynamic> toJson() => _$CreatePageRequestToJson(this);
}

@JsonSerializable()
class UpdatePageRequest {
  final String? pageTitle;
  final String? pageContent;
  final String? pageType;
  final bool? isActive;
  final int? sortOrder;
  final String? slug;
  final String? metaDescription;
  final List<String>? keywords;

  UpdatePageRequest({
    this.pageTitle,
    this.pageContent,
    this.pageType,
    this.isActive,
    this.sortOrder,
    this.slug,
    this.metaDescription,
    this.keywords,
  });

  factory UpdatePageRequest.fromJson(Map<String, dynamic> json) => _$UpdatePageRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdatePageRequestToJson(this);
}
