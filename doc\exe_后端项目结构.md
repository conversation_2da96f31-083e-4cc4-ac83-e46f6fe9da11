# 后端云函数项目结构

## 一、概述

后端采用 **腾讯云开发 Cloud Functions (Node.js 16.13)** 作为 BaaS 层，仅需部署 **两个云函数**：

1. **exeFunction**（用户侧）
2. **exeAdmin**（管理侧）

二者通过统一的 **Action 路由参数** 区分具体业务，减少函数数量，便于维护与权限控制。

> AI 调用逻辑及 API Key 由独立的 **Go 中转服务** 承担，云函数仅负责业务数据（用户、智能体、模型、页面配置）与鉴权。

---

## 二、顶级目录结构

```text
cloudfunctions/
├─ exeFunction/        # 用户侧云函数
│  ├─ index.js         # 入口文件（路由分发）
│  ├─ package.json
│  ├─ src/
│  │  ├─ handlers/     # 各业务处理器
│  │  │  ├─ auth.js            # 登录 / 注册
│  │  │  ├─ agents.js          # 获取智能体列表
│  │  │  ├─ models.js          # 获取模型列表
│  │  │  └─ user_info.js       # 获取用户信息
│  │  ├─ middleware/
│  │  │  ├─ auth.js            # JWT 鉴权中间件
│  │  │  └─ error_handler.js   # 错误统一处理
│  │  └─ utils/
│  │      ├─ db.js             # Tencent Cloud DB 初始化
│  │      ├─ validate.js       # 参数校验
│  │      └─ logger.js         # 日志封装
│  └─ .env.example             # 环境变量模板
├─ exeAdmin/           # 管理侧云函数
│  ├─ index.js
│  ├─ package.json
│  ├─ src/
│  │  ├─ handlers/
│  │  │  ├─ admin_auth.js      # 管理员登录 & Token 校验
│  │  │  ├─ user_manage.js     # 用户 CRUD & 配额管理
│  │  │  ├─ agent_manage.js    # 智能体 CRUD
│  │  │  ├─ model_manage.js    # 模型 CRUD
│  │  │  ├─ page_manage.js     # 页面配置 CRUD
│  │  │  ├─ pricing_tier_manage.js # 算力档次 CRUD
│  │  │  ├─ log_view.js        # 调用日志查询
│  │  │  └─ stats_view.js      # 调用统计
│  │  ├─ middleware/
│  │  │  ├─ auth.js
│  │  │  └─ error_handler.js
│  │  └─ utils/
│  │      ├─ db.js
│  │      ├─ validate.js
│  │      └─ logger.js
│  └─ .env.example
└─ shared/             # 可选公共模块（使用 layer 或代码复制）
   ├─ crypto.js        # AES / bcrypt 封装
   ├─ constants.js     # 常量 & 错误码
   └─ schema.js        # Joi 校验 Schema
```

---

## 三、Action 路由设计

调用云函数时，前端需携带 `action` 字段（或 `queryString` 参数），云函数根据值分发到对应处理器：

| 云函数 | Action | 处理器 | 需要鉴权 | 对应集合 |
|--------|--------|--------|----------|----------|
| exeFunction | `login` | `auth.login` | 否 | exe_users |
|  | `register` | `auth.register` | 否 | exe_users |
|  | `refreshToken` | `auth.refresh` | 否 | exe_users |
|  | `getAgents` | `agents.getList` | 是 | exe_agents |
|  | `getModels` | `models.getList` | 是 | exe_models |
|  | `getUserInfo` | `user_info.get` | 是 | exe_users |
|  | `updateUsage` | `user_info.updateUsage` | 是 | exe_users |
| exeAdmin | `adminLogin` | `admin_auth.login` | 否 | exe_admins |
|  | `listUsers` | `user_manage.list` | 是 | exe_users |
|  | `createUser` | `user_manage.create` | 是 | exe_users |
|  | `updateUserQuota` | `user_manage.updateQuota` | 是 | exe_users |
|  | `updateUserStatus` | `user_manage.updateStatus` | 是 | exe_users |
|  | `listAgents` | `agent_manage.list` | 是 | exe_agents |
|  | `createAgent` | `agent_manage.create` | 是 | exe_agents |
|  | `updateAgent` | `agent_manage.update` | 是 | exe_agents |
|  | `deleteAgent` | `agent_manage.delete` | 是 | exe_agents |
|  | `listModels` | `model_manage.list` | 是 | exe_models |
|  | `createModel` | `model_manage.create` | 是 | exe_models |
|  | `updateModel` | `model_manage.update` | 是 | exe_models |
|  | `deleteModel` | `model_manage.delete` | 是 | exe_models |
|  | `listPages` | `page_manage.list` | 是 | exe_pages |
|  | `updatePage` | `page_manage.update` | 是 | exe_pages |
|  | `listPricingTiers` | `pricing_tier_manage.list` | 是 | exe_pricing_tiers |
|  | `createPricingTier` | `pricing_tier_manage.create` | 是 | exe_pricing_tiers |
|  | `updatePricingTier` | `pricing_tier_manage.update` | 是 | exe_pricing_tiers |
|  | `deletePricingTier` | `pricing_tier_manage.delete` | 是 | exe_pricing_tiers |
|  | `viewLogs` | `log_view.list` | 是 | - |
|  | `stats` | `stats_view.overview` | 是 | - |

---

## 四、核心业务逻辑：算力管理

本节为云函数中算力相关功能的核心实现提供逻辑指导。

### 1. 算力充值 (`updateUserQuota`)

**输入参数**：`userId`, `addedCount`, `reason`

**核心处理流程**：
1.  根据 `userId`【用户ID】 查找用户。
2.  使用数据库原子操作，为 `availableCount`【可用算力】 字段增加 `addedCount`【增加的算力】 的值。
3.  在 `purchaseHistory`【购买历史】 中添加一条类型为"算力充值"的记录。

### 2. 算力扣费 (`updateUsage`)

**输入参数**：`userId`, `agentId`, `modelId`

**核心处理流程**：
1.  根据 `agentId` 查询智能体的 `pricingTierId`【档次ID】。
2.  根据 `pricingTierId` 查询档次的 `basicModelCost` 和 `advancedModelCost`。
3.  根据 `modelId` 查询模型的 `modelLevel`【模型等级】。
4.  计算实际扣费：初级模型使用 `basicModelCost`，高级模型使用 `advancedModelCost`。
5.  使用数据库原子操作扣减用户的 `availableCount`，增加 `totalUsageCount`。

### 3. 档次管理 (`pricing_tier_manage.js`)

**支持操作**：创建、查询、更新、删除算力档次

**档次结构**：
- `tierName`：档次名称（如基础档次、标准档次、高级档次）
- `basicModelCost`：初级模型每次对话扣除的算力
- `advancedModelCost`：高级模型每次对话扣除的算力

---

## 五、数据库映射

云函数内部推荐使用 **wx-server-sdk** 直连文档型数据库，它能自动识别当前环境，代码更简洁。各集合结构详见 `doc/数据库结构.md`。

```js
// utils/db.js
const cloud = require('wx-server-sdk')
// 在函数入口处初始化
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV // 这一行会自动识别并使用当前云函数所在的环境
})
// 直接获取数据库引用
const db = cloud.database()
module.exports = db
```

- 密码字段使用 **bcrypt** 加密
- 模型 API Key 使用 **AES** 加密，仅在 Go 服务解密

---

## 六、鉴权与安全

1. **JWT (Access Token + Refresh Token)**：
   - 用户登录成功后，返回两种 Token【令牌】：
     - **Access Token**【访问令牌】：用于访问受保护的资源，有效期短（如 2 小时）。
     - **Refresh Token**【刷新令牌】：用于在 Access Token 过期后获取新的 Access Token，有效期长（如 7-30 天）。
   - Refresh Token 会被存储在 `exe_users` 集合中，用于验证。
2. **中间件**：`middleware/auth.js` 校验 Access Token，验证通过后将 `userId` 注入 `ctx.state`。
3. **令牌刷新**：当前端请求因 Access Token 过期而失败时，应使用 Refresh Token 调用 `refreshToken` 接口来获取新的 Access Token，对用户实现无感刷新。
4. **参数校验**：所有输入通过 `Joi`/`Yup` 校验，避免注入攻击。
5. **最小权限**：数据库权限按云函数角色自动管理；建议在腾讯云控制台关闭外网匿名访问。
6. **日志**：所有操作写入 `exe_logs`（可选），便于審計。

---

## 七、部署流程

```