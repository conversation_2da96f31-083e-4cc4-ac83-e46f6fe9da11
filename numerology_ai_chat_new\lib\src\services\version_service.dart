import 'dart:convert';
import 'package:http/http.dart' as http;
import '../core/constants/app_constants.dart';
import '../models/api_response.dart';
import '../models/version_model.dart';

/// 版本检查服务
class VersionService {
  static const String _baseUrl = AppConstants.exeFunctionUrl;

  /// 检查版本更新
  /// 
  /// [currentVersion] 当前应用版本号
  /// 返回版本检查结果
  Future<VersionCheckResult> checkVersion({
    String? currentVersion,
  }) async {
    try {
      // 获取当前版本号，如果没有传入则使用应用常量中的版本号
      final version = currentVersion ?? AppConstants.appVersion;
      
      print('VersionService: 开始检查版本更新, 当前版本: $version');

      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'action': 'checkVersion',
          'currentVersion': version,
        }),
      ).timeout(AppConstants.connectTimeout);

      print('VersionService: 版本检查响应状态: ${response.statusCode}');
      print('VersionService: 版本检查响应内容: ${response.body}');

      if (response.statusCode != 200) {
        throw Exception('HTTP ${response.statusCode}: ${response.reasonPhrase}');
      }

      final jsonData = jsonDecode(response.body) as Map<String, dynamic>;
      
      // 解析API响应
      final apiResponse = ApiResponse.fromJson(
        jsonData,
        (data) => CloudFunctionData.fromJson(
          data as Map<String, dynamic>,
          (innerData) => VersionCheckApiResponse.fromJson(innerData as Map<String, dynamic>),
        ),
      );

      if (!apiResponse.isSuccess) {
        throw Exception('API错误: ${apiResponse.message}');
      }

      final cloudData = apiResponse.data;
      if (cloudData == null || !cloudData.success) {
        throw Exception('云函数错误: ${cloudData?.message ?? '未知错误'}');
      }

      // 解析版本检查响应数据
      final versionData = cloudData.data?.data;
      if (versionData == null) {
        throw Exception('版本检查数据为空');
      }

      final versionResponse = VersionCheckResponse.fromJson(versionData);
      
      print('VersionService: 版本检查成功, 结果: $versionResponse');
      
      return VersionCheckResult.success(versionResponse);

    } catch (e) {
      print('VersionService: 版本检查失败: $e');
      return VersionCheckResult.failure('版本检查失败: $e');
    }
  }

  /// 获取当前应用版本号
  String getCurrentVersion() {
    return AppConstants.appVersion;
  }

  /// 比较版本号
  /// 
  /// [version1] 版本号1
  /// [version2] 版本号2
  /// 返回: 1 表示 version1 > version2, 0 表示相等, -1 表示 version1 < version2
  int compareVersions(String version1, String version2) {
    final v1Parts = version1.split('.').map(int.parse).toList();
    final v2Parts = version2.split('.').map(int.parse).toList();
    
    final maxLength = v1Parts.length > v2Parts.length ? v1Parts.length : v2Parts.length;
    
    for (int i = 0; i < maxLength; i++) {
      final v1Part = i < v1Parts.length ? v1Parts[i] : 0;
      final v2Part = i < v2Parts.length ? v2Parts[i] : 0;
      
      if (v1Part > v2Part) return 1;
      if (v1Part < v2Part) return -1;
    }
    
    return 0;
  }

  /// 检查是否需要更新
  /// 
  /// [currentVersion] 当前版本
  /// [latestVersion] 最新版本
  bool needsUpdate(String currentVersion, String latestVersion) {
    return compareVersions(latestVersion, currentVersion) > 0;
  }

  /// 验证版本号格式
  /// 
  /// [version] 版本号字符串
  /// 返回是否为有效的语义化版本号
  bool isValidVersion(String version) {
    final regex = RegExp(r'^\d+\.\d+\.\d+$');
    return regex.hasMatch(version);
  }

  /// 格式化版本号显示
  /// 
  /// [version] 版本号
  /// [versionName] 版本名称
  String formatVersionDisplay(String version, String? versionName) {
    if (versionName != null && versionName.isNotEmpty) {
      return '$version ($versionName)';
    }
    return version;
  }

  /// 获取更新信息
  /// 
  /// [response] 版本检查响应
  UpdateInfo? getUpdateInfo(VersionCheckResponse response) {
    if (!response.hasUpdate) {
      return null;
    }
    
    return UpdateInfo.fromVersionCheckResponse(response);
  }

  /// 检查版本兼容性
  /// 
  /// [currentVersion] 当前版本
  /// [minSupportedVersion] 最小支持版本
  bool isVersionSupported(String currentVersion, String minSupportedVersion) {
    return compareVersions(currentVersion, minSupportedVersion) >= 0;
  }

  /// 获取版本更新类型
  /// 
  /// [currentVersion] 当前版本
  /// [latestVersion] 最新版本
  VersionUpdateType getUpdateType(String currentVersion, String latestVersion) {
    if (!needsUpdate(currentVersion, latestVersion)) {
      return VersionUpdateType.none;
    }

    final currentParts = currentVersion.split('.').map(int.parse).toList();
    final latestParts = latestVersion.split('.').map(int.parse).toList();

    // 主版本号变化
    if (latestParts[0] > currentParts[0]) {
      return VersionUpdateType.major;
    }

    // 次版本号变化
    if (latestParts.length > 1 && currentParts.length > 1 && latestParts[1] > currentParts[1]) {
      return VersionUpdateType.minor;
    }

    // 修订版本号变化
    return VersionUpdateType.patch;
  }
}

/// 版本更新类型
enum VersionUpdateType {
  /// 无更新
  none,
  /// 补丁更新
  patch,
  /// 次要更新
  minor,
  /// 主要更新
  major,
}

extension VersionUpdateTypeExtension on VersionUpdateType {
  /// 获取更新类型描述
  String get description {
    switch (this) {
      case VersionUpdateType.none:
        return '无更新';
      case VersionUpdateType.patch:
        return '补丁更新';
      case VersionUpdateType.minor:
        return '功能更新';
      case VersionUpdateType.major:
        return '重大更新';
    }
  }

  /// 是否为重要更新
  bool get isImportant {
    return this == VersionUpdateType.major || this == VersionUpdateType.minor;
  }
}
