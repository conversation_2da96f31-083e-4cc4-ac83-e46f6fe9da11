package api

import (
	"go_proxy/internal/api/handlers"
	"go_proxy/internal/config"
	"go_proxy/internal/middleware"

	"github.com/gin-gonic/gin"
)

// NewRouter 创建新的路由器
func NewRouter(cfg *config.Config) *gin.Engine {
	router := gin.New()

	// 添加中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// CORS中间件
	router.Use(func(c *gin.Context) {
		c.<PERSON>er("Access-Control-Allow-Origin", "*")
		c.<PERSON>er("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON>er("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		c.Next()
	})

	// 健康检查端点（无需鉴权）
	router.GET("/healthz", handlers.HealthCheck)

	// API v1 路由组
	v1 := router.Group("/v1")
	{
		// 聊天完成端点（需要鉴权）
		v1.POST("/chat/completions", middleware.AuthMiddleware(cfg), handlers.HandleChatCompletion(cfg))
	}

	// API 路由组（兼容前端配置）
	api := router.Group("/api")
	{
		// 聊天完成端点（需要鉴权）
		api.POST("/chat/completions", middleware.AuthMiddleware(cfg), handlers.HandleChatCompletion(cfg))
	}

	return router
}