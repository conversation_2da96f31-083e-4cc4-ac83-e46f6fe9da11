# 图片选择功能使用说明

## 功能概述

numerology_ai_chat_new项目已成功集成图片选择功能，用户可以在聊天对话中选择图片并发送给大语言模型进行分析。

## 主要功能

### 1. 图片选择
- **相册选择**：支持从相册中选择多张图片（最多5张）
- **相机拍照**：支持直接使用相机拍照
- **格式支持**：JPEG、PNG、GIF、WebP格式
- **大小限制**：单张图片最大2MB，自动压缩超大图片

### 2. 图片预览
- **缩略图显示**：选择后在输入框上方显示80x80像素预览
- **删除功能**：每张图片右上角有删除按钮
- **网格布局**：多张图片以网格形式排列

### 3. 图片发送
- **纯图片消息**：可以只发送图片，不包含文字
- **图文混合**：可以图片和文字一起发送
- **消息类型**：自动识别消息类型（文本/图片/混合）

### 4. 图片显示
- **单张图片**：在聊天气泡中显示，最大300x300像素
- **多张图片**：网格布局显示，支持2x2或3x3网格
- **点击放大**：点击图片可以放大查看
- **错误处理**：图片加载失败显示占位符

## 使用步骤

### 步骤1：选择图片
1. 在聊天界面，点击输入框左侧的**图片按钮**（📷图标）
2. 选择图片来源：
   - **从相册选择**：可以选择多张图片
   - **拍照**：使用相机拍摄新照片

### 步骤2：预览和编辑
1. 选择的图片会在输入框上方显示预览
2. 如需删除某张图片，点击图片右上角的**×**按钮
3. 可以继续添加更多图片（总数不超过5张）

### 步骤3：发送消息
1. **纯图片**：选择图片后直接点击发送按钮
2. **图文混合**：选择图片后在文本框中输入文字，再点击发送
3. 发送按钮会根据是否有内容（文字或图片）自动启用/禁用

### 步骤4：查看图片
1. 发送的图片会在聊天记录中显示
2. 点击任意图片可以放大查看
3. 在放大视图中可以缩放和拖拽图片

## 技术特性

### 图片处理
- **自动压缩**：超过2MB或1920x1920像素的图片会自动压缩
- **格式转换**：大图片会转换为JPEG格式以减小文件大小
- **质量控制**：压缩质量设置为85%，平衡文件大小和图片质量

### 存储策略
- **本地缓存**：图片临时存储在应用缓存目录
- **Base64编码**：图片数据以Base64格式传输给后端
- **自动清理**：应用会自动清理过期的缓存文件

### 安全性
- **文件验证**：检查文件类型和大小，拒绝不支持的格式
- **错误处理**：网络错误或文件损坏时显示友好的错误提示
- **内存管理**：及时释放图片资源，避免内存泄漏

## 注意事项

### 使用限制
1. **数量限制**：单次最多选择5张图片
2. **大小限制**：单张图片最大2MB
3. **格式限制**：仅支持JPEG、PNG、GIF、WebP格式
4. **网络要求**：发送图片需要网络连接

### 最佳实践
1. **图片质量**：建议使用清晰、高质量的图片以获得更好的AI分析效果
2. **文件大小**：虽然支持自动压缩，但建议选择适中大小的图片以提高传输速度
3. **内容相关**：选择与对话主题相关的图片，以获得更准确的AI回复
4. **隐私保护**：注意不要发送包含敏感信息的图片

### 故障排除
1. **选择失败**：检查应用是否有相册和相机权限
2. **发送失败**：检查网络连接和图片格式
3. **显示异常**：重启应用或清理缓存
4. **性能问题**：减少同时选择的图片数量

## 后端支持

### Go中转服务
- 图片数据通过Go中转服务传递给大语言模型
- 支持图片的base64编码传输
- 包含图片元信息（尺寸、文件大小、MIME类型）

### 数据格式
```json
{
  "role": "user",
  "content": "请分析这张图片",
  "images": [
    {
      "id": "1703123456789",
      "base64_data": "data:image/jpeg;base64,/9j/4AAQ...",
      "mime_type": "image/jpeg",
      "width": 1920,
      "height": 1080,
      "file_size": 1048576
    }
  ],
  "message_type": "mixed"
}
```

## 更新日志

### v1.0.0 (2025-06-15)
- ✅ 初始版本发布
- ✅ 支持图片选择和发送
- ✅ 支持图片预览和删除
- ✅ 支持图片显示和放大查看
- ✅ 集成Go中转服务
- ✅ 完整的错误处理和用户反馈

---

**开发团队**：numerology_ai_chat_new项目组  
**更新时间**：2025年6月15日  
**版本**：v1.0.0
