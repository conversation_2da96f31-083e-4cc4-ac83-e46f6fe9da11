import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/payment_log_model.dart';
import '../services/admin_api_service.dart';
import 'auth_provider.dart';

class PaymentLogState {
  final List<PaymentLogModel> logs;
  final bool isLoading;
  final String? error;
  final int currentPage;
  final int totalPages;
  final int totalCount;
  final String searchQuery;
  final String operationTypeFilter;
  final String statusFilter;

  const PaymentLogState({
    this.logs = const [],
    this.isLoading = false,
    this.error,
    this.currentPage = 1,
    this.totalPages = 1,
    this.totalCount = 0,
    this.searchQuery = '',
    this.operationTypeFilter = '',
    this.statusFilter = '',
  });

  PaymentLogState copyWith({
    List<PaymentLogModel>? logs,
    bool? isLoading,
    String? error,
    int? currentPage,
    int? totalPages,
    int? totalCount,
    String? searchQuery,
    String? operationTypeFilter,
    String? statusFilter,
  }) {
    return PaymentLogState(
      logs: logs ?? this.logs,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      totalCount: totalCount ?? this.totalCount,
      searchQuery: searchQuery ?? this.searchQuery,
      operationTypeFilter: operationTypeFilter ?? this.operationTypeFilter,
      statusFilter: statusFilter ?? this.statusFilter,
    );
  }
}

class PaymentLogNotifier extends StateNotifier<PaymentLogState> {
  final AdminApiService _apiService;
  final Ref _ref;

  PaymentLogNotifier(this._apiService, this._ref) : super(const PaymentLogState());

  Future<void> loadPaymentLogs({
    int page = 1,
    int limit = 20,
    String? search,
    String? operationType,
    String? status,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final authState = _ref.read(authProvider);
      if (authState.token == null) {
        state = state.copyWith(isLoading: false, error: '未登录');
        return;
      }

      final response = await _apiService.getPaymentLogList(
        authState.token!,
        page: page,
        limit: limit,
        search: search,
        operationType: operationType,
        status: status,
      );

      if (response['code'] == 0) {
        final outerData = response['data'] as Map<String, dynamic>;
        final innerData = outerData['data'] as Map<String, dynamic>;
        final logsData = innerData['logs'] as List;
        final logs = logsData.map((json) => PaymentLogModel.fromJson(json)).toList();
        
        state = state.copyWith(
          logs: logs,
          isLoading: false,
          currentPage: page,
          totalCount: innerData['total'] ?? 0,
          totalPages: ((innerData['total'] ?? 0) / limit).ceil(),
          searchQuery: search ?? '',
          operationTypeFilter: operationType ?? '',
          statusFilter: status ?? '',
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response['message'] ?? '加载支付日志失败',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: '网络错误: $e',
      );
    }
  }

  Future<void> refreshLogs() async {
    await loadPaymentLogs(
      page: state.currentPage,
      search: state.searchQuery.isNotEmpty ? state.searchQuery : null,
      operationType: state.operationTypeFilter.isNotEmpty ? state.operationTypeFilter : null,
      status: state.statusFilter.isNotEmpty ? state.statusFilter : null,
    );
  }

  Future<void> searchLogs(String query) async {
    await loadPaymentLogs(
      page: 1,
      search: query.isNotEmpty ? query : null,
      operationType: state.operationTypeFilter.isNotEmpty ? state.operationTypeFilter : null,
      status: state.statusFilter.isNotEmpty ? state.statusFilter : null,
    );
  }

  Future<void> filterByOperationType(String operationType) async {
    await loadPaymentLogs(
      page: 1,
      search: state.searchQuery.isNotEmpty ? state.searchQuery : null,
      operationType: operationType.isNotEmpty ? operationType : null,
      status: state.statusFilter.isNotEmpty ? state.statusFilter : null,
    );
  }

  Future<void> filterByStatus(String status) async {
    await loadPaymentLogs(
      page: 1,
      search: state.searchQuery.isNotEmpty ? state.searchQuery : null,
      operationType: state.operationTypeFilter.isNotEmpty ? state.operationTypeFilter : null,
      status: status.isNotEmpty ? status : null,
    );
  }

  Future<void> loadPage(int page) async {
    await loadPaymentLogs(
      page: page,
      search: state.searchQuery.isNotEmpty ? state.searchQuery : null,
      operationType: state.operationTypeFilter.isNotEmpty ? state.operationTypeFilter : null,
      status: state.statusFilter.isNotEmpty ? state.statusFilter : null,
    );
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

final paymentLogProvider = StateNotifierProvider<PaymentLogNotifier, PaymentLogState>((ref) {
  final apiService = ref.read(adminApiServiceProvider);
  return PaymentLogNotifier(apiService, ref);
});
