#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
八字排盘修复后的完整验证测试
测试三个关键样例，验证修复效果
"""

import requests
import json
import time

# 云函数API地址
API_URL = "https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exeFunction"

def test_bazi_sample(name, description, request_data, expected_results):
    """测试单个八字样例"""
    print(f"\n=== {name} ===")
    print(f"描述：{description}")
    print(f"请求数据：{json.dumps(request_data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(
            API_URL,
            headers={'Content-Type': 'application/json'},
            json=request_data,
            timeout=30
        )
        
        if response.status_code != 200:
            print(f"❌ HTTP错误：{response.status_code}")
            return False
            
        result = response.json()
        
        if result.get('code') != 0:
            print(f"❌ API错误：{result.get('message', '未知错误')}")
            return False
            
        data = result.get('data', {})
        bazi_str = data.get('baziStr', '')
        
        print(f"✅ 请求成功")
        print(f"八字结果：{bazi_str}")
        
        # 验证预期结果
        success = True
        for key, expected_value in expected_results.items():
            actual_value = data.get(key, {})
            if isinstance(actual_value, dict) and 'ganZhi' in actual_value:
                actual_value = actual_value['ganZhi']
            
            if actual_value == expected_value:
                print(f"✅ {key}：{actual_value} （正确）")
            else:
                print(f"❌ {key}：{actual_value} （预期：{expected_value}）")
                success = False
        
        return success
        
    except Exception as e:
        print(f"❌ 请求异常：{str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始八字排盘修复验证测试")
    print("=" * 60)
    
    # 样例1：年柱测试 - 2024年2月4日16点26分（立春前1分钟）
    sample1_success = test_bazi_sample(
        "样例1：年柱测试",
        "2024年2月4日16点26分，立春时间是16点27分，年柱应为癸卯",
        {
            "action": "baziAnalyze",
            "name": "测试者1",
            "gender": "男",
            "calendarType": "公历",
            "birthDate": "2024-02-04",
            "birthTime": "16:26",
            "isLeapMonth": False,
            "birthPlace": "北京",
            "ziTimeHandling": 0  # 晚子时（默认）
        },
        {
            "year": "癸卯",  # 预期年柱
        }
    )
    
    time.sleep(1)  # 避免请求过快
    
    # 样例2a：子时测试（晚子时） - 1990年12月15日23点30分
    sample2a_success = test_bazi_sample(
        "样例2a：子时测试（晚子时）",
        "1990年12月15日23点30分，使用晚子时规则，日柱应为甲寅",
        {
            "action": "baziAnalyze",
            "name": "测试者2a",
            "gender": "男",
            "calendarType": "公历",
            "birthDate": "1990-12-15",
            "birthTime": "23:30",
            "isLeapMonth": False,
            "birthPlace": "广州",
            "ziTimeHandling": 0  # 晚子时
        },
        {
            "day": "甲寅",  # 预期日柱（晚子时）
        }
    )
    
    time.sleep(1)
    
    # 样例2b：子时测试（早子时） - 1990年12月15日23点30分
    sample2b_success = test_bazi_sample(
        "样例2b：子时测试（早子时）",
        "1990年12月15日23点30分，使用早子时规则，日柱应为乙卯",
        {
            "action": "baziAnalyze",
            "name": "测试者2b",
            "gender": "男",
            "calendarType": "公历",
            "birthDate": "1990-12-15",
            "birthTime": "23:30",
            "isLeapMonth": False,
            "birthPlace": "广州",
            "ziTimeHandling": 1  # 早子时
        },
        {
            "day": "乙卯",  # 预期日柱（早子时）
        }
    )
    
    time.sleep(1)
    
    # 样例3：月柱节气交接测试 - 1990年11月8日23点30分
    sample3_success = test_bazi_sample(
        "样例3：月柱节气交接测试",
        "1990年11月8日23点30分，立冬时间是0点23分，月柱应为丁亥",
        {
            "action": "baziAnalyze",
            "name": "测试者3",
            "gender": "男",
            "calendarType": "公历",
            "birthDate": "1990-11-08",
            "birthTime": "23:30",
            "isLeapMonth": False,
            "birthPlace": "测试地",
            "ziTimeHandling": 0  # 晚子时（默认）
        },
        {
            "month": "丁亥",  # 预期月柱
        }
    )
    
    # 汇总测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    total_tests = 4
    passed_tests = sum([sample1_success, sample2a_success, sample2b_success, sample3_success])
    
    print(f"总测试数：{total_tests}")
    print(f"通过测试：{passed_tests}")
    print(f"失败测试：{total_tests - passed_tests}")
    print(f"通过率：{passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！八字排盘修复成功！")
        print("✅ 年柱节气交接问题已修复")
        print("✅ 子时处理规则已正确实现")
        print("✅ 月柱节气交接问题已修复")
        print("✅ 用户可以自由选择子时处理规则")
    else:
        print(f"\n⚠️  有 {total_tests - passed_tests} 个测试失败，需要进一步检查")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
